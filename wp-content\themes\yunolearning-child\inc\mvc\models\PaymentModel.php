<?php

namespace V4;

use Razorpay\Api\Api;

/**
 * Payment model
 */

class PaymentModel extends Model
{
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('locale');
        $this->loadLibary('utility');
    }

    public function getPayment($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // doc id generated randomly
            $paymentDataResponse = $this->es->read('paymentsuccessevent', 'paymentsuccessevent-' . $query['id']);
            $payment = $paymentDataResponse['body']['_source']['data']['details'];
        } elseif ($query['custom']) {
            $paymentDataResponse = $this->es->customQuery($query['custom'], 'paymentsuccessevent');
            if ($paymentDataResponse['status_code'] == 200) {
                $payment = $paymentDataResponse['body']['hits']['hits'][0]['_source']['data']['details'];
            } else {
                return false;
            }
        } else {
            return false;
        }

        if (isset($payment)) {
            $this->loadModel('locale');
            $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
            $tax = $this->localeModel->getTax($ccCode);
            //get enrollment details
            $enrollmentQuery['custom'] = [
                "query" => [
                    "terms" => [
                        "data.details.enrollment_id" => $payment['enrollment_id'] // Use the keyword subfield
                    ]
                ]
            ];
            $enrollmentData = $this->load->subData("enrollment", "getEnrollment", $enrollmentQuery);
            $paymentResponse = [
                'id' => $payment['payment_id'],
                'date' => [
                    'time' => $this->dt->convertToSystemDT($payment['event_date']),  // Assuming the event_date is the transaction time
                    'timezone' => $this->locale->activeTimezone(),               // Adjust the timezone if available in the data
                ],
                'mode' => $payment['payment_method'],             // Hardcoded, as no matching field in data
                'status' => $payment['link_status'] ?? '',
                'full_part' => $enrollmentData['full_part']['type'] ?? '',          // Assumed as full payment since no field exists for this
                'item' => [
                    'type' => 'ENROLLMENT',            // You can dynamically update this based on the data
                    'item_id' => $payment['enrollment_id'], // Assuming 'batch_name' is the item ID
                ],
                'gateway' => [
                    'platform' => 'OFFLINE',            // Payment method is Offline, assume platform to be the same
                    'name' => $payment['payment_method'],
                    'fav_icon' => [
                        'url' => '', // No direct favicon, assuming this image
                        'alt_text' => ''
                    ],
                    'transaction_id' => $payment['receipt_id'],
                ],
                'currency' => $this->load->subData("user", "getUser", $payment['user_id'], ['key' => 'app_currency']),
                'amount' => $payment['link_amount'],
                'amount_due' => $payment['link_amount'],
                'tax' => $tax
            ];

            // Validate the formatted response using the 'Enrollment' schema
            return $this->schema->validate($paymentResponse, 'Payment', $filter);
        }

        return false;
    }

    public function getPayments($query, $filter = [])
    {
        if ($query['custom']) {
            $paymentCntResponse = $this->es->count('paymentsuccessevent', $query['custom']);
            if ($paymentCntResponse['status_code'] == 200) {
                $paymentDataResponse = $this->es->customQuery($query['custom'], 'paymentsuccessevent', $query['qryStr']);

                if ($paymentDataResponse['status_code'] == 200) {
                    $payments = $paymentDataResponse['body']['hits']['hits'];
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }

        $schemaPayments = [
            'Refer#Payment'
        ];

        if (count($payments)) {
            $this->loadModel('locale');
            $responseCount = $paymentCntResponse['body']['count'];
            foreach ($payments as $payment) {
                $details = $payment['_source']['data']['details'];
                //get enrollment details
                $enrollmentQuery['custom'] = [
                    "query" => [
                        "terms" => [
                            "data.details.enrollment_id" => $details['enrollment_id'] // Use the keyword subfield
                        ]
                    ]
                ];

                $enrollmentData = $this->load->subData("enrollment", "getEnrollment", $enrollmentQuery);

                $ccCode = isset($query['ccCode']) ? $query['ccCode'] : $this->locale->activeCurrency('code');
                $tax = $this->localeModel->getTax($ccCode);

                $paymentsResponse[] = array(
                    'id' => $details['payment_id'],
                    'date' => [
                        'time' => $this->dt->convertToSystemDT($details['event_date']),  // Assuming the event_date is the transaction time
                        'timezone' => $this->locale->activeTimezone(),               // Adjust the timezone if available in the data
                    ],
                    'mode' => $details['payment_method'] ?? '',               // Hardcoded, as no matching field in data
                    'status' => $details['link_status'] ?? '',
                    'full_part' => $enrollmentData['full_part']['type'] ?? '',
                    'item' => [
                        'type' => 'ENROLLMENT',            // You can dynamically update this based on the data
                        'item_id' => $details['enrollment_id'], // Assuming 'batch_name' is the item ID
                    ],
                    'gateway' => [
                        'platform' => $details['payment_link'],            // Payment method is Offline, assume platform to be the same
                        'name' => $details['payment_method'],
                        'fav_icon' => [
                            'url' => '', // No direct favicon, assuming this image
                            'alt_text' => ''
                        ],
                        'transaction_id' => $details['receipt_id'],
                    ],
                    'currency' => $this->load->subData("user", "getUser", $details['user_id'], ['key' => 'app_currency']),
                    'amount' => $details['link_amount'],
                    'amount_due' => $details['link_amount_due'],
                    'tax' => $tax
                );
            }

            if (isset($filter['schema'])) {
                $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
            }

            return $this->schema->validate(['count' => $responseCount, 'data' => $paymentsResponse], ['count' => 'integer', 'data' => $schemaPayments], $filter);
        }

        return false;
    }

    /**
     * Posts a payment success event to Elasticsearch and performs related actions.
     *
     * @param array $request The request data containing payment and user details.
     * @param int $enrollmentId The ID of the associated enrollment record.
     * @return void
     */
    public function addPayment($reqArgs, $batchDetails, $enrollmentId)
    {
        global $wpdb;
        $this->loadModel('user');
        $this->loadModel('course');
        $this->loadModel('counselor');
        $this->loadModel('org');
        $this->loadModel('instructor');
        $this->loadModel('enrollment');

        // Prepare data for inserting into the custom post type 'payment'
        $postData = [
            'post_title'   => 'Payment for User ID: ' . $reqArgs['user_id'], // Adjust the title as needed
            'post_content' => '', // Add relevant content if needed
            'post_status'  => 'publish', // Set the status to publish or draft as needed
            'post_type'    => 'payment', // Your custom post type slug
        ];

        // Insert the post into the custom post type
        $postId = wp_insert_post($postData);

        // Check if the post was inserted successfully
        if (is_wp_error($postId)) {
            ynLog('Failed to insert payment post: ' . $postId->get_error_message(), 'addPayment_V4');
            $this->enrollmentModel->delEnrollment($enrollmentId);
            return false;
        }

        $receipt = $this->generateReceipt($reqArgs['user_id']);

        // Update post meta with payment data
        update_post_meta($postId, 'user_id', $reqArgs['user_id']);
        update_post_meta($postId, 'course_id', $reqArgs['course_id']);
        update_post_meta($postId, 'batch_id', $reqArgs['batch_id']);
        update_post_meta($postId, 'batch_end_date', $batchDetails['end_date']);
        update_post_meta($postId, 'duration', $this->courseModel->getCourse($reqArgs['course_id'])['duration']);
        update_post_meta($postId, 'enrollment_id', $enrollmentId);
        update_post_meta($postId, 'amount', $reqArgs['amount']);
        update_post_meta($postId, 'amount_due', $reqArgs['amount_due']);
        update_post_meta($postId, 'amount_paid', $reqArgs['amount_paid']);
        update_post_meta($postId, 'currency', $reqArgs['currency_code']);
        update_post_meta($postId, 'payment_status', 'ISSUED');
        update_post_meta($postId, 'payment_gateway', $reqArgs['payment_gateway']);
        update_post_meta($postId, 'payment_gateway_message', $reqArgs['payment_gateway_message']);
        update_post_meta($postId, 'notes', $reqArgs['self_notes']);
        update_post_meta($postId, 'payment_description', $reqArgs['payment_description']);
        update_post_meta($postId, 'payment_mode', $reqArgs['payment_mode']);
        update_post_meta($postId, 'total_instalments', $reqArgs['total_instalments']);
        update_post_meta($postId, 'instructor_id', get_post_meta($reqArgs['batch_id'], 'instructor_id', true));
        update_post_meta($postId, 'counselor_id', $reqArgs['counselor_id']);
        update_post_meta($postId, 'receipt_id', $receipt);
        update_post_meta($postId, 'created_on', $this->dt->currentSystemDT("Y-m-d H:i:s"));
        update_post_meta($postId, 'payment_date_time', $this->dt->currentSystemDT("Y-m-d H:i:s"));
        update_post_meta($enrollmentId, 'payment_id', $postId);

        // Log success
        ynLog("Payment post created with ID: $postId", 'addPayment_V4');

        $paymentData = [
            "data" => [
                'details' => [
                    'payment_id' => $postId,
                    'enrollment_id' => $enrollmentId,
                    'user_id'          => $reqArgs['user_id'],
                    'course_id'        => $reqArgs['course_id'],
                    'event_type'       => 'paymentsuccessevent',
                    'event_label'      => 'Paid successfully',
                    'event_date' => date('Y-m-d H:i:s', strtotime($this->dt->currentSystemDT())),
                    'image'            => $this->userModel->getUser($reqArgs['user_id'])['image_url'] ?? '', // Assuming you have a method to get user image URL
                    'name'             =>  $this->userModel->getUser($reqArgs['user_id'])['full_name'] ?? '',
                    'payment_link'     => '', // Update as needed
                    'payment_method'   => $reqArgs['payment_gateway'],
                    'link_amount'      => $reqArgs['amount'],
                    'link_status'      => 'ISSUED', // Update as per your logic
                    'counselor_name'   => $this->counselorModel->getCounselor($reqArgs['counselor_id'])['full_name'] ?? '',
                    'counselor_image'  => $this->counselorModel->getCounselor($reqArgs['counselor_id'])['image_url'] ?? '',
                    'course_name'      => $this->courseModel->getCourse($reqArgs['course_id'])['title'] ?? '',
                    'org_admin' => $this->orgModel->getOrganization($reqArgs['org_id'], ['schema' => 'Organization_Minimal']),
                    'category'         => implode(',', wp_get_post_terms($reqArgs['course_id'], 'course_category', ['fields' => 'names'])),
                    'batch_name'       => $reqArgs['batch_id'],
                    'instructor_name'  => $this->instructorModel->getInstructor($batchDetails['instructor_id'])['user']['full_name'] ?? '',
                    'instructor_image' => $this->instructorModel->getInstructor($batchDetails['instructor_id'])['user']['image_url'] ?? '',
                    'generate_link'    => '', // Update as needed
                    'invoice_id'       => '',
                    'receipt_id'       => get_post_meta($paymentPostId, 'receipt_id', true),
                    'created_on'       => $this->dt->currentSystemDT()
                ],
                '@timestamp' => $this->dt->currentSystemDT()
            ]
        ];

        // Send data to Elasticsearch
        $esResponse = $this->es->create('paymentsuccessevent', $paymentData, 'payment-' . $postId);
        if ($esResponse['status_code'] == 201) {
            ynLog("Payment ES entry created with ID: $postId", 'addPayment_V4');
            return $postId;
        }else{
            // Delete the post if indexing fails
            wp_delete_post($postId, true);
            $this->enrollmentModel->delEnrollment($enrollmentId);
            return false;
        }

        return false;
    }

    /**
     * Retrieves the provided receipt or generates a new one if not provided.
     *
     * @param string|null $receipt The existing receipt, if any.
     * @param int $userId The ID of the user.
     * @return string The existing or newly generated receipt.
     */
    public function generateReceipt($userId)
    {
        $randomPart1 = mt_rand(0, 99999);
        $randomPart2 = rand(10, 9999);
        return "receipt-" . $randomPart1 . $randomPart2 . $userId;
    }


    public function updatePayment($paymentId, $args)
    {
        global $wpdb;

        // Get the payment post ID from the custom table (assuming it is stored in the 'post_id' column)
        if (empty($paymentId) || !get_post($paymentId)) {
            ynLog("No payment post found for payment ID: {$paymentId}", 'updatePayment');
            return false;
        }

        // Update the custom post type's metadata
        update_post_meta($paymentId, 'payment_status', $args['status']);
        update_post_meta($paymentId, 'updated_at', $this->dt->currentSystemDT());

        ynLog("Custom post payment updated successfully for post ID: {$paymentId}", 'updatePayment');

        $paymentData = [
            "data" => [
                "details" => [
                    "link_status" => $args['status']
                ]
            ]
        ];

        $paymentDataResponse = $this->es->update('paymentsuccessevent', 'payment-' . $paymentId, $paymentData);

        ynLog("updatePayment - paymentDataESResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse), 'updatePayment');

        if ($paymentDataResponse['status_code'] == 200) {
            return true;
        }

        return false;
    }

    /**
     * Creates a payment link using Razorpay API.
     *
     * @param WP_REST_Request $request The request object containing payment details.
     * @return WP_REST_Response|WP_Error Returns a REST response with payment link data or WP_Error on failure.
     */
    public function createPaymentLink($request)
    {
        $requestData = $request;
        $razorpayKeyId = esc_attr(get_option('yuno_razorpay_api_key'));
        $razorpaySecretKey = esc_attr(get_option('yuno_razorpay_secret_key'));

        $paymentUrl = 'https://' . $razorpayKeyId . ':' . $razorpaySecretKey . '@api.razorpay.com/v1/invoices/';

        $paymentParams = [
            'customer' => [
                'name' => $requestData['customer']['name'],
                'email' => $requestData['customer']['email'],
                'contact' => $requestData['customer']['contact'],
            ],
            'type' => $requestData['type'],
            'notes' => [
                'org_on_yuno' => $requestData['notes']['org_id'] ?? 0,
            ],
            'view_less' => 1,
            'amount' => $requestData['amount'] * 100, // Convert amount to paise
            'currency' => $requestData['currency'],
            'description' => $requestData['description'],
            'receipt' => $requestData['receipt'],
            //'callback_url' => $requestData['callback_url'],
            //'expired_at' => RAZORPAY_EXPIRY_IN_DAYS,
            //'expire_by' => RAZORPAY_EXPIRY_IN_DAYS,
            'callback_method' => "get",
            'partial_payment' => $requestData['partial_payment'],
            'order' => [
                'payment_capture' => 1 // Set to 1 for immediate capture, 0 for authorization only
            ]
        ];

        ynLog("payment - paymentParams - createPaymentLink " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentParams), 'payment_model');

        // Call the cURL request function
        $headers = [
            "Content-Type: application/json",
            "Accept: application/json",
        ];
        //$apiResult = $this->payment->sendCurlRequest($paymentUrl, $paymentParams, $headers);
        $apiResult = $this->utility->curlRequest($paymentUrl, 'POST',  $paymentParams, $headers);

        ynLog("payment - apiResult - createPaymentLink " . date("Y-m-d H:i:s") . " === ID: " . json_encode($apiResult), 'payment_model');

        $apiResponse = $apiResult['response'];
        $httpCode = $apiResult['code'];

        // Check for errors
        if ($httpCode !== 200 || isset($apiResponse['error'])) {
            return false; // Return false if there's an error
        }

        // Call the new function to insert the payment link data into the database
        $insertSuccess = $this->insertPaymentLinkIntoDatabase($apiResponse, $requestData);

        // Format the response for successful cases
        $paymentResponse = [
            'amount' => $apiResponse['amount'],
            'amount_due' => $apiResponse['amount_due'],
            'gross_amount' => $apiResponse['gross_amount'],
            'invoice_url' => $apiResponse['short_url'],
            'message' => "Payment link successfully generated",
        ];

        // Return true with formatted response
        return $apiResponse['short_url'];
    }
    /**
     * Handle offline payments.
     */
    public function handleOfflinePayment($reqArgs, $batchDetails, $paymentId, $enrollmentId)
    {
        $this->loadModel('enrollment');
        // Update payment statuses

        ynLog("addEnrollment  - handleOfflinePayment -- reqArgs " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($reqArgs), 'handleOfflinePayment');
        ynLog("addEnrollment  - handleOfflinePayment -- batchDetails " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($batchDetails), 'handleOfflinePayment');
        ynLog("addEnrollment  - handleOfflinePayment -- paymentId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($paymentId), 'handleOfflinePayment');
        ynLog("addEnrollment  - handleOfflinePayment -- enrollmentId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentId), 'handleOfflinePayment');

        // $paymentPostUpdateId = $this->updateCustomPostPayment($paymentId, [
        //     'status' => 'COMPLETE'
        // ]);

        // ynLog("addEnrollment  - handleOfflinePayment  --- paymentPostUpdateId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($paymentPostUpdateId), 'handleOfflinePayment');

        $paymentEsResUpdate = $this->updatePayment($paymentId, [
            'status' => 'COMPLETE',
        ]);

        ynLog("addEnrollment  - handleOfflinePayment -- paymentEsResUpdate " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($paymentEsResUpdate), 'handleOfflinePayment');

        // Proceed if payment updates are successful
        if ($paymentEsResUpdate) {
            // Update enrollment statuses

            // $enrollmentPostUpdateId = $this->enrollmentModel->updateCustomPostEnrollment($enrollmentId, [
            //     'status' => 'ACTIVE'
            // ]);

            // error_log("addEnrollment  - handleOfflinePayment -- enrollmentPostUpdateId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentPostUpdateId) . "\n\n", 3, $logFile);

            $enrollmentEsResUpdate = $this->enrollmentModel->updateEnrollment($enrollmentId, [
                'status' => 'ACTIVE',
            ]);

            ynLog("addEnrollment  - handleOfflinePayment -- enrollmentEsUpdateId " . date("Y-m-d H:i:s") . " === Offline: " . json_encode($enrollmentEsResUpdate), 'handleOfflinePayment');

            if ($enrollmentEsResUpdate) {
                // Perform post-enrollment actions
                return true;
            }
        }

        return false;
    }

    /**
     * Handle online payments.
     */
    public function handleOnlinePayment($reqArgs, $wpBatches, $enrollmentId, $paymentId)
    {
        // Build payment parameters
        $paymentParams = $this->buildPaymentParams($reqArgs, $paymentId);
        ynLog(" - handleOnlinePayment -- params call " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentParams), 'handleOnlinePayment');

        // Generate payment link
        $paymentLinkResponse = $this->createPaymentLink($paymentParams);

        ynLog("handleOnlinePayment - linkk call  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentLinkResponse), 'handleOnlinePayment');

        // Return false if payment link generation failed
        if (empty($paymentLinkResponse)) {
            return false;
        }

        ynLog("handleOnlinePayment - linkk call -- end " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentLinkResponse), 'handleOnlinePayment');

        // Return the payment link if processing succeeds, otherwise false
        return $paymentLinkResponse;
    }
    
    public function buildPaymentParams($reqArgs, $paymentId)
    {
        $this->loadModel('user');
        $receiptId = get_post_meta($paymentId, 'receipt_id', true);
        return [
            'customer' => [
                'name' => $this->userModel->getUser($reqArgs['user_id'],['schema' => ['full_name'=>'string'], 'key'=>'full_name']), // Use org_user_name for customer name
                'email' => $this->userModel->getUser($reqArgs['user_id'],['schema' => ['email'=>'string'], 'key'=>'email']), // Use org_user_email for customer email
                'contact' => $this->userModel->getUser($reqArgs['user_id'],['schema' => ['phone'=>'string'], 'key'=>'phone']), // Use org_user_phone for customer contact
            ],
            'type' => 'link', // Assuming a fixed type for Razorpay payment link
            'notes' => [
                'org_on_yuno' => $reqArgs['org_id'] ?? 0, // Pass org_id
            ],
            'view_less' => 1, // Enable a minimal view for Razorpay link
            'amount' => $reqArgs['amount'], // Convert amount to smallest currency unit
            'currency' => $reqArgs['currency_code'], // Use currency_code from request
            'description' => $reqArgs['payment_description'], // Add a meaningful description
            'receipt' => $receiptId, // Generate a unique receipt ID
            // 'callback_url' => site_url('/payment-callback'), // Set callback URL for payment status
            //'callback_url' => site_url() . '/wp-json/yuno/v4/enrollment/webhook',
            'expired_at' => RAZORPAY_EXPIRY_IN_DAYS, // Razorpay expiry duration
            'expire_by' => RAZORPAY_EXPIRY_IN_DAYS, // Razorpay expiry duration
            'callback_method' => "post", // Razorpay callback method
            'partial_payment' => $reqArgs['total_instalments'] > 0 ? true : false, // Enable partial payment if instalments exist
        ];
    }

    public function handleDirectOnlinePayment($reqArgs, $wpBatches, $enrollmentId, $paymentId)
    {
        ynLog("handleDirectOnlinePayment - reqArgs start " . date("Y-m-d H:i:s") . " === ID: " . json_encode($reqArgs), 'handleDirectOnlinePayment');
        // Generate Order Id From Razorpay
        global $wpdb;
        $razorpayKeyId = esc_attr(get_option('yuno_razorpay_api_key'));
        $razorpaySecretKey = esc_attr(get_option('yuno_razorpay_secret_key'));

        $api = new Api($razorpayKeyId, $razorpaySecretKey);
        //$receiptId = $this->generateReceipt($reqArgs['user_id']);
        $receiptId = get_post_meta($paymentId, 'receipt_id', true);

        ynLog("handleDirectOnlinePayment - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId), 'handleDirectOnlinePayment');
        $amount = $reqArgs['amount'] * 100; // Convert to paise
        $currency = $reqArgs['currency_code'];
        $paymentCapture = 1; // Set to 1 for immediate capture, 0 for authorization only
        $orderRazorResponse = $this->createRazorpayOrder($api, $receiptId, $amount, $currency, $paymentCapture);

        ynLog("handleDirectOnlinePayment - createRazorpayOrder call " . date("Y-m-d H:i:s") . " === ID: " . json_encode($orderRazorResponse), 'handleDirectOnlinePayment');

        //  Fetch `course_name` from Elasticsearch
        $courseName = $this->es->read('course', 'course-' . $reqArgs['course_id'], ['_source' => 'data.details.title'])['body']['_source']['data']['details']['title'] ?? '';

        $userDetails = $this->es->read('signedup', 'signedup-' . $reqArgs['user_id'], ['_source' => 'data.details.user']);
        $userPhone = $userDetails['body']['_source']['data']['details']['user']['phone'] ?? '';
        $userEmail = $userDetails['body']['_source']['data']['details']['user']['email'] ?? '';

        if ($orderRazorResponse) {
            $checkoutPayload = [
                "status" => "success",
                "receipt_id" => $receiptId,
                "key" => $razorpayKeyId,
                "amount" => $reqArgs['amount'],
                "name" => $courseName ?? 'Course Payment',
                "description" => $courseName ?? 'Course Payment',
                "image" => "/wp-content/uploads/2018/10/yuno_logo-1.png",
                "order_id" => $orderRazorResponse,
                "prefill" => [
                    "email" => $userEmail ?? '',
                    "contact" => $userPhone ?? ''
                ],
                "notes" => [
                    "address" => 'Yunolearning',
                    "org_on_yuno" => $reqArgs['org_id'] ?? 0
                ],
                "theme" => [
                    "color" => "#104378"
                ]
            ];

            ynLog("handleDirectOnlinePayment - checkoutPayload " . date("Y-m-d H:i:s") . " === ID: " . json_encode($checkoutPayload), 'handleDirectOnlinePayment');
            // Prepare the response
            return $checkoutPayload;
        } else {
            ynLog("handleDirectOnlinePayment: Failed to create Razorpay order for user_id: " . ($reqArgs['user_id'] ?? 'UNKNOWN') . " at " . date('Y-m-d H:i:s'), 'handleDirectOnlinePayment');
            return false;
        }
    }

    public function createRazorpayOrder($api, $receiptId, $amount, $currency, $paymentCapture = 1)
    {
        ynLog("createRazorpayOrder - requesttttt receiptId" . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId), 'createRazorpayOrder');
        try {
            $orderData = [
                'receipt' => $receiptId,
                'amount' => $amount, // Razorpay accepts amount in paise
                'currency' => $currency,
                'payment_capture' => $paymentCapture
            ];
            ynLog("createRazorpayOrder - orderData " . date("Y-m-d H:i:s") . " === ID: " . json_encode($orderData), 'createRazorpayOrder');

            $razorpayOrder = $api->order->create($orderData);
            ynLog("createRazorpayOrder - razorpayOrder " . date("Y-m-d H:i:s") . " === ID: " . json_encode($razorpayOrder), 'createRazorpayOrder');
            return $razorpayOrder['id']; // returns the Razorpay Order ID
        } catch (\Exception $e) {
            // Log the error or handle exception as needed
            ynLog("createRazorpayOrder - Exception - Razorpay Order Creation Error" . date("Y-m-d H:i:s") . " === ID: " . json_encode($e->getMessage()), 'createRazorpayOrder');
            // error_log("Razorpay Order Creation Error: " . $e->getMessage());
            return false;
        }
    }

    // This function handles inserting payment link data into the database
    public function insertPaymentLinkIntoDatabase($apiResponse, $requestData)
    {
        global $wpdb;

        // Prepare data for insertion into the payment_links table
        $paymentLink = [
            'razerpay_invoice_id' => $apiResponse['id'], // Razorpay invoice ID
            'receipt_id' => $requestData['receipt'], // Receipt ID from the request
            'payment_id' => $apiResponse['payments'][0]['id'] ?? '', // Payment ID from the response
            'payment_link' => $apiResponse['short_url'], // Razorpay short URL
            'amount' => $apiResponse['amount'], // Amount (convert from paise to rupees)
            'link_status' => 'ISSUED', // Initial status
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql'),
            'link_expiry' => date('Y-m-d H:i:s', strtotime("+1 day")), // Example: Expire in 1 day
        ];

        // Define table name
        $table_name = $wpdb->prefix . 'payment_links';

        // Insert data into the database
        $inserted = $wpdb->insert($table_name, $paymentLink);

        // Check if the insertion failed
        if ($inserted === false) {
            ynLog("Failed to insert payment link into the database.");
            return false; // Return false if the insertion fails
        }

        return true; // Return true if insertion is successful
    }

    public function handleWebhook()
    {
        // Extract callback data
        $callbackData = $_REQUEST;
        // Log received data for debugging
        ynLog("Callback received: " . print_r($callbackData, true), 'handleWebhook');

        // Ensure all required fields are present
        if (
            !isset($callbackData['razorpay_invoice_id']) ||
            !isset($callbackData['razorpay_payment_id']) ||
            !isset($callbackData['razorpay_signature'])
        ) {
            ynLog("Invalid callback payload", 'handleWebhook');
            http_response_code(400);
            echo json_encode(['status' => 'error', 'message' => 'Invalid callback payload']);
            exit;
        }

        // Extract parameters
        $invoiceId = $callbackData['razorpay_invoice_id'];
        $paymentId = $callbackData['razorpay_payment_id'];
        $signature = $callbackData['razorpay_signature'];
        $status = $callbackData['razorpay_invoice_status'];
        $receipt = $callbackData['razorpay_invoice_receipt'] ?? 'N/A';

        // Verify signature
        $secretKey = esc_attr(get_option('yuno_razorpay_secret_key'));
        // Process based on invoice status
        switch ($status) {
            case 'paid':
                $this->handleInvoicePaid($invoiceId, $paymentId, $status, $receipt);
                break;

            case 'failed':
                $this->handlePaymentFailed($invoiceId, $paymentId, $status, $receipt);
                break;

            default:
                ynLog("Unhandled status: " . $status, 'handleWebhook');
                break;
        }

        // Respond to Razorpay
        http_response_code(200);
        echo json_encode(['status' => 'success']);
        exit;
    }

    /**
     * Handle Invoice Paid Event
     */
    public function handleInvoicePaid($invoiceId, $paymentId, $status, $receipt)
    {
        // Fetch additional details if needed
        $invoiceDetails = $this->fetchInvoiceDetails($invoiceId);
        $amount = $invoiceDetails['amount'] ?? 0;

        // Log event details
        ynLog("Invoice Paid: ID = $invoiceId, Payment ID = $paymentId, Amount = $amount, Receipt = $receipt", 'handleWebhook');
        // Update database or perform other logic
        // Example: Update payment status in your database
        return $invoiceDetails;
    }

    /**
     * Handle Payment Failed Event
     */
    public function handlePaymentFailed($invoiceId, $paymentId, $status, $receipt)
    {
        // Log event details
        ynLog("Payment Failed: ID = $invoiceId, Payment ID = $paymentId, Status = $status, Receipt = $receipt", 'handleWebhook');
        // Update database or perform other logic
        // Example: Mark payment as failed in your database
    }

    /**
     * Fetch Invoice Details from Razorpay API
     */
    private function fetchInvoiceDetails($invoiceId)
    {
        $apiKey = esc_attr(get_option('yuno_razorpay_api_key'));
        $apiSecret = esc_attr(get_option('yuno_razorpay_secret_key'));
        $url = "https://api.razorpay.com/v1/invoices/$invoiceId";

        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_USERPWD, "$apiKey:$apiSecret");
        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            ynLog('Curl error: ' . curl_error($curl), 'handleWebhook');
            return null;
        }

        curl_close($curl);
        return json_decode($response, true);
    }

    /**
     * Fetch Payment Details from Razorpay API
     */
    public function fetchPaymentDetails($paymentId)
    {
        // Razorpay API credentials
        $apiKey = esc_attr(get_option('yuno_razorpay_api_key'));
        $apiSecret = esc_attr(get_option('yuno_razorpay_secret_key'));
        $url = "https://api.razorpay.com/v1/payments/$paymentId";

        // Initialize cURL
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($curl, CURLOPT_USERPWD, "$apiKey:$apiSecret");

        // Execute API request
        $response = curl_exec($curl);

        // Check for errors
        if (curl_errno($curl)) {
            ynLog('Curl error: ' . curl_error($curl), 'fetchPaymentDetails');
            curl_close($curl);
            return null;
        }

        curl_close($curl);

        // Decode and return the response
        return json_decode($response, true);
    }

    public function retrivePayment($userId, $courseId, $batchId)
    {
        // Build the query
        $query = [
            'custom' => [
                "query" => [
                    "bool" => [
                        "must" => [
                            [
                                "match" => [
                                    'data.details.user_id' => $userId
                                ]
                            ],
                            [
                                "match" => [
                                    'data.details.course_id' => $courseId
                                ]
                            ],
                            [
                                "match" => [
                                    'data.details.batch_name' => $batchId
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // Call the getPayment method with the query
        return $this->getPayment($query);
    }

    public function getPaymentsCount()
    {
        // Handle the response and return the document count
        $responseData = $this->es->count('paymentsuccessevent');

        if ($responseData['status_code'] == 200) {
            $responseCount = $responseData['body']['count'];
        } else {
            return false;
        }

        if (isset($responseCount)) {
            return (int) $responseCount;
        }
        return 0;
    }

    /**
     * Update Payment Details via after payment webhook
     * 
     * @param array $updateInfo
     * @return bool
     */
    public function updatePaymentDetails($updateInfo)
    {
        ynLog("updatePaymentDetails - updateInfo " . date("Y-m-d H:i:s") . " === ID: " . json_encode($updateInfo), 'updatePaymentDetails');

        $status = $updateInfo['status'];
        ynLog("updatePaymentDetails - status from webhook" . date("Y-m-d H:i:s") . " === ID: " . json_encode($status), 'updatePaymentDetails');

        if ($status === 'paid') {
            $status = 'PAID';
            ynLog("updatePaymentDetails - status " . date("Y-m-d H:i:s") . " === ID: " . json_encode($status), 'updatePaymentDetails');

            $receiptId =  $updateInfo['receipt'];

            ynLog("updatePaymentDetails - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId), 'updatePaymentDetails');

            $payments = get_posts([
                'post_type'  => 'payment', // or specify 'your_post_type' if needed
                'meta_key'   => 'receipt_id',
                'meta_value' => $receiptId,
                'fields'     => 'ids', // Only return post IDs
                'numberposts' => 1     // Limit to 1 result
            ]);

            // Check if any posts were found
            ynLog("updatePaymentDetails - posts " . date("Y-m-d H:i:s") . " === ID: " . json_encode($payments), 'updatePaymentDetails');

            if (!empty($payments)) {
                $paymentId = $payments[0];
                ynLog("updatePaymentDetails - paymentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentId), 'updatePaymentDetails');
            }

            ynLog("updatePaymentDetails - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - paymentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentId), 'updatePaymentDetails');

            if ($paymentId) {
                update_post_meta($paymentId, 'payment_status', 'PAID');
                update_post_meta($paymentId, 'invoice_id', $updateInfo['invoice_id']);
                update_post_meta($paymentId, 'razorpay_payment_id', $updateInfo['payment_id']);
            }

            $userId = get_post_meta($paymentId, 'user_id', true);
            $courseId = get_post_meta($paymentId, 'course_id', true);
            $batchId = get_post_meta($paymentId, 'batch_id', true);
            $enrollmentId = get_post_meta($paymentId, 'enrollment_id', true);

            ynLog("updatePaymentDetails - userId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($userId), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - courseId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($courseId), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - batchId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($batchId), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - enrollmentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentId), 'updatePaymentDetails');

            // update enrollment status
            update_post_meta($enrollmentId, 'enrollment_status', 'ACTIVE');

            ynLog("updatePaymentDetails - update_post_meta " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentId), 'updatePaymentDetails');

            // get es enrollment
            $esQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.course_id" => $courseId]],
                            ["match" => ["data.details.batch_id" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.course_id",
                    "data.details.enrollment_status"
                ]
            ];

            ynLog("updatePaymentDetails - esQuery " . date("Y-m-d H:i:s") . " === ID: " . json_encode($esQuery), 'updatePaymentDetails');

            // Fetch enrollment data from Elasticsearch (or any other data source)
            $enrollmentDataResponse = $this->es->customQuery($esQuery, 'batchenrollmentevent');

            ynLog("updatePaymentDetails - enrollmentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentDataResponse), 'updatePaymentDetails');
            if ($enrollmentDataResponse['status_code'] == 200) {
                $enrollmentESId = $enrollmentDataResponse['body']['hits']['hits'][0]['_id'] ?? '';
                ynLog("updatePaymentDetails - enrollmentESId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentESId), 'updatePaymentDetails');
            } else {
                ynLog("Failed to fetch enrollment data from Elasticsearch.", 'updatePaymentDetails');
                //return false;
            }

            $paymentQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.batch_name" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.receipt_id",
                    "data.details.link_status"
                ]
            ];

            ynLog("updatePaymentDetails - paymentQuery " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentQuery), 'updatePaymentDetails');
            // Fetch payment data from Elasticsearch (or any other data source)
            $paymentDataResponse = $this->es->customQuery($paymentQuery, 'paymentsuccessevent');
            ynLog("updatePaymentDetails - paymentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse), 'updatePaymentDetails');
            if ($paymentDataResponse['status_code'] == 200) {
                $paymentEsId = $paymentDataResponse['body']['hits']['hits'][0]['_id'];
                ynLog("updatePaymentDetails - paymentEsId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsId), 'updatePaymentDetails');
            } else {
                ynLog("Failed to fetch payment data from Elasticsearch.", 'updatePaymentDetails');
                //return false;
            }

            ynLog("updatePaymentDetails - paymentEsId  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsId), 'updatePaymentDetails');

            // Update Elasticsearch
            $paymentData = [
                "data" => [
                    "details" => [
                        "link_status" => 'PAID',
                        "invoice_id" => $updateInfo['invoice_id'],
                    ]
                ]
            ];

            ynLog("updatePaymentDetails - paymentData " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentData), 'updatePaymentDetails');
            $paymentDataResponse = $this->es->update('paymentsuccessevent', $paymentEsId, $paymentData);
            ynLog("updatePaymentDetails - paymentDataResponse after update call " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse), 'updatePaymentDetails');
            $paymentEsUpdateSuccess = $paymentDataResponse['status_code'] == 200;
            ynLog("updatePaymentDetails - paymentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsUpdateSuccess), 'updatePaymentDetails');
            // Update Elasticsearch for enrollment
            $enrollmentData = [
                "data" => [
                    "details" => [
                        "enrollment_status" => 'ACTIVE'
                    ]
                ]
            ];
            $enrollmentDataResponse = $this->es->update('batchenrollmentevent', $enrollmentESId, $enrollmentData);
            ynLog("updatePaymentDetails - enrollmentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentDataResponse), 'updatePaymentDetails');
            $enrollmentEsUpdateSuccess = $enrollmentDataResponse['status_code'] == 200;
            ynLog("updatePaymentDetails - enrollmentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentEsUpdateSuccess), 'updatePaymentDetails');
            // Return true if all updates were successful
            if ($paymentEsUpdateSuccess && $enrollmentEsUpdateSuccess) {

                ynLog("updatePaymentDetails - paymentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsUpdateSuccess), 'updatePaymentDetails');
                
                ynLog("updatePaymentDetails - enrollmentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentEsUpdateSuccess), 'updatePaymentDetails');

                // If any update fails, return false
                ynLog("updatePaymentDetails - update failed " . date("Y-m-d H:i:s") . " === ID: " . json_encode($status), 'updatePaymentDetails');
                $eventDetails = [
                    'user_id'   => $userId,
                    'action'    => 'Payment Success',
                    'target'    => 'payment',
                    'target_id' => $paymentEsId,
                    'timestamp' => $this->dt->currentSystemDT()
                ];
                ynLog("updatePaymentDetails - trigger_custom_event " . date("Y-m-d H:i:s") . " === ID: " . json_encode($eventDetails), 'updatePaymentDetails');
                // Trigger custom event
                $enrollmentEventdetails = [
                    'user_id'   => $userId,
                    'action'    => 'enrollment',
                    'target'    => 'enrollment',
                    'target_id' => $enrollmentESId,
                    'timestamp' => $this->dt->currentSystemDT()
                ];
                trigger_custom_event($enrollmentEventdetails);
                ynLog("updatePaymentDetails - trigger_custom_event after" . date("Y-m-d H:i:s") . " === ID: " . json_encode($eventDetails), 'updatePaymentDetails');
            }

        } else if ($status === 'failed') {
            $status = 'FAILED';
            ynLog("updatePaymentDetails - status " . date("Y-m-d H:i:s") . " === ID: " . json_encode($status), 'updatePaymentDetails');

            $receiptId =  $updateInfo['receipt'];
            ynLog("updatePaymentDetails - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId), 'updatePaymentDetails');
            $payments = get_posts([
                'post_type'  => 'payment', // or specify 'your_post_type' if needed
                'meta_key'   => 'receipt_id',
                'meta_value' => $receiptId,
                'fields'     => 'ids', // Only return post IDs
                'numberposts' => 1     // Limit to 1 result
            ]);
            // Check if any posts were found
            ynLog("updatePaymentDetails - posts " . date("Y-m-d H:i:s") . " === ID: " . json_encode($payments), 'updatePaymentDetails');
            if (!empty($payments)) {
                $paymentId = $payments[0];
                ynLog("updatePaymentDetails - paymentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentId), 'updatePaymentDetails');
            }
            ynLog("updatePaymentDetails - receiptId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($receiptId), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - paymentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentId), 'updatePaymentDetails');
            if ($paymentId) {
                update_post_meta($paymentId, 'payment_status', 'FAILED');
                update_post_meta($paymentId, 'razorpay_payment_id', $updateInfo['payment_id']);
                update_post_meta($paymentId, 'invoice_id', $updateInfo['invoice_id']);
            }
            $userId = get_post_meta($paymentId, 'user_id', true);
            $courseId = get_post_meta($paymentId, 'course_id', true);
            $batchId = get_post_meta($paymentId, 'batch_id', true);
            $enrollmentId = get_post_meta($paymentId, 'enrollment_id', true);

            ynLog("updatePaymentDetails - userId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($userId), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - courseId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($courseId), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - batchId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($batchId), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - enrollmentId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentId), 'updatePaymentDetails');

            // update enrollment status
            update_post_meta($enrollmentId, 'enrollment_status', 'INACTIVE');
            ynLog("updatePaymentDetails - update_post_meta " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentId), 'updatePaymentDetails');

            // get existing elasticsearch payment
            // get es enrollment
            $esQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.course_id" => $courseId]],
                            ["match" => ["data.details.batch_id" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.course_id",
                    "data.details.enrollment_status"
                ]
            ];

            ynLog("updatePaymentDetails - esQuery " . date("Y-m-d H:i:s") . " === ID: " . json_encode($esQuery), 'updatePaymentDetails');

            // Fetch enrollment data from Elasticsearch (or any other data source)
            $enrollmentDataResponse = $this->es->customQuery($esQuery, 'batchenrollmentevent');
            ynLog("updatePaymentDetails - enrollmentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentDataResponse), 'updatePaymentDetails');
            if ($enrollmentDataResponse['status_code'] == 200) {
                $enrollmentESId = $enrollmentDataResponse['body']['hits']['hits'][0]['_id'] ?? '';
                ynLog("updatePaymentDetails - enrollmentESId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentESId), 'updatePaymentDetails');
            } else {
                ynLog("Failed to fetch enrollment data from Elasticsearch.", 'updatePaymentDetails');
                //return false;
            }
            $paymentQuery = [
                "query" => [
                    "bool" => [
                        "must" => [
                            ["match" => ["data.details.user_id" => $userId]],
                            ["match" => ["data.details.batch_name" => $batchId]]
                        ]
                    ]
                ],
                "_source" => [
                    "data.details.user_id",
                    "data.details.receipt_id",
                    "data.details.link_status"
                ]
            ];
            ynLog("updatePaymentDetails - paymentQuery " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentQuery), 'updatePaymentDetails');

            $paymentDataResponse = $this->es->customQuery($paymentQuery, 'paymentsuccessevent');
            ynLog("updatePaymentDetails - paymentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse), 'updatePaymentDetails');
            if ($paymentDataResponse['status_code'] == 200) {
                $paymentEsId = $paymentDataResponse['body']['hits']['hits'][0]['_id'];
                ynLog("updatePaymentDetails - paymentEsId " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsId), 'updatePaymentDetails');
            } else {
                ynLog("Failed to fetch payment data from Elasticsearch.", 'updatePaymentDetails');
                //return false;
            }
            ynLog("updatePaymentDetails - paymentEsId  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentEsId), 'updatePaymentDetails');

            // Update Elasticsearch
            $paymentData = [
                "data" => [
                    "details" => [
                        "link_status" => 'FAILED',
                        "invoice_id" => $updateInfo['invoice_id'],
                    ]
                ]
            ];
            ynLog("updatePaymentDetails - paymentData " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentData), 'updatePaymentDetails');
            $paymentDataResponse = $this->es->update('paymentsuccessevent', $paymentEsId, $paymentData);
            ynLog("updatePaymentDetails - paymentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($paymentDataResponse), 'updatePaymentDetails');
            $esUpdateSuccess = $paymentDataResponse['status_code'] == 200;
            ynLog("updatePaymentDetails - esUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($esUpdateSuccess), 'updatePaymentDetails');

            // Update Elasticsearch for enrollment
            $enrollmentData = [
                "data" => [
                    "details" => [
                        "enrollment_status" => 'INACTIVE'
                    ]
                ]
            ];
            $enrollmentDataResponse = $this->es->update('batchenrollmentevent', $enrollmentESId, $enrollmentData);
            $enrollmentEsUpdateSuccess = $enrollmentDataResponse['status_code'] == 200;
            ynLog("updatePaymentDetails - enrollmentEsUpdateSuccess " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentEsUpdateSuccess), 'updatePaymentDetails');
            ynLog("updatePaymentDetails - enrollmentDataResponse " . date("Y-m-d H:i:s") . " === ID: " . json_encode($enrollmentDataResponse), 'updatePaymentDetails');
            // Trigger custom event
            $eventDetails = [
                'user_id'   => $userId,
                'action'    => 'Payment Failed',
                'target'    => 'payment',
                'target_id' => 'payment-'.$paymentEsId,
                'timestamp' => $this->dt->currentSystemDT()
            ];

        } else {
            //$status = 'FAILED';
            ynLog("updatePaymentDetails - status " . date("Y-m-d H:i:s") . " === ID: " . json_encode($status), 'updatePaymentDetails');
        }
        ynLog("updatePaymentDetails - trigger_custom_event " . date("Y-m-d H:i:s") . " === ID: " . json_encode($eventDetails), 'updatePaymentDetails');
            // Trigger custom event
        trigger_custom_event($eventDetails);

        ynLog("updatePaymentDetails - trigger_custom_event after" . date("Y-m-d H:i:s") . " === ID: " . json_encode($eventDetails), 'updatePaymentDetails');
    }
    /**
     * Generates filter options for payment status selection.
     *
     * @package V4
     * @since 1.0.0
     * <AUTHOR>
     *
     * @param string $paymentStatus The currently selected payment status.
     * @return array An array containing payment status filter data.
     *
     * The filter options include:
     * - "All": Show all payment statuses.
     * - "Pending": Show payments that are pending.
     * - "Full": Show fully completed payments.
     */
    public function generateEnrollmentPaymentFilters($paymentStatus)
    {
        return [
            'filter' => 'payment_status',
            'title' => 'Payment Status',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Payment Status',
            'ui_control_type' => 'dropdown',
            'selected' => $paymentStatus,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => [
                ['slug' => 'all', 'label' => 'All', 'filter' => 'payment_status'],
                ['slug' => 'pending', 'label' => 'Pending', 'filter' => 'payment_status'],
                ['slug' => 'full', 'label' => 'Full', 'filter' => 'payment_status']
            ]
        ];
    }

    /**
     * Generates filter options for selecting a payment date range.
     *
     * @package V4
     * @since 1.0.0
     * <AUTHOR>
     *
     * @return array An array containing date range filter options for payment records.
     *
     * The available date ranges include:
     * - "Last 7 Days"
     * - "Last 15 Days"
     * - "Last 30 Days"
     * - "Last 60 Days"
     */
    public function generatePaymentDaysFilters($paymentDays)
    {
        return [
            'filter' => 'payment_days',
            'title' => 'Payment Date Range',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Date Range',
            'ui_control_type' => 'dropdown',
            'selected' => $paymentDays,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => [
                ['slug' => 7, 'label' => 'Last 7 Days', 'filter' => 'payment_days'],
                ['slug' => 15, 'label' => 'Last 15 Days', 'filter' => 'payment_days'],
                ['slug' => 30, 'label' => 'Last 30 Days', 'filter' => 'payment_days'],
                ['slug' => 60, 'label' => 'Last 60 Days', 'filter' => 'payment_days']
            ]
        ];
    }
}
