<?php

namespace V4;

/**
 * Class BatchFilter
 * Handles Batch-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */

class BatchFilter extends Filter
{

    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('locale');
        $this->loadModel('locale');
    }
    /**
     * Generates filters for batch teaching modes.
     *
     * @param string $teachingMode The selected teaching mode.
     * @return array The filter configuration.
     */
    public function generateBatchTeachingModeFilters($teachingMode)
    {
        return [
            'filter' => 'teaching_mode',
            'title' => 'Teaching Mode',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Teaching Mode',
            'ui_control_type' => 'dropdown',
            'selected' => $teachingMode,
            'current' => '',
            'loading' => false,
            'success' => false,
            'is_disabled' => false,
            'items' => [
                ['slug' => 'all', 'label' => 'All Modes', 'filter' => 'teaching_mode'],
                ['slug' => 'onine', 'label' => 'Online', 'filter' => 'teaching_mode'],
                ['slug' => 'inperson', 'label' => 'In Person', 'filter' => 'teaching_mode']
            ]
        ];
    }

    /**
     * Generates filters for batch personalisation types.
     *
     * @param string $personalisation The selected personalisation type.
     * @return array The filter configuration.
     */
    public function generateBatchPersonalisationFilters($personalisation)
    {
        return [
            'filter' => 'personalisation',
            'title' => 'Personalisation',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Personalisation',
            'ui_control_type' => 'dropdown',
            'selected' => $personalisation,
            'current' => '',
            'loading' => false,
            'success' => false,
            'is_disabled' => false,
            'items' => [
                ['slug' => 'all', 'label' => 'All Personalisation', 'filter' => 'personalisation'],
                ['slug' => 'one_to_one', 'label' => 'One to One', 'filter' => 'personalisation'],
                ['slug' => 'one_to_many', 'label' => 'One to Many', 'filter' => 'personalisation']
            ]
        ];
    }

    /**
     * Generates filters for batch days of the week.
     */
    public function generateBatchDayFilters($classDays)
    {
        return [
            'filter' => 'batch_days',
            'title' => 'Batch Days',
            'is_active' => true,
            'multiple' => true,
            'placeholder' => 'Batch Days',
            'ui_control_type' => 'dropdown_multi_select',
            'selected' => [['slug' => 'all', 'label' => 'All Days', 'filter' => 'batch_days']],
            'current' => '',
            'loading' => false,
            'success' => false,
            'is_disabled' => false,
            'items' => [
                ['slug' => 'all', 'label' => 'All Days', 'filter' => 'batch_days'],
                ['slug' => 'monday', 'label' => 'Monday', 'filter' => 'batch_days'],
                ['slug' => 'tuesday', 'label' => 'Tuesday', 'filter' => 'batch_days'],
                ['slug' => 'wednesday', 'label' => 'Wednesday', 'filter' => 'batch_days'],
                ['slug' => 'thursday', 'label' => 'Thursday', 'filter' => 'batch_days'],
                ['slug' => 'friday', 'label' => 'Friday', 'filter' => 'batch_days'],
                ['slug' => 'saturday', 'label' => 'Saturday', 'filter' => 'batch_days'],
                ['slug' => 'sunday', 'label' => 'Sunday', 'filter' => 'batch_days']
            ]
        ];
    }

    /**
     * Generates filters for batch time slots.
     *
     * @param string $classTime The selected class time.
     * @return array The filter configuration.
     */
    public function generateBatchTimeFilters($classTime)
    {
        return [
            'filter' => 'batch_time',
            'title' => 'Batch Time',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Batch Time',
            'ui_control_type' => 'dropdown_multi_select',
            'selected' => [['slug' => 'all', 'label' => 'All Times', 'filter' => 'batch_time']],
            'current' => '',
            'loading' => false,
            'success' => false,
            'is_disabled' => false,
            'items' => [
                ['slug' => 'all', 'label' => 'All Times', 'filter' => 'batch_time'],
                ['slug' => 'morning', 'label' => 'Morning', 'filter' => 'batch_time'],
                ['slug' => 'afternoon', 'label' => 'Afternoon', 'filter' => 'batch_time'],
                ['slug' => 'evening', 'label' => 'Evening', 'filter' => 'batch_time'],
                ['slug' => 'night', 'label' => 'Night', 'filter' => 'batch_time']
            ]
        ];
    }

    /**
     * Generates filters for batch enrolment status.
     *
     * @param bool $enrollable The enrolment status.
     * @return array The filter configuration.
     */
    public function generateBatchEnrollableFilters($enrollable)
    {
        return [
            'filter' => 'only_enrollable',
            'title' => 'Show Only Enrollable',
            'is_active' => true,
            'placeholder' => 'Enrollable',
            'ui_control_type' => 'checkbox',
            'selected' => $enrollable,
            'current' => '',
            'loading' => false,
            'success' => false,
            'is_disabled' => false
        ];
    }

    /**
     * Generates filters for batch locked status.
     *
     * @param bool $locked The locked status.
     * @return array The filter configuration.
     */
    public function generateBatchLockedFilters($locked)
    {
        return [
            'filter' => 'ony_locked',
            'title' => 'Show Only Locked',
            'is_active' => true,
            'placeholder' => 'Locked',
            'ui_control_type' => 'checkbox',
            'selected' => false,
            'current' => '',
            'loading' => false,
            'success' => false,
            'is_disabled' => false
        ];
    }
}
