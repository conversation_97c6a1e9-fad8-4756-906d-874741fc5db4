<?php
namespace V1;
use WP_REST_Controller;
use WP_REST_Response;
use WP_REST_Request;
use WP_Error;
use WP_REST_Server;
use UserElasticSearch;
use WP_User_Query;
use Utility;
use WP_Query;
use WP_User;
class UserController
{	
	public $Orgadmin_learner_demo_requests;
	public $Orgadmin_learner_demo_request_details;
	public $user_auth_token;
	function __construct()
	{
		$this->namespace = 'yuno/v1';
		$this->resource_name = '/instructor/(?P<userID>\d+)/getUserDetail'; // route name
		$this->resource_name_1 = 'profile'; // route name
		$this->resource_name_2 = 'updateUserDetail'; // update group
		$this->resource_name_3 = 'role'; // update group
		$this->resource_name_4 = '/users/(?P<user_id>\d+)/(?P<category>[a-zA-Z0-9-]+)';
		$this->resource_name_5 = '/users/signup/(?P<user_id>\d+)';
		$this->resource_name_6 = '/users/signup/template/(?P<uri>[a-zA-Z0-9-]+)';
		$this->resource_name_7 = '/loginWithGoogle';
		$this->resource_name_8 = '/mylearners/(?P<role>[a-zA-Z0-9-]+)/(?P<instructorID>\d+)/(?P<limit>\d+)/(?P<offset>\d+)';
		$this->resource_name_9 = 'post/mylearners'; // route name (for admin)
		$this->resource_name_10 = '/instructor/list';
		$this->resource_name_11 = '/counselor/list';
		$this->resource_name_12 = '/user/(?P<user_id>\d+)/address/(?P<criteria>[a-zA-Z0-9-]+)';
		$this->resource_name_13 = '/(?P<action>[a-zA-Z0-9-]+)/user/address';
		$this->resource_name_14 = '/create/sso/user/zoom';
		$this->resource_name_15 = '/user/info/(?P<userId>\d+)';
		$this->resource_name_16 = '/user/iptolocation/(?P<ip>[a-zA-Z0-9-]+)'; // route name (to get location from ip)

		$this->resource_name_17 = '/user/languages'; // route name (to get languages)
		$this->resource_name_18 = '/countries'; // route name (to get countries)
		$this->resource_name_19 = '/countries/(?P<country_id>\d+)'; // route name (to get country name)
		$this->resource_name_20 = '/states'; // route name (to get states)
		$this->resource_name_21 = '/states/(?P<state_id>\d+)'; // route name (to get state name)
		$this->resource_name_22 = '/cities'; // route name (to get cities)
		$this->resource_name_23 = '/cities/(?P<city_id>\d+)'; // route name (to get city name)	
		$this->resource_name_24 = '/state/country/(?P<country_id>\d+)'; // route name (to get states by country id)
		$this->resource_name_25 = '/city/state/(?P<state_id>\d+)'; // route name (to get cities by state id)
		$this->resource_name_26 = '/user/autofill/(?P<country_code>[a-zA-Z0-9-]+)'; // route name (to get auto file values by country code)	
		$this->resource_name_27 = '/(?P<role>[a-zA-Z0-9-]+)/list'; // route name (to get all learners plus org-users users)
		$this->resource_name_28 = '/instructor/availability/create'; // route name (to post instructor availability hours)
		$this->resource_name_29 = '/instructor/availability/update'; // route name (to put instructor availability hours)
		$this->resource_name_30 = '/instructor/availability/(?P<instructor_id>\d+)'; // route name (to get instructor availability hours)
		$this->resource_name_31 = '/users/list/(?P<role>[a-zA-Z0-9-]+)'; // route name (to get users of any role)
		$this->resource_name_32 = '/updateusermeta/(?P<role>[a-zA-Z0-9-]+)/(?P<status>[a-zA-Z0-9-]+)/(?P<limit>[a-zA-Z0-9-]+)/(?P<offset>[a-zA-Z0-9-]+)'; // route name (to update user's profile privacy of any role)
		$this->resource_name_33 = '/instructor/list/(?P<user_id>\d+)'; //route name (to get category admin user's categories instructors)
		$this->resource_name_34 = '/instructor/availability/slots'; // route name (to get instructor availability slots)
		$this->resource_name_35 = '/create/user';
		$this->resource_name_37 = '/instructor/availability/days/(?P<instructor_id>\d+)'; // route name (to get instructor availability days slots)
		$this->resource_name_36 = '/logout/user/(?P<user_id>\d+)';
		$this->resource_name_38 = '/user/expire/time';
		$this->resource_name_39 = '/google/refresh/token';
		$this->resource_name_40 = '/user/forte/(?P<userId>\d+)';
		$this->resource_name_41 = '/users/notificationsettings/(?P<user_id>\d+)';
		$this->resource_name_42 = '/users/notificationsettings';
		$this->resource_name_43 = '/user/(?P<user_encoded_info>[a-zA-Z0-9-]+)';
		$this->resource_name_44 = '/user/insert/category';
		$this->resource_name_45 = '/user/(?P<userId>\d+)/category';
		$this->resource_name_46 = '/instructor/list/(?P<user_id>\d+)/(?P<criteria>[a-zA-Z0-9-]+)'; //route name (to get paid instructor users)
		$this->resource_name_47 = '/schedule/mylearners/(?P<instructorID>\d+)/(?P<criteria>[a-zA-Z0-9-]+)';  
		$this->resource_name_48 = '/instructor/availability/(?P<instructor_id>\d+)/(?P<criteria>[a-zA-Z0-9-]+)';
		$this->resource_name_49 = '/user/detail_from_org/(?P<user_id>\d+)';
		$this->resource_name_50 = '/user/create/jwt/token/(?P<user_id>\d+)';
		$this->resource_name_51 = '/user/token/(?P<user_id>\d+)';
		$this->resource_name_52 = '/user/add/category/';
		$this->resource_name_53 = '/user/add/delete/requests/';
		$this->resource_name_54 = '/user/get/delete/requests/';
		$this->resource_name_55 = '/demo-requests/yuno-admin/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->resource_name_56 = '/demo-requests/(?P<learnerId>\d+)';
		$this->resource_name_57 = '/instructor/mylearners/(?P<instructorId>\d+)/(?P<limit>\d+)/(?P<view>[a-zA-Z0-9-]+)/(?P<offset>\d+)';
		$this->Orgadmin_learner_demo_requests = '/demo-requests/org-admin/(?P<user_id>\d+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
		$this->Orgadmin_learner_demo_request_details = '/demo-requests/(?P<org_id>\d+)/(?P<learnerId>\d+)';
		$this->user_auth_token = '/user/auth/token/(?P<user_id>\d+)/(?P<class_id>\d+)';
	}

	public function register_routes() {	   
	    register_rest_route( $this->namespace, $this->resource_name, array(
		    array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'get_user_detail' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
		        ),
		    ),
		) );

	    register_rest_route( $this->namespace, '/users/(?P<userID>\d+)/' . $this->resource_name_1, array(
		    array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'get_user_profile' ),
		        'permission_callback' => array( $this, 'get_user_profile_permissions_check' ),
		        'args'                => array(
		        ),
		    ),
		) );

		register_rest_route( $this->namespace, '/users/(?P<userID>\d+)/' . $this->resource_name_3, array(
		    array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'get_user_role' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
		        ),
		    ),
		) );


		register_rest_route( $this->namespace, '/' . $this->resource_name_2, array(
	      array(
		        'methods'             => WP_REST_Server::EDITABLE,
		        'callback'            => array( $this, 'update_user_detail' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_4, array(
	      array(
		        'methods'             => WP_REST_Server::CREATABLE,
		        'callback'            => array( $this, 'user_signup' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_14, array(
	      array(
		        'methods'             => WP_REST_Server::CREATABLE,
		        'callback'            => array( $this, 'createSSOUserZoom' ),
		        'permission_callback' => array( $this, 'createSSOUserZoom_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_5, array(
	      array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'get_signup_completed' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_6, array(
	      array(
			'methods'             => WP_REST_Server::READABLE,
			'callback'            => array( $this, 'get_signup_template' ),
			'permission_callback' => array( $this, 'get_signup_template_permissions_check' ),
			'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_7, array(
	      array(
		        'methods'             => WP_REST_Server::CREATABLE,
		        'callback'            => array( $this, 'login_with_google' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_8, array(
	      array(
	        'methods'             => WP_REST_Server::READABLE,
	        'callback'            => array( $this, 'getMylearners' ),
	        'permission_callback' => array( $this, 'getMylearners_permissions_check' ),
	        'args'                => array(
	 
	        ),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_9, array(
	      array(
		        'methods'             => WP_REST_Server::CREATABLE,
		        'callback'            => array( $this, 'postMylearners' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_10, array(
	      array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'get_instructor_list' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_11, array(
	      array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'get_counselor_list' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_12, array(
	      array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'getting_address' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_13, array(
	      array(
		        'methods'             => WP_REST_Server::CREATABLE,
		        'callback'            => array( $this, 'address_update' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_15, array(
	      array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'user_info' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
		) );
		
	    register_rest_route( $this->namespace, $this->resource_name_16, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'user_iptolocation' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		  register_rest_route( $this->namespace, $this->resource_name_17, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'user_languages' ),
				  'permission_callback' => array( $this, 'user_languages_permissions_check' ),
				  'args'                => array(
			  ),
			),

		  ) );		

		  register_rest_route( $this->namespace, $this->resource_name_18, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'countries' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	

		  register_rest_route( $this->namespace, $this->resource_name_19, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_country' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );		
		  
		  register_rest_route( $this->namespace, $this->resource_name_20, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'states' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	

		  register_rest_route( $this->namespace, $this->resource_name_21, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_state' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	  	
		  register_rest_route( $this->namespace, $this->resource_name_22, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'cities' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	

		  register_rest_route( $this->namespace, $this->resource_name_23, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_city' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	
		  
		  register_rest_route( $this->namespace, $this->resource_name_24, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_statesbycountry' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );
		  
		  register_rest_route( $this->namespace, $this->resource_name_25, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_citiesbystate' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	  
		  		  
		  register_rest_route( $this->namespace, $this->resource_name_26, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_autofill' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );  
		  register_rest_route( $this->namespace, $this->resource_name_27, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_learner_list' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		  register_rest_route( $this->namespace, $this->resource_name_28, array(
			array(
				  'methods'             => WP_REST_Server::CREATABLE,
				  'callback'            => array( $this, 'createInstructorAvailabilitySlots' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );  
		  register_rest_route( $this->namespace, $this->resource_name_29, array(
			array(
				  'methods'             => WP_REST_Server::EDITABLE,
				  'callback'            => array( $this, 'updateInstructorAvailabilitySlots' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) ); 		  
		  register_rest_route( $this->namespace, $this->resource_name_30, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'getInstructorAvailabilitySlots' ),
				  'permission_callback' => array( $this, 'getInstructorAvailabilitySlots_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		  register_rest_route( $this->namespace, $this->resource_name_31, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'getRoleBasedUsers' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	
		  register_rest_route( $this->namespace, $this->resource_name_32, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'updateUserMeta' ),
				  'permission_callback' => array( $this, 'updateUserMeta_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	
		  register_rest_route( $this->namespace, $this->resource_name_33, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_instructor_list_for_category_admin' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	

		  register_rest_route( $this->namespace, $this->resource_name_34, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'getInstructorAvailabilityHourSlots' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	  	  

		  register_rest_route( $this->namespace, $this->resource_name_35, array(
	      array(
		        'methods'             => WP_REST_Server::CREATABLE,
		        'callback'            => array( $this, 'new_user_register' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );
	  
		register_rest_route( $this->namespace, $this->resource_name_37, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'getInstructorAvailabilityPerDayHourSlots' ),
				  'permission_callback' => array( $this, 'getInstructorAvailabilityPerDayHourSlotsPermissionsCheck' ),
				  'args'                => array(
			  ),
			),
		  ) );

		register_rest_route( $this->namespace, $this->resource_name_36, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'user_logout' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		) );

		register_rest_route( $this->namespace, $this->resource_name_38, array(
			array(
				  'methods'             => WP_REST_Server::CREATABLE,
				  'callback'            => array( $this, 'user_token_expire_time' ),
				  'permission_callback' => array( $this, 'user_token_expire_time_permissions_check' ),
				  'args'                => array(
			  ),
			),
		) );

		register_rest_route( $this->namespace, $this->resource_name_39, array(
			array(
				  'methods'             => WP_REST_Server::CREATABLE,
				  'callback'            => array( $this, 'generate_google_id_token' ),
				  'permission_callback' => array( $this, 'generate_google_refresh_token_permissions_check' ),
				  'args'                => array(
			  ),
			),
		) );

		register_rest_route( $this->namespace, $this->resource_name_40, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'user_permission' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		) );
		register_rest_route( $this->namespace, $this->resource_name_41, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'user_notificationsettings' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		) );

		register_rest_route( $this->namespace, '/' . $this->resource_name_42, array(
			array(
				  'methods'             => WP_REST_Server::EDITABLE,
				  'callback'            => array( $this, 'update_user_notificationsettings' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		register_rest_route( $this->namespace, '/' . $this->resource_name_43, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_user_info' ),
				  'permission_callback' => array( $this, 'get_user_info_permissions_check' ),
				  'args'                => array(
			  ),
			),
		) );

		register_rest_route( $this->namespace, '/' . $this->resource_name_44, array(
			array(
				  'methods'             => WP_REST_Server::CREATABLE,
				  'callback'            => array( $this, 'update_user_category' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		) );

		register_rest_route( $this->namespace, '/' . $this->resource_name_45, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'user_category' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		) );

		register_rest_route( $this->namespace, $this->resource_name_46, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_instructor_list_for_category_admin' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );	
          register_rest_route( $this->namespace, $this->resource_name_47, array(
            array(
              'methods'             => WP_REST_Server::READABLE,
              'callback'            => array( $this, 'getMylearners' ),
              'permission_callback' => array( $this, 'getMylearners_permissions_check' ),
              'args'                => array(
       
              ),
            ),
          ) ); 
		  register_rest_route( $this->namespace, $this->resource_name_48, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'getInstructorAvailabilitySlots' ),
				  'permission_callback' => array( $this, 'getInstructorAvailabilitySlots_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		  register_rest_route( $this->namespace, $this->resource_name_49, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_detail_from_org' ),
				  'permission_callback' => array( $this, 'get_detail_from_org_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		  register_rest_route( $this->namespace, $this->resource_name_50, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'create_jwt_token' ),
				  'permission_callback' => array( $this, 'create_jwt_token_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		  register_rest_route( $this->namespace, $this->resource_name_51, array(
			array(
				  'methods'             => WP_REST_Server::READABLE,
				  'callback'            => array( $this, 'get_jwt_token' ),
				  'permission_callback' => array( $this, 'get_jwt_token_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		  register_rest_route( $this->namespace, $this->resource_name_52, array(
			array(
				  'methods'             => WP_REST_Server::CREATABLE,
				  'callback'            => array( $this, 'update_user_active_category' ),
				  //'permission_callback' => array( $this, 'update_user_active_category_permissions_check' ),
				  'permission_callback' => array( $this, 'check_access_permissions_check' ),
				  'args'                => array(
			  ),
			),
		  ) );

		register_rest_route( $this->namespace, $this->resource_name_53, array(
	      array(
		        'methods'             => WP_REST_Server::CREATABLE,
		        'callback'            => array( $this, 'post_user_delete_requests' ),
		        'permission_callback' => array( $this, 'check_access_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );

	    register_rest_route( $this->namespace, $this->resource_name_54, array(
	      array(
		        'methods'             => WP_REST_Server::READABLE,
		        'callback'            => array( $this, 'get_user_delete_requests' ),
		        'permission_callback' => array( $this, 'get_user_delete_requests_permissions_check' ),
		        'args'                => array(
			),
	      ),
	    ) );
		register_rest_route($this->namespace, $this->resource_name_55, array(
            array(
                'methods'             => WP_REST_Server::CREATABLE,
                'callback'            => array($this, 'get_demo_requests'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args'                => array(),
            ),
        ));
        register_rest_route($this->namespace, $this->resource_name_56, array(
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array($this, 'get_demo_request'),
				'permission_callback' => array($this, 'check_access_permissions_check'),
				'args'                => array(),
			),
		));
		register_rest_route($this->namespace, $this->resource_name_57, array(
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array($this, 'get_mylearners_instructor'),
				'permission_callback' => array($this, 'check_access_permissions_check'),
				'args'                => array(),
			),
		));
		register_rest_route($this->namespace, $this->Orgadmin_learner_demo_requests, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'get_demo_requests'),
				'permission_callback' => array($this, 'org_admin_access_permissions_check'),
				'args'                => array(),
			),
		));
		register_rest_route($this->namespace, $this->Orgadmin_learner_demo_request_details, array(
			array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array($this, 'get_demo_request'),
				'permission_callback' => array($this, 'org_admin_access_permissions_check'),
				'args'                => array(),
			)
			));

		register_rest_route( $this->namespace, $this->user_auth_token, array(
		array(
				'methods'             => WP_REST_Server::READABLE,
				'callback'            => array( $this, 'get_auth_token' ),
				'permission_callback' => array( $this, 'get_auth_token_permissions_check' ),
				'args'                => array(
			),
		),
		) );
    }
	/**
	 * Permissions check for getting the authentication token.
	 * This function allows access to all users.
	 */
    public function get_auth_token_permissions_check() {
    	return true;
    }
    public function get_detail_from_org_permissions_check() {
    	return true;
    }

	public function org_admin_access_permissions_check() {
    	return true;
    }

    /**
     * Getting detail from org information 
     */
    public function get_detail_from_org($request) {
    	$codes = error_code_setting();
    	$user_id = $request['user_id'];
    	$result = UserElasticSearch::get_signedup("details_from_org",$user_id);
    	if ($result == "204") {
    		return new WP_Error($codes["GET_FAIL"]["code"], "Detail from org not found", array('status' => $codes["GET_FAIL"]["status"]));
    	} else {
			$response = array(
				"code" => $codes["GET_SUCCESS"]["code"], 
	    		"message" => 'Detail from org found', 
	    		"data" => $result
	    	);
			return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);    	
		}
    }
    /**
     * Token authorization check
     * This function common for all post login apis  
     */
    public function check_access_permissions_check(WP_REST_Request $request) {
		return true;
    	$authToken = $request->get_header('authorization');
		if (empty($authToken)) { 
			return false; 
		}
        list($bearer, $token) = explode(" ", $authToken);
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman
            if ($return) { 
				return true;
			} 
			else { 
				return false; 
			}

        }
    }

    /**
     * Getting user info
     * Current using for signup page for optimize performance speed up of login
     * call only 1 api instead of 3 apis
     */
    public function user_info($request) {
		$codes=error_code_setting();		
		global $wpdb;
		/*$userInfoCacheData = get_cache("user_info");
		if (!empty($userInfoCacheData)) {
			return new WP_REST_Response($userInfoCacheData, 200);
		}*/
    	$userId = (int)$request['userId'];
    	if ($userId > 0) {} else {
			return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));			
    	}

    	$userdata = get_userdata($userId);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));			
		}		
		$user_email = $userdata->data->user_email;
		$profileUserIdReference = get_user_meta($userId, 'profile_user_id_reference', true);
		$profileURL = get_permalink($profileUserIdReference);
		$profileURLArray = explode("/", $profileURL);
		$userProfileArray = array_filter($profileURLArray);
		$userProfileName = end($userProfileArray);
		$user_nicename = $userProfileName;
		//$user_nicename = $userdata->data->user_nicename;

		$roles = $userdata->roles ? $userdata->roles : '';		
    	if ($userdata->roles) {
			if (in_array('um_instructor', $userdata->roles)) {
				$current_role = 'Instructor'; 			
			} else if(in_array('um_counselor', $userdata->roles)) {
				$current_role = 'Counselor';			
			} else if (in_array('um_yuno-admin', $userdata->roles)) {
				$current_role = 'yuno-admin';				
			} else if (in_array('um_content-admin', $userdata->roles)) {
				$current_role = 'content-admin';						
			} else if (in_array('um_yuno-category-admin', $userdata->roles)) {
				$current_role = 'yuno-category-admin';				
			} else if (in_array('administrator', $userdata->roles)) {
				$current_role = 'administrator';				
			} else if (in_array('um_dashboard-viewer', $userdata->roles)) {
				$current_role = 'dashboard-viewer';				
 			} else {
				$current_role = 'Learner';
			}
		}	
    	$signupDetail = get_user_meta($userId, 'is_signup_complete', true);
		if ($signupDetail == true || $signupDetail == "true" || $signupDetail == 1) {
			$isSignup = "completed";
			//$is_lead_created = false;
			$isInvited = false;
		} else {
			$isInvited = get_user_meta($userId, 'Yuno_Is_Invited', true);
			if ($isInvited == 1) {
				$isInvited = true;
			} else {
				$isInvited = false;
			}
			$isSignup = "pending";
			//$is_lead_created = true;
			$classEnrollmentStatus = get_user_meta($userId, 'UNBOUNCE_CLASS_ENROLLMENT_STATUS', true);
			//error_log("classEnrollmentStatus123 === ".$classEnrollmentStatus);
			if ($classEnrollmentStatus == true) {
				$hasWebinarEnrolled = true;
			}
		}
		$l_category=get_user_meta($userId, 'Category_URL_For_Signup', true);
		if(is_array($l_category) && count($l_category)>0){  
			$learnerCategory=implode(",",$l_category);
		  } else {
			if(strpos($l_category, '/') !== false) {
			  $cat=strtolower(ltrim($l_category, '/'));
			} else {
			  $cat=strtolower($l_category);
			}
	
			if ($cat == trim($cat) && strpos($cat, ' ') !== false) {
			  $learner_cat=str_replace(' ', '-', $cat);
			  $learnerCategory=$learner_cat;  
			} else {
			  $learnerCategory=$cat;         
			}
		  }

		if ($current_role == "Learner") {
			date_default_timezone_set('Asia/Kolkata');
    		$currentDate = date("Y-m-d H:i:s");
    		$enrollmentData = [];
			$enrollmentFalseStatusData = [];
			$data = $wpdb->get_results("SELECT id,product_db_id, enrollment_end_date, enrollment_status FROM wp_enrollment WHERE user_id='".$userId."'", ARRAY_A);
			foreach ($data as $key => $value) {
				if($currentDate < $value['enrollment_end_date']){
      				$enrollmentData[] = [
      					"course_id" => $value['product_db_id'],
						"id" => $value['id'],
      					//"enrollment_status" => "active"
      				];
      			}
			}
    		
    		// get category belongs to logged in learner
    		if (count($enrollmentData) > 0) {
    			$category = [];
    			foreach ($enrollmentData as $key => $value) {
    				$categories = wp_get_post_terms((int)$value['course_id'], 'course_category');
    				foreach ($categories as $k => $v) {
    					if ($v->parent == 0) {
    						$category[] = strtolower($v->slug);	
    					}
    				}
    			}

						$userData = [
							"first_name" => get_user_meta($userId, 'yuno_first_name', true),
							"last_name" => get_user_meta($userId, 'yuno_last_name', true),
							'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
							'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
							'email' => $user_email,			
							'role' => $current_role,
							'vc_google' => get_user_meta($userId, 'google_meet_vc', true) == 1 ? true :false,
							'ins_meet_permission' => get_user_meta($userId,'ins_meet_permission',true) == 1 ? true :false,
							'profile_name' => $user_nicename,
							"mobile" => get_user_meta($userId, 'yuno_gplus_mobile', true),
							"category_url" => $learnerCategory,
							"is_signup_completed" => $isSignup,
							//"is_lead_created" => $is_lead_created,
							'is_enrollment_active' => true,
							'category' => $category,
							'isInvited' => $isInvited
						];
    		} else {
				$enrollmentStatus = "INACTIVE";
				$update_status = [
				'enrollment_status' => $enrollmentStatus
				];				
				foreach ($data as $key => $value) {
					if($currentDate > $value['enrollment_end_date']){
						  $enrollmentFalseStatusData[] = [
							"id" => $value['id'],
							  //"enrollment_status" => "active"
						  ];
					  }
				}				
				if (count($enrollmentFalseStatusData) > 0) {
					foreach ($enrollmentFalseStatusData as $key => $value) {
					$wpdb->update('wp_enrollment', $update_status, ['id' => $value['id']]);						
					}	
				}
						$userData = [
							"first_name" => get_user_meta($userId, 'yuno_first_name', true),
							"last_name" => get_user_meta($userId, 'yuno_last_name', true),
							'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
							'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
							'email' => $user_email,			
							'role' => $current_role,
							'vc_google' => get_user_meta($userId, 'google_meet_vc', true) == 1 ? true :false,
							'ins_meet_permission' => get_user_meta($userId,'ins_meet_permission',true) == 1 ? true :false,
							'profile_name' => $user_nicename,
							"mobile" => get_user_meta($userId, 'yuno_gplus_mobile', true),
							"category_url" => $learnerCategory,
							"is_signup_completed" => $isSignup,
							//"is_lead_created" => $is_lead_created,
							'is_enrollment_active' => false,
							'category' => [],
							'isInvited' => $isInvited
						];
    		}
		} else {
			$userData = [
				"first_name" => get_user_meta($userId, 'yuno_first_name', true),
				"last_name" => get_user_meta($userId, 'yuno_last_name', true),
				'profile_img' => get_user_meta($userId, 'googleplus_profile_img', true),
				'yuno_display_name' => get_user_meta($userId, 'yuno_display_name', true),
				'email' => $user_email,			
				'role' => $current_role,
				'vc_google' => get_user_meta($userId, 'google_meet_vc', true) == 1 ? true :false,
				'ins_meet_permission' => get_user_meta($userId,'ins_meet_permission',true) == 1 ? true :false,
				'profile_name' => $user_nicename,
				"mobile" => get_user_meta($userId, 'yuno_gplus_mobile', true),
				"category_url" => $learnerCategory,
				"is_signup_completed" => $isSignup,
				//"is_lead_created" => true,
				'isInvited' => $isInvited
			];
		}
        
		if (!empty($hasWebinarEnrolled)) {
			$userData = array_merge($userData, ["hasWebinarEnrolled" => $hasWebinarEnrolled]);
		}

    	$response = array('code' => $codes["GET_SUCCESS"]["code"], 
    					'message' => 'User info found', 
    					"data" => $userData);
    	//add_cache('user_info', $response, '', 300);
		return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
    }

    public function user_info_permissions_check() {
    	return true;
    }

    /**
     * Get all category of users, according to user enrollment in courses
     */
    public function user_category($request) {
    	$codes=error_code_setting();		
		global $wpdb;
		$categoryArgs = array(
		    'taxonomy' => 'course_category',
		    'parent' => 0,
		    //'orderby' => 'name',
		    'hide_empty' => 1,
		    'order'     => 'ASC',
            'meta_key'  => 'home_category_order',
            'orderby'   => 'meta_value_num'
		    //'order'   => 'ASC'
		);

		$categoryData = get_categories($categoryArgs);
		$category = [];
		foreach ($categoryData as $key => $value) {
			$category_visibility = get_field('android_visibility', $value->taxonomy.'_'.$value->term_id );
			if ($category_visibility == false) {
				$category_excerpt = get_field('category_excerpt', $value->taxonomy.'_'.$value->term_id );
				if (empty($category_excerpt)) {
				 	$category_excerpt = "";
				}
			    $imageData = get_field('image_path', $value->taxonomy.'_'.$value->term_id );
	    		$image = $imageData['sizes']['medium_large'];
			    $category[] = [
			    	"id" => $value->term_id,
					"name" => $value->name,
			        "slug" => $value->slug,
			        "description" => $value->description,
	    			"image" => $image,
	    			"excerpt" => $category_excerpt
			    ];
		    }
		}
	
    		if (empty($category)) {
    			return new WP_Error($codes["USER_FAIL"]["code"], "Category not found", array('status' => $codes["USER_FAIL"]["status"]));
    		}
			//$finalCategory = array_unique($category, SORT_REGULAR);
			//$finalCategory = array_values($finalCategory);
    		$response = array('code' => $codes["GET_SUCCESS"]["code"], 
    					'message' => 'Category found', 
    					"data" => $category);
			return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
    	//}
    }

    public function user_category_permissions_check() {
    	return true;
    }

   /**
     * Getting city name by id
     */	
    public function get_city($request) {
		$city_id = (int)$request['city_id'];
		$codes=error_code_setting();
		global $wpdb;
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."cities";
	    $resultCollections=[];
		$cities = $wpdb->get_results("SELECT * FROM $table_name_1 where id=$city_id" );
		foreach($cities as $city){
			$resultCollections=['id'=>$city->id,'name'=>$city->name,'country_code'=>$city->country_code];
		}
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'city(s) are found', 'data' => $resultCollections);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);	
	}
   /**
     * Getting city name by id
     */	
    public function get_city_permissions_check() {
    	return true;
	}
    /**
     * Getting address on the basis of criteria 
     * Criteria: Short/Detail
     */
    public function getting_address($request) {
    	$criteria = $request['criteria'];
		$codes=error_code_setting();
    	$user_id = $request['user_id'];
    	global $wpdb;
		$userExist = $wpdb->get_results("SELECT ID from wp_users where ID=$user_id", ARRAY_A);
    	if ($user_id == '' || $user_id == 0 || count($userExist) == 0) {
			return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));			
    	}

    	if ($criteria == 'detail') {
    		$country = get_user_meta($user_id, 'yuno_user_address_country', true);
	    	$pin_code = get_user_meta($user_id, 'yuno_user_address_pin_code', true);
	    	$house = get_user_meta($user_id, 'yuno_user_address_flat_house_number', true);
	    	$street = get_user_meta($user_id, 'yuno_user_address_street', true);
	    	$landmark = get_user_meta($user_id, 'yuno_user_address_landmark', true);
	    	$city = get_user_meta($user_id, 'yuno_user_address_city', true);
	    	$state = get_user_meta($user_id, 'yuno_user_address_state', true);
	    	$address_type = get_user_meta($user_id, 'yuno_user_address_type', true);

	    	if ($country == "" && $pin_code == "" && $house == "" && $street == "" 
	    			&& $landmark == "" && $city == "" && $state == "" && $address_type == "") {
	    		return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find address detail", array('status' => $codes["GET_FAIL"]["status"]));
	    	}
			$cityData['city_id']=$city;
			$city_name=$this->get_city($cityData);
			$cityName=$city_name->data['data']['name'];
	    	$addressData = [
	    		"country" => $country,
	    		"pin_code" => $pin_code,
	    		"flat_house_number" => $house,
	    		"street" => $street,
	    		"landmark" => $landmark,
	    		"city" => $cityName,
	    		"state" => $state,
	    		"address_type" => $address_type
			];
			$otherdetails=[];
			if ( filter_var($city, FILTER_VALIDATE_INT) === true ) {
				$cityExist = $wpdb->get_row("SELECT wp_cities.name  as city_name,wp_states.`name`  as state_name,wp_countries.iso2,wp_countries.phonecode,wp_countries.currency ,wp_countries.timezones FROM `wp_countries` left join wp_states on wp_states.country_id=wp_countries.id left join wp_cities on wp_cities.country_id=wp_countries.id where wp_cities.id=$city");
				$curl = curl_init();

				curl_setopt_array($curl, array(
				CURLOPT_URL => 'https://app.zipcodebase.com/api/v1/code/city?city='.$cityExist->city_name.'&state_code='.$cityExist->state_name.'&country='.$cityExist->iso2,
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'GET',
				CURLOPT_HTTPHEADER => array(
					': 02e2ba90-646d-11eb-b55d-754dae387822',
					'apikey: 02e2ba90-646d-11eb-b55d-754dae387822',
					'Cookie: __cfduid=d86db6af94e19c543d4e1fea7329353db1612170655'
				),
				));

				$postal_code = json_decode(curl_exec($curl));
				curl_close($curl);

				$timezones=json_decode($cityExist->timezones);
				foreach($timezones as $timezone){
					$zones=["zoneName"=>$timezone->zoneName,"gmtOffsetName"=>$timezone->gmtOffsetName];
				}
				$postals=$postal_code->results;
				foreach($postals as $postal){
					$zip_code=$postal;
				}
			$otherdetails=["country_calling_code"=>$cityExist->phonecode,"currency"=>$cityExist->currency,"timezones"=>$zones,"postal_code"=>$zip_code];
			  }

	    	$response = array('code' => $codes["GET_SUCCESS"]["code"], 
    					'message' => 'Address detail found', "data" => $addressData);
			return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
    	} else if ($criteria == 'short') {
    		$country = get_user_meta($user_id, 'yuno_user_address_country', true);
	    	$city = get_user_meta($user_id, 'yuno_user_address_city', true);
	    	$state = get_user_meta($user_id, 'yuno_user_address_state', true);

	    	if ($country == "" && $city == "" && $state == "") {
	    		return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find address detail", array('status' => $codes["GET_FAIL"]["status"]));
	    	}
			$cityData['city_id']=$city;
			$city_name=$this->get_city($cityData);
			$cityName=$city_name->data['data']['name'];
	    	$addressData = [
	    		"country" => $country,
	    		"city" => $cityName,
	    		"state" => $state
			];
			
	    	$response = array('code' =>  $codes["GET_SUCCESS"]["code"], 
    					'message' => 'Short address found', "data" => $addressData);
			return new WP_REST_Response($response,  $codes["GET_SUCCESS"]["code"]);
    	} else {
    		return new WP_Error($codes["GET_FAIL"]["code"], 'Incorrect or missing API request parameters', array('status' => $codes["GET_FAIL"]["status"]));
    	}
    }

    public function getting_address_permissions_check() {
    	return true;
    }

    /**
     * Update address for user
     */
    public function address_update($request) {
	
		$codes=error_code_setting();
    	$data = json_decode($request->get_body(), true);
    	$user_id = (int)$data['user_id'];
    	$country = $data['country'];
    	$pin_code = $data['pin_code'];
    	$flat_house_number = $data['flat_house_number'];
    	$street = $data['street'];
    	$landmark = $data['landmark'];
		$city = $data['city'];
    	$state = $data['state'];
		$address_type = $data['address_type'];
		
		// validation
		global $wpdb;
		$userdata = get_userdata($user_id);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
		}
		$details = [];
		if ($userdata->roles) {

			if (in_array('um_instructor', $userdata->roles)) {
				$current_role = 'instructor';
			}
			else if (in_array('um_counselor', $userdata->roles)) {
				$current_role = 'counselor';
			}
			else if (in_array('um_yuno-admin', $userdata->roles)) {
				$current_role = 'yuno-admin';
			}
			else if (in_array('um_content-admin', $userdata->roles)) {
				$current_role = 'content-admin';
			}
			else if (in_array('um_yuno-category-admin', $userdata->roles)) {
				$current_role = 'category-admin';
			}
			else if (in_array('um_dashboard-viewer', $userdata->roles)) {
				$current_role = 'dashboard-viewer';
			}
			else if (in_array('um_org-admin', $userdata->roles)) {
				$current_role = 'org-admin';
			}
			else {
				$current_role = 'learner';
			}
		}
		$userExist = $wpdb->get_results("SELECT ID from wp_users where ID=$user_id", ARRAY_A);
		if ($user_id == '' || $user_id == 0 || count($userExist) == 0) {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the user's ID and try again", array('status' => $codes["GET_FAIL"]["status"]));
		} else if ($country == '') {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the country and try again", array('status' => $codes["GET_FAIL"]["status"]));
		} else if ($pin_code == '') {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the pin code and try again", array('status' => $codes["GET_FAIL"]["status"]));
		} else if ($flat_house_number == '') {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the address and try again", array('status' => $codes["GET_FAIL"]["status"]));
		} else if ($street == '') {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the area and try again", array('status' => $codes["GET_FAIL"]["status"]));
		} else if ($landmark == '') {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the landmark and try again", array('status' => $codes["GET_FAIL"]["status"]));
		} else if ($city == '') {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the city and try again", array('status' => $codes["GET_FAIL"]["status"]));
		} else if ($state == '') {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the state and try again", array('status' => $codes["GET_FAIL"]["status"]));
		} else if ($address_type == '') {
			return new WP_Error($codes["GET_FAIL"]["code"], "Please check the address type and try again", array('status' => $codes["GET_FAIL"]["status"]));
		}
    	update_user_meta($user_id, 'yuno_user_address_country', $country);
    	update_user_meta($user_id, 'yuno_user_address_pin_code', $pin_code);
    	update_user_meta($user_id, 'yuno_user_address_flat_house_number', $flat_house_number);
    	update_user_meta($user_id, 'yuno_user_address_street', $street);
    	update_user_meta($user_id, 'yuno_user_address_landmark', $landmark);
    	update_user_meta($user_id, 'yuno_user_address_city', $city);
    	update_user_meta($user_id, 'yuno_user_address_state', $state);
    	update_user_meta($user_id, 'yuno_user_address_type', $address_type);
		$city_id=get_user_meta($user_id, 'yuno_user_address_city', true );
		$city = $wpdb->get_row( "SELECT name FROM wp_cities where id=$city_id");
		$curlUpdate = [
			"data" =>[
			  "details" => [
				"user_id" => $user_id,
				"record_id" => $user_id,
				"update_event_type" => "instructorsignedup",
				"country"=> get_user_meta($user_id, 'yuno_user_address_country', true),
				"address_pin_code"=> get_user_meta($user_id, 'yuno_user_address_pin_code', true),
				"address_flat_house_number"=> get_user_meta($user_id, 'yuno_user_address_flat_house_number', true),
				"address_street"=> get_user_meta($user_id, 'yuno_user_address_street', true),           
				"address_landmark"=> get_user_meta($user_id, 'yuno_user_address_landmark', true),
				"city_id"=> get_user_meta($user_id, 'yuno_user_address_city', true),
				"address_state"=> get_user_meta($user_id, 'yuno_user_address_state', true),
				"address_type"=> get_user_meta($user_id, 'yuno_user_address_type', true),								
				"city_name"=> $city->name            
			  ],
			  "@timestamp" => date("Y-m-d H:i:s")
			  ]
			];    		
		  update_elastic_event($curlUpdate);  
		  $user_obj     = [
			"name"  => get_user_meta($user_id, 'yuno_display_name', true),
			"email" => get_user_meta($user_id, 'yuno_gplus_email', true),
			"phone" => get_user_meta($user_id, 'yuno_gplus_mobile', true),
			"image" => get_user_meta($user_id, 'googleplus_profile_img', true)
		];
		  $location_obj = [
			"country"           => get_user_meta($user_id, 'yuno_user_address_country', true),
			"pin_code"          => get_user_meta($user_id, 'yuno_user_address_pin_code', true),
			"flat_house_number" => get_user_meta($user_id, 'yuno_user_address_flat_house_number', true),
			"street"            => get_user_meta($user_id, 'yuno_user_address_street', true),
			"landmark"          => get_user_meta($user_id, 'yuno_user_address_landmark', true),
			"city"              => $city->name,
			"state"             => get_user_meta($user_id, 'yuno_user_address_state', true),
			"address_type"      => get_user_meta($user_id, 'yuno_user_address_type', true)
		];

		$details    = [
			"properties"   => [
				"details"    => [
					"user_id"       => $user_id,
					"role"          => $current_role,
					"user"          => $user_obj,
					"location"      => $location_obj
				],
			]
		];

		$properties = $details['properties'];
		$curlPost['data'] = [
			"data" => $properties,
		];
		//error_log("every post es call" . date("Y-m-d H:i:s") . "=== " . $url . "Event type " . json_encode($curlPost) . "=== Response" . json_encode($response) . "\n", 3, ABSPATH . "error-logs/every-remote.log");
		UserElasticSearch::update_signedup("basic_details", $curlPost);
		
    	$response = array('code' => $codes["POST_INSERT"]["code"], 
    					'message' => 'Address updated successfully', 
    					"data" => array('status' => $codes["POST_INSERT"]["code"]));
		delete_cache('get_user_profile_'.$user_id, '', [$user_id]);				
		return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]);
    }

    public function address_update_permissions_check() {
    	return true;
    }

    /**
     * Get list of counselor
     */
    public function get_counselor_list($request) {
		$codes=error_code_setting();
    	$args = array(
		    'role'    => 'um_counselor',
		    'orderby' => 'display_name',
		    'order'   => 'ASC',
			'fields' => array('ID','display_name','user_email') /// Nov-16 - 2023--added
		);
		$users = get_users( $args );   /// Nov-16 - 2023--added
		$instructor_array = array();
		foreach ($users as $key => $value) {
		  //$zoho_ins_id = get_user_meta($value->ID,'zoho_instructor_id',true);
		  //if($zoho_ins_id!==''){
		    //$value->zoho_ins_id = $zoho_ins_id;
		    $instructor_array[] = [
		    	"id" => $value->ID,
		    	"name" => $value->display_name,
		    	"email" => $value->user_email,
		    	"name_email" => $value->display_name." (".$value->user_email.")"
		    ];
		  //}
		}
		if (!empty($instructor_array)) {
			$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'Instructor data found', 'data' => $instructor_array);
			return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);	
		} else {
			return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find any instructor. Check back later as new instructor get added to the list", array('status' => $codes["GET_FAIL"]["status"]));	
		}
    }

    public function get_counselor_list_permissions_check() {
    	return true;
    }

    public function get_instructor_list_permissions_check() {
    	return true;
    }

    /**
     * Get list of instructors
     */
    public function get_instructor_list($request) {
		$codes=error_code_setting();

		$args = array(
			'role'    => 'um_instructor',
			'orderby' => 'display_name',
			'order'   => 'ASC',
			'meta_query' => array(
			  array(
				  'key' => 'zoho_instructor_id',
				  'value' => array(''),
				  'compare' => '!=',
			  )
			),
			'fields' => array('ID','display_name','user_email')
		  );
		  $users = get_users( $args );  /// Nov-16 - 2023--added
		  $instructor_array = array();
		  foreach ($users as $key => $value) {
			$instructor_array[] = [
			  "id" => $value->ID,
			  "name" => $value->display_name,
			  "email" => $value->user_email,
			  "name_email" => $value->display_name." (".$value->user_email.")"
			];
		  }

    	// $args = array(
		//     'role'    => 'um_instructor',
		//     'orderby' => 'display_name',
		//     'order'   => 'ASC'
		// );
		// $users = get_users( $args );  //// commented
		// $instructor_array = array();
		// foreach ($users as $key => $value) {
		//   $zoho_ins_id = get_user_meta($value->ID,'zoho_instructor_id',true);
		//   if($zoho_ins_id!==''){
		//     $value->zoho_ins_id = $zoho_ins_id;
		//     $instructor_array[] = [
		//     	"id" => $value->ID,
		//     	"name" => $value->display_name,
		//     	"email" => $value->user_email,
		//     	"name_email" => $value->display_name." (".$value->user_email.")"
		//     ];
		//   }
		// }

		if (!empty($instructor_array)) {
			$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'Instructor data found', 'data' => $instructor_array);
			return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);	
		} else {
			return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find any instructor. Check back later as new instructor get added to the list", array('status' => $codes["GET_FAIL"]["status"]));	
		}


    }

    /**
     * Login with google
     * @return auth url for google login
     */
    public function login_with_google($request) {
		$codes=error_code_setting();
    	$data = json_decode($request->get_body());
    	$uri = $data->state;
    	$authUrl = get_auth_url($uri);
        $login_url = array(
							'code' => $codes["POST_INSERT"]["code"], 
							'message' => 'Login url created', 
							'data' => array('auth_url' => $authUrl)
					);
		return new WP_REST_Response($login_url, $codes["POST_INSERT"]["code"]);
    }

    /**
     * Category base signup - insert user input
     */
    public function user_signup($request) {
    	global $wpdb;
		$codes=error_code_setting();
    	$user_id = $request['user_id'];
    	$category = strtolower($request['category']);
    	if ($category == "anil-lamba-on-finance") {
    		$category = "finance";
    	}
    	if (empty($user_id) || empty($category)) {
	    	return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));	
	    }
	    $isExist = update_user_meta($user_id, 'is_signup_complete', true);
		$status="private";
		update_user_meta($user_id, 'profile_privacy',$status);
		update_user_meta($user_id, 'classattendevent',"false");		
	    if($isExist == ture || $isExist == "ture"){
	    	return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User already signed up', array('status' => $codes["POST_INSERT_FAIL"]["code"]));	
	    }
		$data = json_decode($request->get_body());
		$yuno_gplus_mobile = $data->yuno_gplus_mobile;
    	$yuno_user_whatsapp_check = $data->yuno_user_whatsapp_check;
    	if (empty($yuno_gplus_mobile) || strlen($yuno_gplus_mobile) != 10) {
	    	return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"], 'message' => 'Please enter valid mobile number of 10 digit'));	
	    }
    	if ($category == "ielts") {
	    	$when_planning_ielts = $data->when_planning_ielts;
	    	$time_of_study = $data->time_of_study;
	    	$type_of_ielts = $data->type_of_ielts;
	    	$target_band = $data->target_band;
	    	$term_and_condition = $data->term_and_condition;
	    	if (empty($when_planning_ielts) || empty($time_of_study) || 
	    			empty($type_of_ielts) || empty($target_band) || 
	    				empty($term_and_condition) || empty($yuno_gplus_mobile)) {
	    		return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));	
	    	}
	    	update_user_meta($user_id, 'term_and_condition', $term_and_condition);
	    	update_user_meta($user_id, 'yuno_user_whatsapp_check', $yuno_user_whatsapp_check);
	    	update_user_meta($user_id, 'Category_URL_For_Signup', $category);
    		$data = [
    			"user_id" => $user_id,
    			"when_planning_ielts" => $when_planning_ielts,
    			"time_of_study" => $time_of_study,
    			"type_of_ielts" => $type_of_ielts,
    			"target_band" => $target_band
    		];
    		$table = "wp_ielts_demographic";
    		$inserted = $wpdb->insert($table, $data);
    		update_user_meta($user_id, 'yuno_gplus_mobile', $yuno_gplus_mobile);
			$curlPost = [
				"data" =>[
					"details" => [
						"user_id" => $user_id,
						"event_type" => "signedup",
						"event_label" => "User signed up",
						"role" => "Learner",
						"category_url_for_signup" => $category,
						"yuno_user_whatsapp_check" => $yuno_user_whatsapp_check,
						"privacy_policy_terms_of_service" => $term_and_condition,
						"when_planning_ielts" =>  $when_planning_ielts,
						"time_of_study" =>  $time_of_study,
						"type_of_ielts" =>  $type_of_ielts,
						"ielts_target_band" =>  $target_band,
						"referred_by" =>  ""			
					],
					"@timestamp" => date("Y-m-d H:i:s")
				  ]
				];
			//error_log($category.'Call post_signup_event'.json_encode($curlPost));    		
			post_elastic_event($curlPost); 
    	} else if ($category == "coding-for-kids") {
    		$student_age = $data->student_age;
    		$term_and_condition = $data->term_and_condition;
    		$what_best_describes_you = $data->what_best_describes_you;
    		$laptop_desktop_access = $data->laptop_desktop_access;
    		if (empty($student_age) || empty($what_best_describes_you) || 
	    			empty($term_and_condition) || empty($yuno_gplus_mobile)) {
	    		return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));	
	    	}
	    	update_user_meta($user_id, 'term_and_condition', $term_and_condition);
	    	update_user_meta($user_id, 'what_best_describes_you', $what_best_describes_you);
	    	update_user_meta($user_id, 'laptop_desktop_access', $laptop_desktop_access);
	    	update_user_meta($user_id, 'yuno_user_whatsapp_check', $yuno_user_whatsapp_check);
	    	update_user_meta($user_id, 'Category_URL_For_Signup', $category);
    		$data = [
    			"user_id" => $user_id,
    			"student_age" => $student_age
    		];
    		$table = "wp_coding_for_kids_demographic";
    		$inserted = $wpdb->insert($table, $data);

    		update_user_meta($user_id, 'yuno_gplus_mobile', $yuno_gplus_mobile);
			$curlPost = [
				"data" =>[
					"details" => [
						"user_id" => $user_id,
						"event_type" => "signedup",
						"event_label" => "User signed up",
						"role" => "Learner",
						"category_url_for_signup" => $category,
						"yuno_user_whatsapp_check" => $yuno_user_whatsapp_check,
						"privacy_policy_terms_of_service" => $term_and_condition,
						"what_best_describes_you" =>  $what_best_describes_you,
						"laptop_desktop_access" =>  $laptop_desktop_access,
						"student_age" =>  $student_age,
						"referred_by" =>  ""				
					],
					"@timestamp" => date("Y-m-d H:i:s")
				  ]
				];
			//error_log($category.'Call post_signup_event'.json_encode($curlPost));    		
			post_elastic_event($curlPost); 
    	} else if ($category == "english-speaking") {
    		//error_log("sign up raman === ".json_encode($data->reason_enroll));
    		$reason_enroll_crm = json_encode($data->reason_enroll);
    		$reason_enroll = serialize($data->reason_enroll);
    		$classes_duration = isset($data->classes_duration) ? $data->classes_duration : "";
    		$what_best_describes_you = $data->what_best_describes_you;
    		$term_and_condition = $data->term_and_condition;
	    	if (empty($reason_enroll) || empty($what_best_describes_you) || 
	    		empty($term_and_condition) || empty($yuno_gplus_mobile)) {
	    		return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));	
	    	}
	    	update_user_meta($user_id, 'term_and_condition', $term_and_condition);
	    	update_user_meta($user_id, 'what_best_describes_you', $what_best_describes_you);
	    	update_user_meta($user_id, 'yuno_user_whatsapp_check', $yuno_user_whatsapp_check);
	    	update_user_meta($user_id, 'Category_URL_For_Signup', $category);
    		$data = [
    			"user_id" => $user_id,
    			"reason_enroll" => $reason_enroll,
    			"classes_duration" => $classes_duration
    		];
    		$table = "wp_english_speaking_demographic";
    		$inserted = $wpdb->insert($table, $data);

    		$reason_enroll_string = implode(", ", json_decode($reason_enroll_crm, true));
    		
    		update_user_meta($user_id, 'yuno_gplus_mobile', $yuno_gplus_mobile);
			$curlPost = [
				"data" =>[
					"details" => [
						"user_id" => $user_id,
						"event_type" => "signedup",
						"event_label" => "User signed up",
						"role" => "Learner",
						"category_url_for_signup" => $category,
						"yuno_user_whatsapp_check" => $yuno_user_whatsapp_check,
						"privacy_policy_terms_of_service" => $term_and_condition,
						"what_best_describes_you" =>  $what_best_describes_you,
						"reason_enroll" =>  $reason_enroll,
						"classes_duration" =>  $classes_duration,
						"referred_by" =>  ""				
					],
					"@timestamp" => date("Y-m-d H:i:s")
				  ]
				];
			//error_log($category.'Call post_signup_event'.json_encode($curlPost));    		
			post_elastic_event($curlPost); 			
    	} else if ($category == "general" || $category == "vedic-maths" 
    		|| $category == "microsoft-excel" || $category == "python-programming") {
    		$what_best_describes_you = $data->what_best_describes_you;
    		$term_and_condition = $data->term_and_condition;
    		$categoryName = isset($data->category_name) ? $data->category_name : $category;
	    	if (empty($what_best_describes_you) || empty($term_and_condition)) {
	    		return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));	
	    	}
	    	update_user_meta($user_id, 'term_and_condition', $term_and_condition);
	    	update_user_meta($user_id, 'what_best_describes_you', $what_best_describes_you);
	    	update_user_meta($user_id, 'yuno_user_whatsapp_check', $yuno_user_whatsapp_check);
	    	update_user_meta($user_id, 'Category_URL_For_Signup', $categoryName);
    		update_user_meta($user_id, 'yuno_gplus_mobile', $yuno_gplus_mobile);
    		update_user_meta($user_id, 'is_signup_complete', true);
			$signup_result = array(
				'code' => $codes["POST_INSERT"]["code"], 
				'message' => 'User signup is done', 
				//'data' => array('record_inserted' => $lastInsertedId)
			);

			$userdata = get_userdata($user_id);
			$email = $userdata->data->user_email;
			$whatsappFlag = $yuno_user_whatsapp_check == "true" ? true : false;
			$settings_array = [
				"user_id"=> $user_id,
	            "email"=> $email,
	            "mobile"=> $yuno_gplus_mobile,
	            "first_name"=> get_user_meta($user_id, 'yuno_first_name', true),
	            "last_name"=>  get_user_meta($user_id, 'yuno_last_name', true),
	            "whatsapp"=> $yuno_gplus_mobile,
	            "yuno_user_whatsapp_check"=> $whatsappFlag,
			];
			self::userWhatsAppNotificationSettings($settings_array);
			if($yuno_user_whatsapp_check == true || $yuno_user_whatsapp_check == "true"){
				$event_trigger_obj = [
		          "event_id"=> "1",
		          "user_ids"=> (explode(" ",$user_id)),
		          "customfields"=> []
		        ];
		        $notification_obj = json_encode($event_trigger_obj);
		        //error_log('signup trigger====='.$notification_obj);
				$channel="whatsapp";
				$item="welcome";
				sendNotification($notification_obj,$channel,$item); 
			}
			$curlPost = [
				"data" =>[
					"details" => [
						"user_id" => $user_id,
						"event_type" => "signedup",
						"event_label" => "User signed up",
						"role" => "Learner",
						"category_url_for_signup" => $categoryName,
						"yuno_user_whatsapp_check" => $yuno_user_whatsapp_check,
						"privacy_policy_terms_of_service" => $term_and_condition,
						"what_best_describes_you" =>  $what_best_describes_you,
						"referred_by" =>  ""				
					],
					"@timestamp" => date("Y-m-d H:i:s")
				  ]
				];
			//error_log($categoryName.'Call post_signup_event'.json_encode($curlPost));    		
			post_elastic_event($curlPost); 
			return new WP_REST_Response($signup_result, $codes["POST_INSERT"]["code"]);
    	} else if ($category == "finance") {
    		$what_best_describes_you = $data->what_best_describes_you;
    		$term_and_condition = $data->term_and_condition;
	    	if (empty($what_best_describes_you) || empty($term_and_condition)) {
	    		return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));	
	    	}
	    	update_user_meta($user_id, 'term_and_condition', $term_and_condition);
	    	update_user_meta($user_id, 'what_best_describes_you', $what_best_describes_you);
	    	update_user_meta($user_id, 'yuno_user_whatsapp_check', $yuno_user_whatsapp_check);
	    	update_user_meta($user_id, 'Category_URL_For_Signup', "anil-lamba-on-finance");
    		update_user_meta($user_id, 'yuno_gplus_mobile', $yuno_gplus_mobile);
    		update_user_meta($user_id, 'is_signup_complete', true);
			$signup_result = array(
				'code' => $codes["POST_INSERT"]["code"], 
				'message' => 'User signup is done', 
				//'data' => array('record_inserted' => $lastInsertedId)
			);

			$userdata = get_userdata($user_id);
			$email = $userdata->data->user_email;
			$whatsappFlag = $yuno_user_whatsapp_check == "true" ? true : false;
			$settings_array = [
				"user_id"=> $user_id,
	            "email"=> $email,
	            "mobile"=> $yuno_gplus_mobile,
	            "first_name"=> get_user_meta($user_id, 'yuno_first_name', true),
	            "last_name"=>  get_user_meta($user_id, 'yuno_last_name', true),
	            "whatsapp"=> $yuno_gplus_mobile,
	            "yuno_user_whatsapp_check"=> $whatsappFlag,
			];
			self::userWhatsAppNotificationSettings($settings_array);
			if($yuno_user_whatsapp_check == true || $yuno_user_whatsapp_check == "true"){
				$event_trigger_obj = [
		          "event_id"=> "1",
		          "user_ids"=> (explode(" ",$user_id)),
		          "customfields"=> []
		        ];
		        $notification_obj = json_encode($event_trigger_obj);
		        //error_log('signup trigger====='.$notification_obj);
				$channel="whatsapp";
				$item="welcome";
				sendNotification($notification_obj,$channel,$item); 
			}
			$curlPost = [
				"data" =>[
					"details" => [
						"user_id" => $user_id,
						"event_type" => "signedup",
						"event_label" => "User signed up",
						"role" => "Learner",
						"category_url_for_signup" => "anil-lamba-on-finance",
						"yuno_user_whatsapp_check" => $yuno_user_whatsapp_check,
						"privacy_policy_terms_of_service" => $term_and_condition,
						"what_best_describes_you" =>  $what_best_describes_you,
						"referred_by" =>  ""				
					],
					"@timestamp" => date("Y-m-d H:i:s")
				  ]
				];
			//error_log('anil-lamba-on-finance Call post_signup_event'.json_encode($curlPost));    		
			post_elastic_event($curlPost); 			
			return new WP_REST_Response($signup_result, $codes["POST_INSERT"]["code"]);
    	}

    	if ($inserted) {
        	$lastInsertedId = $wpdb->insert_id;
        	update_user_meta($user_id, 'is_signup_complete', true);
			$signup_result = array(
				'code' => $codes["POST_INSERT"]["code"], 
				'message' => 'User signup is done', 
				'data' => array('record_inserted' => $lastInsertedId)
			);
			$userdata = get_userdata($user_id);
			$email = $userdata->data->user_email;
			$whatsappFlag = $yuno_user_whatsapp_check == "true" ? true : false;
			$settings_array = [
				"user_id"=> $user_id,
	            "email"=> $email,
	            "mobile"=> $yuno_gplus_mobile,
	            "first_name"=> get_user_meta($user_id, 'yuno_first_name', true),
	            "last_name"=>  get_user_meta($user_id, 'yuno_last_name', true),
	            "whatsapp"=> $yuno_gplus_mobile,
	            "yuno_user_whatsapp_check"=> $whatsappFlag,
			];
			self::userWhatsAppNotificationSettings($settings_array);
			if($yuno_user_whatsapp_check == true || $yuno_user_whatsapp_check == "true"){
				$event_trigger_obj = [
		          "event_id"=> "1",
		          "user_ids"=> (explode(" ",$user_id)),
		          "customfields"=> []
		        ];
		        $notification_obj = json_encode($event_trigger_obj);
		        //error_log('signup trigger====='.$notification_obj);
				$channel="whatsapp";
				$item="welcome";
				sendNotification($notification_obj,$channel,$item); 
			}
			return new WP_REST_Response($signup_result, $codes["POST_INSERT"]["code"]);               	
        } else {
			return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User signup is not done', array('status' => $codes["POST_INSERT_FAIL"]["code"]));	
        }
    }

    public static function userWhatsAppNotificationSettings($settings_array){
		if($settings_array['yuno_user_whatsapp_check']){
			$yuno_user_whatsapp_check="true";
		} else {
			$yuno_user_whatsapp_check="false";
		}
    	$notification_data = [
            "user_id"=> $settings_array['user_id'],
            "email"=> $settings_array['email'],
            "phoneno"=> $settings_array['mobile'],
            "first_name"=> $settings_array['first_name'],
            "last_name"=> $settings_array['last_name'],
            "whatsapp"=> $settings_array['mobile'],
            "events"=> [
            	[
		            "id"=> "1",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "2",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "3",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "4",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "5",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "6",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "7",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "8",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "9",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "10",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "11",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ], [
		            "id"=> "12",
		            "whatsapp"=> $yuno_user_whatsapp_check,
		            "sms"=> "false",
		            "email"=> "true",
		            "voice"=> "false",
		            "desktop_push"=> "false",
		            "mobile_push" => "true"
		        ]
		    ]
        ];
        $settings_obj = json_encode($notification_data);
		//error_log('notification settings====='.$settings_obj);
        postNotificationSettings($settings_obj);
        return;
    }
    /**
     * Getting current logged in user complete sign up process or not
     */
    public function get_signup_completed($request) {
		$codes=error_code_setting();
    	$user_id = $request['user_id'];
    	$is_signup_complete = get_user_meta($user_id, 'is_signup_complete', true);
    	if ($is_signup_complete == true) {
    		$response = array('code' =>  $codes["GET_SUCCESS"]["code"], 
    							'message' => 'User has signed up already', 
    								'data' => 'completed');
			return new WP_REST_Response($response,  $codes["GET_SUCCESS"]["code"]);
    	} else {
    		$response = array('code' =>  $codes["GET_SUCCESS"]["code"], 
    							'message' => 'User has not signed up yet',
    								'data' => 'pending');
			return new WP_REST_Response($response,  $codes["GET_SUCCESS"]["code"]);
    	}
    }

    /**
     * Get sign up template according to category 
     */
    public function get_signup_template($request) {
    	global $wpdb;
		$codes=error_code_setting();
    	$uri = explode("/", $request['uri']);
    	$filteredURI = array_values(array_filter($uri));
    	$templateData = $wpdb->get_results("SELECT category, template_name, template_url from wp_signup_template", ARRAY_A);
    	foreach ($templateData as $key => $value) {
    		if (in_array($value['category'], $filteredURI)) {
    			$template_name = $value['template_name'];
    			$template_url = $value['template_url'];
    			$response = ['code' =>  $codes["GET_SUCCESS"]["code"], 
    							'message' => 'Template found',
    								'data' => ['template_name' => $template_name, 'template_url' => $template_url]];
    			return new WP_REST_Response($response,  $codes["GET_SUCCESS"]["code"]);
    		}
    	}
    	$response = ['code' =>  $codes["GET_SUCCESS"]["code"], 
    					'message' => 'Template found',
    						'data' => ['template_name' => 'general', 'template_url' => 'general']];
    	return new WP_REST_Response($response,  $codes["GET_SUCCESS"]["code"]);
    }


    public function update_user_detail($request) {
    	global $wpdb;
		$codes=error_code_setting();
    	$data = json_decode($request->get_body());
    	$userID = (int)$data->user_id; // change according to you
		$criteria = $data->criteria;
    	//$instructor_address = $data->address;
    	$instructor_experience = $data->experience;
		if($criteria == "instructor"){
			$u = new WP_User($userID);
			// Remove role
			$u->remove_role('SEO Manager');					
			// Add role
			$u->add_role('um_instructor');
			update_user_meta($userID, 'profile_privacy',"public");
			update_user_meta($userID, 'zoom_user_status',"free");	
			//update_user_meta($userID, 'current_user_type',"instructor");	

			$instructor_country = $data->steps[0]->fields->country;	
			$instructor_state = $data->steps[0]->fields->state;
			$instructor_city = $data->steps[0]->fields->city;	
			$fluent_in = $data->steps[0]->fields->fluent_in;	
			$native_language = $data->steps[0]->fields->native_language;	
			//$native_language = $data->steps->fields->native_language;	
			$instructor_understand = $data->steps[0]->fields->understand;
			$teaching_preference = $data->steps[1]->fields->teaching_preference;
			$laptop_availability = $data->steps[0]->fields->laptop_availability;
			$first_name = $data->steps[0]->fields->first_name;
			$last_name = $data->steps[0]->fields->last_name;
			$phone = $data->steps[0]->fields->phone;
			$broadband_connection_availability = $data->steps[0]->fields->broadband_connection_availability;
			$online_teaching_exp = $data->steps[0]->fields->online_teaching_exp;
			$dob = $data->steps[0]->fields->dob;
			$can_teach = array_values($data->steps[1]->fields->can_teach);
			if($data->steps[0]->is_step_completed){
				$is_completed_step_1 = "yes";
			} else {
				$is_completed_step_1 = "no";
			}
	
			if($data->steps[1]->is_step_completed){
				$is_completed_step_2 = "yes";
			} else {
				$is_completed_step_2 = "no";
			}
	
			if($data->steps[0]->fields->yuno_user_whatsapp_check){
				$yuno_user_whatsapp_check = "yes";
			} else {
				$yuno_user_whatsapp_check = "no";
			}
			$login_status="de-active";
			if($data->steps[2]->is_step_completed){
				$is_completed_step_3 = "yes";	
				update_user_meta($userID, 'account_login_status', 'active');	
				$login_status="active";
				$user_data = get_userdata($userID);
				insert_notification($userID);
				$output=delete_events_from_specific_learner($userID);
				$userId = $userID;
				$userLeadId = get_user_meta($userId,'zoho_lead_id', true);
				$mobile = get_user_meta($userId,'yuno_gplus_mobile', true);
				$current_user_type = get_user_meta($userId,'current_user_type', true);
	   
			   update_user_meta($userId, 'zoom_vc', "false");
			   update_user_meta($userId, 'google_meet_vc', "false");
			   update_user_meta($userId, 'ins_meet_permission', "false");
			   update_user_meta($userId, 'zoom_refresh_token', "test");
			   update_user_meta($userId, 'profile_privacy',"public");
			   /*end*/
				if ($current_user_type == '' || $current_user_type=='contact'){
					update_user_meta($userId,'current_user_type','contact');
					   /**
						* Create custom post profile for instructor 
						*/
					   $profileUserIdReference = get_user_meta($userId, 'profile_user_id_reference', true);
					   if ($profileUserIdReference !== '') {
						   $profileUserFirstName = get_user_meta($userId, 'yuno_first_name', true);
						   $profileUserLastName = get_user_meta($userId, 'yuno_last_name', true);
						   $postProfileTitle = trim($profileUserFirstName." ".$profileUserLastName);
						   $profilePost = [];
						   $profilePost['ID'] = $profileUserIdReference;
						   $profilePost['post_status']   = 'publish';
						   $profilePost['post_type']     = 'profile';
						   $profilePost['post_title']    = $postProfileTitle;
						   $profilePost['post_author']   = 2;
						   wp_update_post( $profilePost );
					   } else {
						   $profileUserFirstName = get_user_meta($userId, 'yuno_first_name', true);
						   $profileUserLastName = get_user_meta($userId, 'yuno_last_name', true);
						   $postProfileTitle = trim($profileUserFirstName." ".$profileUserLastName);
						   $profilePost = [];
						   $profilePost['post_status']   = 'publish';
						   $profilePost['post_type']     = 'profile';
						   $profilePost['post_title']    = $postProfileTitle;
						   $profilePost['post_author']   = 2;
						   // Create Post
						   $post_id = wp_insert_post( $profilePost );
						   update_post_meta($post_id, 'profile_user_id', $userId);
						   update_user_meta($userId, 'profile_user_id_reference', $post_id);
					   }
						/**
						 * Update referral code of instructor as referrer
						*/
						to_become_referrer($userId);					   
				} else {
	   
				}
           		
			} else {
				$is_completed_step_3 = "no";
			}
		} else {
			$instructor_country = $data->country;
			$instructor_city = $data->city;
			$fluent_in = $data->fluent_in;
			$native_language = $data->native_language;
			$instructor_understand = $data->understand;
			$instructor_state = $data->state;
		}
		$instructor_pin_code = $data->pin_code;
		$instructor_flat_house_number = $data->flat_house_number;
		$instructor_street = $data->street;
		$instructor_landmark = $data->landmark;
		//$instructor_address_type = $data->address_type;
    	$userdata = get_userdata($userID);
    	if (empty($userdata)) {
			return new WP_Error($codes["PUT_UPDATE_FAIL"]["code"], 'User not found for update', array('status' => $codes["PUT_UPDATE_FAIL"]["code"]));
		}
    	if($data->is_about == true || $data->is_about == 1){    		
    		update_user_meta($userID, 'Instructor_About', $data->about);	
    		$profilePostId = get_user_meta($userID, 'profile_user_id_reference', true);
    		if ($profilePostId !== '') {
    			$profilePost = [];
	            $profilePost['ID']   = $profilePostId;
	            $profilePost['post_status']   = 'publish';
	            $profilePost['post_type']     = 'profile';
	            $profilePost['post_content']  = $data->about;
	            $profilePost['post_excerpt']  = substr($data->about, 0, 140);
	            $profilePost['post_author']   = 2;
	            // Create Post
	            wp_update_post( $profilePost );
    		}
    	} else if($data->is_basic == true || $data->is_basic == 1){    		
    		update_user_meta($userID, 'Fluent_In', implode(',',$data->fluent_in));	
    		update_user_meta($userID, 'Native_Language', $data->native_language);	
			update_user_meta($userID, 'Instructor_Understand', implode(',',$data->understand));
			update_user_meta($userID, 'Instructor_Experience', $data->experience);
			$curlUpdate = [
				"data" =>[
				  "details" => [
					"user_id" => $userID,
					"record_id" => $userID,
					"update_event_type" => "instructorsignedup",
					"fluent_in"=> get_user_meta($userID, 'Fluent_In', true),
					"native_language"=> get_user_meta($userID, 'Native_Language', true),
					"instructor_understand"=> get_user_meta($userID, 'Instructor_Understand', true),
					"instructor_experience"=> get_user_meta($userID, 'Instructor_Experience', true)            
				  ],
				  "@timestamp" => date("Y-m-d H:i:s")
				  ]
				];   		
			  update_elastic_event($curlUpdate);  

			  $privacy_policy_terms_of_service = get_user_meta($userID, 'term_and_condition', true) ? get_user_meta($userID, 'term_and_condition', true) : true;
			  if (!empty(get_user_meta($userID, 'Yuno_Last_Login_Time', true))) {
				$yuno_Last_Login_Time = get_user_meta($userID, 'Yuno_Last_Login_Time', true);
			}
			else {
				$yuno_Last_Login_Time = get_user_meta($userID, 'yuno_gplus_rgdate', true);
			}
			  $user_obj     = [
				"name"  => get_user_meta($userID, 'yuno_display_name', true),
				"email" => get_user_meta($userID, 'yuno_gplus_email', true),
				"phone" => get_user_meta($userID, 'yuno_gplus_mobile', true),
				"image" => get_user_meta($userID, 'googleplus_profile_img', true)
			  ];
			//   $basic_details = [
			// 	"locale"                          => "",
			// 	"referred_by"                     => get_user_meta($userID, 'referred_by', true),
			// 	"native_language"                 => get_user_meta($userID, 'Native_Language', true),
				
			// 	// "category_url_for_signup"         => $org_val,
			// 	"yuno_user_whatsapp_check"        => (bool) $yuno_user_whatsapp_check,
			// 	"privacy_policy_terms_of_service" => (bool) $privacy_policy_terms_of_service,
			// 	"what_best_describes_you"         => get_user_meta($userID, 'what_best_describes_you', true),
			// 	"classes_duration"                => get_user_meta($userID, 'classes_duration', true),
			// 	"registration_date"               => get_user_meta($userID, 'yuno_gplus_rgdate', true),
			// 	"when_planning_ielts"             => get_user_meta($userID, 'when_planning_ielts', true),
			// 	// "time_of_study"                   => get_user_meta($userID, 'time_of_study', true),
			// 	"type_of_ielts"                   => get_user_meta($userID, 'type_of_ielts', true),
			// 	"ielts_target_band"               => get_user_meta($userID, 'target_band', true),
			// 	"last_login_time"                 => $yuno_Last_Login_Time,
			// 	"zoho_lead_id"                    => get_user_meta($userID, 'zoho_lead_id', true),
			// 	"zoho_contact_id"                 => get_user_meta($userID, 'zoho_contact_id', true)
			// ];
	
			$details    = [
				"properties"   => [
					"details"    => [
						"user_id"       => $userID,
						"user"          => $user_obj,
						"native_language"=> get_user_meta($userID, 'Native_Language', true),
						"role" 			 => "instructor",
					],
				]
			];
	
			$properties = $details['properties'];
			$curlPost['data'] = [
				"data" => $properties,
			];
			UserElasticSearch::update_signedup("basic_details", $curlPost);
			  $reqquested_data=["document_type"=>"course","fields"=>["mapped_instructor_ids"=>$userID]];
			  update_instructor_lang_es($reqquested_data); 
    	} else {
            
			if($criteria == "instructor"){
				update_user_meta($userID, 'Fluent_In', implode(',',$fluent_in));
				update_user_meta($userID, 'Native_Language', $native_language);
				update_user_meta($userID, 'yuno_user_laptop_availability', $laptop_availability);
				update_user_meta($userID, 'yuno_first_name', $first_name);
				update_user_meta($userID, 'yuno_last_name', $last_name);
				update_user_meta($userID, 'yuno_display_name', $first_name." ".$last_name);
				update_user_meta($userID, 'yuno_user_whatsapp_check', $yuno_user_whatsapp_check);
				update_user_meta($userID, 'yuno_user_broadband_connection_availability', $broadband_connection_availability);
				update_user_meta($userID, 'yuno_user_online_teaching_exp', $online_teaching_exp);
				update_user_meta($userID, 'yuno_user_dob', $dob);
				$instructor_can_teach = count($can_teach)>0 ? implode(',',$can_teach) : "";
				update_user_meta($userID, 'can_teach', $instructor_can_teach);
				$instructor_preference = count($teaching_preference)>0 ? implode(',',$teaching_preference) : "";
				update_user_meta($userID, 'yuno_user_teaching_preference', $instructor_preference);
				update_user_meta($userID, 'yuno_user_address_country', $instructor_country);
				update_user_meta($userID, 'yuno_gplus_mobile', $phone);
				update_user_meta($userID, 'is_completed_step_1', $is_completed_step_1);
				update_user_meta($userID, 'is_completed_step_2', $is_completed_step_2);
				update_user_meta($userID, 'is_completed_step_3', $is_completed_step_3);
				update_user_meta($userID, 'Instructor_Understand', implode(',',$instructor_understand));
				update_user_meta($userID, 'yuno_user_address_city', $instructor_city);
				update_user_meta($userID, 'yuno_user_address_state', $instructor_state);
			} else {
            //update_user_meta($userID, 'Instructor_Address', $instructor_address);
			update_user_meta($userID, 'Fluent_In', implode(',',$fluent_in));
			update_user_meta($userID, 'Native_Language', $native_language);
            update_user_meta($userID, 'Instructor_Understand', implode(',',$instructor_understand));
            update_user_meta($userID, 'Instructor_Experience', $instructor_experience);
			update_user_meta($userID, 'yuno_user_address_country', $instructor_country);
			update_user_meta($userID, 'yuno_user_address_pin_code', $instructor_pin_code);
			update_user_meta($userID, 'yuno_user_address_flat_house_number', $instructor_flat_house_number);
			update_user_meta($userID, 'yuno_user_address_street', $instructor_street);
			update_user_meta($userID, 'yuno_user_address_landmark', $instructor_landmark);
			update_user_meta($userID, 'yuno_user_address_city', $instructor_city);
			update_user_meta($userID, 'yuno_user_address_state', $instructor_state);
			//update_user_meta($userID, 'yuno_user_address_type', $instructor_address_type);
			}
    	}    	
    	//delete_cache();
		if($criteria == "instructor"){
			$city_id=get_user_meta($userID, 'yuno_user_address_city', true );
			$city = $wpdb->get_row( "SELECT name FROM wp_cities where id=$city_id");
			$fields=["user_id"=>$userID];
			$postData=["document_type"=>"instructorsignedup","fields"=>$fields];      
			$get_events=getExistanceOfEvent($postData);  
			if(!$get_events){ 
				$curlPost = [
					"data" =>[
					  "details" => [
						"user_id"=>$userID,
						"record_id" => $userID,
						"event_type" => "instructorsignedup",
						"event_label" => "Instructor signed up",  
						"account_login_status"=>$login_status,
						"first_name" => get_user_meta($userID, 'yuno_first_name', true),
						"last_name"=> get_user_meta($userID, 'yuno_last_name', true),
						"country"=> get_user_meta($userID, 'yuno_user_address_country', true),
						"city_id"=> get_user_meta($userID, 'yuno_user_address_city', true),
						"city_name"=> $city->name,
						"fluent_in"=> get_user_meta($userID, 'Fluent_In', true),
						"native_language"=> get_user_meta($userID, 'Native_Language', true),
						"instructor_understand"=> get_user_meta($userID, 'Instructor_Understand', true),
						"laptop_availability"=> get_user_meta($userID, 'yuno_user_laptop_availability', true),
						"broadband_connection_availability"=> get_user_meta($userID, 'yuno_user_broadband_connection_availability', true),
						"online_teaching_exp"=> get_user_meta($userID, 'yuno_user_online_teaching_exp', true),
						"dob"=> get_user_meta($userID, 'yuno_user_dob', true),
						"can_teach"=> get_user_meta($userID, 'can_teach', true),
						"teaching_preference"=> get_user_meta($userID, 'yuno_user_teaching_preference', true),
						"instructor_experience"=> get_user_meta($userID, 'Instructor_Experience', true),
						"address_pin_code"=> get_user_meta($userID, 'yuno_user_address_pin_code', true),
						"address_flat_house_number"=> get_user_meta($userID, 'yuno_user_address_flat_house_number', true),
						"address_street"=> get_user_meta($userID, 'yuno_user_address_street', true),           
						"address_landmark"=> get_user_meta($userID, 'yuno_user_address_landmark', true),
						"address_state"=> get_user_meta($userID, 'yuno_user_address_state', true),
						"is_featured"=> false,
						"is_completed_step_1"=> get_user_meta($userID, 'is_completed_step_1', true),
						"is_completed_step_2"=> get_user_meta($userID, 'is_completed_step_2', true),
						"is_completed_step_3"=> !empty(get_user_meta($userID, 'is_completed_step_3', true)) ? get_user_meta($userID, 'is_completed_step_3', true) : "no",
						"yuno_user_whatsapp_check"=> get_user_meta($userID, 'yuno_user_whatsapp_check', true),
						"active_enrolments"=> '',
						"completed_enrolments"=> '',
						"live_classes_delivered"=> '',
						"attendance"=> '',
						"active_batches"=> 0,
						"learner_avg_class_rating"=> 0,
						"staff_avg_class_rating"=> 0,
						"reviews_count"=> 0,
						"vc_status" => 'free',
						"avg_rating" => ''
					  ],
					  "@timestamp" => date("Y-m-d H:i:s")
					  ]
					];
				  post_elastic_event($curlPost);  
				  $user_obj     = [
					"name"  => get_user_meta($userID, 'yuno_display_name', true),
					"email" => get_user_meta($userID, 'yuno_gplus_email', true),
					"phone" => get_user_meta($userID, 'yuno_gplus_mobile', true),
					"image" => get_user_meta($userID, 'googleplus_profile_img', true)
				  ];
				//   $basic_details = [
				// 	"locale"                          => "",
				// 	"referred_by"                     => get_user_meta($userID, 'referred_by', true),
				// 	"native_language"                 => get_user_meta($userID, 'Native_Language', true),
					
				// 	// "category_url_for_signup"         => $org_val,
				// 	"yuno_user_whatsapp_check"        => (bool) $yuno_user_whatsapp_check,
				// 	"privacy_policy_terms_of_service" => (bool) $privacy_policy_terms_of_service,
				// 	"what_best_describes_you"         => get_user_meta($userID, 'what_best_describes_you', true),
				// 	"classes_duration"                => get_user_meta($userID, 'classes_duration', true),
				// 	"registration_date"               => get_user_meta($userID, 'yuno_gplus_rgdate', true),
				// 	"when_planning_ielts"             => get_user_meta($userID, 'when_planning_ielts', true),
				// 	// "time_of_study"                   => get_user_meta($userID, 'time_of_study', true),
				// 	"type_of_ielts"                   => get_user_meta($userID, 'type_of_ielts', true),
				// 	"ielts_target_band"               => get_user_meta($userID, 'target_band', true),
				// 	"last_login_time"                 => $yuno_Last_Login_Time,
				// 	"zoho_lead_id"                    => get_user_meta($userID, 'zoho_lead_id', true),
				// 	"zoho_contact_id"                 => get_user_meta($userID, 'zoho_contact_id', true)
				// ];
		
				$details    = [
					"properties"   => [
						"details"    => [
							"user_id"       => $userID,
							"user"          => $user_obj,
							"native_language"=> get_user_meta($userID, 'Native_Language', true),
							"role" 			 => "instructor",
						],
					]
				];
		
				$properties = $details['properties'];
				$curlPost['data'] = [
					"data" => $properties,
				];
				UserElasticSearch::update_signedup("basic_details", $curlPost);
				
			} else {
				$curlUpdate = [
					"data" =>[
					  "details" => [
						"user_id" => $userID,
						"record_id" => $userID,
						"account_login_status"=>$login_status,
						"update_event_type" => "instructorsignedup",
						"first_name" => get_user_meta($userID, 'yuno_first_name', true),
						"last_name"=> get_user_meta($userID, 'yuno_last_name', true),
						"country"=> get_user_meta($userID, 'yuno_user_address_country', true),
						"city_id"=> get_user_meta($userID, 'yuno_user_address_city', true),
						"city_name"=> $city->name,
						"fluent_in"=> get_user_meta($userID, 'Fluent_In', true),
						"native_language"=> get_user_meta($userID, 'Native_Language', true),
						"instructor_understand"=> get_user_meta($userID, 'Instructor_Understand', true),
						"laptop_availability"=> get_user_meta($userID, 'yuno_user_laptop_availability', true),
						"broadband_connection_availability"=> get_user_meta($userID, 'yuno_user_broadband_connection_availability', true),
						"online_teaching_exp"=> get_user_meta($userID, 'yuno_user_online_teaching_exp', true),
						"dob"=> get_user_meta($userID, 'yuno_user_dob', true),
						"can_teach"=> get_user_meta($userID, 'can_teach', true),
						"teaching_preference"=> get_user_meta($userID, 'yuno_user_teaching_preference', true),
						"instructor_experience"=> get_user_meta($userID, 'Instructor_Experience', true),
						"address_pin_code"=> get_user_meta($userID, 'yuno_user_address_pin_code', true),
						"address_flat_house_number"=> get_user_meta($userID, 'yuno_user_address_flat_house_number', true),
						"address_street"=> get_user_meta($userID, 'yuno_user_address_street', true),           
						"address_landmark"=> get_user_meta($userID, 'yuno_user_address_landmark', true),
						"address_state"=> get_user_meta($userID, 'yuno_user_address_state', true),
						"is_completed_step_1"=> get_user_meta($userID, 'is_completed_step_1', true),
						"is_completed_step_2"=> get_user_meta($userID, 'is_completed_step_2', true),
						"is_completed_step_3"=> !empty(get_user_meta($userID, 'is_completed_step_3', true)) ? get_user_meta($userID, 'is_completed_step_3', true) : "no",
						"yuno_user_whatsapp_check"=> get_user_meta($userID, 'yuno_user_whatsapp_check', true)               
					  ],
					  "@timestamp" => date("Y-m-d H:i:s")
					  ]
					];
				  update_elastic_event($curlUpdate);  
				  $user_obj     = [
					"name"  => get_user_meta($userID, 'yuno_display_name', true),
					"email" => get_user_meta($userID, 'yuno_gplus_email', true),
					"phone" => get_user_meta($userID, 'yuno_gplus_mobile', true),
					"image" => get_user_meta($userID, 'googleplus_profile_img', true)
				  ];
				//   $basic_details = [
				// 	"locale"                          => "",
				// 	"referred_by"                     => get_user_meta($userID, 'referred_by', true),
				// 	"native_language"                 => get_user_meta($userID, 'Native_Language', true),
					
				// 	// "category_url_for_signup"         => $org_val,
				// 	"yuno_user_whatsapp_check"        => (bool) $yuno_user_whatsapp_check,
				// 	"privacy_policy_terms_of_service" => (bool) $privacy_policy_terms_of_service,
				// 	"what_best_describes_you"         => get_user_meta($userID, 'what_best_describes_you', true),
				// 	"classes_duration"                => get_user_meta($userID, 'classes_duration', true),
				// 	"registration_date"               => get_user_meta($userID, 'yuno_gplus_rgdate', true),
				// 	"when_planning_ielts"             => get_user_meta($userID, 'when_planning_ielts', true),
				// 	// "time_of_study"                   => get_user_meta($userID, 'time_of_study', true),
				// 	"type_of_ielts"                   => get_user_meta($userID, 'type_of_ielts', true),
				// 	"ielts_target_band"               => get_user_meta($userID, 'target_band', true),
				// 	"last_login_time"                 => $yuno_Last_Login_Time,
				// 	"zoho_lead_id"                    => get_user_meta($userID, 'zoho_lead_id', true),
				// 	"zoho_contact_id"                 => get_user_meta($userID, 'zoho_contact_id', true)
				// ];
		
				$details    = [
					"properties"   => [
						"details"    => [
							"user_id"       => $userID,
							"user"          => $user_obj,
							"native_language"=> get_user_meta($userID, 'Native_Language', true),
							"role" 			 => "instructor",
						],
					]
				];
		
				$properties = $details['properties'];
				$curlPost['data'] = [
					"data" => $properties,
				];
				UserElasticSearch::update_signedup("basic_details", $curlPost);
				  $reqquested_data=["document_type"=>"course","fields"=>["mapped_instructor_ids"=>$userID]];
				  update_instructor_lang_es($reqquested_data); 
			}
			$is_completed_step_3 = get_user_meta($userID, 'is_completed_step_3', true);
				if($is_completed_step_3 == "yes") {
				//email notification send to new instructor
				email_notification('WELCOME_NEW_INSTRUCTOR', $userID);
				$post_id = get_user_meta($userID, 'profile_user_id_reference', true);		
				update_post_meta($post_id, '_yoast_wpseo_meta-robots-noindex', '2');		
				}
		}
		
		$user_update_result = array('code' => $codes["PUT_UPDATE"]["code"], 'message' => 'User detail updated');
		delete_cache('get_user_profile_'.$userID, '', [$userID]);	
		return new WP_REST_Response($user_update_result, $codes["PUT_UPDATE"]["code"]);
		
    }

    public function get_user_detail($request) {
    	global $wpdb;
		$codes=error_code_setting();
    	$user_id = (int)$request['userID'];    
		$userdata = get_userdata($user_id);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));     			
		}
		// $userCacheData = get_cache("get_user_detail_".$user_id);
        // if (!empty($userCacheData)) {
        //     return new WP_REST_Response($userCacheData, 200); 
        // }
		$user_email = $userdata->data->user_email;
		$user_registered = $userdata->data->user_registered;
		$user_detail = array(
		 	'email' => $user_email,
			'registered_on' => date('F Y',strtotime($user_registered)),
			'first_name' => get_user_meta($user_id, 'yuno_first_name', true),
		 	'last_name' => get_user_meta($user_id, 'yuno_last_name', true),
			'profile_img' => get_user_meta($user_id, 'googleplus_profile_img', true),
			'yuno_display_name' => get_user_meta($user_id, 'yuno_display_name', true),
			'mobile' => get_user_meta($user_id, 'yuno_gplus_mobile', true),
			'counselor_name' => get_user_meta($user_id, 'Counselor_Name', true),
			'capabilities' => get_user_meta($user_id, 'wp_capabilities', true),
			'about' => get_user_meta($user_id, 'Instructor_About', true),
			'fluent_in' => get_user_meta($user_id, 'Fluent_In', true),
			'address' => get_user_meta($user_id, 'Instructor_Address', true),
			'understand' => get_user_meta($user_id, 'Instructor_Understand', true),
			'experience' => get_user_meta($user_id, 'Instructor_Experience', true),
			'roles' => $userdata->roles ? $userdata->roles : ''
		);		
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User detail found', 'data' => $user_detail);
		//add_cache("get_user_detail_".$user_id, $result, '', 3600);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]); 
    }

    public function get_user_profile($request) {
    	global $wpdb;
		$codes=error_code_setting();
    	$parms = json_decode($request->get_body());
    	(int)$user_id = $request['userID'] ? $request['userID'] : '';
		$userdata = get_userdata($user_id);
		if (empty($userdata)) {
			return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find user detail", array('status' => $codes["GET_FAIL"]["status"]));
		}
	
		$user_email = $userdata->data->user_email;
		$user_nicename = $userdata->data->user_nicename;
		$user_registered = $userdata->data->user_registered;
		$current_role = '';
		if($userdata->roles){
			if(in_array('um_instructor', $userdata->roles)){
				$current_role = 'Instructor';
			}else if(in_array('um_counselor', $userdata->roles)){
				$current_role = 'Counselor';
			}else{
				$current_role = 'Learner';
			}
		}

		$signupDetail = get_user_meta($user_id, 'is_signup_complete', true);
		if ($signupDetail == true || $signupDetail == "true" || $signupDetail == 1) {
			$isSignup = "completed";
		} else {
			$isSignup = "pending";
		}
 
		$requestedData=["user_id"=>$user_id,"criteria"=>'detail'];
		$address=$this->getting_address($requestedData);
		$addressData=$address->data['data'];
		if(isset($address->errors)){
	    	$address_details = [
	    		"country" => "",
	    		"pin_code" => "",
	    		"flat_house_number" => "",
	    		"street" => "",
	    		"landmark" => "",
	    		"city" => "",
	    		"state" => "",
	    		"address_type" => ""
			];
		} else {
			$address_details =$addressData;
		}
		$fluent_in=get_user_meta($user_id, 'Fluent_In', true);
		$understand=get_user_meta($user_id, 'Instructor_Understand', true);
		$native_language=get_user_meta($user_id, 'Native_Language', true);
		if(strlen($fluent_in)>0) {
			$fluent=explode(',',$fluent_in);
		} else {
			$fluent=[];
		}

		if(strlen($understand)>0) {
			$understanding=explode(',',$understand);
		} else {
			$understanding=[];
		}
		$l_category=get_user_meta($user_id, 'Category_URL_For_Signup', true);
		if(is_array($l_category) && count($l_category)>0){  
			$learnerCategory=implode(",",$l_category);
		  } else {
			if(strpos($l_category, '/') !== false) {
			  $cat=strtolower(ltrim($l_category, '/'));
			} else {
			  $cat=strtolower($l_category);
			}
	
			if ($cat == trim($cat) && strpos($cat, ' ') !== false) {
			  $learner_cat=str_replace(' ', '-', $cat);
			  $learnerCategory=$learner_cat;  
			} else {
			  $learnerCategory=$cat;         
			}
		  }

		$user_detail = array(
		 	//'email' => $user_email,
			'registered_on' => date('F Y',strtotime($user_registered)),
			'first_name' => get_user_meta($user_id, 'yuno_first_name', true),
		 	'last_name' => get_user_meta($user_id, 'yuno_last_name', true),
			'profile_img' => get_user_meta($user_id, 'googleplus_profile_img', true),
			'yuno_display_name' => get_user_meta($user_id, 'yuno_display_name', true),
			//'mobile' => get_user_meta($user_id, 'yuno_gplus_mobile', true),
			'counselor_name' => get_user_meta($user_id, 'Counselor_Name', true),
			'about' => get_user_meta($user_id, 'Instructor_About', true),
			'fluent_in' => $fluent,
			'address' => $address_details,
			'understand' => $understanding,
			'native_language' => $native_language,
			'experience' => get_user_meta($user_id, 'Instructor_Experience', true),
			'profile_name' => $user_nicename,
			//'mobile' => get_user_meta($user_id, 'yuno_gplus_mobile', true),
			'category_url' => $learnerCategory,
			'is_signup_completed' => $isSignup,
			'is_lead_created' => true,
			'roles' => $userdata->roles ? $userdata->roles : '',
			'capabilities' => get_user_meta($user_id, 'wp_capabilities', true),
			'current_role' => $current_role
		);		
		$result = array('code' =>  $codes["GET_SUCCESS"]["code"], 'message' => 'User detail found', 'data' => $user_detail);
		//add_cache("get_user_profile_".$user_id, $result, '', 3600);
		return new WP_REST_Response($result,  $codes["GET_SUCCESS"]["code"]); 
    }

    /**
     * Getting role of logged in user and if role is equal to learner then 
     * finding if his enrollment is active or not if enrollment is active then 
     * checking in which category he/she belongs to.
     */
    public function get_user_role($request){
    	$user_id = (int)$request['userID'];
		$codes=error_code_setting();    
		$userdata = get_userdata($user_id);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));   
		}
    	$roles = $userdata->roles ? $userdata->roles : '';
    	if ($userdata->roles) {
			if (in_array('um_instructor', $userdata->roles)) {
				$current_role = 'Instructor';
			} else if(in_array('um_counselor', $userdata->roles)) {
				$current_role = 'Counselor';
			} else if (in_array('um_yuno-admin', $userdata->roles)) {
				$current_role = 'yuno-admin';
			} else if (in_array('um_content-admin', $userdata->roles)) {
				$current_role = 'content-admin';
			} else {
				$current_role = 'Learner';
			}
		}


		if ($current_role == "Learner") {
			global $wpdb;
			date_default_timezone_set('Asia/Kolkata');
    		$currentDate = date("Y-m-d H:i:s");
    		$enrollmentData = [];
			$enrollmentFalseStatusData = [];
			$data = $wpdb->get_results("SELECT id,product_db_id, enrollment_end_date, enrollment_status FROM wp_enrollment WHERE user_id='".$user_id."'", ARRAY_A);
			foreach ($data as $key => $value) {
				if($currentDate < $value['enrollment_end_date']){
      				$enrollmentData[] = [
      					"course_id" => $value['product_db_id'],
						"id" => $value['id'],
      					//"enrollment_status" => "active"
      				];
      			}
			}
    		
    		// get category belogs to logged in learner
    		if (count($enrollmentData) > 0) {
    			$category = [];
    			foreach ($enrollmentData as $key => $value) {
    				$categories = wp_get_post_terms((int)$value['course_id'], 'course_category');
    				foreach ($categories as $k => $v) {
    					if ($v->parent == 0) {
    						$category[] = strtolower($v->slug);	
    					}
    				}
    			}
    			$result = array('code' => $codes["GET_SUCCESS"]["code"], 
    						'message' => 'User roles found', 
    						'data' => $current_role,
    						'is_enrollment_active' => true,
    						'category' => $category
    					);
				return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
    		} else {
				$enrollmentStatus = "INACTIVE";
				$update_status = [
				'enrollment_status' => $enrollmentStatus
				];				
				foreach ($data as $key => $value) {
					if($currentDate > $value['enrollment_end_date']){
						  $enrollmentFalseStatusData[] = [
							"id" => $value['id'],
							  //"enrollment_status" => "active"
						  ];
					  }
				}				
				if (count($enrollmentFalseStatusData) > 0) {
					foreach ($enrollmentFalseStatusData as $key => $value) {
					$wpdb->update('wp_enrollment', $update_status, ['id' => $value['id']]);						
					}	
				}
    			$result = array('code' => $codes["GET_SUCCESS"]["code"], 
    						'message' => 'User roles found', 
    						'data' => $current_role,
    						'is_enrollment_active' => false,
    						'category' => []
    					);
				return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
    		}
		}

    	if(count($roles)){
	    	$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User roles found', 'data' => $current_role);
			return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]); 	    		
    	}else{
    		return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find user role", array('status' => $codes["GET_FAIL"]["status"]));	
    	}
    }
    /**
     * For role instructor: Get my learners (working for only logged in instructor)
     * For role yuno-admin: Get all learners 
     */
    public function getMylearners($request){
      	global $wpdb;
		$codes=error_code_setting();
      	$limit = (int)$request['limit'];
      	$offset = (int)$request['offset'];
		$criteria = $request['criteria'];		  
      	$user = [];
      	$role = $request['role'];
      	if ($role == "yuno-admin") {
      		// query for count learners
      		/*$args2 = array(
			    'role'    => 'SEO Manager',
			    'orderby' => 'ID',
			    'order'   => 'DESC'
			);
			$users_count = new WP_User_Query( $args2 );
			$totalRecords = count($users_count->get_results());*/
			$resultTotalRecords = $wpdb->get_row("SELECT count(u.ID) as total_records FROM wp_users u, wp_usermeta m WHERE u.ID = m.user_id AND m.meta_key LIKE 'wp_capabilities' AND m.meta_value LIKE '%SEO Manager%'", ARRAY_A); 
			$totalRecords = (int)$resultTotalRecords['total_records'];
			// query for get learners
					// $args = array(
					//     'role'    => 'SEO Manager',
					//     'orderby' => 'ID',
					//     'order'   => 'DESC',
					//     'number'  => $limit,
					// 	'offset'  => $offset  
					// );
					// $users = get_users($args);  //// commented
					// foreach ($users as $key => $value) {
					// 	$user_id = (int)$value->ID;
					// 	$userdata = get_userdata($user_id);
					// 	$email = $userdata->data->user_email;
					// 	$image = get_user_meta($user_id, 'googleplus_profile_img', true); 
					// 	$name = get_user_meta($user_id, 'yuno_display_name', true);
					// 	$phone = get_user_meta($user_id, 'yuno_gplus_mobile', true);
					// 	$email_name = $name.' ('.$email.')';
					// 	$user[] = [
					// 		"id" => $user_id,
					// 		"image" => $image,
					// 		"name" => $name,
					// 		"email" => $email,
					// 		"phone" => $phone,
					// 		"email_name" => $email_name
					// 	];
					// }


					/// Nov-16 -2023 --  s-- updated

					  $args = array(
						'role' => 'SEO Manager',
						'orderby' => 'ID',
						'order' => 'DESC',
						'number' => $limit,
						'offset' => $offset,
						'fields' => array('ID','user_email') /// Nov-16 - 2023--added
					  );
					  $users = get_users($args);   /// Nov-16 -2023 ---- updated
					  $meta_keys = ['googleplus_profile_img','yuno_display_name','yuno_gplus_mobile'];
					  $user_ids = array_column($users, 'ID');
					  $user_placeholders = implode(', ', array_fill(0, count($user_ids), '%d'));
					  $meta_placeholders = implode(', ', array_fill(0, count($meta_keys), '%s'));
					  $query = $wpdb->prepare("SELECT * FROM {$wpdb->usermeta} WHERE meta_key IN ($meta_placeholders) AND user_id IN ($user_placeholders)",array_merge($meta_keys, $user_ids));
					  $user_meta_results = $wpdb->get_results($query );
					  $user_meta_data = [];
					  foreach ($user_meta_results as $obj) {
						$user_meta_data[$obj->user_id][$obj->meta_key] = $obj->meta_value;
					  }
					  
					  foreach ($users as $key => $value) {
						$user_id = (int) $value->ID;
						$email = $value->user_email;
						$image = $user_meta_data[$user_id][ 'googleplus_profile_img'];
						$name = $user_meta_data[$user_id][ 'yuno_display_name'];
						$phone = $user_meta_data[$user_id][ 'yuno_gplus_mobile'];
						$email_name = $name . ' (' . $email . ')';
						$user[] = [
						  "id" => $user_id,
						  "image" => $image,
						  "name" => $name,
						  "email" => $email,
						  "phone" => $phone,
						  "email_name" => $email_name
						];
					  }
					  
					/// Nov-16 -2023 --  e-- updated

			$userArray = [];
			$userArray = [
						  "rows" => $user,
						  "columns" => [
										[
											"field"=> "name",
											"label" => "Name",
											"sortable" => true
										],
										[
											"field" => "email",
											"label" => "Email",
											"sortable" => true
										],
										[
											"field" => "phone",
											"label" => "Phone",
											"sortable" => true
										]
									]
						];
			if (empty($user)) {
				return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["message"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));	
			}
			  $response = array('code' => $codes["GET_SUCCESS"]["code"], 
				  'message' => 'Learners data found', 
				  'count' => $totalRecords, 
				  'data' => $userArray);
			  return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]); 			
      	} else {
			if($criteria=="android"){
      		//$filterArray = json_decode(str_replace("\\", "", $_GET['filter']));
      		$where = '';
      		/*foreach ($filterArray as $key => $value) {
      			$filterActive = $value->active;
				$filterValue = $value->value;
				if ($filterValue == 'myContacts') {
					$where .= " and is_contact = true ";
				} else if ($filterValue == 'myReferrals') {
					$where .= " and is_referral = true ";
				}
      		}*/
      		$instructorID = (int)$request['instructorID'];
      		$dataCount = $wpdb->get_results("SELECT * from wp_my_learners WHERE instructor_id ='".$instructorID."' $where and learner_id IN (SELECT ID from wp_users) group by learner_id ORDER BY id DESC", ARRAY_A);
      		$totalRecords = count($dataCount);
			if(!empty($offset) && !empty($limit))  {
				$sub_query="LIMIT $offset, $limit";
			} else {
				$sub_query="";
			}
			$data = $wpdb->get_results("SELECT * from wp_my_learners WHERE instructor_id ='".$instructorID."' $where and learner_id IN (SELECT ID from wp_users) group by learner_id ORDER BY id DESC $sub_query", ARRAY_A);	
			foreach ($data as $key => $value) {
				$user_id = (int)$value['learner_id'];
				$userdata = get_userdata($user_id);
				$email = $userdata->data->user_email;
				$image = get_user_meta($user_id, 'googleplus_profile_img', true); 
				$name = get_user_meta($user_id, 'yuno_display_name', true);
				//$phone = get_user_meta($user_id, 'yuno_gplus_mobile', true);
				//$email_name = $name.' ('.$email.')';
				/*if (!empty($value['is_contact'])) {
					$isContact = true;	
				} else {
					$isContact = false;
					$email = '';
					$phone = '';
				}
				if (!empty($value['is_referral'])) {
					$isReferral = true;
				} else {
					$isReferral = false;
				}*/
				$user[] = [
					"id" => $user_id,
					"photo" => $image,
					"name" => $name,
					"email" => $email,
					//"phone" => $phone,
					//"email_name" => $email_name,
					//"is_contact" => $isContact,
					//"is_referral" => $isReferral
				];
			}

			if (empty($user)) {
				return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["message"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));	
			}
			  $response = array('code' => $codes["GET_SUCCESS"]["code"], 
				  'message' => 'Learners data found', 
				  'count' => $totalRecords, 
				  'data' => $user);
			  return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]); 
			} else {
      		//$filterArray = json_decode(str_replace("\\", "", $_GET['filter']));
      		$where = '';
      		/*foreach ($filterArray as $key => $value) {
      			$filterActive = $value->active;
				$filterValue = $value->value;
				if ($filterValue == 'myContacts') {
					$where .= " and is_contact = true ";
				} else if ($filterValue == 'myReferrals') {
					$where .= " and is_referral = true ";
				}
      		}*/
      		$instructorID = (int)$request['instructorID'];
      		$dataCount = $wpdb->get_results("SELECT * from wp_my_learners WHERE instructor_id ='".$instructorID."' $where and learner_id IN (SELECT ID from wp_users) group by learner_id ORDER BY id DESC", ARRAY_A);
      		$totalRecords = count($dataCount);
			if(!empty($offset) && !empty($limit))  {
				$sub_query="LIMIT $offset, $limit";
			} else {
				$sub_query="";
			}
			$data = $wpdb->get_results("SELECT * from wp_my_learners WHERE instructor_id ='".$instructorID."' $where and learner_id IN (SELECT ID from wp_users) group by learner_id ORDER BY id DESC $sub_query", ARRAY_A);	
			foreach ($data as $key => $value) {
				$user_id = (int)$value['learner_id'];
				$userdata = get_userdata($user_id);
				$email = $userdata->data->user_email;
				$image = get_user_meta($user_id, 'googleplus_profile_img', true); 
				$name = get_user_meta($user_id, 'yuno_display_name', true);
				$phone = get_user_meta($user_id, 'yuno_gplus_mobile', true);
				$email_name = $name.' ('.$email.')';
				/*if (!empty($value['is_contact'])) {
					$isContact = true;	
				} else {
					$isContact = false;
					$email = '';
					$phone = '';
				}
				if (!empty($value['is_referral'])) {
					$isReferral = true;
				} else {
					$isReferral = false;
				}*/
				$user[] = [
					"id" => $user_id,
					"image" => $image,
					"name" => $name,
					"email" => $email,
					"phone" => $phone,
					"email_name" => $email_name,
					//"is_contact" => $isContact,
					//"is_referral" => $isReferral
				];
			}
			$userArray = [];
			$userArray = [
						  "rows" => $user,
						  "columns" => [
										[
											"field"=> "name",
											"label" => "Name",
											"sortable" => true
										],
										[
											"field" => "email",
											"label" => "Email",
											"sortable" => true
										],
										[
											"field" => "phone",
											"label" => "Phone",
											"sortable" => true
										]
									]
						];
			if (empty($user)) {
				return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["message"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));	
			}
			  $response = array('code' => $codes["GET_SUCCESS"]["code"], 
				  'message' => 'Learners data found', 
				  'count' => $totalRecords, 
				  'data' => $userArray);
			  return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]); 	
			}		
      	}
    }

    public function postMylearners($request){
      	global $wpdb;
		$codes=error_code_setting();  
      	$detail = [];
      	$instructorID = (int)$request['instructor_id'];
      	$learnerID = (int)$request['learner_id'];
      	$isExist = $wpdb->get_row("SELECT * from wp_my_learners WHERE instructor_id ='".$instructorID."' AND learner_id ='".$learnerID."'", ARRAY_A);
      	$data = [
			"instructor_id" => $instructorID,
			"learner_id" => $learnerID
		];
		if($isExist){
			$response = array('code' => $codes["POST_INSERT_FAIL"]["code"], 'message' => 'already exist');
      		return new WP_REST_Response($response, 404); 	
		}
		$table = "wp_my_learners";
		$inserted = $wpdb->insert($table, $data);
      	$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => 'Data saved');
      	return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]); 
    }

    public function createSSOUserZoom($request){
    	global $wpdb;
		$codes=error_code_setting();
    	$user_id = $request['user_id'];
    	//error_log('instructor_id== '.$user_id);
    	$response = ssoCreateUser($user_id);  
      	return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]); 
    }							    

    public function get_user_detail_permissions_check() {
    	return true;
    }

    public function update_user_detail_permissions_check() {
    	return true;
    }

    public function get_user_profile_permissions_check() {
    	return true;
    }

    public function get_user_role_permissions_check() {
    	return true;
    }

    public function user_signup_permissions_check() {
    	return true;
    }

    public function get_signup_completed_permissions_check() {
    	return true;
    }

    public function get_signup_template_permissions_check() {
    	return true;
    }

    public function login_with_google_permissions_check() {
    	return true;
    }

    public function getMylearners_permissions_check() {
    	return true;
    }

    public function postMylearners_permissions_check() {
    	return true;
    }
    public function createSSOUserZoom_permissions_check() {
    	return true;
	}
    /**
     * Getting all ip to location
     */	
    public function user_iptolocation($request){
    	global $wpdb;
		$codes=error_code_setting();
		$ip = (string)$request['ip'];
		// set IP address and API access key 
		$ipAddress = long2ip($ip);
		$access_key = '6437428d40475def8367995a67432cca';

		// Initialize CURL:
		$ch = curl_init('http://api.ipstack.com/'.$ipAddress.'?access_key='.$access_key.'');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

		// Store the data:
		$json = curl_exec($ch);
		curl_close($ch);

		// Decode JSON response:
		$api_result = json_decode($json, true);
		if(isset($api_result["time_zone"]['id'])){
			$id=$api_result["time_zone"]['id'];	
		} else {
			$id="";
		}

		if(isset($api_result["time_zone"]['current_time'])){
			$current_time=$api_result["time_zone"]['current_time'];	
		} else {
			$current_time="";
		}

		if(isset($api_result["time_zone"]['code'])){
			$code=$api_result["time_zone"]['code'];	
		} else {
			$code="";
		}

		$time_zone=[
			"id"=>$id,
			"current_time"=>$current_time,
			"code"=>$code,
		];

		if(isset($api_result["currency"]['code'])){
			$currencyCode=$api_result["currency"]['code'];	
		} else {
			$currencyCode="";
		}

		if(isset($api_result["currency"]['name'])){
			$name=$api_result["currency"]['name'];	
		} else {
			$name="";
		}

		$currency=[
			"code"=>$currencyCode,
			"name"=>$name,
		];
		$data=["ip"=>$api_result['ip'],
		"country_code"=>$api_result['country_code'],
		"country_name"=>$api_result['country_name'],
		"region_name"=>$api_result['region_name'],
		"city"=>$api_result['city'],
		"zip"=>$api_result['zip'],
		"latitude"=>$api_result['latitude'],
		"longitude"=>$api_result['longitude'],
		"time_zone"=>$time_zone,
		"currency"=>$currency,
		"calling_code"=>$api_result["location"]['calling_code']
		];
		$message = "information has been found successfully";
		$code = $codes["GET_SUCCESS"]["code"];	
		$response = array('code' => $code, 
		'message' => $message, 
		'data' => $data
		); 	
      	return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]); 
    }							    
    /**
     * Getting all ip to location
     */	
    public function user_iptolocation_permissions_check() {
    	return true;

	}
   /**
     * Getting all languages
     */	
    public function user_languages() {
		$codes=error_code_setting();
		$message = "languages has been found successfully";

		//$data=json_decode(file_get_contents(get_template_directory_uri().'/inc/json/lang.json'));
		$data = json_decode(file_get_contents(get_stylesheet_directory_uri() . '/inc/json/lang.json'));

		//echo "<pre>";
		
		$name=[];
		foreach($data as $record){
			$name[]=["name"=>$record->name];
		}//print_r($name);die;
		$code = $codes["GET_SUCCESS"]["code"];	
		$response = array('code' => $code, 
		'message' => $message, 
		'data' =>$name
		); 	
      	return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]); 
    }	
   /**
     * Getting all languages
     */	
    public function user_languages_permissions_check() {
    	return true;

	}
   /**
     * Getting all countries
     */	
    public function countries() {
		global $wpdb;
		$codes=error_code_setting();
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."countries";
	    $resultCollections=[];
		$countries = $wpdb->get_results("SELECT * FROM $table_name_1" );
		foreach($countries as $country){
			$resultCollections[]=[
				'id'=>$country->id,
				'name'=>$country->name,
				'country_code'=>$country->iso2
				];
		}
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'Country(s) are found', 'data' => $resultCollections);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);		

	}
   /**
     * Getting all countries
     */	
    public function countries_permissions_check() {
    	return true;
    }			
   /**
     * Getting country name by id
     */	
    public function get_country($request) {
		$country_id = (int)$request['country_id'];
		$codes=error_code_setting();
		global $wpdb;
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."countries";
	    $resultCollections=[];
		$countries = $wpdb->get_results("SELECT * FROM $table_name_1 where id=$country_id" );
		if(count($countries)){
			foreach($countries as $country){
				$resultCollections=[
					'id'=>$country->id,
					'name'=>$country->name,
					'country_code'=>$country->iso2,
					];
			}
			$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'Country(s) are found', 'data' => $resultCollections);
			return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
		}		
		
		return new WP_Error(
                $codes["GET_FAIL"]["code"], 
                "Country detail not found", 
                array('status' => $codes["GET_FAIL"]["status"])
            );
	}
   /**
     * Getting country name by id
     */	
    public function get_country_permissions_check() {
    	return true;
	}	
   /**
     * Getting all states
     */	
    public function states() {
		global $wpdb;
		$codes=error_code_setting();
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."states";
	    $resultCollections=[];
		$states = $wpdb->get_results("SELECT * FROM $table_name_1" );
		foreach($states as $state){
			$resultCollections[]=['id'=>$state->id,'name'=>$state->name,'country_code'=>$state->country_code];
		}
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'state(s) are found', 'data' => $resultCollections);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);		

	}
   /**
     * Getting all states
     */	
    public function states_permissions_check() {
    	return true;
    }			
   /**
     * Getting state name by id
     */	
    public function get_state($request) {
		$state_id = (int)$request['state_id'];
		$codes=error_code_setting();
		global $wpdb;
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."states";
	    $resultCollections=[];
		$states = $wpdb->get_results("SELECT * FROM $table_name_1 where id=$state_id" );
		foreach($states as $state){
			$resultCollections=['id'=>$state->id,'name'=>$state->name,'country_code'=>$state->country_code];
		}
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'State(s) are found', 'data' => $resultCollections);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);	
	}
   /**
     * Getting state name by id
     */	
    public function get_state_permissions_check() {
    	return true;
	}	
   /**
     * Getting all cities
     */	
    public function cities() {
		global $wpdb;
		$codes=error_code_setting();
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."cities";
	    $resultCollections=[];
		$cities = $wpdb->get_results("SELECT * FROM $table_name_1" );
		foreach($cities as $city){
			$resultCollections[]=['id'=>$city->id,'name'=>$city->name,'country_code'=>$city->country_code];
		}
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'city(s) are found', 'data' => $resultCollections);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);		

	}
   /**
     * Getting all cities
     */	
    public function cities_permissions_check() {
    	return true;
    }			
	
   /**
     * Getting states by country id
     */	
    public function get_statesbycountry($request) {
		$country_id = (int)$request['country_id'];
		$codes=error_code_setting();
		global $wpdb;
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."states";
	    $resultCollections=[];
		$states = $wpdb->get_results("SELECT * FROM $table_name_1 where country_id=$country_id" );
		foreach($states as $state){
			$resultCollections[]=['id'=>$state->id,'name'=>$state->name,'country_code'=>$state->country_code];
		}
		$name = array();
		foreach ($resultCollections as $key => $row)
		{
		$name[$key] = $row['name'];
		}
		array_multisort($name, SORT_ASC, $resultCollections);
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'state(s) are found', 'data' => $resultCollections);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);	
	}
   /**
     * Getting states by country id
     */	
    public function get_statesbycountry_permissions_check() {
    	return true;
	}
   /**
     * Getting cities by state id
     */	
    public function get_citiesbystate($request) {
		$state_id = (int)$request['state_id'];
		$codes=error_code_setting();
		global $wpdb;
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."cities";
	    $resultCollections=[];
		$cities = $wpdb->get_results("SELECT * FROM $table_name_1 where state_id=$state_id" );
		foreach($cities as $city){
			$resultCollections[]=['id'=>$city->id,'name'=>$city->name,'country_code'=>$city->country_code];
		}
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'city(s) are found', 'data' => $resultCollections);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);	
	}
   /**
     * Getting cities by state id
     */	
    public function get_citiesbystate_permissions_check() {
    	return true;
	}	
	   /**
     * Getting auto file values by country code
     */	
    public function get_autofill($request) {
		$country_code = (string)$request['country_code'];
		$codes=error_code_setting();
		global $wpdb;
		$resultCollections=[];
		$table_name_1 = $wpdb->prefix."countries";
		$country = $wpdb->get_row("SELECT * FROM $table_name_1 where iso2='".$country_code."'");
		$timezones=json_decode($country->timezones);
		foreach($timezones as $timezone){
			$timezone=["zoneName"=>$timezone->zoneName,"gmtOffsetName"=>$timezone->gmtOffsetName];
		}
		
		$resultCollections=['id'=>$country->id,'name'=>$country->name,'iso3'=>$country->iso3,'iso2'=>$country->iso2,'country_calling_code'=>$country->phonecode,'country_currency'=>$country->currency,'currency_symbol'=>$country->currency_symbol,'region'=>$country->region,'timezones'=>$timezone];

		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'data(s) has been found', 'data' => $resultCollections);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
	}	
	   /**
     * Getting  auto file values by country code
     */	
    public function get_autofill_permissions_check() {
    	return true;
	}	
    /**
     * Get list of learner
     */
    public function get_learner_list_old($request) {
    	// $cacheData = get_cache("get_learner_list");
        // if (!empty($cacheData)) {
        //     return new WP_REST_Response($cacheData, 200); 
        // }
		global $wpdb;
		$codes=error_code_setting();
		$type = (string)$request['role'];
		if ($type == "org-admin") {
			$role__in = array('um_org-admin');
		} else {				
			$role__in = array('SEO Manager');

		}
		$query_string = $request->get_param('search');	
		if(strlen($query_string)<3){
			return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["message"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));				
		}

			$typeCollectionArray = array("learner","org-admin");
			if (!in_array($type, $typeCollectionArray)) {
				return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the user role', array('status' => $codes["GET_FAIL"]["status"]));
			}
			$learner_array = array();
			if(isset($query_string) && $query_string=="all"){
				// 	$args = array(
				// 		'role__in'    => $role__in,
				// 		'orderby' => 'display_name',
				// 		'order'   => 'DESC'
				// 	);
				
				// $users = get_users( $args );	   /// Nov-16 -2023 ------ commented
				// foreach ($users as $key => $value) {
				// 	$learner_array[] = [
				// 		"id" => $value->ID,
				// 		"name" => get_user_meta($value->ID, 'yuno_display_name', true),
				// 		"email" => $value->user_email,
				// 		//"phone" => get_user_meta($value->ID, 'yuno_gplus_mobile', true),
				// 		//"role" => $value->roles,
				// 		"name_email" => get_user_meta($value->ID, 'yuno_display_name', true)." (".$value->user_email.")"
				// 	];
				// }

				/// Nov-16 -2023 ---s--- updated
						$args = array(
							'role__in'    => $role__in,
							'orderby' => 'display_name',
							'order'   => 'DESC',
							'fields' => array('ID','user_email')
						);
						$users = get_users( $args );	 /// Nov-16 -2023 ------ updated

						$meta_keys = ['yuno_display_name'];
						$user_ids = array_column($users, 'ID');
						$user_placeholders = implode(', ', array_fill(0, count($user_ids), '%d'));
						$meta_placeholders = implode(', ', array_fill(0, count($meta_keys), '%s'));
						$query = $wpdb->prepare("SELECT * FROM {$wpdb->usermeta} WHERE meta_key IN ($meta_placeholders) AND user_id IN ($user_placeholders)",array_merge($meta_keys, $user_ids));
						$user_meta_results = $wpdb->get_results($query );
						$user_meta_data = array_column($user_meta_results ,'meta_value' , 'user_id' );

						foreach ($users as $key => $value) {
							$learner_array[] = [
							"id" => $value->ID,
							"name" => $user_meta_data[ $value->ID ] ,
							"email" => $value->user_email,
							"name_email" => $user_meta_data[ $value->ID ] ." (".$value->user_email.")"
							];
						}
					
				/// Nov-16 -2023 ---e--- updated
			
			} else {
				if ($query_string == trim($query_string) && strpos($query_string, ' ') !== false) {

				

        //   echo "function name :- get_instructor_list_for_category_admin  <br/><pre>";
        //   $start_time = microtime(true);

		

										// $ary=explode(' ',$query_string);
										// $sql_1="SELECT user.ID FROM wp_users as user 
										// LEFT JOIN wp_usermeta as meta on meta.user_id = user.id 
										// WHERE meta.meta_key = 'yuno_first_name' AND meta.meta_value like '%".$ary[0]."%'";
										// $users1 =$wpdb->get_results($wpdb->prepare($sql_1));
						
										// $sql_2="SELECT user.ID FROM wp_users as user 
										// LEFT JOIN wp_usermeta as meta on meta.user_id = user.id 
										// WHERE meta.meta_key = 'yuno_first_name' AND meta.meta_value like '%".$ary[1]."%'";	
										// $users2 = $wpdb->get_results($wpdb->prepare($sql_2));
						
										// $sql_3="SELECT user.ID FROM wp_users as user 
										// LEFT JOIN wp_usermeta as meta on meta.user_id = user.id 
										// WHERE meta.meta_key = 'yuno_last_name' AND meta.meta_value like '%".$ary[0]."%'";
										// $users3 = $wpdb->get_results($wpdb->prepare($sql_3));
						
										// $sql_4="SELECT user.ID FROM wp_users as user 
										// LEFT JOIN wp_usermeta as meta on meta.user_id = user.id 
										// WHERE meta.meta_key = 'yuno_last_name' AND meta.meta_value like '%".$ary[1]."%'";	
										// $users4 = $wpdb->get_results($wpdb->prepare($sql_4));
										// //if(count($users1)>0 || count($users2)>0 || count($users3)>0 || count($users4)>0){
										// if(count((array)$users1)>0 || count((array)$users2)>0 || count((array)$users3)>0 || count((array)$users4)>0){
										// 	$users=array_merge($users1,$users2,$users3,$users4);
										// }

										// $learnerUniqueArray=[];	
										// $learnerUniqueArrayp=[];	
										// if (is_array($users) || is_object($users)){


										// 	$meta_keys = ['yuno_display_name'];
										// 	$user_meta_data = [];
										// 	$user_ids = array_column($users, 'ID');
										// 	if (isset($user_ids)) {
										// 		$user_placeholders = implode(', ', array_fill(0, count($user_ids), '%d'));
										// 		$meta_placeholders = implode(', ', array_fill(0, count($meta_keys), '%s'));
										// 		$query = $wpdb->prepare("SELECT meta_value, meta_key, user_id FROM {$wpdb->usermeta} WHERE meta_key IN ($meta_placeholders) AND user_id IN ($user_placeholders)", array_merge($meta_keys, $user_ids));
										// 		$user_meta_results = $wpdb->get_results($query);
										// 		$user_meta_data = array_column($user_meta_results, "meta_value", "user_id");
										// 	}
										// 	error_log("17.11.1.0.1  get_learner_list, users -- ".date("Y-m-d H:i:s")." === ".json_encode($users)."\n\n", 3, ABSPATH."error-logs/payment_test.log"); 
											

										// 	foreach ($users as $key => $value) {
										// 		$user_meta=get_userdata($value->ID);
										// 		$user_roles=$user_meta->roles;

												
										// 		if (in_array($role__in[0], $user_roles) ) {
										// 			$yuno_display_name = $user_meta_data[$value->ID] ??  get_user_meta($value->ID, 'yuno_display_name', true);
										// 			// $learnerUniqueArrayp [] = $value->ID;
										// 			$learnerUniqueArray [] =  [
										// 				"id" => $value->ID,
										// 				"name" => $yuno_display_name,
										// 				"email" => $user_meta->data->user_email,
										// 				"name_email" => $yuno_display_name ." (".$user_meta->data->user_email.")",
										// 				"user_role" => $user_roles
										// 			];
										// 		}
										// 	}
										// }
										// $learner_array=array_unique($learnerUniqueArray);





									$ary = explode(' ', $query_string);
									$sql = "SELECT DISTINCT user.ID, user.user_email, 
															meta_role.meta_value AS user_role,
															meta_display.meta_value AS yuno_display_name
											FROM wp_users AS user 
											LEFT JOIN wp_usermeta AS meta ON user.ID = meta.user_id 
											LEFT JOIN wp_usermeta AS meta_role ON user.ID = meta_role.user_id AND meta_role.meta_key = %s
											LEFT JOIN wp_usermeta AS meta_display ON user.ID = meta_display.user_id AND meta_display.meta_key = 'yuno_display_name'
											WHERE ((meta.meta_key = 'yuno_first_name' AND meta.meta_value LIKE %s) 
													OR (meta.meta_key = 'yuno_first_name' AND meta.meta_value LIKE %s) 
													OR (meta.meta_key = 'yuno_last_name' AND meta.meta_value LIKE %s) 
													OR (meta.meta_key = 'yuno_last_name' AND meta.meta_value LIKE %s))
											AND (meta_role.meta_value LIKE %s OR meta_role.meta_value LIKE %s)
											ORDER BY CASE 
														WHEN meta.meta_key = 'yuno_first_name' AND meta.meta_value LIKE %s THEN 0 
														ELSE 1 
														END limit 50";

									$search1 = '%' . $wpdb->esc_like($ary[0]) . '%';
									$search2 = '%' . $wpdb->esc_like($ary[1]) . '%';
									$seoManagerRole = '%"SEO Manager"%';
									$umOrgAdminRole = '%"um_org-admin"%';

									$users = $wpdb->get_results($wpdb->prepare($sql, $wpdb->prefix . 'capabilities', $search1, $search2, $search1, $search2, $seoManagerRole, $umOrgAdminRole, $search1));				
																		
									$learnerUniqueArray=[];	
									$learnerUniqueArrayp=[];	
									if (is_array($users) || is_object($users)){										
										foreach ($users as $key => $value) {												
											$user_roles = array_keys(maybe_unserialize($value->user_role));
											
											if (in_array($role__in[0], $user_roles) ) {
												$yuno_display_name = $value->yuno_display_name;													
												$learnerUniqueArray [] =  [
													"id" => $value->ID,
													"name" => $yuno_display_name,
													"email" => $value->user_email,
													"name_email" => $yuno_display_name ." (".$value->user_email.")",
												];
											}
										}
									}
									// $learner_array=array_unique($learnerUniqueArray); /// commented Nov-20 
									$learner_array = $learnerUniqueArray;


				} else {





			// 	$args = array(
			// 		'role__in'    => $role__in,
			// 		'orderby' => 'display_name',
			// 		'order'   => 'DESC',
			// 		'meta_query' => array(
			// 			array(
			// 				'key' => 'yuno_gplus_mobile',
			// 				'value' => $query_string,
			// 				'compare' => 'LIKE'
			// 			)
			// 		)
			// 	);
			// 	$users = get_users($args); /// Nov-16 - 2023--commented
			// 	if(count($users)==0) {
			// 	$users_found = new WP_User_Query( array(
			// 		'role__in'    => $role__in,
			// 		'orderby' => 'display_name',
			// 		'order'   => 'DESC',				
			// 		'search'         => '*'.esc_attr( $query_string ).'*',
			// 		'search_columns' => array(
			// 			'user_login',
			// 			'user_nicename',
			// 			'user_email',
			// 			'user_url',
			// 		),
			// 	) );	
			// 	$users = $users_found->get_results();
			// }	
			// foreach ($users as $key => $value) {
			// 	$learner_array[] = [
			// 		"id" => $value->ID,
			// 		"name" => get_user_meta($value->ID, 'yuno_display_name', true),
			// 		"email" => $value->user_email,
			// 		//"phone" => get_user_meta($value->ID, 'yuno_gplus_mobile', true),
			// 		//"role" => $value->roles,
			// 		"name_email" => get_user_meta($value->ID, 'yuno_display_name', true)." (".$value->user_email.")"
			// 	];
			// }
                           
			/// Nov-16 - 2023--  s-- updated
			$args = array(
				'role__in'    => $role__in,
				'orderby' => 'display_name',
				'order'   => 'DESC',
				'meta_query' => array(
				  array(
					'key' => 'yuno_gplus_mobile',
					'value' => $query_string,
					'compare' => 'LIKE'
				  )
				  ),
				  'fields' => array('ID','user_email') /// Nov-16 - 2023--added
			  );
			  $users = get_users($args);  /// Nov-16 - 2023-- updated
			  if(count($users)==0) {
			  $users_found = new WP_User_Query( array(
				'role__in'    => $role__in,
				'orderby' => 'display_name',
				'order'   => 'DESC',				
				'search'         => '*'.esc_attr( $query_string ).'*',
				'search_columns' => array(
				  'user_login',
				  'user_nicename',
				  'user_email',
				  'user_url',
				),
				'fields' => array('ID','user_email') /// Nov-16 - 2023--added
			  ) );	
			  $users = $users_found->get_results();
			  }			  			  
			  
			  $meta_keys = ['yuno_display_name'];
			  $user_ids = array_column($users, 'ID');
			  $user_placeholders = implode(', ', array_fill(0, count($user_ids), '%d'));
			  $meta_placeholders = implode(', ', array_fill(0, count($meta_keys), '%s'));
			  $query = $wpdb->prepare("SELECT meta_value, user_id FROM {$wpdb->usermeta} WHERE meta_key IN ($meta_placeholders) AND user_id IN ($user_placeholders)",array_merge($meta_keys, $user_ids));
			  $user_meta_results = $wpdb->get_results($query );
			  $user_meta_data = array_column($user_meta_results,"meta_value" , "user_id");
			  
			  foreach ($users as $key => $value) {
			  $learner_array[] = [
				"id" => $value->ID,
				"name" => $user_meta_data[$value->ID],
				"email" => $value->user_email,
				"name_email" => $user_meta_data[$value->ID]." (".$value->user_email.")"
			  ];
			  }
			/// Nov-16 - 2023--  e-- updated










		}
		}
			if (!empty($learner_array)) {
				$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User data found', 'data' => $learner_array);
				//add_cache("get_learner_list", $response, '', 3600);
				return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);	
			} else {
				return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["code"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));	
			}
		//}
    }
    /**
     * Get list of learner
     */
    public function get_learner_list($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $type = (string) $request['role'];
        if ($type == "org-admin") {
            $role__in = array('um_org-admin');
        } else {
            $role__in = array('SEO Manager');

        }
        $query_string = $request->get_param('search');
        if (strlen($query_string) < 3) {
            return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["message"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));
        }

        $typeCollectionArray = array("learner", "org-admin");
        if (!in_array($type, $typeCollectionArray)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the user role', array('status' => $codes["GET_FAIL"]["status"]));
        }
        $learner_array = array();
        if (isset($query_string) && $query_string == "all") {
			$search_params = []; 
			$users = UserElasticSearch::get_signedup("search_all_users_info_from_es",$search_params);
			if (!empty($users)) {
				foreach ($users->hits->hits as $result) {
				  $learner_array[] = [
                    "id" => $result->_source->data->details->user_id,
                    "name" => $result->_source->data->details->user->name,
                    "email" => $result->_source->data->details->user->email,
                    "name_email" => $result->_source->data->details->user->name. " (" . $result->_source->data->details->user->email. ")",
                ];
				}
			  }
        } else {
            if ($query_string == trim($query_string) && strpos($query_string, ' ') !== false) {
                $ary = explode(' ', $query_string);
                $search_params = ["first_name" => $ary[0], "last_name" => $ary[1]]; 
                $users = UserElasticSearch::get_signedup("search_string_users_info_from_es",$search_params);
                if (!empty($users)) {
                    foreach ($users->hits->hits as $result) {
                      $learner_array[] = [
                        "id" => $result->_source->data->details->user_id,
                        "name" => $result->_source->data->details->user->name,
                        "email" => $result->_source->data->details->user->email,
                        "name_email" => $result->_source->data->details->user->name. " (" . $result->_source->data->details->user->email. ")",
                    ];
                    }
                  }

            } else {
                $search_params = ["name" => $query_string]; 
                $users = UserElasticSearch::get_signedup("search_name_users_info_from_es",$search_params);
                if (!empty($users)) {
                    foreach ($users->hits->hits as $result) {
                      $learner_array[] = [
                        "id" => $result->_source->data->details->user_id,
                        "name" => $result->_source->data->details->user->name,
                        "email" => $result->_source->data->details->user->email,
                        "name_email" => $result->_source->data->details->user->name. " (" . $result->_source->data->details->user->email. ")",
                    ];
                    }
                  } 
                  else {
                    $search_params = ["phone" => $query_string]; 
                    $user_names = UserElasticSearch::get_signedup("search_phone_users_info_from_es",$search_params);
                    if (!empty($user_names)) {
                        foreach ($user_names->hits->hits as $result) {
                          $learner_array[] = [
                            "id" => $result->_source->data->details->user_id,
                            "name" => $result->_source->data->details->user->name,
                            "email" => $result->_source->data->details->user->email,
                            "name_email" => $result->_source->data->details->user->name. " (" . $result->_source->data->details->user->email. ")",
                        ];
                        }
                      } 
                  }
            }
        }
        if (!empty($learner_array)) {
            $response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User data found', 'data' => $learner_array);
            return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
        } else {
            return new WP_Error($codes["LEARNER_GET_FAIL"]["code"], $codes["LEARNER_GET_FAIL"]["code"], array('status' => $codes["LEARNER_GET_FAIL"]["status"]));
        }
    }
	/**
	 * Get list of learner
	 */	
    public function get_learner_list_permissions_check() {
    	return true;
	}
	/**
	 * To post instructor availability Slots hours
	 */	
    public function createInstructorAvailabilitySlots($request) {
		global $wpdb;
		$codes=error_code_setting();
		$date = date_default_timezone_set('Asia/Kolkata');
		$table = $wpdb->prefix."instructors_availability_slots";
		$detail = [];
		$request = json_decode($request->get_body(), true);
		$instructor_id = (int)$request['instructor_id'];
		$weeks = $request['weeks'];	
			
		foreach($weeks as $week){
			$title=$week['day'];

			if($week['isDayOff']){
				$start_time="00:00";
				$end_time="00:00";
				$duration="0";
				$status="0"; // slot is off
				$data = [
					"instructor_id" => $instructor_id,
					"title" => $title,
					"start_time" => $start_time,
					"end_time" => $start_time,
					"status" => $status,
					"duration" => $duration,
				];
				$inserted = $wpdb->insert($table, $data);					
			}

			if($week['is24Hours']){
				$start_time="01:00";
				$end_time="24:00";
				$checkTime = strtotime($start_time);				
				$loginTime = strtotime($end_time);
				$diff = $loginTime - $checkTime;
				$duration =$diff/60;
				$status="2";
				$data = [
					"instructor_id" => $instructor_id,
					"title" => $title,
					"start_time" => $start_time,
					"end_time" => $end_time,
					"status" => $status,
					"duration" => $duration,

				];
				$inserted = $wpdb->insert($table, $data);					
			}			

			$availablitySlots=$week['availablity'];
			foreach($availablitySlots as $availablitySlot) {
				if(!$week['is24Hours'] && !$week['isDayOff']){
					$start_time=$availablitySlot['start'];
					$end_time=$availablitySlot['end'];
					$checkTime = strtotime($start_time);				
					$loginTime = strtotime($end_time);
					$diff = $loginTime - $checkTime;
					$duration =$diff/60;
	
	
					$isRecordExist = $wpdb->get_results("SELECT * from $table WHERE instructor_id ='".$instructor_id."' AND title ='".$title."' AND start_time ='".$start_time."' AND end_time ='".$end_time."'");
	
					if(count($isRecordExist)>0) {
	
					} else {
	
				$data = [
					"instructor_id" => $instructor_id,
					"title" => $title,
					"start_time" => $start_time,
					"end_time" => $end_time,
					"duration" => $duration,
				];
				$inserted = $wpdb->insert($table, $data);
			}
				}
		}  				
		}
		if($inserted){
			delete_cache();
			$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => 'Data has been saved');
			  return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]); 	
		}	
	}
	
	/**
	 * To post instructor availability Slots hours
	 */	
    public function createInstructorAvailabilitySlots_permissions_check() {
    	return true;
	}	
	/**
	 * To put instructor availability Slots hours
	 */	
    public function updateInstructorAvailabilitySlots($request) {
		global $wpdb;
		$codes=error_code_setting();
		$date = date_default_timezone_set('Asia/Kolkata');
		$table = $wpdb->prefix."instructors_availability_slots";
		$detail = [];
		$request = json_decode($request->get_body(), true);
		$instructor_id = (int)$request['instructor_id'];
		$weeks = $request['weeks'];	
			
		foreach($weeks as $week){
			$title=$week['day'];
			if($week['isDayOff'] && !$week['is24Hours']){
				$isRecordExists = $wpdb->get_results("SELECT * from $table WHERE instructor_id ='".$instructor_id."' and title='".$title."'");
				foreach($isRecordExists as $isRecordExist){
					if(count($isRecordExists)>=1 && $isRecordExist->status!='0'){
						$inserted = $wpdb->get_row("delete from $table WHERE instructor_id ='".$instructor_id."' and title='".$title."'");
						$start_time="00:00";
						$end_time="00:00";
						$duration="0";
						$status="0"; // slot is off
						$data = [
							"instructor_id" => $instructor_id,
							"title" => $title,
							"start_time" => $start_time,
							"end_time" => $start_time,
							"status" => $status,
							"duration" => $duration,
						];
						$inserted = $wpdb->insert($table, $data);						
						} else {
							if(count($isRecordExists)==1 && $isRecordExist->status=='0'){
								$inserted=true;
							}
						}	
				}					
			}

			if($week['is24Hours'] && !$week['isDayOff']){
				$isRecordExists = $wpdb->get_results("SELECT * from $table WHERE instructor_id ='".$instructor_id."' and title='".$title."'");
				foreach($isRecordExists as $isRecordExist){
					if(count($isRecordExists)>0 && $isRecordExist->status!='2'){
						$inserted = $wpdb->get_row("delete from $table WHERE instructor_id ='".$instructor_id."' and title='".$title."'");
						$start_time="01:00";
						$end_time="24:00";
						$checkTime = strtotime($start_time);				
						$loginTime = strtotime($end_time);
						$diff = $loginTime - $checkTime;
						$duration =$diff/60;
						$status="2";
						$data = [
							"instructor_id" => $instructor_id,
							"title" => $title,
							"start_time" => $start_time,
							"end_time" => $end_time,
							"status" => $status,
							"duration" => $duration,
		
						];
						$inserted = $wpdb->insert($table, $data);						
						} else {
							if(count($isRecordExists)==1 && $isRecordExist->status=='2'){
								$inserted=true;
							}
						}	
				}				
			}
			$availablitySlots=$week['availablity'];
			foreach($availablitySlots as $availablitySlot) {
				if($availablitySlot['id']==0){
					if(!$week['is24Hours']){	
						if(!$week['isDayOff']){
							$inserted = $wpdb->get_row("delete from $table WHERE instructor_id ='".$instructor_id."' and title='".$title."' and status='0'");	
						}
					
					$start_time=$availablitySlot['start'];
					$end_time=$availablitySlot['end'];
					$checkTime = strtotime($start_time);				
					$loginTime = strtotime($end_time);
					$diff = $loginTime - $checkTime;
					$duration =$diff/60;
	
	
					$isRecordExist = $wpdb->get_results("SELECT * from $table WHERE instructor_id ='".$instructor_id."' AND title ='".$title."' AND start_time ='".$start_time."' AND end_time ='".$end_time."'");
	
					if(count($isRecordExist)>0) {
						$inserted=true;
					} else {
	
				$data = [
					"instructor_id" => $instructor_id,
					"title" => $title,
					"start_time" => $start_time,
					"end_time" => $end_time,
					"duration" => $duration,
				];
				$inserted = $wpdb->insert($table, $data);
			}
		}
				} else {
					if($availablitySlot['isActive']){
						$start_time=$availablitySlot['start'];
						$end_time=$availablitySlot['end'];
						$checkTime = strtotime($start_time);				
						$loginTime = strtotime($end_time);
						$diff = $loginTime - $checkTime;
						$duration =$diff/60;
						if(!$week['isDayOff'] && $week['is24Hours']){
							$start_time="01:00";
							$end_time="24:00";
							$checkTime = strtotime($start_time);				
							$loginTime = strtotime($end_time);
							$diff = $loginTime - $checkTime;
							$duration =$diff/60;
							$status="2";
						}else if($week['isDayOff'] && !$week['is24Hours']){
							$status='0';
							$start_time="00:00";
							$end_time="00:00";
							$duration="0";
						}else if(!$week['isDayOff'] && !$week['is24Hours']){

							$status='1';
						}
						$updated = [
							"start_time" => $start_time,
							"end_time" => $end_time,
							"duration" => $duration,
							"status"=>$status
						];	
						$inserted = $wpdb->update($table, $updated,['id'=>$availablitySlot['id']]);
					} else {
						$inserted = $wpdb->get_row("delete from $table WHERE id ='".$availablitySlot['id']."'");
					}
				

				}

			  $inserted=true;
		}  				
		}
		if($inserted){
			delete_cache();
			$response = array('code' => $codes["PUT_UPDATE"]["code"], 'message' => 'Data has been updated');
			  return new WP_REST_Response($response, $codes["PUT_UPDATE"]["code"]); 	
		}	
	}
	
	/**
	 * To post instructor availability Slots hours
	 */	
    public function updateInstructorAvailabilitySlots_permissions_check() {
    	return true;
	}	

	public function getInstructorAvailabilityPerDayHourSlotsPermissionsCheck(){
		return true;
	}
	/**
	 * To get instructor availability Slots hours
	 */	
    public static function getInstructorAvailabilitySlots($request) {
		$instructor_id = (int)$request['instructor_id'];
		$criteria = $request['criteria'];
		global $wpdb;
		$codes=error_code_setting();
		// $getInstructorAvailabilitySlotsCacheData = get_cache("getInstructorAvailabilitySlots".$criteria);
		// if (!empty($getInstructorAvailabilitySlotsCacheData)) {
		// 	return new WP_REST_Response($getInstructorAvailabilitySlotsCacheData, 200); 
		// }
		$table_name_1 = $wpdb->prefix."instructors_availability_slots";
		$availabilitySlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."'");
		if($criteria=="clock"){
			$columns=[
                [
                    "field"=> "clock",
                    "label"=> ""
				],
                [
					"field"=> "mon",
                    "label"=> "Mon"					
				],
                [
                    "field"=> "tue",
                    "label"=> "Tue"					
				],
                [
                    "field"=> "wed",
                    "label"=> "Wed"
				],
                [
                    "field"=> "thu",
                    "label"=> "Thu"					
				],
                [
                    "field"=> "fri",
                    "label"=> "Fri"					
				],
                [
                    "field"=> "sat",
                    "label"=> "Sat"					
				],
                [
                    "field"=> "sun",
                    "label"=> "Sun"					
				]
            ];
			$req['instructor_id']=$instructor_id;
			$controller = new UserController();
			$rows=$controller->getInstructorAvailabilityPerDayHourSlots($req);
			$rows_counts=$rows->data['data']['rows']; 
			//echo "<pre>";print_r($rows_counts);die;
			$mornings=["06:00","06:30","07:00","07:30","08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","12:00"];
			$afternoons=["12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00"];
			$evenings=["16:00","16:30","17:00","17:30","18:00","18:30","19:00","19:30","20:00"];
			$nights=["20:00","20:30","21:00","21:30","22:00","22:30","23:00"];
			$m_mon_status=false;//morning
			$m_tue_status=false;
			$m_wed_status=false;
			$m_thu_status=false;
			$m_fri_status=false;
			$m_sat_status=false;
			$m_sun_status=false;
			$a_mon_status=false;//afternoon
			$a_tue_status=false;
			$a_wed_status=false;
			$a_thu_status=false;
			$a_fri_status=false;
			$a_sat_status=false;
			$a_sun_status=false;
			$e_mon_status=false;//evening
			$e_tue_status=false;
			$e_wed_status=false;
			$e_thu_status=false;
			$e_fri_status=false;
			$e_sat_status=false;
			$e_sun_status=false;
			$n_mon_status=false;//night
			$n_tue_status=false;
			$n_wed_status=false;
			$n_thu_status=false;
			$n_fri_status=false;
			$n_sat_status=false;
			$n_sun_status=false;
			foreach($rows_counts as $rows_count){
				foreach($mornings as $morning){
					//morning
					if($rows_count["slug"]==$morning && $rows_count["mon"] === true){
						$m_mon_status=true;
					} 
					if($rows_count["slug"]==$morning && $rows_count["tue"] === true){
						$m_tue_status=true;
					} 
					if($rows_count["slug"]==$morning && $rows_count["wed"] === true){
						$m_wed_status=true;
					} 
					if($rows_count["slug"]==$morning && $rows_count["thu"] === true){
						$m_thu_status=true;
					} 
					if($rows_count["slug"]==$morning && $rows_count["fri"] === true){
						$m_fri_status=true;
					} 
					if($rows_count["slug"]==$morning && $rows_count["sat"] === true){
						$m_sat_status=true;
					} 
					if($rows_count["slug"]==$morning && $rows_count["sun"] === true){
						$m_sun_status=true;
					} 
				}
					foreach($afternoons as $afternoon){				
					//afternoon
					if($rows_count["slug"]==$afternoon && $rows_count["mon"] === true){
						$a_mon_status=true;
					} 
					if($rows_count["slug"]==$afternoon && $rows_count["tue"] === true){
						$a_tue_status=true;
					} 
					if($rows_count["slug"]==$afternoon && $rows_count["wed"] === true){
						$a_wed_status=true;
					} 
					if($rows_count["slug"]==$afternoon && $rows_count["thu"] === true){
						$a_thu_status=true;
					} 
					if($rows_count["slug"]==$afternoon && $rows_count["fri"] === true){
						$a_fri_status=true;
					} 
					if($rows_count["slug"]==$afternoon && $rows_count["sat"] === true){
						$a_sat_status=true;
					} 
					if($rows_count["slug"]==$afternoon && $rows_count["sun"] === true){
						$a_sun_status=true;
					} 
				}
				foreach($evenings as $evening){						
					//evening
					if($rows_count["slug"]==$evening && $rows_count["mon"] === true){
						$e_mon_status=true;
					} 
					if($rows_count["slug"]==$evening && $rows_count["tue"] === true){
						$e_tue_status=true;
					} 
					if($rows_count["slug"]==$evening && $rows_count["wed"] === true){
						$e_wed_status=true;
					} 
					if($rows_count["slug"]==$evening && $rows_count["thu"] === true){
						$e_thu_status=true;
					} 
					if($rows_count["slug"]==$evening && $rows_count["fri"] === true){
						$e_fri_status=true;
					} 
					if($rows_count["slug"]==$evening && $rows_count["sat"] === true){
						$e_sat_status=true;
					} 
					if($rows_count["slug"]==$evening && $rows_count["sun"] === true){
						$e_sun_status=true;
					} 
				}
				foreach($nights as $night){						
					//night
					if($rows_count["slug"]==$night && $rows_count["mon"] === true){
					$n_mon_status=true;
					} 
					if($rows_count["slug"]==$night && $rows_count["tue"] === true){
					$n_tue_status=true;
					} 
					if($rows_count["slug"]==$night && $rows_count["wed"] === true){
					$n_wed_status=true;
					} 
					if($rows_count["slug"]==$night && $rows_count["thu"] === true){
					$n_thu_status=true;
					} 
					if($rows_count["slug"]==$night && $rows_count["fri"] === true){
					$n_fri_status=true;
					} 
					if($rows_count["slug"]==$night && $rows_count["sat"] === true){
					$n_sat_status=true;
					} 
					if($rows_count["slug"]==$night && $rows_count["sun"] === true){
					$n_sun_status=true;
					} 
				}
			}
			// if(!empty(array_intersect($morning, $rows_counts))){
			// 	$morning_slot=true;	
			// } else {
			// 	$morning_slot=false;
			// }

			$afternoon=[];
			$evening=[];
			$night=[];

			$rows=[
                [
                    "clock"=> "Morning (6a-12p)",
                    "mon"=> $m_mon_status,
                    "tue"=> $m_tue_status,
                    "wed"=> $m_wed_status,
                    "thu"=> $m_thu_status,
                    "fri"=> $m_fri_status,
                    "sat"=> $m_sat_status,
                    "sun"=> $m_sun_status					
				],
                [
                    "clock"=> "Afternoon (12p-4p)",
                    "mon"=> $a_mon_status,
                    "tue"=> $a_tue_status,
                    "wed"=> $a_wed_status,
                    "thu"=> $a_thu_status,
                    "fri"=> $a_fri_status,
                    "sat"=> $a_sat_status,
                    "sun"=> $a_sun_status					
				],
                [
                    "clock"=> "Evening (4p-8p)",
                    "mon"=> $e_mon_status,
                    "tue"=> $e_tue_status,
                    "wed"=> $e_wed_status,
                    "thu"=> $e_thu_status,
                    "fri"=> $e_fri_status,
                    "sat"=> $e_sat_status,
                    "sun"=> $e_sun_status					
				],
				[
                    "clock"=> "Night (8p-11p)",
                    "mon"=> $n_mon_status,
                    "tue"=> $n_tue_status,
                    "wed"=> $n_wed_status,
                    "thu"=> $n_thu_status,
                    "fri"=> $n_fri_status,
                    "sat"=> $n_sat_status,
                    "sun"=> $n_sun_status					
				]
            ];
			$resultCollections=["slots"=>["columns"=>$columns,"rows"=>$rows]];
		} else {
		if(count($availabilitySlots)==0) {
				$data='{
					"instructor_id": '.$instructor_id.',
					"hasRecord":false,
					"weeks": [
						{
							"day": "Sunday",
							"isDayOff": true,
							"is24Hours": false,
							"availablity": []
						},
						{
							"day": "Monday",
							"isDayOff": true,
							"is24Hours": false,							
							"availablity": []
						},
						{
							"day": "Tuesday",
							"isDayOff": true,
							"is24Hours": false,							
							"availablity": []
						},
						{
							"day": "Wednesday",
							"isDayOff": true,
							"is24Hours": false,							
							"availablity": []
						},
						{
							"day": "Thursday",
							"isDayOff": true,
							"is24Hours": false,							
							"availablity": []
						},
						{
							"day": "Friday",
							"isDayOff": true,
							"is24Hours": false,							
							"availablity": []
						},
						{
							"day": "Saturday",
							"isDayOff": true,
							"is24Hours": false,							
							"availablity": []
						}
					]
				}';
			$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'records has been found', 'data' => json_decode($data,true));
			//add_cache('getInstructorAvailabilitySlots'.$criteria, $response, '', 3600);
			  return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
		}
		$firstAvailablity=[];
		$firstDayOfWeek="Sunday";
		$availabilityFirstSlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."' and title='".$firstDayOfWeek."'");
        $a=0;
		foreach($availabilityFirstSlots as $availabilitySlot){
			if($availabilitySlot->status=='0'){
				//$firstAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$firstAvailablity=[];
				$isDayOff=true;
				$is24Hours=false;	
				$hasRecord1=false;		
			} else if($availabilitySlot->status=='2'){
				//$firstAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$firstAvailablity=[];
				$isDayOff=false;
				$is24Hours=true;
				$hasRecord1=true;
			} else {
				$firstAvailablity[]=["id"=>$availabilitySlot->id,"start"=>$availabilitySlot->start_time,"end"=>$availabilitySlot->end_time,"isActive"=>true,"slotID"=>$a];
				
				$isDayOff=false;
				$is24Hours=false;
				$hasRecord1=true;
			
			}

			$firstDayWeek=["day"=>$availabilitySlot->title,"isDayOff"=>$isDayOff,"is24Hours"=>$is24Hours,"availablity"=>$firstAvailablity];	
			$a++;
		}

		$secondAvailablity=[];
		$secondDayOfWeek="Monday";
		$availabilitySecondSlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."' and title='".$secondDayOfWeek."'");
        $b=0;
		foreach($availabilitySecondSlots as $availabilitySlot){
			if($availabilitySlot->status=='0'){
				//$secondAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$secondAvailablity=[];
				$isDayOff=true;
				$is24Hours=false;
				$hasRecord2=false;

			} else if($availabilitySlot->status=='2'){
				//$secondAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$secondAvailablity=[];
				$isDayOff=false;
				$is24Hours=true;
				$hasRecord2=true;
			
			} else {
				$secondAvailablity[]=["id"=>$availabilitySlot->id,"start"=>$availabilitySlot->start_time,"end"=>$availabilitySlot->end_time,"isActive"=>true,"slotID"=>$b];
				
				$isDayOff=false;
				$is24Hours=false;
				$hasRecord2=true;
			}

			$secondDayWeek=["day"=>$availabilitySlot->title,"isDayOff"=>$isDayOff,"is24Hours"=>$is24Hours,"availablity"=>$secondAvailablity];
			$b++;	
		}		

		$thirthAvailablity=[];
		$thirdDayOfWeek="Tuesday";
		$availabilityThirdSlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."' and title='".$thirdDayOfWeek."'");
        $c=0;
		foreach($availabilityThirdSlots as $availabilitySlot){
			if($availabilitySlot->status=='0'){
				//$thirthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$thirthAvailablity=[];
				$isDayOff=true;
				$is24Hours=false;
				$hasRecord3=false;
			} else if($availabilitySlot->status=='2'){
				//$thirthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$thirthAvailablity=[];
				$isDayOff=false;
				$is24Hours=true;
				$hasRecord3=true;
			} else {
				$thirthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>$availabilitySlot->start_time,"end"=>$availabilitySlot->end_time,"isActive"=>true,"slotID"=>$c];
				
				$isDayOff=false;
				$is24Hours=false;
				$hasRecord3=true;
			}
			$thirdDayWeek=["day"=>$availabilitySlot->title,"isDayOff"=>$isDayOff,"is24Hours"=>$is24Hours,"availablity"=>$thirthAvailablity];
			$c++;		
		}	

		$fourthAvailablity=[];
		$fourthDayOfWeek="Wednesday";
		$availabilityFourthSlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."' and title='".$fourthDayOfWeek."'");
        $d=0;
		foreach($availabilityFourthSlots as $availabilitySlot){
			if($availabilitySlot->status=='0'){
				//$fourthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$fourthAvailablity=[];
				$isDayOff=true;
				$is24Hours=false;
				$hasRecord4=false;
			} else if($availabilitySlot->status=='2'){
				//$fourthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$fourthAvailablity=[];
				$isDayOff=false;
				$is24Hours=true;
				$hasRecord4=true;
			} else {
				$fourthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>$availabilitySlot->start_time,"end"=>$availabilitySlot->end_time,"isActive"=>true,"slotID"=>$d];	
				
				$isDayOff=false;
				$is24Hours=false;
				$hasRecord4=true;
			}
			$fourthDayWeek=["day"=>$availabilitySlot->title,"isDayOff"=>$isDayOff,"is24Hours"=>$is24Hours,"availablity"=>$fourthAvailablity];
			$d++;
		}	


		$fivthAvailablity=[];
		$fivthDayOfWeek="Thursday";
		$availabilityFivthSlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."' and title='".$fivthDayOfWeek."'");
        $e=0;
		foreach($availabilityFivthSlots as $availabilitySlot){
			if($availabilitySlot->status=='0'){
				//$fivthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$fivthAvailablity=[];
				$isDayOff=true;
				$is24Hours=false;
				$hasRecord5=false;
			} else if($availabilitySlot->status=='2'){
				//$fivthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$fivthAvailablity=[];
				$isDayOff=false;
				$is24Hours=true;
				$hasRecord5=true;
			} else {

				$isDayOff=false;
				$is24Hours=false;
				$hasRecord5=true;
				$fivthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>$availabilitySlot->start_time,"end"=>$availabilitySlot->end_time,"isActive"=>true,"slotID"=>$e];
				
			}

			$fivthDayWeek=["day"=>$availabilitySlot->title,"isDayOff"=>$isDayOff,"is24Hours"=>$is24Hours,"availablity"=>$fivthAvailablity];
			$e++;
		}	


		$sixthAvailablity=[];
		$sixthDayOfWeek="Friday";
		$availabilitySixthSlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."' and title='".$sixthDayOfWeek."'");
        $f=0;
		foreach($availabilitySixthSlots as $availabilitySlot){
			if($availabilitySlot->status=='0'){
				//$sixthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$sixthAvailablity=[];
				$isDayOff=true;
				$is24Hours=false;
				$hasRecord6=false;
			} else if($availabilitySlot->status=='2'){
				//$sixthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$sixthAvailablity=[];
				$isDayOff=false;
				$is24Hours=true;
				$hasRecord6=true;
			} else {
				$sixthAvailablity[]=["id"=>$availabilitySlot->id,"start"=>$availabilitySlot->start_time,"end"=>$availabilitySlot->end_time,"isActive"=>true,"slotID"=>$f];
				
				$isDayOff=false;
				$is24Hours=false;
				$hasRecord6=true;
			}

			$sexthDayWeek=["day"=>$availabilitySlot->title,"isDayOff"=>$isDayOff,"is24Hours"=>$is24Hours,"availablity"=>$sixthAvailablity];
			$f++;
		}	

		$seventhAvailablity=[];
		$seventhDayOfWeek="Saturday";
		$availabilitySeventhSlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."' and title='".$seventhDayOfWeek."'");
        $g=0;
		foreach($availabilitySeventhSlots as $availabilitySlot){
			if($availabilitySlot->status=='0'){
				//$seventhAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$seventhAvailablity=[];
				$isDayOff=true;
				$is24Hours=false;
				$hasRecord7=false;
			} else if($availabilitySlot->status=='2'){
				//$seventhAvailablity[]=["id"=>$availabilitySlot->id,"start"=>"","end"=>"","isActive"=>true];
				$seventhAvailablity=[];
				$isDayOff=false;
				$is24Hours=true;
				$hasRecord7=true;
			} else {
				$seventhAvailablity[]=["id"=>$availabilitySlot->id,"start"=>$availabilitySlot->start_time,"end"=>$availabilitySlot->end_time,"isActive"=>true,"slotID"=>$g];
				
				$isDayOff=false;
				$is24Hours=false;
				$hasRecord7=true;
			}


			$sevenDayWeek=["day"=>$availabilitySlot->title,"isDayOff"=>$isDayOff,"is24Hours"=>$is24Hours,"availablity"=>$seventhAvailablity];
			$g++;
		}	
		$allvalues = array($hasRecord1, $hasRecord2, $hasRecord3,$hasRecord4,$hasRecord5,$hasRecord6,$hasRecord7);
		if(array_sum($allvalues) >0) {
	$hasRecord=true;	
} else {
	$hasRecord=false;	
}

		$weeks=[$firstDayWeek,$secondDayWeek,$thirdDayWeek,$fourthDayWeek,$fivthDayWeek,$sexthDayWeek,$sevenDayWeek];
		$resultCollections=["instructor_id"=>$instructor_id,"hasRecord"=>$hasRecord,"weeks"=>$weeks];
		}
		$result = array('code' => $codes["GET_SUCCESS"]["code"],'status' => $codes["GET_SUCCESS"]["status"], 'message' => 'Instructor availability found', 'data' => $resultCollections);
		//add_cache('getInstructorAvailabilitySlots'.$criteria, $result, '', 3600);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
		
	}		
	/**
	 * To get instructor availability Slots hours
	 */	
    public function getInstructorAvailabilitySlots_permissions_check() {
    	return true;
	}
	/**
	 * To update user's profile privacy of any role
	 */	
    public function getRoleBasedUsers($request) {
		$userRole = $request['role'];//SEO Manager
		$codes=error_code_setting();
		if($userRole=='learner'){
			$role="SEO Manager";
		} else if($userRole=='instructor') {
			$role="um_instructor";
		} else if($userRole=='counselor') {
			$role="um_counselor";
		} else if($userRole=='yuno-admin') {
			$role="um_yuno-admin";
		} else if($userRole=='yuno-content-admin') {
			$role="um_content-admin";
		} else if($userRole=='yuno-category-admin') {
			$role="um_yuno-category-admin";
		} else if($userRole=='dashboard-viewer') {
			$role="um_dashboard-viewer";
		}

					$args = array(
						'role' => $role,
						'orderby' => 'display_name',
						'order' => 'ASC',
						'fields' => array('ID','user_email','display_name') /// Nov-16 - 2023--added
					  );
					  $users = get_users($args); /// Nov-16 - 2023----updated
					  $instructor_array = array();
					  foreach ($users as $key => $value) {
							$instructor_array[] = [
							  "id" => $value->ID,
							  "name" => $value->display_name,
							  "email" => $value->user_email,
							  "name_email" => $value->display_name . " (" . $value->user_email . ")"
							];
					  }
					/// Nov-16 - 2023--e--updated
					  

		if (!empty($instructor_array)) {
			$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'user data are found', 'data' => $instructor_array);
			return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);	
		} else {
			return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find any useur", array('status' => $codes["GET_FAIL"]["status"]));	
		}
	}
	
	/**
	 * To get users of any role
	 */	
    public function getRoleBasedUsers_permissions_check() {
    	return true;
	}		
	/**
	 * To get users of any role
	 */	
    public function updateUserMeta($request) {
		global $wpdb;
		$userRole = $request['role'];//SEO Manager
		$codes=error_code_setting();
		$offset = $request['offset'];
		$status = $request['status'];
		$limit = $request['limit'];
		if($userRole=='learner'){
			$role="SEO Manager";
		} else if($userRole=='instructor') {
			$role="um_instructor";
		} else if($userRole=='counselor') {
			$role="um_counselor";
		} else if($userRole=='yuno-admin') {
			$role="um_yuno-admin";
		} else if($userRole=='yuno-content-admin') {
			$role="um_content-admin";
		} else if($userRole=='yuno-category-admin') {
			$role="um_yuno-category-admin";
		}
		$args = array(
			'role' => $role,
			'orderby' => 'display_name',
			'order' => 'ASC',
			'fields' => array('ID','user_email','display_name') /// Nov-16 - 2023--added
		  );
		  $users = get_users($args);  /// Nov-16 - 2023-- updated
		  $instructor_array = array();
		  foreach ($users as $key => $value) {
				$instructor_array[] = [
				  "id" => $value->ID,
				  "name" => $value->display_name,
				  "email" => $value->user_email,
				  "name_email" => $value->display_name . " (" . $value->user_email . ")"
				];
		  }
		/// Nov-16 - 2023-----e--udpated
		
		
		
		if (!empty($instructor_array)) {

			if($limit=="all" && $offset=="all" && $role!="learner") {
			foreach($instructor_array  as $res){
				update_user_meta($res['id'], 'account_login_status',$status);
			} 


			$roleUsers=[];
			  $meta_keys = ['account_login_status'];
			  $user_ids = array_column($users, 'ID');
			  $user_placeholders = implode(', ', array_fill(0, count($user_ids), '%d'));
			  $meta_placeholders = implode(', ', array_fill(0, count($meta_keys), '%s'));
			  $query = $wpdb->prepare("SELECT meta_value , user_id FROM {$wpdb->usermeta} WHERE meta_key IN ($meta_placeholders) AND user_id IN ($user_placeholders)",array_merge($meta_keys, $user_ids));
			  $user_meta_results = $wpdb->get_results($query );
			  $user_meta_data_account_login_status = array_column($user_meta_results, 'meta_value','user_id');
			  
			  $roleUsers=[];
			  foreach ($users as $key => $value) {
				$account_login_status = $user_meta_data_account_login_status[$value->ID];
				  $roleUsers[] = [
					"id" => $value->ID,
					"name" => $value->display_name,
					"email" => $value->user_email,
					"account_login_status" => $account_login_status,
					"name_email" => $value->display_name." (".$value->user_email.")"
				  ];
				}
			  /// Nov-16 - 2023-----e--udpated


		} else {
			$int = (int)$limit;
			if($int >100) {	
				//return new WP_Error('404', 'you can not use large limit', array('status' => 404));					
			}

			$final_result = array_slice($instructor_array, $offset, $limit);
			foreach($final_result  as $res){
				update_user_meta($res['id'], 'account_login_status',$status);
			} 
			$roleUsers=[];
			foreach ($final_result as $key => $value) {
				$account_login_status = get_user_meta($value['id'],'account_login_status',true);
				  $roleUsers[] = [
					  "id" => $value['id'],
					  "name" => $value['name'],
					  "email" => $value['email'],
					  "account_login_status" => $account_login_status,
					  "name_email" => $value['name']." (".$value['email'].")"
				  ];
				
	  
			  }			

		}
		$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'user data are found','count'=>count($roleUsers), 'data' => $roleUsers);		
			return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);	
		} else {
			return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find any user", array('status' => $codes["GET_FAIL"]["status"]));	
		}
	}
	
	/**
	 * To update user's profile privacy of any role
	 */	
    public function updateUserMeta_permissions_check() {
    	return true;
	}
    /**
     * To get category admin user's categories instructors
     */		
	public function get_instructor_list_for_category_admin_permissions_check() {
    	return true;
    }

    /**
     * To get category admin user's categories instructors
     */
    public function get_instructor_list_for_category_admin($request) {
		global $wpdb;
		$codes=error_code_setting();
		$category_admin_id = (int)$request['user_id'];
		$criteria = $request['criteria'];
		$userdata = get_userdata($category_admin_id);
		if($userdata->roles){
			if(in_array('um_yuno-category-admin', $userdata->roles)){
				$table_name = $wpdb->prefix . "category_admin_relationships"; 
				$query="select * from $table_name where user_id=$category_admin_id";	
				$results = $wpdb->get_results($wpdb->prepare($query));
				$assignedCategories = [];
				foreach($results as $result){	
				  $assignedCategories[]=$result->category_id;		
				}
				if($criteria=="paid"){
				$meta_query = array(
				  'relation' => 'AND', 
				  array(
					  'key'     => 'zoom_user_status',
					  'value'   => 'yuno-licenced',
					  'compare' => '='
				  ),
				  array(
					  'key'     => 'zoho_instructor_id',
					  'value'   => '',  
					  'compare' => '!='
				  )
				);
				
				} else {
				$meta_query = array(
				  array(
					'key'     => 'zoho_instructor_id',
					'value'   => '', 
					'compare' => '!='
				) 
				);						
				}
				
				$args = array(
				  'role'    => 'um_instructor',
				  'orderby' => 'display_name',
				  'order'   => 'ASC',
				  'meta_query'=> $meta_query,
				  'fields' => array('ID','user_email','display_name') /// Nov-17 - 2023--added
				);
				$users = get_users( $args );   /// Nov-17 - 2023-------udpated  
				$instructor_array = array();
				foreach ($users as $key => $value) {
					$table_name_1 = $wpdb->prefix . "course_instructor_relationships"; 
					$sql="select * from $table_name_1 where instructor_id =$value->ID";	
					$data = $wpdb->get_results($wpdb->prepare($sql));
					  if (count($data) > 0) {
					  $category_id = [];
					  foreach($data as $eachRecord){
						$categories = wp_get_post_terms($eachRecord->course_id, 'course_category');
						foreach ($categories as $key => $val) {
						  $category_id[] = (string)$val->term_id; 
						}  
					  }
					  $channelIDs=array_unique($category_id);
					  foreach($assignedCategories as $assignedCategory){
						if(in_array($assignedCategory,$channelIDs)){
						  $instructor_array[] = [
							"id" => $value->ID,
							"name" => $value->display_name,
							"email" => $value->user_email,
							"name_email" => $value->display_name." (".$value->user_email.")"
						  ];
						}
					  }
					}
				}

				if (!empty($instructor_array)) {
					$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'Instructor data found', 'data' => array_values(array_unique($instructor_array, SORT_REGULAR)));
					return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);	
				} else {
					return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find any instructor. Check back later as new instructor get added to the list", array('status' => $codes["GET_FAIL"]["status"]));	
				}
			}else{
				if($criteria=="paid"){
					$meta_query = array(
						'relation' => 'AND',  // Specify the relationship between the queries
						array(
						'key'     => 'zoom_user_status',
						'value'   => 'yuno-licenced',
						'compare' => '='
						),
						array(
						'key'     => 'zoho_instructor_id',
						'value'   => '',  // Check for a non-empty value
						'compare' => '!='
						)
					);
						} else {
					$meta_query = array(
						array(
						'key' => 'zoho_instructor_id',
						'value' => '',
						'compare' => '!='
						)
					);						
					}			
					$args = array(
						'role'    => 'um_instructor',
						'orderby' => 'display_name',
						'order'   => 'ASC',
						'meta_query'=> $meta_query,
						'fields' => array('ID','user_email','display_name')
					);
					
					
					$users = get_users( $args );  /// Nov-16 - 2023-------udpated  
					$instructor_array = array();
					foreach ($users as $key => $value) {
						$instructor_array[] = [
						"id" => $value->ID,
						"name" => $value->display_name,
						"email" => $value->user_email,
						"name_email" => $value->display_name." (".$value->user_email.")"
						];
					}
				/// Nov-16 - 2023-----e--udpated  






				
				if (!empty($instructor_array)) {
					$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'Instructor data found', 'data' => $instructor_array);
					return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);	
				} else {
					return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find any instructor. Check back later as new instructor get added to the list", array('status' => $codes["GET_FAIL"]["status"]));	
				}
			}
		} else {
			return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
		}
    }

/**
 * To get instructor availability slots
 */		
public function getInstructorAvailabilityHourSlots() {
	$codes=error_code_setting();
	// $getInstructorAvailabilityHourSlotsCacheData = get_cache("getInstructorAvailabilityHourSlots");
	// if (!empty($getInstructorAvailabilityHourSlotsCacheData)) {
	// 	return new WP_REST_Response($getInstructorAvailabilityHourSlotsCacheData, 200); 
	// }	
	//$slot1=["label"=>"24 hours","slug"=>"24"];
	$slot2=["label"=>"12:00 AM","slug"=>"24:00"];
	$slot3=["label"=>"12:30 AM","slug"=>"00:30"];
	$slot4=["label"=>"1:00 AM","slug"=>"01:00"];
	$slot5=["label"=>"1:30 AM","slug"=>"01:30"];
	$slot6=["label"=>"2:00 AM","slug"=>"02:00"];
	$slot7=["label"=>"2:30 AM","slug"=>"02:30"];
	$slot8=["label"=>"3:00 AM","slug"=>"03:00"];
	$slot9=["label"=>"3:30 AM","slug"=>"03:30"];
	$slot10=["label"=>"4:00 AM","slug"=>"04:00"];
	$slot11=["label"=>"4:30 AM","slug"=>"04:30"];
	$slot12=["label"=>"5:00 AM","slug"=>"05:00"];
	$slot13=["label"=>"5:30 AM","slug"=>"05:30"];
	$slot14=["label"=>"6:00 AM","slug"=>"06:00"];
	$slot15=["label"=>"6:30 AM","slug"=>"06:30"];
	$slot16=["label"=>"7:00 AM","slug"=>"07:00"];
	$slot17=["label"=>"7:30 AM","slug"=>"07:30"];
	$slot18=["label"=>"8:00 AM","slug"=>"08:00"];
	$slot19=["label"=>"8:30 AM","slug"=>"08:30"];
	$slot20=["label"=>"9:00 AM","slug"=>"09:00"];
	$slot21=["label"=>"9:30 AM","slug"=>"09:30"];
	$slot22=["label"=>"10:00 AM","slug"=>"10:00"];
	$slot23=["label"=>"10:30 AM","slug"=>"10:30"];
	$slot24=["label"=>"11:00 AM","slug"=>"11:00"];
	$slot25=["label"=>"11:30 AM","slug"=>"11:30"];
	$slot26=["label"=>"12:00 PM","slug"=>"12:00"];
	$slot27=["label"=>"12:30 PM","slug"=>"12:30"];
	$slot28=["label"=>"1:00 PM","slug"=>"13:00"];
	$slot29=["label"=>"1:30 PM","slug"=>"13:30"];
	$slot30=["label"=>"2:00 PM","slug"=>"14:00"];
	$slot31=["label"=>"2:30 PM","slug"=>"14:30"];
	$slot32=["label"=>"3:00 PM","slug"=>"15:00"];
	$slot33=["label"=>"3:30 PM","slug"=>"15:30"];
	$slot34=["label"=>"4:00 PM","slug"=>"16:00"];
	$slot35=["label"=>"4:30 PM","slug"=>"16:30"];
	$slot36=["label"=>"5:00 PM","slug"=>"17:00"];
	$slot37=["label"=>"5:30 PM","slug"=>"17:30"];
	$slot38=["label"=>"6:00 PM","slug"=>"18:00"];
	$slot39=["label"=>"6:30 PM","slug"=>"18:30"];
	$slot40=["label"=>"7:00 PM","slug"=>"19:00"];
	$slot41=["label"=>"7:30 PM","slug"=>"19:30"];
	$slot42=["label"=>"8:00 PM","slug"=>"20:00"];
	$slot43=["label"=>"8:30 PM","slug"=>"20:30"];
	$slot44=["label"=>"9:00 PM","slug"=>"21:00"];
	$slot45=["label"=>"9:30 PM","slug"=>"21:30"];
	$slot46=["label"=>"10:00 PM","slug"=>"22:00"];
	$slot47=["label"=>"10:30 PM","slug"=>"22:30"];
	$slot48=["label"=>"11:00 PM","slug"=>"23:00"];
	$slot49=["label"=>"11:30 PM","slug"=>"23:30"];
	$slots=[$slot2,$slot3,$slot4,$slot5,$slot6,$slot7,$slot8,$slot9,$slot10,$slot11,$slot12,$slot13,$slot14,$slot15,$slot16,$slot17,$slot18,$slot19,$slot20,$slot21,$slot22,$slot23,$slot24,$slot25,$slot26,$slot27,$slot28,$slot29,$slot30,$slot31,$slot32,$slot33,$slot34,$slot35,$slot36,$slot37,$slot38,$slot39,$slot40,$slot41,$slot42,$slot43,$slot44,$slot45,$slot46,$slot47,$slot48,$slot49];
	$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'data has been found', 'data' => $slots);
	//add_cache('getInstructorAvailabilityHourSlots', $response, '', 3600);
	return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
}
	/**
	 * To get instructor availability slots
	 */		
	public function getInstructorAvailabilityHourSlots_permissions_check() {
    	return true;
    }			

    /**
     * Create new user while coming from android application
     */
    public function new_user_register($request) {
    		$user = $service->userinfo->get();				
			$users = get_user_by( 'email', $user->email );
			$uemailid = $user->email;
			$user_id = $users->ID;	
			$yuno_user_name = $user->name;
			$yuno_user_name_arr = explode(" ",$yuno_user_name);
			$yuno_user_name = $yuno_user_name_arr[0];			
			$yuno_user_fisrtname = sanitize_user( $yuno_user_name_arr[0] );
			$yuno_user_lastname = sanitize_user( $yuno_user_name_arr[1] );
			$yuno_user_name = sanitize_user( $yuno_user_name );
			$yuno_user_name = str_replace(array(" ","."),"",$yuno_user_name);
			$yuno_user_name_check = username_exists( $yuno_user_name );
			$yuno_user_authentication_code=$_GET['code'];
			$cookie_name = "yuno_user_login_id";
		  	if($user_id){
		  		$GoogleState = json_decode(str_replace("\\", "", $_GET['state']));
				//error_log("googleState-1--- ". $GoogleState->googleMeet);
				if(isset($GoogleState->googleMeet) && $GoogleState->googleMeet == true){
					//error_log("googleState-2--- ". $GoogleState->googleMeet);
					update_user_meta($user_id, 'ins_meet_permission', true);
				}
	  			$prev_url = $_SERVER['HTTP_REFERER'];
	  			$get_yuno_user_refresh_token = get_user_meta($user_id,'yuno_user_refresh_token', true );
	  			$currentDate = date("Y-m-d H:i:s");
				$users = get_user_by( 'id', $user_id ); 
				$users = get_user_by('login',$users->user_login);
				update_user_meta( $user_id, 'yuno_user_access_token', $access_token);
				if(!empty($get_yuno_user_refresh_token)){
						//error_log("yuno_user_refresh_token IF".$yuno_user_refresh_token);
					}else{
						// error_log("yuno_user_refresh_token ELSE".$yuno_user_refresh_token);
						update_user_meta( $user_id, 'yuno_user_refresh_token', $post_user_refresh_token);
						//error_log('updated---');
					}
				update_user_meta( $user_id, 'yuno_user_id_token', $id_token);
				update_user_meta( $user_id, 'yuno_user_authentication_code', $yuno_user_authentication_code);
				update_user_meta( $user_id, 'googleplus_access_token', $user->id );
				update_user_meta( $user_id, 'googleplus_profile_img', $user->picture );
				update_user_meta( $user_id, 'yuno_display_name', $user->name );
				update_user_meta( $user_id, 'yuno_first_name', $yuno_user_fisrtname );
				update_user_meta( $user_id, 'yuno_last_name', $yuno_user_lastname );
				update_user_meta( $user_id, 'yuno_gplus_email', $uemailid );
				update_user_meta( $user_id, 'yuno_gplus_rgdate', $currentDate );
			
			
				if(!isset($_COOKIE[$cookie_name])) {
					//error_log("COOKIE=== ".$_COOKIE[$cookie_name]);
					setcookie($cookie_name,$user_id,time() + (1 * 365 * 24 * 60 * 60), "/");
				}

				$site_url = site_url();
				$userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
				user_last_login_time($user_id, $userLeadId);

				
            	$redirect_u = site_url('/auth/');
	 			wp_redirect($redirect_u);
		  	}else{													
				
				if($yuno_user_name_check) { 
					$yuno_user_name = $yuno_user_name_arr[1];
					$yuno_user_name = sanitize_user( $yuno_user_name );
					$yuno_user_name = str_replace(array(" ","."),"",$yuno_user_name);
					$yuno_user_name_check = username_exists( $yuno_user_name );
				}
				if ($yuno_user_name_check) { 
					$yuno_user_name = $user->name;
					$yuno_user_name = sanitize_user( $yuno_user_name );
					$yuno_user_name = str_replace(array(" ","."),"",$yuno_user_name);
					$yuno_user_name_check = username_exists( $yuno_user_name );
				}
				if($yuno_user_name_check) { 
					$yuno_user_name = $yuno_user_name_arr[0];
					$yuno_user_name = sanitize_user( $yuno_user_name );
					$yuno_user_name = str_replace(array(" ","."),"",$yuno_user_name);
					$yuno_user_name = $yuno_user_name.rand(100, 999);
					$yuno_user_name_check = username_exists( $yuno_user_name );
				}
				
				if(!$yuno_user_name_check && email_exists($user->email) == false) {
					date_default_timezone_set('Asia/Kolkata');
					$cdate = date("Y-m-d H:i:s");
					$random_password = wp_generate_password($length = 12, $include_standard_special_chars = false);
					/* Create user in DB */
					$user_id = wp_create_user($yuno_user_name, $random_password, $user->email);
					/* END */
					$UEmail = $user->email;
					$users = get_user_by( 'id', $user_id );				
					update_user_meta( $user_id, 'yuno_user_id_token', $yuno_user_id_token);
					update_user_meta( $user_id, 'yuno_user_authentication_code', $yuno_user_authentication_code);							
					update_user_meta( $user_id, 'googleplus_access_token', $user->id );
					update_user_meta( $user_id, 'googleplus_profile_img', $user->picture );
					update_user_meta( $user_id, 'yuno_display_name', $user->name );
					update_user_meta( $user_id, 'yuno_first_name', $yuno_user_fisrtname );
					update_user_meta( $user_id, 'yuno_last_name', $yuno_user_lastname );
					update_user_meta( $user_id, 'yuno_gplus_email', $uemailid );
					update_user_meta( $user_id, 'yuno_gplus_rgdate', $cdate );
					/**
					 * Getting mobile and categoryURL from state variable
					 */
					$stateArray = json_decode(str_replace("\\", "", $_GET['state']));
					$mobile = isset($stateArray->mobile) ? $stateArray->mobile : '';
					$categoryURL = isset($stateArray->categoryURL) ? $stateArray->categoryURL : '';
					$productCode = isset($stateArray->productCode) ? $stateArray->productCode : '';
					$leadStatus = isset($stateArray->leadStatus) ? $stateArray->leadStatus : '';
					$variant = isset($stateArray->variant) ? $stateArray->variant : '';
					$utmSource = isset($stateArray->utmSource) ? $stateArray->utmSource : '';
					$utmCampaign = isset($stateArray->utmCampaign) ? $stateArray->utmCampaign : '';
					$utmMedium = isset($stateArray->utmMedium) ? $stateArray->utmMedium : '';
					$adGroupID = isset($stateArray->adGroupID) ? $stateArray->adGroupID : '';
					$adContent = isset($stateArray->adContent) ? $stateArray->adContent : '';
					$utmTerm = isset($stateArray->utmTerm) ? $stateArray->utmTerm : '';
					$gclid = isset($stateArray->gclid) ? $stateArray->gclid : '';

					if ($mobile != '' && $mobile != false) {
						update_user_meta($user_id, 'yuno_gplus_mobile', $mobile);
					}
					if ($categoryURL != '' && $categoryURL != false) {
						update_user_meta($user_id, 'Category_URL_For_Signup', $categoryURL);
						$categoryURL = str_replace("/", "", $categoryURL);
					}
					if ($productCode != '' && $productCode != false) {
						update_user_meta($user_id, 'Yuno_Product_Code', $productCode);
					}
					if ($leadStatus != '' && $leadStatus != false) {
						update_user_meta($user_id, 'Yuno_Lead_Status', $leadStatus);
					}
					if ($variant != '' && $variant != false) {
						update_user_meta($user_id, 'Yuno_Variant', $variant);
					}
					if ($utmSource != '' && $utmSource != false) {
						update_user_meta($user_id, 'Yuno_UTM_Source', $utmSource);
					}
					if ($utmCampaign != '' && $utmCampaign != false) {
						update_user_meta($user_id, 'Yuno_UTM_Campaign', $utmCampaign);
					}
					if ($utmMedium != '' && $utmMedium != false) {
						update_user_meta($user_id, 'Yuno_UTM_Medium', $utmMedium);
					}
					if ($adGroupID != '' && $adGroupID != false) {
						update_user_meta($user_id, 'Yuno_Ad_Group_ID', $adGroupID);
					}
					if ($adContent != '' && $adContent != false) {
						update_user_meta($user_id, 'Yuno_Ad_Content', $adContent);
					}
					if ($utmTerm != '' && $utmTerm != false) {
						update_user_meta($user_id, 'Yuno_UTM_Term', $utmTerm);
					}
					if ($gclid != '' && $gclid != false) {
						update_user_meta($user_id, 'Yuno_GCLID', $gclid);
					}
				}
			 	$userLeadId = get_user_meta($user_id, 'zoho_lead_id', true);
		  		user_last_login_time($user_id, $userLeadId);
		}
	}
/**
 * To get instructor availability days slots
 */		
public function getNumbersRange($start,$end) {
	if($start=="24:00"){
    $first="1";
	}
	if($start=="00:30"){
		$first="2";
	}
	if($start=="01:00"){
		$first="3";
	}	
	if($start=="01:30"){
		$first="4";
	}
	if($start=="02:00"){
		$first="5";
	}
	if($start=="02:30"){
		$first="6";
	}
	if($start=="03:00"){
		$first="7";
	}	
	if($start=="03:30"){
		$first="8";
	}	
	if($start=="04:00"){
		$first="9";
	}	
	if($start=="04:30"){
		$first="10";
	}	
	if($start=="05:00"){
		$first="11";
	}	
	if($start=="05:30"){
		$first="12";
	}				
	if($start=="06:00"){
		$first="13";
	}	
	if($start=="06:30"){
		$first="14";
	}		
	if($start=="07:00"){
		$first="15";
	}	
	if($start=="07:30"){
		$first="16";
	}		
	if($start=="08:00"){
		$first="17";
	}	
	if($start=="08:30"){
		$first="18";
	}	
	if($start=="09:00"){
		$first="19";
	}		
	if($start=="09:30"){
		$first="20";
	}			
	if($start=="10:00"){
		$first="21";
	}		
	if($start=="10:30"){
		$first="22";
	}		
	if($start=="11:00"){
		$first="23";
	}		
	if($start=="11:30"){
		$first="24";
	}	
	if($start=="12:00"){
		$first="25";
	}	
	if($start=="12:30"){
		$first="26";
	}		
	if($start=="13:00"){
		$first="27";
	}	
	if($start=="13:30"){
		$first="28";
	}	
	if($start=="14:00"){
		$first="29";
	}		
	if($start=="14:30"){
		$first="30";
	}	
	if($start=="15:00"){
		$first="31";
	}	
	if($start=="15:30"){
		$first="32";
	}	
	if($start=="16:00"){
		$first="33";
	}	
	if($start=="16:30"){
		$first="34";
	}	
	if($start=="17:00"){
		$first="35";
	}		
	if($start=="17:30"){
		$first="36";
	}	
	if($start=="18:00"){
		$first="37";
	}	
	if($start=="18:30"){
		$first="38";
	}		
	if($start=="19:00"){
		$first="39";
	}		
	if($start=="19:30"){
		$first="40";
	}		
	if($start=="20:00"){
		$first="41";
	}	
	if($start=="20:30"){
		$first="42";
	}		
	if($start=="21:00"){
		$first="43";
	}		
	if($start=="21:30"){
		$first="44";
	}		
	if($start=="22:00"){
		$first="45";
	}			
	if($start=="22:30"){
		$first="46";
	}		
	if($start=="23:00"){
		$first="47";
	}		
	if($start=="23:30"){
		$first="48";
	}																	

	if($end=="24:00"){
		$second="1";
		}
		if($end=="00:30"){
			$second="2";
		}
		if($end=="01:00"){
			$second="3";
		}	
		if($end=="01:30"){
			$second="4";
		}
		if($end=="02:00"){
			$second="5";
		}
		if($end=="02:30"){
			$second="6";
		}
		if($end=="03:00"){
			$second="7";
		}	
		if($end=="03:30"){
			$second="8";
		}	
		if($end=="04:00"){
			$second="9";
		}	
		if($end=="04:30"){
			$second="10";
		}	
		if($end=="05:00"){
			$second="11";
		}	
		if($end=="05:30"){
			$second="12";
		}				
		if($end=="06:00"){
			$second="13";
		}	
		if($end=="06:30"){
			$second="14";
		}		
		if($end=="07:00"){
			$second="15";
		}	
		if($end=="07:30"){
			$second="16";
		}		
		if($end=="08:00"){
			$second="17";
		}	
		if($end=="08:30"){
			$second="18";
		}	
		if($end=="09:00"){
			$second="19";
		}		
		if($end=="09:30"){
			$second="20";
		}			
		if($end=="10:00"){
			$second="21";
		}		
		if($end=="10:30"){
			$second="22";
		}		
		if($end=="11:00"){
			$second="23";
		}		
		if($end=="11:30"){
			$second="24";
		}	
		if($end=="12:00"){
			$second="25";
		}	
		if($end=="12:30"){
			$second="26";
		}		
		if($end=="13:00"){
			$second="27";
		}	
		if($end=="13:30"){
			$second="28";
		}	
		if($end=="14:00"){
			$second="29";
		}		
		if($end=="14:30"){
			$second="30";
		}	
		if($end=="15:00"){
			$second="31";
		}	
		if($end=="15:30"){
			$second="32";
		}	
		if($end=="16:00"){
			$second="33";
		}	
		if($end=="16:30"){
			$second="34";
		}	
		if($end=="17:00"){
			$second="35";
		}		
		if($end=="17:30"){
			$second="36";
		}	
		if($end=="18:00"){
			$second="37";
		}	
		if($end=="18:30"){
			$second="38";
		}		
		if($end=="19:00"){
			$second="39";
		}		
		if($end=="19:30"){
			$second="40";
		}		
		if($end=="20:00"){
			$second="41";
		}	
		if($end=="20:30"){
			$second="42";
		}		
		if($end=="21:00"){
			$second="43";
		}		
		if($end=="21:30"){
			$second="44";
		}		
		if($end=="22:00"){
			$second="45";
		}			
		if($end=="22:30"){
			$second="46";
		}		
		if($end=="23:00"){
			$second="47";
		}		
		if($end=="23:30"){
			$second="48";
		}	
	return [$first,$second];
}
/**
 * To get instructor availability days slots
 */	
public function array_2d_to_1d ($input_array) {
    $output_array = array();

    for ($i = 0; $i < count($input_array); $i++) {
      for ($j = 0; $j < count($input_array[$i]); $j++) {
        $output_array[] = $input_array[$i][$j];
      }
    }

    return $output_array;
}
/**
 * To get instructor availability days slots
 */		
public function getInstructorAvailabilityPerDayHourSlots($request) {
	$instructor_id = (int)$request['instructor_id'];
	$codes=error_code_setting();
	global $wpdb;
	// $getInstructorAvailabilityPerDayHourSlotsCacheData = get_cache("getInstructorAvailabilityPerDayHourSlots");
	// if (!empty($getInstructorAvailabilityPerDayHourSlotsCacheData)) {
	// 	return new WP_REST_Response($getInstructorAvailabilityPerDayHourSlotsCacheData, 200); 
	// }
		$table_name_1 = $wpdb->prefix."instructors_availability_slots";
		$availabilitySlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."'");
		$daysOfWeek=["sun"=>"Sunday","mon"=>"Monday","tue"=>"Tuesday","wed"=>"Wednesday","thu"=>"Thursday","fri"=>"Friday","sat"=>"Saturday"];
		$results=$this->getInstructorAvailabilityHourSlots();
		$slot1=$results->data['data'];
		foreach ($slot1 as $key=>$v )
		{
		$slot1[$key] ['slot'] = $slot1[$key] ['label'];
		unset($slot1[$key]['label']);
		}

		if(count($availabilitySlots)==0) {

		$j=1;	
		foreach($daysOfWeek as $key=>$val){
			$k=0;
			for($i=1;$i<=48;$i++){
			//sun
			if($j==1){
				$slot1[$k][$key] =false;
				}
	
				//mon
					if($j==2){ 
					$slot1[$k][$key] =false;
					}
	
				//tue
					if($j==3){
					$slot1[$k][$key] =false;
					}
	
				//wed
					if($j==4){
					$slot1[$k][$key] =false;
					}	
	
				//thu
					if($j==5){
						$slot1[$k][$key]=false;
					}		
	
				//fri
					if($j==6){
						$slot1[$k][$key]=false;
					}
	
				//sat
					if($j==7){
						$slot1[$k][$key]=false;
					}				
				$k++;	}
			$j++;	}
		
			// table view
			$mappingData = [];
			$mappingData = [
							"rows" => $slot1,
							"columns" => [
										[
											"field" => "sun",
											"label" => "Sun",
											"sortable"=> true
										],
										[
											"field"=> "mon",
											"label" => "Mon",
											"sortable" => true
										],
										[
											"field" => "tue",
											"label" => "Tue",
											"sortable"=> true
										],
										[
											"field"=> "wed",
											"label" => "Wed",
											"sortable" => true
										],
										[
											"field" => "thu",
											"label" => "Thu",
											"sortable"=> true
										],
										[
											"field"=> "fri",
											"label" => "Fri",
											"sortable" => true
										],
										[
											"field" => "sat",
											"label" => "Sat",
											"sortable"=> true
										]
									]
						];
		$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => "data has been found", 'data' =>$mappingData );
		//add_cache('getInstructorAvailabilityPerDayHourSlots', $response, '', 3600);
		return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
		}

	$j=1;
	foreach($daysOfWeek as $key=>$val){
		$availablityArray=[];		
		$availabilitySlots = $wpdb->get_results("SELECT * FROM $table_name_1 where instructor_id='".$instructor_id."' and title='".$val."'");
		foreach($availabilitySlots as $availabilitySlot){
			if($availabilitySlot->status=='0'){
				$isSavedCase=false;		
			} else if($availabilitySlot->status=='2'){
				$isSavedCase=true;
			} else {
				$start=$availabilitySlot->start_time;
				$end=$availabilitySlot->end_time;
				$range=$this->getNumbersRange($start,$end);
				$availablityArray[]=range($range[0],$range[1]);
				$isSavedCase=2;		
			}
		}
		if(count($availabilitySlots)>1){
			$availablity=$this->array_2d_to_1d($availablityArray);
		} else {
			$availablity=$availablityArray[0];	
		}

		$k=0;
		for($i=1;$i<=48;$i++){
			if($isSavedCase===2){	
				if(in_array($i, $availablity, true )){
					$isSaved=true;
				} else { 
					$isSaved=false;		
				}
			} else {
				if($isSavedCase){
					$isSaved=true;
				} else {
					$isSaved=false;
				}
			}

			//sun
			if($j==1){
				$slot1[$k][$key] =$isSaved;
			}

			//mon
				if($j==2){ 
				$slot1[$k][$key] =$isSaved;
				}

			//tue
				if($j==3){
				$slot1[$k][$key] =$isSaved;
				}

			//wed
				if($j==4){
				$slot1[$k][$key] =$isSaved;
				}	

			//thu
				if($j==5){
					$slot1[$k][$key]=$isSaved;
				}		

			//fri
				if($j==6){
					$slot1[$k][$key]=$isSaved;
				}

			//sat
				if($j==7){
					$slot1[$k][$key]=$isSaved;
				}
	
 			  				  
		$k++;}       
	$j++;
	}

	$data=$slot1;
		// table view
		$mappingData = [];
		$mappingData = [
					  "rows" => $data,
					  "columns" => [
									[
										"field" => "sun",
										"label" => "Sun",
										"sortable"=> true
									],
									[
										"field"=> "mon",
										"label" => "Mon",
										"sortable" => true
									],
									[
										"field" => "tue",
										"label" => "Tue",
										"sortable"=> true
									],
									[
										"field"=> "wed",
										"label" => "Wed",
										"sortable" => true
									],
									[
										"field" => "thu",
										"label" => "Thu",
										"sortable"=> true
									],
									[
										"field"=> "fri",
										"label" => "Fri",
										"sortable" => true
									],
									[
										"field" => "sat",
										"label" => "Sat",
										"sortable"=> true
									]
								]
					];

	$response = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'data has been found', 'data' => $mappingData);
	//add_cache('getInstructorAvailabilityPerDayHourSlots', $response, '', 3600);
	return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
}
/**
 * To get instructor availability days slots
 */		
	public function getInstructorAvailabilityPerDayHourSlots_permissions_check() {
    	return true;
    }			

	public function new_user_register_permissions_check() {
		return true;
	}

	/**
	 * User logout from android
	 * We can use this api for web logout if needed
	 */
	public function user_logout($request) {
		$codes=error_code_setting();
		$user_id = (int)$request['user_id'];
		update_user_meta($user_id, 'yuno_user_id_token', '');
		$response = array('code' => $codes["GET_SUCCESS"]["code"], 
    					'message' => 'User logout successfully', 
    					'data' => array('status' => $codes["GET_SUCCESS"]["code"]));
		return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
	}

	/**
	 * Return token expiry time
	 */
	public function user_token_expire_time($request) {
		global $wpdb;
		$codes=error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		track_logs(time(), "user_token_expire_time", '', time(), "begining", 'user');
		$data = json_decode($request->get_body(), true);
		$userID = (int)$data['userID'];

		$token = $data['token'];
		$findBearer = 'Bearer';
		$position = strpos($token, $findBearer);

		if ($position === false) {
		    $idToken = $token;
		} else {
			list($bearer, $idToken) = explode(" ", $data['token']);		    
		}

    	$currentTime = time();
    	$result = $wpdb->get_row("SELECT token_expiry from wp_user_tokens where 
    		user_id=$userID and id_token='".$idToken."'", ARRAY_A);
    	if (empty($result)) {
    		$result = $wpdb->get_row("SELECT token_expiry from wp_user_tokens where 
    		user_id=$userID and resource='WEB' order by id desc limit 1", ARRAY_A);
    	}
    	$expiryTime = $result['token_expiry'];
    	$difference = $expiryTime - $currentTime;
		$differenceInMinutes = $difference/60;
		if ($differenceInMinutes < 0) {
			$differenceInMinutes = 0;
		}

		/*$differenceInMinutes = (int)$differenceInMinutes - 48;
		if ($differenceInMinutes < 0 ) {
			$differenceInMinutes = 11;
		}*/
		$result = [
			"minutes" => (int)$differenceInMinutes
			//"minutes" => 12
		];
		$response = array('code' => $codes["POST_INSERT"]["code"], 
    					'message' => 'Token will expire in a few minutes', 
    					'data' => $result);
		track_logs(time(), "user_token_expire_time", '', time(), "end", 'user');
		return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]);
	}

	public function user_token_expire_time_permissions_check() {
		return true;
	}

	/**
	 * getting google id token
	 */
	public function generate_google_id_token($request) {
		global $wpdb;
		$codes=error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		$currentTime = time();
		$data = json_decode($request->get_body(), true);
		$userId = $data['user_id'];
		list($bearer, $idToken) = explode(" ", $data['id_token']);
		$resultData = $wpdb->get_row("SELECT id_token, token_expiry from wp_user_tokens where 
    		user_id=$userId and resource='WEB' order by id desc limit 1", ARRAY_A);
    	if (!empty($resultData)) {
    		$expiryTime = $resultData['token_expiry'];
	    	$difference = $expiryTime - $currentTime;
			$differenceInMinutes = $difference/60;
			if ($differenceInMinutes > 11) {
				//$id_token = $resultData['id_token'];
				$id_token = get_user_meta($userId, "CURRENT_USER_JWT_TOKEN", true);
				$result = array('code' => $codes["POST_INSERT"]["code"], 
					'message' => 'Token created successfully', 
					"data" => ["token" => $id_token]);
				return new WP_REST_Response($result, $codes["POST_INSERT"]["code"]);
			}	
    	}
		/*$dataRefreshToken = $wpdb->get_row("SELECT refresh_token from wp_user_tokens 
				where user_id=$userId 
				and id_token = '".$idToken."' 
				and refresh_token <> '' 
				and resource='WEB'  
				order by id desc limit 1", ARRAY_A);*/
		$dataRefreshToken = $wpdb->get_row("SELECT refresh_token, auth_code from wp_user_tokens 
				where user_id=$userId 
				and refresh_token <> '' 
				and resource='WEB'  
				order by id desc limit 1", ARRAY_A);
		$auth_code = isset($dataRefreshToken['auth_code']) ? $dataRefreshToken['auth_code'] : '';
		if (empty($dataRefreshToken)) {
			$refreshToken = get_user_meta($userId, 'yuno_user_refresh_token', true);
			if (empty($refreshToken)) {
				return new WP_Error(204, "Refresh token missing", array('status' => 'FAIL'));
			}
		} else {
			$refreshToken = $dataRefreshToken['refresh_token'];
		}

		$options = get_option('yuno_google_login_options');
		$client_id = $options['yuno_google_login_client_id'];
		$client_secret = $options['yuno_google_login_client_secret'];
		$postData = 'refresh_token='.$refreshToken.'&grant_type=refresh_token&client_id='.$client_id.'&client_secret='.$client_secret;

		$curl = curl_init();

		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://www.googleapis.com/oauth2/v4/token',
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'POST',
		  CURLOPT_POSTFIELDS => $postData,
		  CURLOPT_HTTPHEADER => array(
		    'Content-Type: Application/X-www-Form-Urlencoded'
		  ),
		));

		$response = curl_exec($curl);
		$err = curl_error($curl);
		if ($err) {
			return new WP_Error(204, "curl fail", array('status' => 'FAIL'));    
		}
		$result = json_decode($response);
		error_log("step 1: 5922: ".date('Y-m-d H:i:s').", post data: ".json_encode($postData)." === ".json_encode($result). "\n\n", 3, ABSPATH.'error-logs/google_refresh.log');
		$access_token = isset($result->access_token) ? $result->access_token : '';
		$id_token = isset($result->id_token) ? $result->id_token : '';
		if (empty($id_token)) {
			return new WP_Error(204, "Data not inserted: $userId: ".$response, array('status' => 'FAIL'));    
		}
		$tokenExists = $wpdb->get_row("SELECT id from wp_user_tokens where 
    		user_id=$userId and resource='WEB' order by id desc limit 1", ARRAY_A);
		if (!empty($tokenExists)) {
			// update
			$updated = [
				"id_token" => $id_token,
				"access_token" => $access_token,
				"refresh_token" => $refreshToken,
				"auth_code" => $auth_code,
				"token_expiry" => strtotime("+1 hour")
			];	
			$updatedRows = $wpdb->update(
				'wp_user_tokens', 
				$updated,
				['id' => $tokenExists['id']]
			);
		} else {
			// insert
			$tokenData = [
				"id_token" => $id_token,
				"access_token" => $access_token,
				"refresh_token" => $refreshToken,
				"token_expiry" => strtotime("+1 hour"),
				"auth_code" => $auth_code,
				"user_id" => $userId,
				"resource" => "WEB"
			];
			$tokenTable = "wp_user_tokens";
			$wpdb->insert($tokenTable, $tokenData);
		}
		curl_close($curl);
		$id_token = get_user_meta($userId, "CURRENT_USER_JWT_TOKEN", true);
		$result = array('code' => $codes["POST_INSERT"]["code"], 
					'message' => 'Token created successfully', 
					"data" => ["token" => $id_token]);
		return new WP_REST_Response($result, $codes["POST_INSERT"]["code"]);
	}

	public function generate_google_refresh_token_permissions_check() {
		return true;
	}
	/**
	 * User permission from web
	 * We can use this api for user permission on web
	 */	
	public function user_permission($request) {
		global $wpdb;
		$codes=error_code_setting();
		// $userPermissionCacheData = get_cache("user_permission");
		// if (!empty($userPermissionCacheData)) {
		// 	return new WP_REST_Response($userPermissionCacheData, 200); 
		// }
    	$userId = (int)$request['userId'];
    	if ($userId > 0) {} else {
			return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));    
    	}

    	$userdata = get_userdata($userId);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"])); 
		}		

		$roles = $userdata->roles ? $userdata->roles : '';

    	if ($userdata->roles) {
			if (in_array('um_instructor', $userdata->roles)) {
				$current_role = 'Instructor';
				$caps=get_role('um_instructor')->capabilities;
				if(count($caps)>=1 && $caps[0]!="read"){
				$capability=$caps;	
				} else {
				$capability=[];	
				}			
			} else if(in_array('um_counselor', $userdata->roles)) {
				$current_role = 'Counselor';
				$caps=get_role('um_counselor')->capabilities;
				if(count($caps)>=1 && $caps[0]!="read"){
					$capability=$caps;	
					} else {
					$capability=[];	
					}			
			} else if (in_array('um_yuno-admin', $userdata->roles)) {
				$current_role = 'yuno-admin';
				$caps=get_role('um_yuno-admin')->capabilities;
				if(count($caps)>=1 && $caps[0]!="read"){
					$capability=$caps;	
					} else {
					$capability=[];	
					}
				
			} else if (in_array('um_content-admin', $userdata->roles)) {
				$current_role = 'content-admin';
				$caps=get_role('um_content-admin')->capabilities;
				if(count($caps)>=1 && $caps[0]!="read"){
					$capability=$caps;	
					} else {
					$capability=[];	
					}							
			} else if (in_array('um_yuno-category-admin', $userdata->roles)) {
				$current_role = 'yuno-category-admin';
				$caps=get_role('um_yuno-category-admin')->capabilities;
				if(count($caps)>=1 && $caps[0]!="read"){
					$capability=$caps;	
					} else {
					$capability=[];	
					}	 				
			} else {
				$current_role = 'Learner';
				$capability=[];	
			}
		}	

    	$response = array('code' => $codes["GET_SUCCESS"]["code"], 
    					'message' => 'User forte are found', 
    					"data" => $capability);
    	//add_cache('user_permission', $response, '', 3600);
		return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);
	}
	public function user_permission_permissions_check() {
		return true;
	}	
	/**
	 * User notification settings from web
	 * We can use this api for notification settings on web
	 */	
	public function user_notificationsettings($request) {
		global $wpdb;
		$codes=error_code_setting();
		// $userNotificationsettingsCacheData = get_cache("user_notificationsettings");
		// if (!empty($userNotificationsettingsCacheData)) {
		// 	return new WP_REST_Response($userNotificationsettingsCacheData, 200); 
		// }
    	$userId = (int)$request['user_id'];
    	if ($userId > 0) {} else {
			return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"])); 
    	}

    	$userdata = get_userdata($userId);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
		}		

		$roles = $userdata->roles ? $userdata->roles : '';

    	if ($userdata->roles) {
			//if (in_array('um_instructor', $userdata->roles)) {
			$rowExist = $wpdb->get_row("SELECT * from wp_notification_sms_flag where user_id=$userId and event_id=5");
			if(empty($rowExist->created_at)){
				$created_at="";
			} else {
				
				$created_at = date("n/d/Y, h:i:s A",strtotime($rowExist->created_at));
			}
			if($rowExist->whatsapp=="true"){
				$whatsapp=true;
			} else {
				$whatsapp = false;
			}
			if(empty($rowExist->updatedAt)){
				$updatedAt="";
			} else {
				$updatedAt = date("n/d/Y, h:i:s A",strtotime($rowExist->updatedAt));
			}
			if($rowExist->voice=="true"){
				$voice=true;
			} else {
				$voice = false;
			}
			if($rowExist->mobile_push=="true"){
				$mobile_push = true;
			} else {
				$mobile_push = false;
			}
			if($rowExist->desktop_push=="true"){
				$desktop_push=true;
			} else {
				$desktop_push = false;
			}
			if($rowExist->sms=="true"){
				$sms=true;
			} else {
				$sms = false;
			}
			if($rowExist->email=="true"){
				$email=true;
			} else {
				$email = false;
			}
			if($rowExist){
			$data=["user_id"=>$rowExist->user_id,
			"created_at"=>$created_at,
			"whatsapp"=>$whatsapp,
			"updatedAt"=>$updatedAt,
			"voice"=>$voice,
			"mobile_push"=>$mobile_push,
			"event_id"=>$rowExist->event_id,
			"desktop_push"=>$desktop_push,
			"sms"=>$sms,
			"email"=>$email,
			"id"=>$rowExist->id
		];					
				$response = array('code' => $codes["GET_SUCCESS"]["code"], 
				'message' => 'Result found', 
				"data" => $data);
			//add_cache('user_notificationsettings', $response, '', 3600);
			return new WP_REST_Response($response, $codes["GET_SUCCESS"]["code"]);				
			} else {
			return new WP_Error($codes["GET_FAIL"]["code"], "We couldn't find notification setting", array('status' => $codes["GET_FAIL"]["status"]));
			}
		//}	
	} else {
		return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
	}
	}
	/**
	 * User notification settings from web
	 * We can use this api for notification settings on web
	 */	
	public function update_user_notificationsettings($request) {
		global $wpdb;
		$codes=error_code_setting();
		$date = date_default_timezone_set('Asia/Kolkata');
		$table = $wpdb->prefix."notification_sms_flag";
		$updatedAt = date('Y-m-d H:i:s');  
		$request = json_decode($request->get_body(), true);
		$user_id = $request['user_id'];
		$email = $request['email'];
		$whatsapp = $request['whatsapp'];
		$push = true;//isset($request['push']) ? $request['push'] : '';

		if($email){
			$email_push="true";
		} else {
			$email_push = "false";
		}
		if($whatsapp){
			$whatsapp_push="true";
		} else {
			$whatsapp_push = "false";
		}
		if($push){
			$mobile_push = "true";
		} else {
			$mobile_push = "false";
		}
		$isRecordExists = $wpdb->get_results("SELECT id from $table WHERE user_id ='".$user_id."'");
			if(count($isRecordExists)>=1){
				$updated = [
					"whatsapp" => $whatsapp_push,
					"email" => $email_push,
					"mobile_push" => $mobile_push,
					"updatedAt" => $updatedAt
				];	
				$updatedRows = $wpdb->update($table, $updated,['user_id'=>$user_id]);
				if($updatedRows){
					delete_cache('user_notificationsettings', '', [$user_id]);
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => 'User Settings Updated');
					  return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]); 	
				} else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User Settings not Updated', array('status' => $codes["POST_INSERT_FAIL"]["code"]));			
				}
			} else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User Settings not Updated', array('status' => $codes["POST_INSERT_FAIL"]["code"]));			
			}
	}	

	/**
	 * Getting user info, this function will work for the users having role 
	 * instructor, yuno admin, category admin
	 * this function is calling from invitation url
	 */
	function get_user_info($request) {
		$codes=error_code_setting();
		$user_encoded_info = $request['user_encoded_info'];
		$user_decoded_info = base64_decode($user_encoded_info);
		list($email, $user_id) = explode("@@@", $user_decoded_info); 
		$userdata = get_userdata($user_id);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
		}
		
		$user_detail = array(
			'first_name' => get_user_meta($user_id, 'yuno_first_name', true),
		 	'last_name' => get_user_meta($user_id, 'yuno_last_name', true),
			'profile_img' => get_user_meta($user_id, 'googleplus_profile_img', true),
			'experience' => get_user_meta($user_id, 'Instructor_Experience', true)." Year(s)"
		);		
		$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User detail found', 'data' => $user_detail);
		return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]); 
	}

	public function get_user_info_permissions_check() {
		return true;
	}

	/**
	 * In case of invitation link, after login we need to get category 
	 * in which learner is intersted, so need to save category, so next time 
	 * whenever user visit to the website, then go on proper signup page 
	 * according to the category if he/she doesn't fill the signup form in last visit.
	 */
	function update_user_category($request) {
		$data = json_decode($request->get_body(), true);
		$codes=error_code_setting();
		$userId = (int)$data['userId'];
		$category = $data['category'];
		$userdata = get_userdata($userId);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
		} else if ($category == '') {
			return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Category should not be empty', array('status' => 'Invalid'));
		}
		update_user_meta($userId, 'Category_URL_For_Signup', $category);
		$response = array('code' => $codes["POST_INSERT"]["code"], 
    					'message' => 'Category inserted successfully', 
    					"data" => array('status' => 'Inserted'));
		return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]);
	}

	/**
	 * Create token for logged in user
	 */
	public function create_jwt_token($request) {
		date_default_timezone_set('Asia/Calcutta');
		$codes=error_code_setting();
		$user_id = $request['user_id'];
		//get user data - username and password
		$user_data = get_userdata($user_id);
		$username = $user_data->user_login;
	    $password = $user_data->user_pass;
		$issuedAt = time();
        $expirationTime = $issuedAt + (7 * 24 * 60 * 60);  // Token expires in 7 days
        $payload = array(
            'iss' => get_bloginfo('url'),  // Issuer
            'iat' => $issuedAt,            // Issued at
            'exp' => $expirationTime,      // Expiration time
            'userId' => $user_id,
        );
      
        $secretKey = JWT_AUTH_SECRET_KEY;
        $jwt = \Firebase\JWT\JWT::encode($payload, $secretKey, 'HS256');
        $response = ["token" => $jwt, "user_email" => $user_data->user_email, "user_nicename" => $user_data->user_nicename,"user_display_name" => $user_data->display_name];
	    return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]);
	}

	public function create_jwt_token_permissions_check() {
		return true;
	}

	public function get_jwt_token_permissions_check() {
		return true;
	}
	/**
	 * Getting user token, this function will work for the users having role 
	 * instructor, yuno admin, category admin
	 * this function is calling from api.yunolearning.com url
	 */
	function get_jwt_token($request) {
		$codes=error_code_setting();
		$user_id = (int)$request['user_id'];
    	if ($user_id > 0) {
		} else {
			return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));			
    	}

    	$userdata = get_userdata($user_id);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));			
		}	
		if ($userdata->roles) {
			$token = get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true);
			$response = validate_jwt_token($token);
			if ($response["code"] == "jwt_auth_valid_token") {
				$user_detail = array(
					'token' => $token
				);	
			}
			elseif ($response["code"] == "jwt_auth_invalid_token") {
				create_jwt_token($user_id);
				$user_detail = array(
					'token' => get_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', true)
				);	
			}
	
			$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User detail found', 'data' => $user_detail);
			return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
		} else {
			return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
		}		 
	}

	public function update_user_active_category($request)
	{
		global $wpdb;
		$data = json_decode($request->get_body(), true);
		$codes=error_code_setting();
		$userId = (int)$data['user_id'];
		$category = $data['active_category']; // it should be slug
		$userdata = get_userdata($userId);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
		} else if ($category == '') {
			return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Category should not be empty', array('status' => 'Invalid'));
		}
		update_user_meta($userId, 'active_category', $category);
		$response = array('code' => $codes["POST_INSERT"]["code"], 
    					'message' => 'Category inserted successfully', 
    					"data" => array('status' => 'Inserted'));
		return new WP_REST_Response($response, $codes["POST_INSERT"]["code"]);
	}

	public function update_user_active_category_permissions_check()
	{
		return true;
	}

	public function post_user_delete_requests($request)
	{
		global $wpdb;
		date_default_timezone_set('Asia/Kolkata');
		$data = json_decode($request->get_body(), true);
		$codes=error_code_setting();
		$user_id = (int)$data['id'];
		$user_email = $data['email'];
		$user_role = $data['role'];
		$reason = $data['reason'];
		$comment = $data['comment'];
		$userdata = get_userdata($user_id);

		// Validate user ID
        if(empty($user_id)) {
          return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'User should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"])); 
        }

		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
		}

		// Get current timestamp for the request
        $requested_datetime = date("Y-m-d H:i:s");

        // Create a new post (del_user_req)
        $post = array();
        $post['post_status']   = 'publish';
        $post['post_type']     = 'del_user_req';
        $post['post_title']    = "Delete User Request";
        $post['post_author']   = 2;
        $post_id = wp_insert_post( $post );
        $message = "Your request has been submitted";
        $code = $codes["POST_INSERT"]["code"];

        // Update post metadata
        update_post_meta($post_id, 'user_id', $user_id);
        update_post_meta($post_id, 'user_role', $user_role);
        update_post_meta($post_id, 'reason', $reason);
        update_post_meta($post_id, 'comment', $comment);
        update_post_meta($post_id, 'request_datetime', $requested_datetime);

        if ($post_id > 0) {
            $result = array(
                'code' => $code,
                'message' => $message,
                'data' => array('status' => $code, 'id' => $post_id)
            );
            return new WP_REST_Response($result, 200);
        } else {
            return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
        }
	}

	public function get_user_delete_requests($request)
	{
		global $wpdb;
		$codes=error_code_setting();
		$role = $request['role'];

		// Retrieve del_user_req details
        $args = array(
            'post_type'      => 'del_user_req',
            'post_status'    => array('publish')
        ); 
        $the_query = new WP_Query($args);
        $requests = [];

        if ($the_query->have_posts()) { 
            while ($the_query->have_posts()) { 
                $the_query->the_post();
                $post_id = get_the_ID();

                $user_id = get_post_meta($post_id, 'user_id', true);
                $reason = get_post_meta($post_id, 'reason', true);
                $comment = get_post_meta($post_id, 'comment', true);
                $request_datetime = get_post_meta($post_id, 'request_datetime', true);

                $requests[] = [
                    "id" => $post_id,
                    "user_id" => $user_id,
                    "reason" => $reason,
                    "comment" => $comment,
                    "request_datetime" => $request_datetime
                ];          
            }
        }

        if (empty($requests)) {
            return new WP_Error($codes["GET_FAIL"]["code"], str_replace("[Module_Name]", "Delete Users Requests", $codes["GET_FAIL"]["message"]), array('status' => $codes["GET_FAIL"]["status"]));
        }
        $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Delete Users Requests", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'data' => $requests);
        return new WP_REST_Response($result, 200);

	}

	public function get_user_delete_requests_permissions_check()
	{
		return true;
	}

	public function get_user_orgs(){
       
        $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/".YN_ES_PREFIX."org/_search?size=" . ELASTIC_RECORDS_COUNT;
        $headers = [
            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache"
        ];
        $curlPost = "";
        $response = Utility::curl_request($elasticsearch_url, 'GET', $curlPost, $headers, ELASTIC_SEARCH_PORT);

        $results = json_decode($response['response']);
       // Initialize an empty array to hold the formatted data
        $items = [];
        // Check if 'hits' and 'hits' array exists in the response
        if (isset($results->hits->hits) && is_array($results->hits->hits)) {
			
            foreach ($results->hits->hits as $hit) {
                // Extracting organisation_name and record_id
                $organisation_name = $hit->_source->data->details->organisation_name;
                $record_id = $hit->_source->data->details->record_id;

                // Create an associative array with label and slug
                $options = [
                    'label' => $organisation_name,
                    'slug' => $record_id,
					'filter' => "org",
					"is_checked"=> false
                ];

                // Add the options array to the items array
                $items[] = $options;
            }
            return $items;
        }

    }

    public function get_user_academies($org = null){
        
        $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/".YN_ES_PREFIX."academies/_search?size=" . ELASTIC_RECORDS_COUNT;
        $headers = [
            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache"
        ];
		if($org){
			$must[] = [
				"terms" => [
					"data.details.org_id" => $org
				]
			];
			$curlPost = [
				"query" => [
					"bool" => [
						"must" => $must
					]
				]
			];
			$method = "POST";
		}else{
			$method = "GET";
			$curlPost = "";
			
		}
		$response = Utility::curl_request($elasticsearch_url, $method, $curlPost, $headers, ELASTIC_SEARCH_PORT);
        $results = json_decode($response['response']);
        $items = [];
		
        // Check if 'hits' and 'hits' array exists in the response
        if (isset($results->hits->hits) && is_array($results->hits->hits)) {
			
            foreach ($results->hits->hits as $hit) {
                // Extracting organisation_name and record_id
                $organisation_name = $hit->_source->data->details->academy_name;
                $record_id = $hit->_source->data->details->record_id;

                // Create an associative array with label and slug
                $options = [
                    'label' => $organisation_name,
                    'slug' => $record_id,
					'filter' => "academy",
					"is_checked"=> false
                ];

                // Add the options array to the items array
                $items[] = $options;
            }
            return $items;
        }

    
    }
	public function get_demo_requests($request)
	{
		// Retrieve parameters from the request object.
		global $Utility;
		$data = $request->get_params();
		$codes = error_code_setting();
		

		$Utility->Add_logger($logtype = "info", $module = "UserController", $action_name = "get_demo_requests", $message = "start", $request);
		// Assign a specific case identifier for the operation, useful for conditional processing later.
		$data['case'] = "get_demo_requests";
        $data['role'] = "learner";
		// Define all possible filters that can be applied to demo requests.
		$filters = ["time_of_study", "cta", "category", "course", "info-available", "demo_request_period", "yl_campaign", "yl_medium", "yl_keyword", "yl_lead_source", "yl_ad_content", "yl_ad_group"];
		if($request['user_id']){
			$user_id = (int) $request['user_id'];
		}else{
			$user_id = CURRENT_LOGGED_IN_USER_ID;
		}
		$userData = get_userdata($user_id);
        			$user_roles = $userData->roles;
					$allowed_roles = ["um_yuno-admin", "um_org-admin"];
					if (empty($user_roles) || array_diff($user_roles, $allowed_roles)) {
						return new WP_Error($codes["USER_FAIL"]["code"], "You are not authorised to view this.", array('status' => $codes["USER_FAIL"]["status"]));
					}

				if(isset($data['org_id']) && !empty($data['org_id']) && in_array('um_org-admin', $user_roles)){

					
					if($user_id > 0){
						$orgs = get_user_meta($user_id, 'organisation', true);
						if(empty($orgs) && in_array($data['org_id'], $orgs)){
							return new WP_Error($codes["USER_FAIL"]["code"], "You are not part of this organisation.", array('status' => $codes["USER_FAIL"]["status"]));
						}else{
							
							$filters[] = "academy";
							$data['filter']['academy'] = get_post_meta($data['org_id'], 'academies', true);
							$data['cat_filters_academy']['academy'] = get_post_meta($data['org_id'], 'academies', true);
						}
					}
					
					
				}elseif(in_array('um_yuno-admin', $user_roles)){
					
					$filters[] ="org";
					$filters[] = "academy";
					$data['filter']['org'] = $this->get_user_orgs();
					if($data['org'] && $data['org'][0] != "all"){

						$data['filter']['academy'] = $this->get_user_academies($data['org']);
					}else{
						$data['filter']['academy'] = $this->get_user_academies();
					}
					if(empty($data['academy']) || $data['academy'][0] == 'all'){
						$data['cat_filters_academy']['academy'] = get_post_meta($data['org'], 'academies', true);
					}elseif(!empty($data['academy']) || $data['academy'][0] != 'all'){
						$data['cat_filters_academy']['academy'] = $data['academy'];
					}
				}
				
		// Categorize filters for specific processing needs.
		$root_filters_keys = ["time_of_study", "role"]; // Filters that apply at the top-level query.
		$cta_keys = ["cta"]; // Filters related to call-to-action.
		$cat_filters_keys = ["category", "course"]; // Category-specific filters.
		$utm_filters_keys = ["yl_campaign", "yl_medium", "yl_keyword", "yl_lead_source", "yl_ad_content", "yl_ad_group"]; // Marketing-related filters (UTM parameters).

		// Explicitly declare that these are the filters to be applied.
		$data['filters'] = $filters;

		// Define possible values for each filter to validate or map request data.
		$date_ranges = ["demo_request_period"];
		$time_of_study = ["morning", "afternoon", "evening", "night"];
		$created = ["all", "180-days", "90-days", "30-days", "7-days", "24-hours"];
		// $created = ["all", "180-days", "90-days", "30-days", "7-days", "24-hours"];
		$categories = ["all", "ielts", "pte", "english-speaking", "toefl", "duolingo", "french","soft-skills","design","artificial-intelligence", "data-science-and-analytics", "coding-for-kids", "anil-lamba-on-finance", "microsoft-excel", "python-programming", "data-science-and-analytics", "sat", "gre", "cuet", "gmat", "german", "spanish", "italian", "hindi", "neet", "jee", "vedic-maths"];
		$cta = ["all", "book_a_demo"];
		$info_available = ["whatsapp", "phone", "email"];

		// Assign filter options back to the data array for use in filtering logic.
		$data['filter']['time_of_study'] = $time_of_study;
		$data['filter']['demo_request_period'] = $created;
		$data['filter']['category'] = $categories;
		$data['filter']['cta'] = $cta;
		$data['filter']['info-available'] = $info_available;

		// Iterate over each filter in the data array to apply specific logic and categorize filters.
		foreach ($data as $key => $value) {
			// Handle 'info-available' filter to set communication preferences.
			if ($key == "info-available") {
				foreach ($value as $v) {
					if (strtolower($v) == "all") {
						// If 'All' is selected, enable all communication methods.
						$data['user_filters']['phone'] = true;
						$data['user_filters']['email'] = true;
						$data['basic_details_filters']['yuno_user_whatsapp_check'] = true;
						break; // Exit the loop once handled.
					} else {
						// Apply specific filters based on the communication method.
						if ($v == "whatsapp") {
							$data['basic_details_filters']['yuno_user_whatsapp_check'] = true;
						} else {
							$data['user_filters'][$v] = true;
						}
					}
				}
			}
			// Reset 'All' value to an empty string for processing.
			if (!is_array($value) && strtolower($value) == "all") {
				$value = "";
				continue;
			}

			if ($key == "org" || $key == "academy" && is_array($value) && $value[0] == "all") {
				$value = "";
				continue;
			}

			// Categorize each filter based on predefined categories for more specific query building.
			if (in_array($key, $root_filters_keys)) {
				$data['root_filters'][$key] = $value;
			} elseif (in_array($key, $cat_filters_keys)) {
				$data['cat_filters'][$key] = $value;
			} elseif (in_array($key, $utm_filters_keys)) {
				$data['utm_filters'][$key] = $value;
			} elseif (in_array($key, $cta_keys)) {
				$data['cta_filters'][$key] = $value;
			} elseif (in_array($key, $date_ranges)) {
				// Convert '24-hours' to '1-day' for consistency and compute the date range.
				if ($value == "24-hours") {
					$value = "1-days";
				}
				if ($value != "" && $value !== "all") {
					$val = str_replace('-days', ' days', $value);
					$val = date("Y-m-d H:i:s", strtotime("-{$val}", strtotime(date("Y-m-d H:i:s"))));
				} else {
					$val = "";
				}
				// Apply the computed date range filter.
				if ($key == "demo_request_period") {
					$data['ranges']["updated_at"] = $val;
				}
			}
		}

		// Instantiate the UserElasticSearch class and fetch demo requests based on the processed filters.
		$UserElasticSearch = new UserElasticSearch;
		$demo_requests = $UserElasticSearch->get_learner_demo_requests($data);
		$Utility->Add_logger($logtype = "info", $module = "UserController", $action_name = "get_demo_requests", $message = "end", $demo_requests);
		// Output the fetched demo requests as JSON.
		
		return new WP_REST_Response($demo_requests, 200);
	}

    public function get_demo_request($request)
	{
		
		// $data = $request->get_params();
		$learner_id = $request['learnerId'];
		if($request['org_id']){
			$UserElasticSearch  = new UserElasticSearch;
		$demo_request = $UserElasticSearch->get_learner_single_demo_requests($learner_id, $request['org_id']);
		}else{
			$UserElasticSearch  = new UserElasticSearch;
		$demo_request = $UserElasticSearch->get_learner_single_demo_requests($learner_id);
		}

		
		return new WP_REST_Response($demo_request, 200);
	}


	public function get_mylearners_instructor($request) {
		
		global $wpdb;
		$codes = error_code_setting();
		$instructorId = $request['instructorId'];
		$view = $request['view'];
		$limit = $request['limit'];
		$offset = $request['offset'];
	
		$typeCollectionArray = array("list-view", "grid-view");
		if (!in_array($view, $typeCollectionArray)) {
			return new WP_Error($codes["GET_FAIL"]["code"], 'Please check the view type', array('status' => $codes["GET_FAIL"]["status"]));
		}
	
		$must = [
			[
				"nested" => [
					"path" => "data.details",
					"query" => [
						"bool" => [
							"must" => [
								[
									"term" => [
										"data.details.role" => "learner"
									]
								],
								[
									"terms" => [
										"data.details.my_instructors" => [$instructorId]
									]
								]
							]
						]
					]
				]
			]
		];
	
		$curlPost = [
			"_source" => [
				"data.details.user_id",
				"data.details.user.name",
				"data.details.user.email",
				"data.details.role",
				"data.details.location.city",
				"data.details.location.country",
				"data.details.basic_details.registration_date",
				"data.details.basic_details.last_login_time",
				"data.details.user.image",
				"data.details.time_of_study",
				"@timestamp"
			],
			"query" => [
				"bool" => [
					"must" => $must
				]
			],
			"size" => $limit,
			"from" => $offset
		];
	
		$curl = curl_init();
		curl_setopt_array($curl, array(
			CURLOPT_PORT => ELASTIC_SEARCH_PORT,
			CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/".YN_ES_PREFIX."signedup/_search",
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => "",
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 30,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => "GET",
			CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
			CURLOPT_HTTPHEADER => array(
				"authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
				"cache-control: no-cache",
				"content-type: application/json"
			),
		));
		$response = curl_exec($curl);
		curl_close($curl);
		$res = json_decode($response, true);
	
		$learners = [];
		if (isset($res['hits']['hits'])) {
			foreach ($res['hits']['hits'] as $hit) {
				$details = $hit['_source']['data']['details'];
				$user = $details['user'];
				$basicDetails = $details['basic_details'];
				$location = $details['location'] ?? [];
	
				$learners[] = [
					'learner' => [
						'name' => $user['name'],
						'image' => $user['image'],
						'user_id' => $details['user_id'],
						'email' => $user['email']
					],
					'time_of_study' => $details['time_of_study'] ?? "",
					'created_time' => Date('M d, Y, h:i A', strtotime($hit['_source']['@timestamp'])),
					'last_login' => Date('M d, Y, h:i A', strtotime($basicDetails['last_login_time'])),
					'country' => $location['country'] ?? "",
					'state' => $location['state'] ?? "",
					'city' => $location['city'] ?? "",
					'is_active' => false,
					'signedup_on' => Date('Y-m-d H:i:s', strtotime($basicDetails['registration_date'])),
					'name_email' => $user['name'] . ' (' . $user['email'] . ')'
				];
			}
		}
	
		$total_records = $res['hits']['total']['value'];
		$learners = array_slice($learners, $offset, $limit);
	
		$columns = [
			[
				"field" => "created_time",
				"label" => "Created Time"
			],
			[
				"field" => "last_login",
				"label" => "Last Login"
			],
			[
				"field" => "country",
				"label" => "Country"
			],
			[
				"field" => "state",
				"label" => "State"
			],
			[
				"field" => "city",
				"label" => "City"
			],
			[
				"field" => "is_active",
				"label" => "Is Active"
			]
		];
	
		$learnerData = [
			"rows" => $learners,
			"columns" => $columns
		];
	
		if (empty($learners)) {
			return new WP_Error($codes["GET_FAIL"]["code"], 'Learners Not Found', array('status' => $codes["GET_FAIL"]["status"]));
		} else {
			if ($view == 'list-view') {
				$result = array(
					'code' => $codes["GET_SUCCESS"]["code"],
					'message' => str_replace("[Module_Name]", "Learners", $codes["GET_SUCCESS"]["message"]),
					'status' => $codes["GET_SUCCESS"]["status"],
					'count' => $total_records,
					'data' => $learners
				);
			} else {
				$result = array(
					'code' => $codes["GET_SUCCESS"]["code"],
					'message' => str_replace("[Module_Name]", "Learners", $codes["GET_SUCCESS"]["message"]),
					'status' => $codes["GET_SUCCESS"]["status"],
					'count' => $total_records,
					'data' => $learnerData
				);
			}
			return new WP_REST_Response($result, 200);
		}
	}


	/**
	 * Getting user auth token, this function will work for the users having role 
	 * instructor, yuno admin, category admin
	 * this function is calling from invitation url
	 * @param array $request Request data
	 * @return WP_Error|WP_REST_Response
	 */
	function get_auth_token($request) {
		$codes=error_code_setting();
		//$data = json_decode($request->get_body());
		$user_id = (int)$request["user_id"];
		$class_id = (int)$request["class_id"];
    	if ($user_id > 0) {
		} else {
			return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));			
    	}

    	$userdata = get_userdata($user_id);
		if (empty($userdata)) {
			return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));			
		}	

		if (empty($class_id)) {
			return new WP_Error($codes["GET_FAIL"]["code"], "Class id is required", array('status' => $codes["GET_FAIL"]["status"]));			
		}
		if ($userdata->roles) {
			$org_id = get_post_meta($class_id, 'org_id', true);
			if (empty($org_id)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Org id is required", array('status' => $codes["GET_FAIL"]["status"]));			
			}
			$userAccessToken = get_google_meet_access_token($user_id,$org_id);
			// Query Elasticsearch to retrieve the plan
			$url = GOOGLE_MEET_API_URL;
			$headers = [
				"Authorization: Bearer " .$userAccessToken,
			];
			$curlPost = '';

			$return = Utility::curl_request($url, 'GET', $curlPost, $headers, '');
			// Decode JSON into associative array
			$data = json_decode($return['response'], true);

			if (is_array($data) && isset($data['error']) && isset($data['error']['code'])) {
				// Access specific values
				$returnStatus = $data['error']['status'] ;
				if ($returnStatus == "UNAUTHENTICATED") {
					return new WP_Error($codes["GET_FAIL"]["code"], "Consent revoked. Please reconnect to virtual classroom in Settings > Account for ".get_the_title($org_id)." to schedule classes.", array('status' => $codes["GET_FAIL"]["status"]));	
				}
			}
			$user_detail = array(
				'access_token' => $userAccessToken,
				"email" => get_user_meta($user_id, 'yuno_gplus_email', true)
			);
			$result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => 'User token found', 'data' => $user_detail);
			return new WP_REST_Response($result, $codes["GET_SUCCESS"]["code"]);
		} else {
			return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
		}		 
	}
}
?>
