Vue.component("yuno-card-wrapper", {
  template: `
      	<section class="classDetails">
        	<template v-if="filterResult.loading">
          		<b-skeleton width="240px"></b-skeleton>
          			<div 
						v-for="i in 2" 
						:key="i" 
						class="classWrapper" 
          			>
						<div class="dateSkeleton col-md-2 pl-0">
							<b-skeleton width="150px"></b-skeleton>
							<b-skeleton width="100px"></b-skeleton>
						</div>
						<div class="cardSkeleton w-100">
							<b-skeleton height="200px" ></b-skeleton>
						</div>
          			</div>
        	</template>
        	<template v-else-if="filterResult.success && filterResult.error === null">
				<div 
					v-for="(date, index) in dateRange"
					:key="date"
					:id="getUniqueIdForMonth(date)"
					class="classWrapper"
				>
				
					<span v-if="isToday(date) && hasNonPastClass(date)" class="caption1 onSurfaceVariant noBold isToday">Today</span>
					<div class="classSchedule" :class="{ 'addMargin': index === 0 }">
						<span class="headline3 noBold" :class="[ isToday(date) ? 'primaryColor' : 'onSurfaceVariant']">{{ dateDetails(date).day }}</span>
						<div class="dayInfoBox" :class="[ isToday(date) ? 'primaryColor' : 'onSurfaceVariant']"> 
							<span class="caption1">{{ dateDetails(date).weekday }}</span>
							<span class="caption1 text-nowrap">{{ dateDetails(date).monthYear }}</span>
						</div>
					</div>
					<div class="cardWrapper">
  						<div v-if="index === 0" class="standard-time-est onSurfaceVariant caption1 noBold mb-2">
							<span>{{ dateRangeHeader }}, {{ module.data.timezone }}</span>
						</div>
						<yuno-classCard-v2
							v-for="(item, index) in groupedData[date] || [{ hasNoClass: true }]"
							:key="index"
							:data="item"
						>
						</yuno-classCard-v2>
					</div>
          		</div>
          		<template>
					<yuno-paging
						:maxVisibleButtons="3"
						:total-pages="totalPages()"
						:total="filterResult.count"
						:per-page="filterResult.limit"
						:current-page="filterResult.currentPage"
						@pagechanged="page => $emit('handlePageChange', page)"
					>
					</yuno-paging>
          		</template>
        	</template>
        	<template v-else-if="filterResult.success">
				<yuno-empty-state-v6
					:errorMessage="filterResult.errorData"
					iconImg="/assets/images/enrollment.png"
					:hasBorder="true"
				>
				</yuno-empty-state-v6>
			</template>
      	</section>
  	`,

  data() {
    return {
      assignedMonths: {}, // Track months for which IDs have been assigned
    };
  },
  computed: {
    ...Vuex.mapState(["user", "filterResult", "userRole", "drawer","module"]),

    /**
     * Returns the currently active tab from filterResult
     */
    activeTab() {
      return this.filterResult.tabs.activeTab;
    },
    /**
     * Groups class data by date, filtering out items without scheduled start times
     */
    groupedData() {
      return (
        this.filterResult?.data?.items
          ?.filter((item) => item?.scheduled?.start?.time)
          ?.reduce((groups, item) => {
            const date = this.formatDate(item.scheduled.start.time);
            if (!groups[date]) {
              groups[date] = [];
            }
            groups[date].push(item);
            return groups;
          }, {}) || {}
      );
    },

    /**
     * Generates a list of dates between the earliest and latest class dates
     */
    dateRange() {
      const validData =
        this.filterResult?.data?.items?.filter(
          (item) => item?.scheduled?.start?.time
        ) || [];
      if (!validData.length) return [];

      // Convert scheduled times to Date objects
      const dates = validData.map(
        (item) => new Date(item.scheduled.start.time)
      );

      // Find min and max dates
      const minDate = new Date(Math.min(...dates));
      const maxDate = new Date(Math.max(...dates));

      const dateList = [];
      let currentDate = new Date(minDate);

      // Iterate through days until maxDate is reached
      while (currentDate <= maxDate) {
        dateList.push(this.formatDate(currentDate.toISOString()));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Ensure maxDate is included in the range
      if (!dateList.includes(this.formatDate(maxDate.toISOString()))) {
        dateList.push(this.formatDate(maxDate.toISOString()));
      }

      return dateList;
    },

    /**
     * Creates a formatted date range header string for display
     */
    dateRangeHeader() {
      const validData =
        this.filterResult?.data?.items?.filter(
          (item) => item?.scheduled?.start?.time
        ) || [];
      if (!validData.length) return "No dates available";

      const dates = validData.map(
        (item) => new Date(item.scheduled.start.time)
      );
      const minDate = new Date(Math.min(...dates));
      const maxDate = new Date(Math.max(...dates));

      const minDateFormatted = this.formatHeaderDate(minDate);
      const maxDateFormatted = this.formatHeaderDate(maxDate);

      return `${minDateFormatted} - ${maxDateFormatted}`;
    },
  },
  methods: {
    isToday(date) {
      return date === new Date().toISOString().split("T")[0];
    },
	hasNonPastClass(date) {
		const items = this.groupedData[date] || [];
		return items.some(item => item.temporal_status !== 'past');
	},
    /**
     * Extracts and formats date details for display
     */
    dateDetails(dateString) {
      const date = new Date(dateString);
      const day = date.getDate();
      const weekday = date.toLocaleDateString("en-US", { weekday: "long" });
      const monthYear = date.toLocaleDateString("en-US", {
        month: "long",
        year: "numeric",
      });

      return {
        day,
        weekday,
        monthYear,
      };
    },

    /**
     * Calculates the total number of pages based on result count and items per page
     */
    totalPages() {
      let count = this.filterResult.count / this.filterResult.limit;
      return Math.ceil(count);
    },

    /**
     * Closes the schedule modal by setting drawer.isActive to false
     */
    closeScheduleModal() {
      this.drawer.isActive = false;
    },

    /**
     * Formats a date string to YYYY-MM-DD format
     */
    formatDate(dateString) {
      const isoDateString = dateString.replace(" ", "T");
      const date = new Date(isoDateString);
      return date.toISOString().split("T")[0];
    },

    /**
     * Formats a date for header display (e.g., "Jan 15")
     */
    formatHeaderDate(date) {
      return date.toLocaleDateString("en-US", {
        day: "numeric",
        month: "short",
      });
    },

    /**
     * Generates a unique ID for the first item of each month
     */
    getUniqueIdForMonth(date) {
      const monthYear = new Date(date).toLocaleDateString("en-US", {
        month: "short",
        year: "2-digit",
      });

      if (!this.assignedMonths[monthYear]) {
        this.assignedMonths[monthYear] = true;
        return `${monthYear.toLowerCase().replace(/ /g, "")}`;
      }

      return null;
    },

    /**
     * Clears the class edit state from localStorage
     */
    clearClassEditState() {
      localStorage.removeItem("classEditState");
    },
  },
  watch: {
    // Watch for changes in filterResult and reset assignedMonths
    "filterResult.data"(newVal) {
      if (newVal) {
        this.assignedMonths = ""; // Clear the assigned months when new data is received
      }
    },
  },

  mounted() {
    this.clearClassEditState();
  },
});
