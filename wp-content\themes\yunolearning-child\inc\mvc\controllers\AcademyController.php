<?php

namespace V4;

/**
 * Class AcademyController
 * Academy controller is used for publish, update, delete, get academy
 * Basically we are creating a new custom post type academy named academy.
 * 
 * * <AUTHOR>
 */


class AcademyController extends Controller
{

    /**
     * Constructor for AcademyController.
     *
     * <AUTHOR>
     */

    public function __construct()
    {
        parent::__construct();

        $this->loadLibary('common');
        $this->loadLibary('validate');
        $this->loadLibary('response');
        $this->loadModel('user');
        $this->loadModel('academy');
        $this->loadModel('category');
    }


    /**
     * Academy Info.
     *
     * @param object $request HTTP request object
     * @return array
     * <AUTHOR>
     */

    public function getAcademy($request)
    {
        try {
            $Academy = $this->academyModel->getAcademy(['id' => $request['academyId']], []);

            if (!$Academy) {
                return $this->response->error("GET_FAIL", ['message' => "No Results found"]);
            }

            return $this->response->success("GET_SUCCESS", $Academy, ['message' => "Results found"]);
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }


    public function getAcademies($request)
    {
        try {
            $queryParams = $request->get_query_params();

            $viewType = $queryParams['viewType'] ?? 'list';
            $orgId = $queryParams['orgId'] ?? null;
            $categories = $queryParams['category'] ?? [];
            $sortBy = $queryParams['sort_by'] ?? 'name';
            $sortOrder = $queryParams['sort_order'] ?? 'asc';
            $limit = (int) ($queryParams['limit'] ?? 20);
            $offset = (int) ($queryParams['offset'] ?? 0);

            if (!in_array($viewType, ['list', 'grid'])) {
                return $this->response->error("INVALID_VIEW_TYPE", [
                    'message' => "Invalid view type. Must be 'list' or 'grid'"
                ]);
            }

            $esQueryBody = [
                "query" => [
                    "bool" => [
                        "must" => []
                    ]
                ],
                "sort" => []
            ];

            if (!empty($orgId)) {
                $esQueryBody["query"]["bool"]["must"][] = [
                    "term" => [
                        "data.details.org_id" => (int) $orgId
                    ]
                ];
            }

            if (!empty($categories)) {
                if (!is_array($categories)) {
                    $categories = explode(',', $categories);
                }

                $categoryIds = array_values(array_filter(array_map('intval', $categories), fn($id) => $id > 0));

                if (!empty($categoryIds)) {
                    $esQueryBody["query"]["bool"]["must"][] = [
                        "terms" => [
                            "data.details.category.id" => $categoryIds
                        ]
                    ];
                }
            }

            if ($sortBy === 'name') {
                $esQueryBody["sort"][] = [
                    "data.details.academy_name.keyword" => [
                        "order" => $sortOrder
                    ]
                ];
            } elseif ($sortBy === 'recently_signed_up') {
                $esQueryBody["sort"][] = [
                    "data.details.published_at" => [
                        "order" => $sortOrder
                    ]
                ];
            }

            $queryToModel = [
                'custom' => $esQueryBody,
                'qryStr' => [
                    'from' => $offset,
                    'size' => $limit
                ]
            ];

            $filter = ['schema' => 'Refer#Academy'];

            $academies = $this->academyModel->getAcademies($queryToModel, $filter);

            if (!$academies || empty($academies['data']) || !is_array($academies['data'])) {
                return $this->response->error("GET_FAIL", ['message' => "No academies found"]);
            }

            return $this->response->success('GET_SUCCESS', [
                'data' => $academies['data'],
                'count' => $academies['count']
            ], ['message' => 'Academies were found']);
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', [
                'message' => $this->common->globalExceptionMessage($e)
            ]);
        }
    }



    /**
     * Generates filter structure for academies including organizations and categories.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The user ID for role-based filtering
     * @param int $orgId The organization ID for role-based filtering
     * @return array Array containing the filter structure
     * <AUTHOR>
     */
    public function getAcademiesFilters($request)
    {
        try {
            $queryParams = $request->get_query_params();

            $orgId = $queryParams['orgId'] ?? null;
            $userId = $queryParams['userId'] ?? null;
            // $hasActiveEnrollments = $queryParams['has_active_enrollments'] ?? null;
            // $hasPastEnrollments = $queryParams['has_past_enrollments'] ?? null;
            // $hasCourse = $queryParams['has_course'] ?? null;

            $filters = $this->academyModel->prepareAcademiesFilterData($userId, $request);

            foreach ($filters as &$filter) {
                $key = $filter['filter'];

                switch ($key) {
                    case 'organization':
                        if (!isset($filter['selected']) || $filter['selected'] === 'all') {
                            $filter['selected'] = $orgId;
                        }
                        break;

                    // case 'has_course':
                    //     if ($hasCourse !== null) {
                    //         $filter['selected'] = (bool)$hasCourse;
                    //     }
                    //     break;

                    // case 'has_active_enrollments':
                    //     if ($hasActiveEnrollments !== null) {
                    //         $filter['selected'] = (bool)$hasActiveEnrollments;
                    //     }
                    //     break;

                    // case 'has_past_enrollments':
                    //     if ($hasPastEnrollments !== null) {
                    //         $filter['selected'] = (bool)$hasPastEnrollments;
                    //     }
                    //     break;

                    default:
                        if (!isset($filter['selected'])) {
                            $filter['selected'] = $filter['ui_control_type'] === 'dropdown'
                                ? ($filter['multiple'] ? [] : null)
                                : false;
                        }
                        break;
                }
            }

            return $this->response->success('GET_SUCCESS', $filters, ['message' => 'Filters were found']);
        } catch (Exception $e) {
            error_log("getAcademiesFilters error: " . $e->getMessage());
            return $this->response->error('GET_FAILED', [], ['message' => 'Failed to fetch filters']);
        }
    }
}
