<?php
return [
     // Instructor Controller API's
     "/working_hours/(?P<resourceType>[a-zA-Z0-9-]+)/(?P<resourceId>\d+)" => [
        "controller" => "InstructorController",
        "methods" => [
            "GET" => ["callback" => "getResourceWorkingHours", "args" => ["resource", "resource_id"], "auth" => false]
        ]
    ],
    "/availability/(?P<resource>[a-zA-Z0-9-]+)" => [
        "controller" => "InstructorController",
        "methods" => [
            "POST" => [
                "callback" => "getResourceAvailability",
                "args" => ["start_date", "end_date", "resource_id", "start_time", "end_time"],
                "auth" => false
            ]
        ]
    ],
    "/settings/virtual-classrooms/(?P<instructorId>\d+)" => [
        "controller" => "InstructorController",
        "methods" => [
            "GET" => ["callback" => "getInstructorVirtualClasserooms", "args" => ["instructorId"], "auth" => true]
        ]
    ],
    "/availability/free_slots" => [
        "controller" => "InstructorController",
        "methods" => [
            "GET" => ["callback" => "getInstructorAvailableSlots", "args" => [], "auth" => false]
        ]
    ],
    "/instructors/(?P<instructorId>\d+)" => [
        "controller" => "InstructorController",
        "methods" => [
            "GET" => ["callback" => "getInstructorDetails", "args" => ["instructorId"], "auth" => false]
        ]
    ],
    "/instructors" => [
        "controller" => "InstructorController",
        "methods" => [
            "POST" => ["callback" => "createInstructor", "auth" => false]
        ]
    ],
    "/instructors/(?P<viewType>grid|list)" => [
        "controller" => "InstructorController",
        "methods" => [
            "GET" => ["callback" => "getInstructors", "auth" => false]
        ]
    ],
    "/instructors/filters" => [
        "controller" => "InstructorController",
        "methods" => [
            "GET" => ["callback" => "getInstructorFilters", "auth" => false]
        ]
    ]
];
