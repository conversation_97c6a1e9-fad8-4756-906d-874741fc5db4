<?php
$requestURI = $_SERVER['REQUEST_URI'];
define("API_BASE_PATH", "wp-json/yuno/v3/");
define("CONTROLLER_BASE_PATH", "inc/classes/rest_api/v3/");
use V3\ClassController;
use V3\BatchesController;
use V3\UserController;
use V3\YunoAcademySubscriptionController;
use V3\OrgInstructorInsightsController;
use V3\GoogleMeetController;
//use V3\CategoryController;

/**
 * Object creation for Product Controller
 * todo
 */
require_once(CONTROLLER_BASE_PATH.'ProductController.php');
add_action('rest_api_init','productDetail');
function productDetail() {
    $pcontroller = new ProductController();
    $pcontroller->registerRoutes();
}
/**
 * Object creation for Menu Controller
 */
require_once(CONTROLLER_BASE_PATH.'MenuController.php');
add_action('rest_api_init','get_menu_detail');
function get_menu_detail() {
    $menuController = new MenuController();
    $menuController->register_routes();
}
/**
 * Object creation for Script Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH . 'signup/form'))) {
    require_once(CONTROLLER_BASE_PATH . 'ScriptController.php');
    add_action('rest_api_init', 'script_controller');
}
function script_controller()
{
    $scriptController = new ScriptController();
    $scriptController->register_routes();
}
/**
 * Object creation for Category Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'category')) || !empty(stripos($requestURI, API_BASE_PATH.'all/category'))) {
    require_once(CONTROLLER_BASE_PATH.'CategoryController.php');
    add_action('rest_api_init','category_controller');
}
function category_controller() {
    $categoryController = new CategoryController();
    $categoryController->register_routes();
}
/**
 * Object creation for User Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'user'))) {
    //require_once(CONTROLLER_BASE_PATH.'UserController.php');
    add_action('rest_api_init','user_controller');
}
function user_controller() {
    $user_controller = new UserController();
    $user_controller->register_routes();
}

/**
 * Object creation for OrgClass Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'org'))) {
    require_once(CONTROLLER_BASE_PATH.'OrgClassController.php');
    add_action('rest_api_init','org_class_controller');
}
function org_class_controller() {
    $orgClassController = new OrgClassController();
    $orgClassController->register_routes();
}

/**
 * Object creation for User Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'instructor')) || !empty(stripos($requestURI, API_BASE_PATH.'class/titles')) || !empty(stripos($requestURI, API_BASE_PATH.'virtual-classroom'))) {
    require_once(CONTROLLER_BASE_PATH.'InstructorInsightsController.php');
    add_action('rest_api_init','instructorInsightsController');
}
function instructorInsightsController() {
    $instructor_controller = new InstructorInsightsController();
    $instructor_controller->register_routes();
}

if (!empty(stripos($requestURI, API_BASE_PATH.'org/instructor'))) {
    // require_once(CONTROLLER_BASE_PATH.'OrgInstructorInsightsController.php');
    add_action('rest_api_init','orginstructorInsightsController');
}
function orginstructorInsightsController() {
    $org_instructor_controller = new OrgInstructorInsightsController();
    $org_instructor_controller->register_routes();
}

/**
 * Object creation for Org Course Schedule Controller
 */
if (!empty(stripos($requestURI, 'org/course')) || 
    !empty(stripos($requestURI, API_BASE_PATH.'activity'))) {
    require_once(CONTROLLER_BASE_PATH.'OrgCourseScheduleController.php');
    add_action('rest_api_init','course_schedule_controller');
}
function course_schedule_controller() {
    $courseScheduleController = new OrgCourseScheduleController();
    $courseScheduleController->registerRoutes();
}

/**
 * Object creation for Org Course Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'org'))) {
    require_once(CONTROLLER_BASE_PATH.'OrgCourseController.php');
    add_action('rest_api_init','org_course_controller');
}
function org_course_controller() {
    $orgCourseController = new OrgCourseController();
    $orgCourseController->registerRoutes();
}

/**
 * Object creation for Org Course Batch Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'org'))) {
    require_once(CONTROLLER_BASE_PATH.'OrgCourseEconomicsController.php');
    add_action('rest_api_init','org_course_economics_controller');
}
function org_course_economics_controller() {
    $orgCourseEconomicsController = new OrgCourseEconomicsController();
    $orgCourseEconomicsController->registerRoutes();
}

/**
 * Object creation for Org Course Batch Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'org'))) {
    require_once(CONTROLLER_BASE_PATH.'OrgBatchController.php');
    add_action('rest_api_init','org_batch_controller');
}
function org_batch_controller() {
    $orgCourseController = new OrgBatchController();
    $orgCourseController->registerRoutes();
}
/**
 * Object creation for Class Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'classes'))) {
    add_action('rest_api_init','get_class_detail');
}
function get_class_detail() {
    $ccontroller = new ClassController();
    $ccontroller->register_routes();
}
/**
 * Object creation for Batch Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'org'))) {
    add_action('rest_api_init','get_batch_detail');
}
function get_batch_detail() {
    $bcontroller = new BatchesController();
    $bcontroller->register_routes();
}
/**
 * Object creation for YunoAcademySubscription Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'academy-subscription'))) {
    add_action('rest_api_init','get_yuno_academy_subscription_detail');
}
function get_yuno_academy_subscription_detail() {
    $yuno_academy_subscription = new YunoAcademySubscriptionController();
    $yuno_academy_subscription->register_routes();
}
/**
 * Object creation for OrgEnrollmentController
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'org'))) {
    require_once(CONTROLLER_BASE_PATH.'OrgEnrollmentController.php');
    add_action('rest_api_init','org_enrollement_detail');
}
function org_enrollement_detail() {
    $ccontroller = new OrgEnrollmentController();
    $ccontroller->registerRoutes();
}

/**
 * Object creation for Academy Controller
 */
if (!empty(stripos($requestURI, API_BASE_PATH.'academy')) || !empty(stripos($requestURI, API_BASE_PATH.'org/academies'))) {
    require_once(CONTROLLER_BASE_PATH.'AcademyController.php');
    add_action('rest_api_init','academy_controller');
}
function academy_controller() {
    $academyController = new AcademyController();
    $academyController->registerRoutes();
}
if (!empty(stripos($requestURI, API_BASE_PATH.'google/meet/update/classes'))) {
    add_action('rest_api_init','googleMeetController');
}
function googleMeetController() {
    $meet_controller = new GoogleMeetController();
    $meet_controller->register_routes();
}

?>
