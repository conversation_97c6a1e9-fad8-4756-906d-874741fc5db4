window.Event = new Vue();

const validationMsg = {
  messages: {
    required: "This field is required",
    numeric: "Numbers only",
    min: "Minimum 10 numbers required",
    max: "Maximum 15 numbers required ",
    is_not: "New batch shouldn't be same as current batch",
  },
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component("yuno-list-places", {
  template: `
        <yuno-page-grid :authorizedRoles="authorizedRoles" @onUserInfo="onUserInfo" :hasSearchBar="false">
			<template v-slot:main>
				<div class="yunoListPlaces">
					<div class="container">
						<div class="mainHeader">
							<div class="block">
								<h1>{{ pageHeader.title }}</h1>
								<div class="action">
									<b-button tag="a"
										:href="pageHeader.button.url"
										class="yunoPrimaryCTA ">
										{{pageHeader.button.label}}
									</b-button>
								</div>
							</div>
						</div>
						<div class="listPlaces">
							<h2 class="onSurface subtitle1 m-bottom-large-times-2">Places & Classrooms</h2>
							<template v-if="filterResult.loading">
								<b-skeleton width="100%" height="200px"></b-skeleton>
							</template>
							<template v-else-if="filterResult.success && filterResult.error === null">
								<b-table 
									ref="table" 
									:data="filterResult.data"
									detailed
									detail-key="id" 
									:detail-transition="transitionName"
									:paginated="true"
									:backend-pagination="true"
									:show-detail-icon="false" 
									:loading="filterResult.isLoadMore" 
									:total="filterResult.count"
									:per-page="filterResult.limit" 
									:current-page="filterResult.currentPage"
									@details-open="onDetailOpen"
									@details-close="onDetailClose"
									@page-change="pageChange($event)"
								>

									<b-table-column v-slot="props">
										<span class="material-icons-outlined arrowIcon" @click="props.toggleDetails(props.row)">
											{{ openRows.has(props.row.id) ? 'expand_more' : 'chevron_right' }}
										</span>
									</b-table-column>

									<b-table-column field="name" label="Place Name" v-slot="props">
										<span class="rowLabel" @click="props.toggleDetails(props.row)">
											{{ props.row.name }} {{ props.row.classrooms.length > 1 ? "(" +
											props.row.classrooms.length + ")" : '' }}
										</span>
									</b-table-column>

									<b-table-column field="type" label="Type" v-slot="props">
										<span class="rowLabel capitalize">{{ formatLabel(props.row.type) }}</span>
									</b-table-column>

									<b-table-column field="facilities" label="Facilities" v-slot="props">
										<span class="rowLabel">{{ placeFacilties(props.row.facilities) }}</span>
									</b-table-column>

									<template #detail="props">
										<div class="content">
											<p class="subtitle2 secondary m-bottom-large-times-2">{{ props.row.name }}</p>
											<div v-for="classroom in props.row.classrooms" :key="classroom.id" class="box">
												<p class="secondary subtitle2 m-bottom-large-times-1">{{ classroom.title }}</p>
												<ul class="wrapper">
													<li>
														<span class="onSurfaceVariant caption1">Floor: </span>
														<span class="onSurfaceVariant caption1 noBold"> {{ formatLabel(classroom.floor.type) }}</span></li>
													<li>
														<span class="onSurfaceVariant caption1">Area (Sqft): </span>
														<span class="onSurfaceVariant caption1 noBold"> {{ classroom.area }}</span>
													</li>
													<li>
														<span class="onSurfaceVariant caption1">Capacity: </span> 
														<span class="onSurfaceVariant caption1 noBold"> {{ classroom.seating_capacity }}</span>
													</li>
													<li>
														<span class="onSurfaceVariant caption1">Computer Terminal: </span>
														<span class="onSurfaceVariant caption1 noBold"> {{ classroom.facilities.computer_terminals }}</span> 
													</li>
													<li class="facilities m-top-small-times-1">
														<span class="onSurfaceVariant caption1">Facilities: </span> 
														<span class="onSurfaceVariant caption1 noBold">{{ classroomFacilties(classroom.facilities) }}</span>
													</li>
												</ul>
											</div>
										</div>
									</template>
								</b-table>
							</template>
							<template v-else-if="filterResult.success">
								<yuno-empty-state-v6 :errorMessage="filterResult.errorData"
									iconImg="/assets/images/enrollment.png" :hasBorder="true">
								</yuno-empty-state-v6>
							</template>
						</div>
					</div>
				</div>
			</template>
		</yuno-page-grid>
   `,
  computed: {
    ...Vuex.mapState(["user", "userInfo", "userRole", "filterResult"]),
  },
  data() {
    return {
      authorizedRoles: ["yuno-admin", "org-admin"],
      pageHeader: {
        title: "My Classrooms",
		button: {
			url: "/add-classroom/",
			label: "Add Clasrooms"
		}
      },
      transitionName: "fade",
      openRows: new Set(),
    };
  },
  methods: {
    onUserInfo(data) {
      this.fetchAllPlaces(true);
    },
    activeOrg() {
      const activeOrg = this.userInfo.data.current_state.org_id;

      if (activeOrg) {
        return activeOrg;
      }
    },
    formatLabel(data) {
      switch (data) {
        case "COMMERCIAL_BUILDING":
          return "Commercial Building";
        case "OFFICE":
          return "Office";
        case "SCHOOL":
          return "School";
        case "ONE_PLUS":
          return "One Plus";
        case "GROUND_PLUS":
          return "Ground Floor";
        default:
          return data;
      }
    },
    onDetailClose(row) {
      this.openRows.delete(row.id);
    },
    onDetailOpen(row) {
      this.openRows.add(row.id);
    },
    placeFacilties(data) {
      if (!data) return "";

      const facilities = [
        data.car_parking?.self_parking && "Self Car Parking",
        data.car_parking?.valet_service && "Valet Car Parking",
        data.bike_parking && "Bike Parking",
      ];

      return facilities.filter(Boolean).join(", ");
    },
    classroomFacilties(data) {
      if (!data) return "";

      const facilities = [
        data.wifi && "Wifi",
		data.blackboard && "Blackboard",
        data.whiteboard && "Whiteboard",
        data.projector && "Projector",
        data.lcd_monitor && "LCD Monitor",
        data.air_conditioning && "Air Conditioning",
        data.power_backup && "Power Backup",
      ];

      return facilities.filter(Boolean).join(", ");
    },
    pageChange(e) {
      this.filterResult.currentPage = e;
      this.filterResult.isLoadMore = true;
      this.filterResult.offset = Math.floor(
        this.filterResult.limit * e - this.filterResult.limit
      );
      this.fetchAllPlaces(false);
    },
    gotPlaces(options) {
      this.filterResult.loading = false;
      this.filterResult.isLoadMore = false;
      const responseData = options?.response?.data;
      if (responseData?.code === 200) {
        let data = responseData.data;
        this.filterResult.count = responseData.count;
        this.filterResult.data = data;
        let placeId = data[0].id;
      }
    },
    fetchAllPlaces(moduleLoading) {
      this.filterResult.loading = moduleLoading;
      const apiUrl =
        this.userRole.data === "org-admin" ? "fetchOrgPlace" : "fetchAllPlace";
      const props = {
        view_type: "list",
        limit: this.filterResult.limit,
        offset: this.filterResult.offset,
        ...(this.userRole.data === "org-admin" && {
          org_id: this.activeOrg(),
        }),
      };

      const options = {
        apiURL: YUNOCommon.config.academy(apiUrl, props),
        module: "gotData",
        store: "filterResult",
        callback: true,
        callbackFunc: this.gotPlaces,
      };

      this.$store.dispatch("fetchData", options);
    },
  },
});
