/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function e(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function t(e,t,n,o){for(var r=-1,a=null==e?0:e.length;++r<a;){var i=e[r];t(o,i,n(i),e)}return o}function n(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}function o(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function r(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(!t(e[n],n,e))return!1;return!0}function a(e,t){for(var n=-1,o=null==e?0:e.length,r=0,a=[];++n<o;){var i=e[n];t(i,n,e)&&(a[r++]=i)}return a}function i(e,t){return!(null==e||!e.length)&&h(e,t,0)>-1}function s(e,t,n){for(var o=-1,r=null==e?0:e.length;++o<r;)if(n(t,e[o]))return!0;return!1}function l(e,t){for(var n=-1,o=null==e?0:e.length,r=Array(o);++n<o;)r[n]=t(e[n],n,e);return r}function u(e,t){for(var n=-1,o=t.length,r=e.length;++n<o;)e[r+n]=t[n];return e}function c(e,t,n,o){var r=-1,a=null==e?0:e.length;for(o&&a&&(n=e[++r]);++r<a;)n=t(n,e[r],r,e);return n}function d(e,t,n,o){var r=null==e?0:e.length;for(o&&r&&(n=e[--r]);r--;)n=t(n,e[r],r,e);return n}function p(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(t(e[n],n,e))return!0;return!1}function m(e){return e.match(Qe)||[]}function f(e,t,n){var o;return n(e,(function(e,n,r){if(t(e,n,r))return o=n,!1})),o}function g(e,t,n,o){for(var r=e.length,a=n+(o?1:-1);o?a--:++a<r;)if(t(e[a],a,e))return a;return-1}function h(e,t,n){return t==t?function(e,t,n){for(var o=n-1,r=e.length;++o<r;)if(e[o]===t)return o;return-1}(e,t,n):g(e,y,n)}function v(e,t,n,o){for(var r=n-1,a=e.length;++r<a;)if(o(e[r],t))return r;return-1}function y(e){return e!=e}function b(e,t){var n=null==e?0:e.length;return n?I(e,t)/n:X}function w(e){return function(t){return null==t?F:t[e]}}function k(e){return function(t){return null==e?F:e[t]}}function _(e,t,n,o,r){return r(e,(function(e,r,a){n=o?(o=!1,e):t(n,e,r,a)})),n}function I(e,t){for(var n,o=-1,r=e.length;++o<r;){var a=t(e[o]);a!==F&&(n=n===F?a:n+a)}return n}function L(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}function C(e){return e?e.slice(0,B(e)+1).replace(Ge,""):e}function A(e){return function(t){return e(t)}}function S(e,t){return l(t,(function(t){return e[t]}))}function j(e,t){return e.has(t)}function P(e,t){for(var n=-1,o=e.length;++n<o&&h(t,e[n],0)>-1;);return n}function D(e,t){for(var n=e.length;n--&&h(t,e[n],0)>-1;);return n}function T(e){return"\\"+Jt[e]}function R(e){return Yt.test(e)}function H(e){return Vt.test(e)}function M(e){var t=-1,n=Array(e.size);return e.forEach((function(e,o){n[++t]=[o,e]})),n}function U(e,t){return function(n){return e(t(n))}}function x(e,t){for(var n=-1,o=e.length,r=0,a=[];++n<o;){var i=e[n];i!==t&&i!==V||(e[n]=V,a[r++]=n)}return a}function $(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function O(e){return R(e)?function(e){for(var t=Ft.lastIndex=0;Ft.test(e);)++t;return t}(e):fn(e)}function E(e){return R(e)?function(e){return e.match(Ft)||[]}(e):function(e){return e.split("")}(e)}function B(e){for(var t=e.length;t--&&We.test(e.charAt(t)););return t}function N(e){return e.match(zt)||[]}var F,z="Expected a function",Y="__lodash_hash_undefined__",V="__lodash_placeholder__",G=16,W=32,q=64,K=128,J=256,Q=1/0,Z=9007199254740991,X=NaN,ee=4294967295,te=ee-1,ne=ee>>>1,oe=[["ary",K],["bind",1],["bindKey",2],["curry",8],["curryRight",G],["flip",512],["partial",W],["partialRight",q],["rearg",J]],re="[object Arguments]",ae="[object Array]",ie="[object Boolean]",se="[object Date]",le="[object Error]",ue="[object Function]",ce="[object GeneratorFunction]",de="[object Map]",pe="[object Number]",me="[object Object]",fe="[object Promise]",ge="[object RegExp]",he="[object Set]",ve="[object String]",ye="[object Symbol]",be="[object WeakMap]",we="[object ArrayBuffer]",ke="[object DataView]",_e="[object Float32Array]",Ie="[object Float64Array]",Le="[object Int8Array]",Ce="[object Int16Array]",Ae="[object Int32Array]",Se="[object Uint8Array]",je="[object Uint8ClampedArray]",Pe="[object Uint16Array]",De="[object Uint32Array]",Te=/\b__p \+= '';/g,Re=/\b(__p \+=) '' \+/g,He=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Me=/&(?:amp|lt|gt|quot|#39);/g,Ue=/[&<>"']/g,xe=RegExp(Me.source),$e=RegExp(Ue.source),Oe=/<%-([\s\S]+?)%>/g,Ee=/<%([\s\S]+?)%>/g,Be=/<%=([\s\S]+?)%>/g,Ne=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Fe=/^\w*$/,ze=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ye=/[\\^$.*+?()[\]{}|]/g,Ve=RegExp(Ye.source),Ge=/^\s+/,We=/\s/,qe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Ke=/\{\n\/\* \[wrapped with (.+)\] \*/,Je=/,? & /,Qe=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Ze=/[()=,{}\[\]\/\s]/,Xe=/\\(\\)?/g,et=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,tt=/\w*$/,nt=/^[-+]0x[0-9a-f]+$/i,ot=/^0b[01]+$/i,rt=/^\[object .+?Constructor\]$/,at=/^0o[0-7]+$/i,it=/^(?:0|[1-9]\d*)$/,st=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,lt=/($^)/,ut=/['\n\r\u2028\u2029\\]/g,ct="\\ud800-\\udfff",dt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",pt="\\u2700-\\u27bf",mt="a-z\\xdf-\\xf6\\xf8-\\xff",ft="A-Z\\xc0-\\xd6\\xd8-\\xde",gt="\\ufe0e\\ufe0f",ht="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vt="['’]",yt="["+ct+"]",bt="["+ht+"]",wt="["+dt+"]",kt="\\d+",_t="["+pt+"]",It="["+mt+"]",Lt="[^"+ct+ht+kt+pt+mt+ft+"]",Ct="\\ud83c[\\udffb-\\udfff]",At="[^"+ct+"]",St="(?:\\ud83c[\\udde6-\\uddff]){2}",jt="[\\ud800-\\udbff][\\udc00-\\udfff]",Pt="["+ft+"]",Dt="\\u200d",Tt="(?:"+It+"|"+Lt+")",Rt="(?:"+Pt+"|"+Lt+")",Ht="(?:['’](?:d|ll|m|re|s|t|ve))?",Mt="(?:['’](?:D|LL|M|RE|S|T|VE))?",Ut="(?:"+wt+"|"+Ct+")"+"?",xt="["+gt+"]?",$t=xt+Ut+("(?:"+Dt+"(?:"+[At,St,jt].join("|")+")"+xt+Ut+")*"),Ot="(?:"+[_t,St,jt].join("|")+")"+$t,Et="(?:"+[At+wt+"?",wt,St,jt,yt].join("|")+")",Bt=RegExp(vt,"g"),Nt=RegExp(wt,"g"),Ft=RegExp(Ct+"(?="+Ct+")|"+Et+$t,"g"),zt=RegExp([Pt+"?"+It+"+"+Ht+"(?="+[bt,Pt,"$"].join("|")+")",Rt+"+"+Mt+"(?="+[bt,Pt+Tt,"$"].join("|")+")",Pt+"?"+Tt+"+"+Ht,Pt+"+"+Mt,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",kt,Ot].join("|"),"g"),Yt=RegExp("["+Dt+ct+dt+gt+"]"),Vt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Gt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Wt=-1,qt={};qt[_e]=qt[Ie]=qt[Le]=qt[Ce]=qt[Ae]=qt[Se]=qt[je]=qt[Pe]=qt[De]=!0,qt[re]=qt[ae]=qt[we]=qt[ie]=qt[ke]=qt[se]=qt[le]=qt[ue]=qt[de]=qt[pe]=qt[me]=qt[ge]=qt[he]=qt[ve]=qt[be]=!1;var Kt={};Kt[re]=Kt[ae]=Kt[we]=Kt[ke]=Kt[ie]=Kt[se]=Kt[_e]=Kt[Ie]=Kt[Le]=Kt[Ce]=Kt[Ae]=Kt[de]=Kt[pe]=Kt[me]=Kt[ge]=Kt[he]=Kt[ve]=Kt[ye]=Kt[Se]=Kt[je]=Kt[Pe]=Kt[De]=!0,Kt[le]=Kt[ue]=Kt[be]=!1;var Jt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Qt=parseFloat,Zt=parseInt,Xt="object"==typeof global&&global&&global.Object===Object&&global,en="object"==typeof self&&self&&self.Object===Object&&self,tn=Xt||en||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,on=nn&&"object"==typeof module&&module&&!module.nodeType&&module,rn=on&&on.exports===nn,an=rn&&Xt.process,sn=function(){try{var e=on&&on.require&&on.require("util").types;return e||an&&an.binding&&an.binding("util")}catch(e){}}(),ln=sn&&sn.isArrayBuffer,un=sn&&sn.isDate,cn=sn&&sn.isMap,dn=sn&&sn.isRegExp,pn=sn&&sn.isSet,mn=sn&&sn.isTypedArray,fn=w("length"),gn=k({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),hn=k({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),vn=k({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),yn=function k(We){function Qe(e){if(Nr(e)&&!Ds(e)&&!(e instanceof pt)){if(e instanceof dt)return e;if(Da.call(e,"__wrapped__"))return mr(e)}return new dt(e)}function ct(){}function dt(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=F}function pt(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ee,this.__views__=[]}function mt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function ft(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function gt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function ht(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new gt;++t<n;)this.add(e[t])}function vt(e){this.size=(this.__data__=new ft(e)).size}function yt(e,t){var n=Ds(e),o=!n&&Ps(e),r=!n&&!o&&Rs(e),a=!n&&!o&&!r&&$s(e),i=n||o||r||a,s=i?L(e.length,Ia):[],l=s.length;for(var u in e)!t&&!Da.call(e,u)||i&&("length"==u||r&&("offset"==u||"parent"==u)||a&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Qo(u,l))||s.push(u);return s}function bt(e){var t=e.length;return t?e[Hn(0,t-1)]:F}function wt(e,t){return ur(co(e),Pt(t,0,e.length))}function kt(e){return ur(co(e))}function _t(e,t,n){(n===F||Hr(e[t],n))&&(n!==F||t in e)||St(e,t,n)}function It(e,t,n){var o=e[t];Da.call(e,t)&&Hr(o,n)&&(n!==F||t in e)||St(e,t,n)}function Lt(e,t){for(var n=e.length;n--;)if(Hr(e[n][0],t))return n;return-1}function Ct(e,t,n,o){return ji(e,(function(e,r,a){t(o,e,n(e),a)})),o}function At(e,t){return e&&po(t,ta(t),e)}function St(e,t,n){"__proto__"==t&&qa?qa(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function jt(e,t){for(var n=-1,o=t.length,r=ha(o),a=null==e;++n<o;)r[n]=a?F:Xr(e,t[n]);return r}function Pt(e,t,n){return e==e&&(n!==F&&(e=e<=n?e:n),t!==F&&(e=e>=t?e:t)),e}function Dt(e,t,o,r,a,i){var s,l=1&t,u=2&t,c=4&t;if(o&&(s=a?o(e,r,a,i):o(e)),s!==F)return s;if(!Br(e))return e;var d=Ds(e);if(d){if(s=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Da.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!l)return co(e,s)}else{var p=Bi(e),m=p==ue||p==ce;if(Rs(e))return ro(e,l);if(p==me||p==re||m&&!a){if(s=u||m?{}:Ko(e),!l)return u?function(e,t){return po(e,Ei(e),t)}(e,function(e,t){return e&&po(t,na(t),e)}(s,e)):function(e,t){return po(e,Oi(e),t)}(e,At(s,e))}else{if(!Kt[p])return a?e:{};s=function(e,t,n){var o=e.constructor;switch(t){case we:return ao(e);case ie:case se:return new o(+e);case ke:return function(e,t){return new e.constructor(t?ao(e.buffer):e.buffer,e.byteOffset,e.byteLength)}(e,n);case _e:case Ie:case Le:case Ce:case Ae:case Se:case je:case Pe:case De:return io(e,n);case de:return new o;case pe:case ve:return new o(e);case ge:return function(e){var t=new e.constructor(e.source,tt.exec(e));return t.lastIndex=e.lastIndex,t}(e);case he:return new o;case ye:return function(e){return Ci?ka(Ci.call(e)):{}}(e)}}(e,p,l)}}i||(i=new vt);var f=i.get(e);if(f)return f;i.set(e,s),xs(e)?e.forEach((function(n){s.add(Dt(n,t,o,n,e,i))})):Ms(e)&&e.forEach((function(n,r){s.set(r,Dt(n,t,o,r,e,i))}));var g=d?F:(c?u?No:Bo:u?na:ta)(e);return n(g||e,(function(n,r){g&&(n=e[r=n]),It(s,r,Dt(n,t,o,r,e,i))})),s}function Tt(e,t,n){var o=n.length;if(null==e)return!o;for(e=ka(e);o--;){var r=n[o],a=t[r],i=e[r];if(i===F&&!(r in e)||!a(i))return!1}return!0}function Rt(e,t,n){if("function"!=typeof e)throw new La(z);return zi((function(){e.apply(F,n)}),t)}function Ht(e,t,n,o){var r=-1,a=i,u=!0,c=e.length,d=[],p=t.length;if(!c)return d;n&&(t=l(t,A(n))),o?(a=s,u=!1):t.length>=200&&(a=j,u=!1,t=new ht(t));e:for(;++r<c;){var m=e[r],f=null==n?m:n(m);if(m=o||0!==m?m:0,u&&f==f){for(var g=p;g--;)if(t[g]===f)continue e;d.push(m)}else a(t,f,o)||d.push(m)}return d}function Mt(e,t){var n=!0;return ji(e,(function(e,o,r){return n=!!t(e,o,r)})),n}function Ut(e,t,n){for(var o=-1,r=e.length;++o<r;){var a=e[o],i=t(a);if(null!=i&&(s===F?i==i&&!Vr(i):n(i,s)))var s=i,l=a}return l}function xt(e,t){var n=[];return ji(e,(function(e,o,r){t(e,o,r)&&n.push(e)})),n}function $t(e,t,n,o,r){var a=-1,i=e.length;for(n||(n=Jo),r||(r=[]);++a<i;){var s=e[a];t>0&&n(s)?t>1?$t(s,t-1,n,o,r):u(r,s):o||(r[r.length]=s)}return r}function Ot(e,t){return e&&Di(e,t,ta)}function Et(e,t){return e&&Ti(e,t,ta)}function Ft(e,t){return a(t,(function(t){return $r(e[t])}))}function zt(e,t){for(var n=0,o=(t=no(t,e)).length;null!=e&&n<o;)e=e[cr(t[n++])];return n&&n==o?e:F}function Yt(e,t,n){var o=t(e);return Ds(e)?o:u(o,n(e))}function Vt(e){return null==e?e===F?"[object Undefined]":"[object Null]":Wa&&Wa in ka(e)?function(e){var t=Da.call(e,Wa),n=e[Wa];try{e[Wa]=F;var o=!0}catch(e){}var r=Ha.call(e);return o&&(t?e[Wa]=n:delete e[Wa]),r}(e):function(e){return Ha.call(e)}(e)}function Jt(e,t){return e>t}function Xt(e,t){return null!=e&&Da.call(e,t)}function en(e,t){return null!=e&&t in ka(e)}function nn(e,t,n){for(var o=n?s:i,r=e[0].length,a=e.length,u=a,c=ha(a),d=1/0,p=[];u--;){var m=e[u];u&&t&&(m=l(m,A(t))),d=ii(m.length,d),c[u]=!n&&(t||r>=120&&m.length>=120)?new ht(u&&m):F}m=e[0];var f=-1,g=c[0];e:for(;++f<r&&p.length<d;){var h=m[f],v=t?t(h):h;if(h=n||0!==h?h:0,!(g?j(g,v):o(p,v,n))){for(u=a;--u;){var y=c[u];if(!(y?j(y,v):o(e[u],v,n)))continue e}g&&g.push(v),p.push(h)}}return p}function on(t,n,o){var r=null==(t=ar(t,n=no(n,t)))?t:t[cr(yr(n))];return null==r?F:e(r,t,o)}function an(e){return Nr(e)&&Vt(e)==re}function sn(e,t,n,o,r){return e===t||(null==e||null==t||!Nr(e)&&!Nr(t)?e!=e&&t!=t:function(e,t,n,o,r,a){var i=Ds(e),s=Ds(t),l=i?ae:Bi(e),u=s?ae:Bi(t);l=l==re?me:l,u=u==re?me:u;var c=l==me,d=u==me,p=l==u;if(p&&Rs(e)){if(!Rs(t))return!1;i=!0,c=!1}if(p&&!c)return a||(a=new vt),i||$s(e)?Oo(e,t,n,o,r,a):function(e,t,n,o,r,a,i){switch(n){case ke:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case we:return!(e.byteLength!=t.byteLength||!a(new Ea(e),new Ea(t)));case ie:case se:case pe:return Hr(+e,+t);case le:return e.name==t.name&&e.message==t.message;case ge:case ve:return e==t+"";case de:var s=M;case he:var l=1&o;if(s||(s=$),e.size!=t.size&&!l)return!1;var u=i.get(e);if(u)return u==t;o|=2,i.set(e,t);var c=Oo(s(e),s(t),o,r,a,i);return i.delete(e),c;case ye:if(Ci)return Ci.call(e)==Ci.call(t)}return!1}(e,t,l,n,o,r,a);if(!(1&n)){var m=c&&Da.call(e,"__wrapped__"),f=d&&Da.call(t,"__wrapped__");if(m||f){var g=m?e.value():e,h=f?t.value():t;return a||(a=new vt),r(g,h,n,o,a)}}return!!p&&(a||(a=new vt),function(e,t,n,o,r,a){var i=1&n,s=Bo(e),l=s.length;if(l!=Bo(t).length&&!i)return!1;for(var u=l;u--;){var c=s[u];if(!(i?c in t:Da.call(t,c)))return!1}var d=a.get(e),p=a.get(t);if(d&&p)return d==t&&p==e;var m=!0;a.set(e,t),a.set(t,e);for(var f=i;++u<l;){var g=e[c=s[u]],h=t[c];if(o)var v=i?o(h,g,c,t,e,a):o(g,h,c,e,t,a);if(!(v===F?g===h||r(g,h,n,o,a):v)){m=!1;break}f||(f="constructor"==c)}if(m&&!f){var y=e.constructor,b=t.constructor;y!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof b&&b instanceof b)&&(m=!1)}return a.delete(e),a.delete(t),m}(e,t,n,o,r,a))}(e,t,n,o,sn,r))}function fn(e,t,n,o){var r=n.length,a=r,i=!o;if(null==e)return!a;for(e=ka(e);r--;){var s=n[r];if(i&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++r<a;){var l=(s=n[r])[0],u=e[l],c=s[1];if(i&&s[2]){if(u===F&&!(l in e))return!1}else{var d=new vt;if(o)var p=o(u,c,l,e,t,d);if(!(p===F?sn(c,u,3,o,d):p))return!1}}return!0}function bn(e){return!(!Br(e)||function(e){return!!Ra&&Ra in e}(e))&&($r(e)?xa:rt).test(dr(e))}function wn(e){return"function"==typeof e?e:null==e?ua:"object"==typeof e?Ds(e)?An(e[0],e[1]):Cn(e):ma(e)}function kn(e){if(!tr(e))return ri(e);var t=[];for(var n in ka(e))Da.call(e,n)&&"constructor"!=n&&t.push(n);return t}function _n(e){if(!Br(e))return function(e){var t=[];if(null!=e)for(var n in ka(e))t.push(n);return t}(e);var t=tr(e),n=[];for(var o in e)("constructor"!=o||!t&&Da.call(e,o))&&n.push(o);return n}function In(e,t){return e<t}function Ln(e,t){var n=-1,o=Mr(e)?ha(e.length):[];return ji(e,(function(e,r,a){o[++n]=t(e,r,a)})),o}function Cn(e){var t=Go(e);return 1==t.length&&t[0][2]?or(t[0][0],t[0][1]):function(n){return n===e||fn(n,e,t)}}function An(e,t){return Xo(e)&&nr(t)?or(cr(e),t):function(n){var o=Xr(n,e);return o===F&&o===t?ea(n,e):sn(t,o,3)}}function Sn(e,t,n,o,r){e!==t&&Di(t,(function(a,i){if(r||(r=new vt),Br(a))!function(e,t,n,o,r,a,i){var s=ir(e,n),l=ir(t,n),u=i.get(l);if(u)return _t(e,n,u),F;var c=a?a(s,l,n+"",e,t,i):F,d=c===F;if(d){var p=Ds(l),m=!p&&Rs(l),f=!p&&!m&&$s(l);c=l,p||m||f?Ds(s)?c=s:Ur(s)?c=co(s):m?(d=!1,c=ro(l,!0)):f?(d=!1,c=io(l,!0)):c=[]:zr(l)||Ps(l)?(c=s,Ps(s)?c=Qr(s):Br(s)&&!$r(s)||(c=Ko(l))):d=!1}d&&(i.set(l,c),r(c,l,o,a,i),i.delete(l)),_t(e,n,c)}(e,t,i,n,Sn,o,r);else{var s=o?o(ir(e,i),a,i+"",e,t,r):F;s===F&&(s=a),_t(e,i,s)}}),na)}function jn(e,t){var n=e.length;if(n)return Qo(t+=t<0?n:0,n)?e[t]:F}function Pn(e,t,n){t=t.length?l(t,(function(e){return Ds(e)?function(t){return zt(t,1===e.length?e[0]:e)}:e})):[ua];var o=-1;return t=l(t,A(Yo())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(Ln(e,(function(e,n,r){return{criteria:l(t,(function(t){return t(e)})),index:++o,value:e}})),(function(e,t){return function(e,t,n){for(var o=-1,r=e.criteria,a=t.criteria,i=r.length,s=n.length;++o<i;){var l=so(r[o],a[o]);if(l)return o>=s?l:l*("desc"==n[o]?-1:1)}return e.index-t.index}(e,t,n)}))}function Dn(e,t,n){for(var o=-1,r=t.length,a={};++o<r;){var i=t[o],s=zt(e,i);n(s,i)&&On(a,no(i,e),s)}return a}function Tn(e,t,n,o){var r=o?v:h,a=-1,i=t.length,s=e;for(e===t&&(t=co(t)),n&&(s=l(e,A(n)));++a<i;)for(var u=0,c=t[a],d=n?n(c):c;(u=r(s,d,u,o))>-1;)s!==e&&Ya.call(s,u,1),Ya.call(e,u,1);return e}function Rn(e,t){for(var n=e?t.length:0,o=n-1;n--;){var r=t[n];if(n==o||r!==a){var a=r;Qo(r)?Ya.call(e,r,1):qn(e,r)}}return e}function Hn(e,t){return e+Xa(ui()*(t-e+1))}function Mn(e,t){var n="";if(!e||t<1||t>Z)return n;do{t%2&&(n+=e),(t=Xa(t/2))&&(e+=e)}while(t);return n}function Un(e,t){return Yi(rr(e,t,ua),e+"")}function xn(e){return bt(ra(e))}function $n(e,t){var n=ra(e);return ur(n,Pt(t,0,n.length))}function On(e,t,n,o){if(!Br(e))return e;for(var r=-1,a=(t=no(t,e)).length,i=a-1,s=e;null!=s&&++r<a;){var l=cr(t[r]),u=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(r!=i){var c=s[l];(u=o?o(c,l,s):F)===F&&(u=Br(c)?c:Qo(t[r+1])?[]:{})}It(s,l,u),s=s[l]}return e}function En(e){return ur(ra(e))}function Bn(e,t,n){var o=-1,r=e.length;t<0&&(t=-t>r?0:r+t),(n=n>r?r:n)<0&&(n+=r),r=t>n?0:n-t>>>0,t>>>=0;for(var a=ha(r);++o<r;)a[o]=e[o+t];return a}function Nn(e,t){var n;return ji(e,(function(e,o,r){return!(n=t(e,o,r))})),!!n}function Fn(e,t,n){var o=0,r=null==e?o:e.length;if("number"==typeof t&&t==t&&r<=ne){for(;o<r;){var a=o+r>>>1,i=e[a];null!==i&&!Vr(i)&&(n?i<=t:i<t)?o=a+1:r=a}return r}return zn(e,t,ua,n)}function zn(e,t,n,o){var r=0,a=null==e?0:e.length;if(0===a)return 0;for(var i=(t=n(t))!=t,s=null===t,l=Vr(t),u=t===F;r<a;){var c=Xa((r+a)/2),d=n(e[c]),p=d!==F,m=null===d,f=d==d,g=Vr(d);if(i)var h=o||f;else h=u?f&&(o||p):s?f&&p&&(o||!m):l?f&&p&&!m&&(o||!g):!m&&!g&&(o?d<=t:d<t);h?r=c+1:a=c}return ii(a,te)}function Yn(e,t){for(var n=-1,o=e.length,r=0,a=[];++n<o;){var i=e[n],s=t?t(i):i;if(!n||!Hr(s,l)){var l=s;a[r++]=0===i?0:i}}return a}function Vn(e){return"number"==typeof e?e:Vr(e)?X:+e}function Gn(e){if("string"==typeof e)return e;if(Ds(e))return l(e,Gn)+"";if(Vr(e))return Ai?Ai.call(e):"";var t=e+"";return"0"==t&&1/e==-Q?"-0":t}function Wn(e,t,n){var o=-1,r=i,a=e.length,l=!0,u=[],c=u;if(n)l=!1,r=s;else if(a>=200){var d=t?null:xi(e);if(d)return $(d);l=!1,r=j,c=new ht}else c=t?[]:u;e:for(;++o<a;){var p=e[o],m=t?t(p):p;if(p=n||0!==p?p:0,l&&m==m){for(var f=c.length;f--;)if(c[f]===m)continue e;t&&c.push(m),u.push(p)}else r(c,m,n)||(c!==u&&c.push(m),u.push(p))}return u}function qn(e,t){return null==(e=ar(e,t=no(t,e)))||delete e[cr(yr(t))]}function Kn(e,t,n,o){return On(e,t,n(zt(e,t)),o)}function Jn(e,t,n,o){for(var r=e.length,a=o?r:-1;(o?a--:++a<r)&&t(e[a],a,e););return n?Bn(e,o?0:a,o?a+1:r):Bn(e,o?a+1:0,o?r:a)}function Qn(e,t){var n=e;return n instanceof pt&&(n=n.value()),c(t,(function(e,t){return t.func.apply(t.thisArg,u([e],t.args))}),n)}function Zn(e,t,n){var o=e.length;if(o<2)return o?Wn(e[0]):[];for(var r=-1,a=ha(o);++r<o;)for(var i=e[r],s=-1;++s<o;)s!=r&&(a[r]=Ht(a[r]||i,e[s],t,n));return Wn($t(a,1),t,n)}function Xn(e,t,n){for(var o=-1,r=e.length,a=t.length,i={};++o<r;)n(i,e[o],o<a?t[o]:F);return i}function eo(e){return Ur(e)?e:[]}function to(e){return"function"==typeof e?e:ua}function no(e,t){return Ds(e)?e:Xo(e,t)?[e]:Vi(Zr(e))}function oo(e,t,n){var o=e.length;return n=n===F?o:n,!t&&n>=o?e:Bn(e,t,n)}function ro(e,t){if(t)return e.slice();var n=e.length,o=Ba?Ba(n):new e.constructor(n);return e.copy(o),o}function ao(e){var t=new e.constructor(e.byteLength);return new Ea(t).set(new Ea(e)),t}function io(e,t){return new e.constructor(t?ao(e.buffer):e.buffer,e.byteOffset,e.length)}function so(e,t){if(e!==t){var n=e!==F,o=null===e,r=e==e,a=Vr(e),i=t!==F,s=null===t,l=t==t,u=Vr(t);if(!s&&!u&&!a&&e>t||a&&i&&l&&!s&&!u||o&&i&&l||!n&&l||!r)return 1;if(!o&&!a&&!u&&e<t||u&&n&&r&&!o&&!a||s&&n&&r||!i&&r||!l)return-1}return 0}function lo(e,t,n,o){for(var r=-1,a=e.length,i=n.length,s=-1,l=t.length,u=ai(a-i,0),c=ha(l+u),d=!o;++s<l;)c[s]=t[s];for(;++r<i;)(d||r<a)&&(c[n[r]]=e[r]);for(;u--;)c[s++]=e[r++];return c}function uo(e,t,n,o){for(var r=-1,a=e.length,i=-1,s=n.length,l=-1,u=t.length,c=ai(a-s,0),d=ha(c+u),p=!o;++r<c;)d[r]=e[r];for(var m=r;++l<u;)d[m+l]=t[l];for(;++i<s;)(p||r<a)&&(d[m+n[i]]=e[r++]);return d}function co(e,t){var n=-1,o=e.length;for(t||(t=ha(o));++n<o;)t[n]=e[n];return t}function po(e,t,n,o){var r=!n;n||(n={});for(var a=-1,i=t.length;++a<i;){var s=t[a],l=o?o(n[s],e[s],s,n,e):F;l===F&&(l=e[s]),r?St(n,s,l):It(n,s,l)}return n}function mo(e,n){return function(o,r){var a=Ds(o)?t:Ct,i=n?n():{};return a(o,e,Yo(r,2),i)}}function fo(e){return Un((function(t,n){var o=-1,r=n.length,a=r>1?n[r-1]:F,i=r>2?n[2]:F;for(a=e.length>3&&"function"==typeof a?(r--,a):F,i&&Zo(n[0],n[1],i)&&(a=r<3?F:a,r=1),t=ka(t);++o<r;){var s=n[o];s&&e(t,s,o,a)}return t}))}function go(e,t){return function(n,o){if(null==n)return n;if(!Mr(n))return e(n,o);for(var r=n.length,a=t?r:-1,i=ka(n);(t?a--:++a<r)&&!1!==o(i[a],a,i););return n}}function ho(e){return function(t,n,o){for(var r=-1,a=ka(t),i=o(t),s=i.length;s--;){var l=i[e?s:++r];if(!1===n(a[l],l,a))break}return t}}function vo(e){return function(t){var n=R(t=Zr(t))?E(t):F,o=n?n[0]:t.charAt(0),r=n?oo(n,1).join(""):t.slice(1);return o[e]()+r}}function yo(e){return function(t){return c(sa(ia(t).replace(Bt,"")),e,"")}}function bo(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Si(e.prototype),o=e.apply(n,t);return Br(o)?o:n}}function wo(t,n,o){var r=bo(t);return function a(){for(var i=arguments.length,s=ha(i),l=i,u=zo(a);l--;)s[l]=arguments[l];var c=i<3&&s[0]!==u&&s[i-1]!==u?[]:x(s,u);return(i-=c.length)<o?To(t,n,Io,a.placeholder,F,s,c,F,F,o-i):e(this&&this!==tn&&this instanceof a?r:t,this,s)}}function ko(e){return function(t,n,o){var r=ka(t);if(!Mr(t)){var a=Yo(n,3);t=ta(t),n=function(e){return a(r[e],e,r)}}var i=e(t,n,o);return i>-1?r[a?t[i]:i]:F}}function _o(e){return Eo((function(t){var n=t.length,o=n,r=dt.prototype.thru;for(e&&t.reverse();o--;){var a=t[o];if("function"!=typeof a)throw new La(z);if(r&&!i&&"wrapper"==Fo(a))var i=new dt([],!0)}for(o=i?o:n;++o<n;){var s=Fo(a=t[o]),l="wrapper"==s?$i(a):F;i=l&&er(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?i[Fo(l[0])].apply(i,l[3]):1==a.length&&er(a)?i[s]():i.thru(a)}return function(){var e=arguments,o=e[0];if(i&&1==e.length&&Ds(o))return i.plant(o).value();for(var r=0,a=n?t[r].apply(this,e):o;++r<n;)a=t[r].call(this,a);return a}}))}function Io(e,t,n,o,r,a,i,s,l,u){var c=t&K,d=1&t,p=2&t,m=24&t,f=512&t,g=p?F:bo(e);return function h(){for(var v=arguments.length,y=ha(v),b=v;b--;)y[b]=arguments[b];if(m)var w=zo(h),k=function(e,t){for(var n=e.length,o=0;n--;)e[n]===t&&++o;return o}(y,w);if(o&&(y=lo(y,o,r,m)),a&&(y=uo(y,a,i,m)),v-=k,m&&v<u)return To(e,t,Io,h.placeholder,n,y,x(y,w),s,l,u-v);var _=d?n:this,I=p?_[e]:e;return v=y.length,s?y=function(e,t){for(var n=e.length,o=ii(t.length,n),r=co(e);o--;){var a=t[o];e[o]=Qo(a,n)?r[a]:F}return e}(y,s):f&&v>1&&y.reverse(),c&&l<v&&(y.length=l),this&&this!==tn&&this instanceof h&&(I=g||bo(I)),I.apply(_,y)}}function Lo(e,t){return function(n,o){return function(e,t,n,o){return Ot(e,(function(e,r,a){t(o,n(e),r,a)})),o}(n,e,t(o),{})}}function Co(e,t){return function(n,o){var r;if(n===F&&o===F)return t;if(n!==F&&(r=n),o!==F){if(r===F)return o;"string"==typeof n||"string"==typeof o?(n=Gn(n),o=Gn(o)):(n=Vn(n),o=Vn(o)),r=e(n,o)}return r}}function Ao(t){return Eo((function(n){return n=l(n,A(Yo())),Un((function(o){var r=this;return t(n,(function(t){return e(t,r,o)}))}))}))}function So(e,t){var n=(t=t===F?" ":Gn(t)).length;if(n<2)return n?Mn(t,e):t;var o=Mn(t,Za(e/O(t)));return R(t)?oo(E(o),0,e).join(""):o.slice(0,e)}function jo(t,n,o,r){var a=1&n,i=bo(t);return function n(){for(var s=-1,l=arguments.length,u=-1,c=r.length,d=ha(c+l),p=this&&this!==tn&&this instanceof n?i:t;++u<c;)d[u]=r[u];for(;l--;)d[u++]=arguments[++s];return e(p,a?o:this,d)}}function Po(e){return function(t,n,o){return o&&"number"!=typeof o&&Zo(t,n,o)&&(n=o=F),t=Wr(t),n===F?(n=t,t=0):n=Wr(n),function(e,t,n,o){for(var r=-1,a=ai(Za((t-e)/(n||1)),0),i=ha(a);a--;)i[o?a:++r]=e,e+=n;return i}(t,n,o=o===F?t<n?1:-1:Wr(o),e)}}function Do(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=Jr(t),n=Jr(n)),e(t,n)}}function To(e,t,n,o,r,a,i,s,l,u){var c=8&t;t|=c?W:q,4&(t&=~(c?q:W))||(t&=-4);var d=[e,t,r,c?a:F,c?i:F,c?F:a,c?F:i,s,l,u],p=n.apply(F,d);return er(e)&&Fi(p,d),p.placeholder=o,sr(p,e,t)}function Ro(e){var t=wa[e];return function(e,n){if(e=Jr(e),(n=null==n?0:ii(qr(n),292))&&ni(e)){var o=(Zr(e)+"e").split("e");return+((o=(Zr(t(o[0]+"e"+(+o[1]+n)))+"e").split("e"))[0]+"e"+(+o[1]-n))}return t(e)}}function Ho(e){return function(t){var n=Bi(t);return n==de?M(t):n==he?function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}(t):function(e,t){return l(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Mo(e,t,n,o,r,a,i,s){var l=2&t;if(!l&&"function"!=typeof e)throw new La(z);var u=o?o.length:0;if(u||(t&=-97,o=r=F),i=i===F?i:ai(qr(i),0),s=s===F?s:qr(s),u-=r?r.length:0,t&q){var c=o,d=r;o=r=F}var p=l?F:$i(e),m=[e,t,n,o,r,c,d,a,i,s];if(p&&function(e,t){var n=e[1],o=t[1],r=n|o,a=r<131,i=o==K&&8==n||o==K&&n==J&&e[7].length<=t[8]||384==o&&t[7].length<=t[8]&&8==n;if(!a&&!i)return e;1&o&&(e[2]=t[2],r|=1&n?0:4);var s=t[3];if(s){var l=e[3];e[3]=l?lo(l,s,t[4]):s,e[4]=l?x(e[3],V):t[4]}s=t[5],s&&(l=e[5],e[5]=l?uo(l,s,t[6]):s,e[6]=l?x(e[5],V):t[6]),s=t[7],s&&(e[7]=s),o&K&&(e[8]=null==e[8]?t[8]:ii(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=r}(m,p),e=m[0],t=m[1],n=m[2],o=m[3],r=m[4],!(s=m[9]=m[9]===F?l?0:e.length:ai(m[9]-u,0))&&24&t&&(t&=-25),t&&1!=t)f=8==t||t==G?wo(e,t,s):t!=W&&33!=t||r.length?Io.apply(F,m):jo(e,t,n,o);else var f=function(e,t,n){var o=1&t,r=bo(e);return function t(){return(this&&this!==tn&&this instanceof t?r:e).apply(o?n:this,arguments)}}(e,t,n);return sr((p?Ri:Fi)(f,m),e,t)}function Uo(e,t,n,o){return e===F||Hr(e,Sa[n])&&!Da.call(o,n)?t:e}function xo(e,t,n,o,r,a){return Br(e)&&Br(t)&&(a.set(t,e),Sn(e,t,F,xo,a),a.delete(t)),e}function $o(e){return zr(e)?F:e}function Oo(e,t,n,o,r,a){var i=1&n,s=e.length,l=t.length;if(s!=l&&!(i&&l>s))return!1;var u=a.get(e),c=a.get(t);if(u&&c)return u==t&&c==e;var d=-1,m=!0,f=2&n?new ht:F;for(a.set(e,t),a.set(t,e);++d<s;){var g=e[d],h=t[d];if(o)var v=i?o(h,g,d,t,e,a):o(g,h,d,e,t,a);if(v!==F){if(v)continue;m=!1;break}if(f){if(!p(t,(function(e,t){if(!j(f,t)&&(g===e||r(g,e,n,o,a)))return f.push(t)}))){m=!1;break}}else if(g!==h&&!r(g,h,n,o,a)){m=!1;break}}return a.delete(e),a.delete(t),m}function Eo(e){return Yi(rr(e,F,hr),e+"")}function Bo(e){return Yt(e,ta,Oi)}function No(e){return Yt(e,na,Ei)}function Fo(e){for(var t=e.name+"",n=yi[t],o=Da.call(yi,t)?n.length:0;o--;){var r=n[o],a=r.func;if(null==a||a==e)return r.name}return t}function zo(e){return(Da.call(Qe,"placeholder")?Qe:e).placeholder}function Yo(){var e=Qe.iteratee||ca;return e=e===ca?wn:e,arguments.length?e(arguments[0],arguments[1]):e}function Vo(e,t){var n=e.__data__;return function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}(t)?n["string"==typeof t?"string":"hash"]:n.map}function Go(e){for(var t=ta(e),n=t.length;n--;){var o=t[n],r=e[o];t[n]=[o,r,nr(r)]}return t}function Wo(e,t){var n=function(e,t){return null==e?F:e[t]}(e,t);return bn(n)?n:F}function qo(e,t,n){for(var o=-1,r=(t=no(t,e)).length,a=!1;++o<r;){var i=cr(t[o]);if(!(a=null!=e&&n(e,i)))break;e=e[i]}return a||++o!=r?a:!!(r=null==e?0:e.length)&&Er(r)&&Qo(i,r)&&(Ds(e)||Ps(e))}function Ko(e){return"function"!=typeof e.constructor||tr(e)?{}:Si(Na(e))}function Jo(e){return Ds(e)||Ps(e)||!!(Va&&e&&e[Va])}function Qo(e,t){var n=typeof e;return!!(t=null==t?Z:t)&&("number"==n||"symbol"!=n&&it.test(e))&&e>-1&&e%1==0&&e<t}function Zo(e,t,n){if(!Br(n))return!1;var o=typeof t;return!!("number"==o?Mr(n)&&Qo(t,n.length):"string"==o&&t in n)&&Hr(n[t],e)}function Xo(e,t){if(Ds(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!Vr(e))||Fe.test(e)||!Ne.test(e)||null!=t&&e in ka(t)}function er(e){var t=Fo(e),n=Qe[t];if("function"!=typeof n||!(t in pt.prototype))return!1;if(e===n)return!0;var o=$i(n);return!!o&&e===o[0]}function tr(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Sa)}function nr(e){return e==e&&!Br(e)}function or(e,t){return function(n){return null!=n&&n[e]===t&&(t!==F||e in ka(n))}}function rr(t,n,o){return n=ai(n===F?t.length-1:n,0),function(){for(var r=arguments,a=-1,i=ai(r.length-n,0),s=ha(i);++a<i;)s[a]=r[n+a];a=-1;for(var l=ha(n+1);++a<n;)l[a]=r[a];return l[n]=o(s),e(t,this,l)}}function ar(e,t){return t.length<2?e:zt(e,Bn(t,0,-1))}function ir(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function sr(e,t,n){var o=t+"";return Yi(e,function(e,t){var n=t.length;if(!n)return e;var o=n-1;return t[o]=(n>1?"& ":"")+t[o],t=t.join(n>2?", ":" "),e.replace(qe,"{\n/* [wrapped with "+t+"] */\n")}(o,pr(function(e){var t=e.match(Ke);return t?t[1].split(Je):[]}(o),n)))}function lr(e){var t=0,n=0;return function(){var o=si(),r=16-(o-n);if(n=o,r>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(F,arguments)}}function ur(e,t){var n=-1,o=e.length,r=o-1;for(t=t===F?o:t;++n<t;){var a=Hn(n,r),i=e[a];e[a]=e[n],e[n]=i}return e.length=t,e}function cr(e){if("string"==typeof e||Vr(e))return e;var t=e+"";return"0"==t&&1/e==-Q?"-0":t}function dr(e){if(null!=e){try{return Pa.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function pr(e,t){return n(oe,(function(n){var o="_."+n[0];t&n[1]&&!i(e,o)&&e.push(o)})),e.sort()}function mr(e){if(e instanceof pt)return e.clone();var t=new dt(e.__wrapped__,e.__chain__);return t.__actions__=co(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function fr(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var r=null==n?0:qr(n);return r<0&&(r=ai(o+r,0)),g(e,Yo(t,3),r)}function gr(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var r=o-1;return n!==F&&(r=qr(n),r=n<0?ai(o+r,0):ii(r,o-1)),g(e,Yo(t,3),r,!0)}function hr(e){return null!=e&&e.length?$t(e,1):[]}function vr(e){return e&&e.length?e[0]:F}function yr(e){var t=null==e?0:e.length;return t?e[t-1]:F}function br(e,t){return e&&e.length&&t&&t.length?Tn(e,t):e}function wr(e){return null==e?e:ci.call(e)}function kr(e){if(!e||!e.length)return[];var t=0;return e=a(e,(function(e){if(Ur(e))return t=ai(e.length,t),!0})),L(t,(function(t){return l(e,w(t))}))}function _r(t,n){if(!t||!t.length)return[];var o=kr(t);return null==n?o:l(o,(function(t){return e(n,F,t)}))}function Ir(e){var t=Qe(e);return t.__chain__=!0,t}function Lr(e,t){return t(e)}function Cr(e,t){return(Ds(e)?n:ji)(e,Yo(t,3))}function Ar(e,t){return(Ds(e)?o:Pi)(e,Yo(t,3))}function Sr(e,t){return(Ds(e)?l:Ln)(e,Yo(t,3))}function jr(e,t,n){return t=n?F:t,t=e&&null==t?e.length:t,Mo(e,K,F,F,F,F,t)}function Pr(e,t){var n;if("function"!=typeof t)throw new La(z);return e=qr(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=F),n}}function Dr(e,t,n){function o(t){var n=l,o=u;return l=u=F,f=t,d=e.apply(o,n)}function r(e){var n=e-m;return m===F||n>=t||n<0||h&&e-f>=c}function a(){var e=ys();return r(e)?i(e):(p=zi(a,function(e){var n=t-(e-m);return h?ii(n,c-(e-f)):n}(e)),F)}function i(e){return p=F,v&&l?o(e):(l=u=F,d)}function s(){var e=ys(),n=r(e);if(l=arguments,u=this,m=e,n){if(p===F)return function(e){return f=e,p=zi(a,t),g?o(e):d}(m);if(h)return Ui(p),p=zi(a,t),o(m)}return p===F&&(p=zi(a,t)),d}var l,u,c,d,p,m,f=0,g=!1,h=!1,v=!0;if("function"!=typeof e)throw new La(z);return t=Jr(t)||0,Br(n)&&(g=!!n.leading,c=(h="maxWait"in n)?ai(Jr(n.maxWait)||0,t):c,v="trailing"in n?!!n.trailing:v),s.cancel=function(){p!==F&&Ui(p),f=0,l=m=u=p=F},s.flush=function(){return p===F?d:i(ys())},s}function Tr(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new La(z);var n=function(){var o=arguments,r=t?t.apply(this,o):o[0],a=n.cache;if(a.has(r))return a.get(r);var i=e.apply(this,o);return n.cache=a.set(r,i)||a,i};return n.cache=new(Tr.Cache||gt),n}function Rr(e){if("function"!=typeof e)throw new La(z);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Hr(e,t){return e===t||e!=e&&t!=t}function Mr(e){return null!=e&&Er(e.length)&&!$r(e)}function Ur(e){return Nr(e)&&Mr(e)}function xr(e){if(!Nr(e))return!1;var t=Vt(e);return t==le||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!zr(e)}function $r(e){if(!Br(e))return!1;var t=Vt(e);return t==ue||t==ce||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Or(e){return"number"==typeof e&&e==qr(e)}function Er(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Z}function Br(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Nr(e){return null!=e&&"object"==typeof e}function Fr(e){return"number"==typeof e||Nr(e)&&Vt(e)==pe}function zr(e){if(!Nr(e)||Vt(e)!=me)return!1;var t=Na(e);if(null===t)return!0;var n=Da.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Pa.call(n)==Ma}function Yr(e){return"string"==typeof e||!Ds(e)&&Nr(e)&&Vt(e)==ve}function Vr(e){return"symbol"==typeof e||Nr(e)&&Vt(e)==ye}function Gr(e){if(!e)return[];if(Mr(e))return Yr(e)?E(e):co(e);if(Ga&&e[Ga])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[Ga]());var t=Bi(e);return(t==de?M:t==he?$:ra)(e)}function Wr(e){return e?(e=Jr(e))===Q||e===-Q?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function qr(e){var t=Wr(e),n=t%1;return t==t?n?t-n:t:0}function Kr(e){return e?Pt(qr(e),0,ee):0}function Jr(e){if("number"==typeof e)return e;if(Vr(e))return X;if(Br(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Br(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=C(e);var n=ot.test(e);return n||at.test(e)?Zt(e.slice(2),n?2:8):nt.test(e)?X:+e}function Qr(e){return po(e,na(e))}function Zr(e){return null==e?"":Gn(e)}function Xr(e,t,n){var o=null==e?F:zt(e,t);return o===F?n:o}function ea(e,t){return null!=e&&qo(e,t,en)}function ta(e){return Mr(e)?yt(e):kn(e)}function na(e){return Mr(e)?yt(e,!0):_n(e)}function oa(e,t){if(null==e)return{};var n=l(No(e),(function(e){return[e]}));return t=Yo(t),Dn(e,n,(function(e,n){return t(e,n[0])}))}function ra(e){return null==e?[]:S(e,ta(e))}function aa(e){return ul(Zr(e).toLowerCase())}function ia(e){return(e=Zr(e))&&e.replace(st,gn).replace(Nt,"")}function sa(e,t,n){return e=Zr(e),(t=n?F:t)===F?H(e)?N(e):m(e):e.match(t)||[]}function la(e){return function(){return e}}function ua(e){return e}function ca(e){return wn("function"==typeof e?e:Dt(e,1))}function da(e,t,o){var r=ta(t),a=Ft(t,r);null!=o||Br(t)&&(a.length||!r.length)||(o=t,t=e,e=this,a=Ft(t,ta(t)));var i=!(Br(o)&&"chain"in o&&!o.chain),s=$r(e);return n(a,(function(n){var o=t[n];e[n]=o,s&&(e.prototype[n]=function(){var t=this.__chain__;if(i||t){var n=e(this.__wrapped__);return(n.__actions__=co(this.__actions__)).push({func:o,args:arguments,thisArg:e}),n.__chain__=t,n}return o.apply(e,u([this.value()],arguments))})})),e}function pa(){}function ma(e){return Xo(e)?w(cr(e)):function(e){return function(t){return zt(t,e)}}(e)}function fa(){return[]}function ga(){return!1}var ha=(We=null==We?tn:yn.defaults(tn.Object(),We,yn.pick(tn,Gt))).Array,va=We.Date,ya=We.Error,ba=We.Function,wa=We.Math,ka=We.Object,_a=We.RegExp,Ia=We.String,La=We.TypeError,Ca=ha.prototype,Aa=ba.prototype,Sa=ka.prototype,ja=We["__core-js_shared__"],Pa=Aa.toString,Da=Sa.hasOwnProperty,Ta=0,Ra=function(){var e=/[^.]+$/.exec(ja&&ja.keys&&ja.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Ha=Sa.toString,Ma=Pa.call(ka),Ua=tn._,xa=_a("^"+Pa.call(Da).replace(Ye,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),$a=rn?We.Buffer:F,Oa=We.Symbol,Ea=We.Uint8Array,Ba=$a?$a.allocUnsafe:F,Na=U(ka.getPrototypeOf,ka),Fa=ka.create,za=Sa.propertyIsEnumerable,Ya=Ca.splice,Va=Oa?Oa.isConcatSpreadable:F,Ga=Oa?Oa.iterator:F,Wa=Oa?Oa.toStringTag:F,qa=function(){try{var e=Wo(ka,"defineProperty");return e({},"",{}),e}catch(e){}}(),Ka=We.clearTimeout!==tn.clearTimeout&&We.clearTimeout,Ja=va&&va.now!==tn.Date.now&&va.now,Qa=We.setTimeout!==tn.setTimeout&&We.setTimeout,Za=wa.ceil,Xa=wa.floor,ei=ka.getOwnPropertySymbols,ti=$a?$a.isBuffer:F,ni=We.isFinite,oi=Ca.join,ri=U(ka.keys,ka),ai=wa.max,ii=wa.min,si=va.now,li=We.parseInt,ui=wa.random,ci=Ca.reverse,di=Wo(We,"DataView"),pi=Wo(We,"Map"),mi=Wo(We,"Promise"),fi=Wo(We,"Set"),gi=Wo(We,"WeakMap"),hi=Wo(ka,"create"),vi=gi&&new gi,yi={},bi=dr(di),wi=dr(pi),ki=dr(mi),_i=dr(fi),Ii=dr(gi),Li=Oa?Oa.prototype:F,Ci=Li?Li.valueOf:F,Ai=Li?Li.toString:F,Si=function(){function e(){}return function(t){if(!Br(t))return{};if(Fa)return Fa(t);e.prototype=t;var n=new e;return e.prototype=F,n}}();Qe.templateSettings={escape:Oe,evaluate:Ee,interpolate:Be,variable:"",imports:{_:Qe}},Qe.prototype=ct.prototype,Qe.prototype.constructor=Qe,dt.prototype=Si(ct.prototype),dt.prototype.constructor=dt,pt.prototype=Si(ct.prototype),pt.prototype.constructor=pt,mt.prototype.clear=function(){this.__data__=hi?hi(null):{},this.size=0},mt.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},mt.prototype.get=function(e){var t=this.__data__;if(hi){var n=t[e];return n===Y?F:n}return Da.call(t,e)?t[e]:F},mt.prototype.has=function(e){var t=this.__data__;return hi?t[e]!==F:Da.call(t,e)},mt.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=hi&&t===F?Y:t,this},ft.prototype.clear=function(){this.__data__=[],this.size=0},ft.prototype.delete=function(e){var t=this.__data__,n=Lt(t,e);return!(n<0||(n==t.length-1?t.pop():Ya.call(t,n,1),--this.size,0))},ft.prototype.get=function(e){var t=this.__data__,n=Lt(t,e);return n<0?F:t[n][1]},ft.prototype.has=function(e){return Lt(this.__data__,e)>-1},ft.prototype.set=function(e,t){var n=this.__data__,o=Lt(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this},gt.prototype.clear=function(){this.size=0,this.__data__={hash:new mt,map:new(pi||ft),string:new mt}},gt.prototype.delete=function(e){var t=Vo(this,e).delete(e);return this.size-=t?1:0,t},gt.prototype.get=function(e){return Vo(this,e).get(e)},gt.prototype.has=function(e){return Vo(this,e).has(e)},gt.prototype.set=function(e,t){var n=Vo(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this},ht.prototype.add=ht.prototype.push=function(e){return this.__data__.set(e,Y),this},ht.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.clear=function(){this.__data__=new ft,this.size=0},vt.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},vt.prototype.get=function(e){return this.__data__.get(e)},vt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ft){var o=n.__data__;if(!pi||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new gt(o)}return n.set(e,t),this.size=n.size,this};var ji=go(Ot),Pi=go(Et,!0),Di=ho(),Ti=ho(!0),Ri=vi?function(e,t){return vi.set(e,t),e}:ua,Hi=qa?function(e,t){return qa(e,"toString",{configurable:!0,enumerable:!1,value:la(t),writable:!0})}:ua,Mi=Un,Ui=Ka||function(e){return tn.clearTimeout(e)},xi=fi&&1/$(new fi([,-0]))[1]==Q?function(e){return new fi(e)}:pa,$i=vi?function(e){return vi.get(e)}:pa,Oi=ei?function(e){return null==e?[]:(e=ka(e),a(ei(e),(function(t){return za.call(e,t)})))}:fa,Ei=ei?function(e){for(var t=[];e;)u(t,Oi(e)),e=Na(e);return t}:fa,Bi=Vt;(di&&Bi(new di(new ArrayBuffer(1)))!=ke||pi&&Bi(new pi)!=de||mi&&Bi(mi.resolve())!=fe||fi&&Bi(new fi)!=he||gi&&Bi(new gi)!=be)&&(Bi=function(e){var t=Vt(e),n=t==me?e.constructor:F,o=n?dr(n):"";if(o)switch(o){case bi:return ke;case wi:return de;case ki:return fe;case _i:return he;case Ii:return be}return t});var Ni=ja?$r:ga,Fi=lr(Ri),zi=Qa||function(e,t){return tn.setTimeout(e,t)},Yi=lr(Hi),Vi=function(e){var t=Tr(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(ze,(function(e,n,o,r){t.push(o?r.replace(Xe,"$1"):n||e)})),t})),Gi=Un((function(e,t){return Ur(e)?Ht(e,$t(t,1,Ur,!0)):[]})),Wi=Un((function(e,t){var n=yr(t);return Ur(n)&&(n=F),Ur(e)?Ht(e,$t(t,1,Ur,!0),Yo(n,2)):[]})),qi=Un((function(e,t){var n=yr(t);return Ur(n)&&(n=F),Ur(e)?Ht(e,$t(t,1,Ur,!0),F,n):[]})),Ki=Un((function(e){var t=l(e,eo);return t.length&&t[0]===e[0]?nn(t):[]})),Ji=Un((function(e){var t=yr(e),n=l(e,eo);return t===yr(n)?t=F:n.pop(),n.length&&n[0]===e[0]?nn(n,Yo(t,2)):[]})),Qi=Un((function(e){var t=yr(e),n=l(e,eo);return(t="function"==typeof t?t:F)&&n.pop(),n.length&&n[0]===e[0]?nn(n,F,t):[]})),Zi=Un(br),Xi=Eo((function(e,t){var n=null==e?0:e.length,o=jt(e,t);return Rn(e,l(t,(function(e){return Qo(e,n)?+e:e})).sort(so)),o})),es=Un((function(e){return Wn($t(e,1,Ur,!0))})),ts=Un((function(e){var t=yr(e);return Ur(t)&&(t=F),Wn($t(e,1,Ur,!0),Yo(t,2))})),ns=Un((function(e){var t=yr(e);return t="function"==typeof t?t:F,Wn($t(e,1,Ur,!0),F,t)})),os=Un((function(e,t){return Ur(e)?Ht(e,t):[]})),rs=Un((function(e){return Zn(a(e,Ur))})),as=Un((function(e){var t=yr(e);return Ur(t)&&(t=F),Zn(a(e,Ur),Yo(t,2))})),is=Un((function(e){var t=yr(e);return t="function"==typeof t?t:F,Zn(a(e,Ur),F,t)})),ss=Un(kr),ls=Un((function(e){var t=e.length,n=t>1?e[t-1]:F;return n="function"==typeof n?(e.pop(),n):F,_r(e,n)})),us=Eo((function(e){var t=e.length,n=t?e[0]:0,o=this.__wrapped__,r=function(t){return jt(t,e)};return!(t>1||this.__actions__.length)&&o instanceof pt&&Qo(n)?((o=o.slice(n,+n+(t?1:0))).__actions__.push({func:Lr,args:[r],thisArg:F}),new dt(o,this.__chain__).thru((function(e){return t&&!e.length&&e.push(F),e}))):this.thru(r)})),cs=mo((function(e,t,n){Da.call(e,n)?++e[n]:St(e,n,1)})),ds=ko(fr),ps=ko(gr),ms=mo((function(e,t,n){Da.call(e,n)?e[n].push(t):St(e,n,[t])})),fs=Un((function(t,n,o){var r=-1,a="function"==typeof n,i=Mr(t)?ha(t.length):[];return ji(t,(function(t){i[++r]=a?e(n,t,o):on(t,n,o)})),i})),gs=mo((function(e,t,n){St(e,n,t)})),hs=mo((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]})),vs=Un((function(e,t){if(null==e)return[];var n=t.length;return n>1&&Zo(e,t[0],t[1])?t=[]:n>2&&Zo(t[0],t[1],t[2])&&(t=[t[0]]),Pn(e,$t(t,1),[])})),ys=Ja||function(){return tn.Date.now()},bs=Un((function(e,t,n){var o=1;if(n.length){var r=x(n,zo(bs));o|=W}return Mo(e,o,t,n,r)})),ws=Un((function(e,t,n){var o=3;if(n.length){var r=x(n,zo(ws));o|=W}return Mo(t,o,e,n,r)})),ks=Un((function(e,t){return Rt(e,1,t)})),_s=Un((function(e,t,n){return Rt(e,Jr(t)||0,n)}));Tr.Cache=gt;var Is=Mi((function(t,n){var o=(n=1==n.length&&Ds(n[0])?l(n[0],A(Yo())):l($t(n,1),A(Yo()))).length;return Un((function(r){for(var a=-1,i=ii(r.length,o);++a<i;)r[a]=n[a].call(this,r[a]);return e(t,this,r)}))})),Ls=Un((function(e,t){return Mo(e,W,F,t,x(t,zo(Ls)))})),Cs=Un((function(e,t){return Mo(e,q,F,t,x(t,zo(Cs)))})),As=Eo((function(e,t){return Mo(e,J,F,F,F,t)})),Ss=Do(Jt),js=Do((function(e,t){return e>=t})),Ps=an(function(){return arguments}())?an:function(e){return Nr(e)&&Da.call(e,"callee")&&!za.call(e,"callee")},Ds=ha.isArray,Ts=ln?A(ln):function(e){return Nr(e)&&Vt(e)==we},Rs=ti||ga,Hs=un?A(un):function(e){return Nr(e)&&Vt(e)==se},Ms=cn?A(cn):function(e){return Nr(e)&&Bi(e)==de},Us=dn?A(dn):function(e){return Nr(e)&&Vt(e)==ge},xs=pn?A(pn):function(e){return Nr(e)&&Bi(e)==he},$s=mn?A(mn):function(e){return Nr(e)&&Er(e.length)&&!!qt[Vt(e)]},Os=Do(In),Es=Do((function(e,t){return e<=t})),Bs=fo((function(e,t){if(tr(t)||Mr(t))return po(t,ta(t),e),F;for(var n in t)Da.call(t,n)&&It(e,n,t[n])})),Ns=fo((function(e,t){po(t,na(t),e)})),Fs=fo((function(e,t,n,o){po(t,na(t),e,o)})),zs=fo((function(e,t,n,o){po(t,ta(t),e,o)})),Ys=Eo(jt),Vs=Un((function(e,t){e=ka(e);var n=-1,o=t.length,r=o>2?t[2]:F;for(r&&Zo(t[0],t[1],r)&&(o=1);++n<o;)for(var a=t[n],i=na(a),s=-1,l=i.length;++s<l;){var u=i[s],c=e[u];(c===F||Hr(c,Sa[u])&&!Da.call(e,u))&&(e[u]=a[u])}return e})),Gs=Un((function(t){return t.push(F,xo),e(Qs,F,t)})),Ws=Lo((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ha.call(t)),e[t]=n}),la(ua)),qs=Lo((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ha.call(t)),Da.call(e,t)?e[t].push(n):e[t]=[n]}),Yo),Ks=Un(on),Js=fo((function(e,t,n){Sn(e,t,n)})),Qs=fo((function(e,t,n,o){Sn(e,t,n,o)})),Zs=Eo((function(e,t){var n={};if(null==e)return n;var o=!1;t=l(t,(function(t){return t=no(t,e),o||(o=t.length>1),t})),po(e,No(e),n),o&&(n=Dt(n,7,$o));for(var r=t.length;r--;)qn(n,t[r]);return n})),Xs=Eo((function(e,t){return null==e?{}:function(e,t){return Dn(e,t,(function(t,n){return ea(e,n)}))}(e,t)})),el=Ho(ta),tl=Ho(na),nl=yo((function(e,t,n){return t=t.toLowerCase(),e+(n?aa(t):t)})),ol=yo((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),rl=yo((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),al=vo("toLowerCase"),il=yo((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()})),sl=yo((function(e,t,n){return e+(n?" ":"")+ul(t)})),ll=yo((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),ul=vo("toUpperCase"),cl=Un((function(t,n){try{return e(t,F,n)}catch(e){return xr(e)?e:new ya(e)}})),dl=Eo((function(e,t){return n(t,(function(t){t=cr(t),St(e,t,bs(e[t],e))})),e})),pl=_o(),ml=_o(!0),fl=Un((function(e,t){return function(n){return on(n,e,t)}})),gl=Un((function(e,t){return function(n){return on(e,n,t)}})),hl=Ao(l),vl=Ao(r),yl=Ao(p),bl=Po(),wl=Po(!0),kl=Co((function(e,t){return e+t}),0),_l=Ro("ceil"),Il=Co((function(e,t){return e/t}),1),Ll=Ro("floor"),Cl=Co((function(e,t){return e*t}),1),Al=Ro("round"),Sl=Co((function(e,t){return e-t}),0);return Qe.after=function(e,t){if("function"!=typeof t)throw new La(z);return e=qr(e),function(){if(--e<1)return t.apply(this,arguments)}},Qe.ary=jr,Qe.assign=Bs,Qe.assignIn=Ns,Qe.assignInWith=Fs,Qe.assignWith=zs,Qe.at=Ys,Qe.before=Pr,Qe.bind=bs,Qe.bindAll=dl,Qe.bindKey=ws,Qe.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Ds(e)?e:[e]},Qe.chain=Ir,Qe.chunk=function(e,t,n){t=(n?Zo(e,t,n):t===F)?1:ai(qr(t),0);var o=null==e?0:e.length;if(!o||t<1)return[];for(var r=0,a=0,i=ha(Za(o/t));r<o;)i[a++]=Bn(e,r,r+=t);return i},Qe.compact=function(e){for(var t=-1,n=null==e?0:e.length,o=0,r=[];++t<n;){var a=e[t];a&&(r[o++]=a)}return r},Qe.concat=function(){var e=arguments.length;if(!e)return[];for(var t=ha(e-1),n=arguments[0],o=e;o--;)t[o-1]=arguments[o];return u(Ds(n)?co(n):[n],$t(t,1))},Qe.cond=function(t){var n=null==t?0:t.length,o=Yo();return t=n?l(t,(function(e){if("function"!=typeof e[1])throw new La(z);return[o(e[0]),e[1]]})):[],Un((function(o){for(var r=-1;++r<n;){var a=t[r];if(e(a[0],this,o))return e(a[1],this,o)}}))},Qe.conforms=function(e){return function(e){var t=ta(e);return function(n){return Tt(n,e,t)}}(Dt(e,1))},Qe.constant=la,Qe.countBy=cs,Qe.create=function(e,t){var n=Si(e);return null==t?n:At(n,t)},Qe.curry=function e(t,n,o){var r=Mo(t,8,F,F,F,F,F,n=o?F:n);return r.placeholder=e.placeholder,r},Qe.curryRight=function e(t,n,o){var r=Mo(t,G,F,F,F,F,F,n=o?F:n);return r.placeholder=e.placeholder,r},Qe.debounce=Dr,Qe.defaults=Vs,Qe.defaultsDeep=Gs,Qe.defer=ks,Qe.delay=_s,Qe.difference=Gi,Qe.differenceBy=Wi,Qe.differenceWith=qi,Qe.drop=function(e,t,n){var o=null==e?0:e.length;return o?Bn(e,(t=n||t===F?1:qr(t))<0?0:t,o):[]},Qe.dropRight=function(e,t,n){var o=null==e?0:e.length;return o?Bn(e,0,(t=o-(t=n||t===F?1:qr(t)))<0?0:t):[]},Qe.dropRightWhile=function(e,t){return e&&e.length?Jn(e,Yo(t,3),!0,!0):[]},Qe.dropWhile=function(e,t){return e&&e.length?Jn(e,Yo(t,3),!0):[]},Qe.fill=function(e,t,n,o){var r=null==e?0:e.length;return r?(n&&"number"!=typeof n&&Zo(e,t,n)&&(n=0,o=r),function(e,t,n,o){var r=e.length;for((n=qr(n))<0&&(n=-n>r?0:r+n),(o=o===F||o>r?r:qr(o))<0&&(o+=r),o=n>o?0:Kr(o);n<o;)e[n++]=t;return e}(e,t,n,o)):[]},Qe.filter=function(e,t){return(Ds(e)?a:xt)(e,Yo(t,3))},Qe.flatMap=function(e,t){return $t(Sr(e,t),1)},Qe.flatMapDeep=function(e,t){return $t(Sr(e,t),Q)},Qe.flatMapDepth=function(e,t,n){return n=n===F?1:qr(n),$t(Sr(e,t),n)},Qe.flatten=hr,Qe.flattenDeep=function(e){return null!=e&&e.length?$t(e,Q):[]},Qe.flattenDepth=function(e,t){return null!=e&&e.length?$t(e,t=t===F?1:qr(t)):[]},Qe.flip=function(e){return Mo(e,512)},Qe.flow=pl,Qe.flowRight=ml,Qe.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,o={};++t<n;){var r=e[t];o[r[0]]=r[1]}return o},Qe.functions=function(e){return null==e?[]:Ft(e,ta(e))},Qe.functionsIn=function(e){return null==e?[]:Ft(e,na(e))},Qe.groupBy=ms,Qe.initial=function(e){return null!=e&&e.length?Bn(e,0,-1):[]},Qe.intersection=Ki,Qe.intersectionBy=Ji,Qe.intersectionWith=Qi,Qe.invert=Ws,Qe.invertBy=qs,Qe.invokeMap=fs,Qe.iteratee=ca,Qe.keyBy=gs,Qe.keys=ta,Qe.keysIn=na,Qe.map=Sr,Qe.mapKeys=function(e,t){var n={};return t=Yo(t,3),Ot(e,(function(e,o,r){St(n,t(e,o,r),e)})),n},Qe.mapValues=function(e,t){var n={};return t=Yo(t,3),Ot(e,(function(e,o,r){St(n,o,t(e,o,r))})),n},Qe.matches=function(e){return Cn(Dt(e,1))},Qe.matchesProperty=function(e,t){return An(e,Dt(t,1))},Qe.memoize=Tr,Qe.merge=Js,Qe.mergeWith=Qs,Qe.method=fl,Qe.methodOf=gl,Qe.mixin=da,Qe.negate=Rr,Qe.nthArg=function(e){return e=qr(e),Un((function(t){return jn(t,e)}))},Qe.omit=Zs,Qe.omitBy=function(e,t){return oa(e,Rr(Yo(t)))},Qe.once=function(e){return Pr(2,e)},Qe.orderBy=function(e,t,n,o){return null==e?[]:(Ds(t)||(t=null==t?[]:[t]),Ds(n=o?F:n)||(n=null==n?[]:[n]),Pn(e,t,n))},Qe.over=hl,Qe.overArgs=Is,Qe.overEvery=vl,Qe.overSome=yl,Qe.partial=Ls,Qe.partialRight=Cs,Qe.partition=hs,Qe.pick=Xs,Qe.pickBy=oa,Qe.property=ma,Qe.propertyOf=function(e){return function(t){return null==e?F:zt(e,t)}},Qe.pull=Zi,Qe.pullAll=br,Qe.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Tn(e,t,Yo(n,2)):e},Qe.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Tn(e,t,F,n):e},Qe.pullAt=Xi,Qe.range=bl,Qe.rangeRight=wl,Qe.rearg=As,Qe.reject=function(e,t){return(Ds(e)?a:xt)(e,Rr(Yo(t,3)))},Qe.remove=function(e,t){var n=[];if(!e||!e.length)return n;var o=-1,r=[],a=e.length;for(t=Yo(t,3);++o<a;){var i=e[o];t(i,o,e)&&(n.push(i),r.push(o))}return Rn(e,r),n},Qe.rest=function(e,t){if("function"!=typeof e)throw new La(z);return Un(e,t=t===F?t:qr(t))},Qe.reverse=wr,Qe.sampleSize=function(e,t,n){return t=(n?Zo(e,t,n):t===F)?1:qr(t),(Ds(e)?wt:$n)(e,t)},Qe.set=function(e,t,n){return null==e?e:On(e,t,n)},Qe.setWith=function(e,t,n,o){return o="function"==typeof o?o:F,null==e?e:On(e,t,n,o)},Qe.shuffle=function(e){return(Ds(e)?kt:En)(e)},Qe.slice=function(e,t,n){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&Zo(e,t,n)?(t=0,n=o):(t=null==t?0:qr(t),n=n===F?o:qr(n)),Bn(e,t,n)):[]},Qe.sortBy=vs,Qe.sortedUniq=function(e){return e&&e.length?Yn(e):[]},Qe.sortedUniqBy=function(e,t){return e&&e.length?Yn(e,Yo(t,2)):[]},Qe.split=function(e,t,n){return n&&"number"!=typeof n&&Zo(e,t,n)&&(t=n=F),(n=n===F?ee:n>>>0)?(e=Zr(e))&&("string"==typeof t||null!=t&&!Us(t))&&(!(t=Gn(t))&&R(e))?oo(E(e),0,n):e.split(t,n):[]},Qe.spread=function(t,n){if("function"!=typeof t)throw new La(z);return n=null==n?0:ai(qr(n),0),Un((function(o){var r=o[n],a=oo(o,0,n);return r&&u(a,r),e(t,this,a)}))},Qe.tail=function(e){var t=null==e?0:e.length;return t?Bn(e,1,t):[]},Qe.take=function(e,t,n){return e&&e.length?Bn(e,0,(t=n||t===F?1:qr(t))<0?0:t):[]},Qe.takeRight=function(e,t,n){var o=null==e?0:e.length;return o?Bn(e,(t=o-(t=n||t===F?1:qr(t)))<0?0:t,o):[]},Qe.takeRightWhile=function(e,t){return e&&e.length?Jn(e,Yo(t,3),!1,!0):[]},Qe.takeWhile=function(e,t){return e&&e.length?Jn(e,Yo(t,3)):[]},Qe.tap=function(e,t){return t(e),e},Qe.throttle=function(e,t,n){var o=!0,r=!0;if("function"!=typeof e)throw new La(z);return Br(n)&&(o="leading"in n?!!n.leading:o,r="trailing"in n?!!n.trailing:r),Dr(e,t,{leading:o,maxWait:t,trailing:r})},Qe.thru=Lr,Qe.toArray=Gr,Qe.toPairs=el,Qe.toPairsIn=tl,Qe.toPath=function(e){return Ds(e)?l(e,cr):Vr(e)?[e]:co(Vi(Zr(e)))},Qe.toPlainObject=Qr,Qe.transform=function(e,t,o){var r=Ds(e),a=r||Rs(e)||$s(e);if(t=Yo(t,4),null==o){var i=e&&e.constructor;o=a?r?new i:[]:Br(e)&&$r(i)?Si(Na(e)):{}}return(a?n:Ot)(e,(function(e,n,r){return t(o,e,n,r)})),o},Qe.unary=function(e){return jr(e,1)},Qe.union=es,Qe.unionBy=ts,Qe.unionWith=ns,Qe.uniq=function(e){return e&&e.length?Wn(e):[]},Qe.uniqBy=function(e,t){return e&&e.length?Wn(e,Yo(t,2)):[]},Qe.uniqWith=function(e,t){return t="function"==typeof t?t:F,e&&e.length?Wn(e,F,t):[]},Qe.unset=function(e,t){return null==e||qn(e,t)},Qe.unzip=kr,Qe.unzipWith=_r,Qe.update=function(e,t,n){return null==e?e:Kn(e,t,to(n))},Qe.updateWith=function(e,t,n,o){return o="function"==typeof o?o:F,null==e?e:Kn(e,t,to(n),o)},Qe.values=ra,Qe.valuesIn=function(e){return null==e?[]:S(e,na(e))},Qe.without=os,Qe.words=sa,Qe.wrap=function(e,t){return Ls(to(t),e)},Qe.xor=rs,Qe.xorBy=as,Qe.xorWith=is,Qe.zip=ss,Qe.zipObject=function(e,t){return Xn(e||[],t||[],It)},Qe.zipObjectDeep=function(e,t){return Xn(e||[],t||[],On)},Qe.zipWith=ls,Qe.entries=el,Qe.entriesIn=tl,Qe.extend=Ns,Qe.extendWith=Fs,da(Qe,Qe),Qe.add=kl,Qe.attempt=cl,Qe.camelCase=nl,Qe.capitalize=aa,Qe.ceil=_l,Qe.clamp=function(e,t,n){return n===F&&(n=t,t=F),n!==F&&(n=(n=Jr(n))==n?n:0),t!==F&&(t=(t=Jr(t))==t?t:0),Pt(Jr(e),t,n)},Qe.clone=function(e){return Dt(e,4)},Qe.cloneDeep=function(e){return Dt(e,5)},Qe.cloneDeepWith=function(e,t){return Dt(e,5,t="function"==typeof t?t:F)},Qe.cloneWith=function(e,t){return Dt(e,4,t="function"==typeof t?t:F)},Qe.conformsTo=function(e,t){return null==t||Tt(e,t,ta(t))},Qe.deburr=ia,Qe.defaultTo=function(e,t){return null==e||e!=e?t:e},Qe.divide=Il,Qe.endsWith=function(e,t,n){e=Zr(e),t=Gn(t);var o=e.length,r=n=n===F?o:Pt(qr(n),0,o);return(n-=t.length)>=0&&e.slice(n,r)==t},Qe.eq=Hr,Qe.escape=function(e){return(e=Zr(e))&&$e.test(e)?e.replace(Ue,hn):e},Qe.escapeRegExp=function(e){return(e=Zr(e))&&Ve.test(e)?e.replace(Ye,"\\$&"):e},Qe.every=function(e,t,n){var o=Ds(e)?r:Mt;return n&&Zo(e,t,n)&&(t=F),o(e,Yo(t,3))},Qe.find=ds,Qe.findIndex=fr,Qe.findKey=function(e,t){return f(e,Yo(t,3),Ot)},Qe.findLast=ps,Qe.findLastIndex=gr,Qe.findLastKey=function(e,t){return f(e,Yo(t,3),Et)},Qe.floor=Ll,Qe.forEach=Cr,Qe.forEachRight=Ar,Qe.forIn=function(e,t){return null==e?e:Di(e,Yo(t,3),na)},Qe.forInRight=function(e,t){return null==e?e:Ti(e,Yo(t,3),na)},Qe.forOwn=function(e,t){return e&&Ot(e,Yo(t,3))},Qe.forOwnRight=function(e,t){return e&&Et(e,Yo(t,3))},Qe.get=Xr,Qe.gt=Ss,Qe.gte=js,Qe.has=function(e,t){return null!=e&&qo(e,t,Xt)},Qe.hasIn=ea,Qe.head=vr,Qe.identity=ua,Qe.includes=function(e,t,n,o){e=Mr(e)?e:ra(e),n=n&&!o?qr(n):0;var r=e.length;return n<0&&(n=ai(r+n,0)),Yr(e)?n<=r&&e.indexOf(t,n)>-1:!!r&&h(e,t,n)>-1},Qe.indexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var r=null==n?0:qr(n);return r<0&&(r=ai(o+r,0)),h(e,t,r)},Qe.inRange=function(e,t,n){return t=Wr(t),n===F?(n=t,t=0):n=Wr(n),function(e,t,n){return e>=ii(t,n)&&e<ai(t,n)}(e=Jr(e),t,n)},Qe.invoke=Ks,Qe.isArguments=Ps,Qe.isArray=Ds,Qe.isArrayBuffer=Ts,Qe.isArrayLike=Mr,Qe.isArrayLikeObject=Ur,Qe.isBoolean=function(e){return!0===e||!1===e||Nr(e)&&Vt(e)==ie},Qe.isBuffer=Rs,Qe.isDate=Hs,Qe.isElement=function(e){return Nr(e)&&1===e.nodeType&&!zr(e)},Qe.isEmpty=function(e){if(null==e)return!0;if(Mr(e)&&(Ds(e)||"string"==typeof e||"function"==typeof e.splice||Rs(e)||$s(e)||Ps(e)))return!e.length;var t=Bi(e);if(t==de||t==he)return!e.size;if(tr(e))return!kn(e).length;for(var n in e)if(Da.call(e,n))return!1;return!0},Qe.isEqual=function(e,t){return sn(e,t)},Qe.isEqualWith=function(e,t,n){var o=(n="function"==typeof n?n:F)?n(e,t):F;return o===F?sn(e,t,F,n):!!o},Qe.isError=xr,Qe.isFinite=function(e){return"number"==typeof e&&ni(e)},Qe.isFunction=$r,Qe.isInteger=Or,Qe.isLength=Er,Qe.isMap=Ms,Qe.isMatch=function(e,t){return e===t||fn(e,t,Go(t))},Qe.isMatchWith=function(e,t,n){return n="function"==typeof n?n:F,fn(e,t,Go(t),n)},Qe.isNaN=function(e){return Fr(e)&&e!=+e},Qe.isNative=function(e){if(Ni(e))throw new ya("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return bn(e)},Qe.isNil=function(e){return null==e},Qe.isNull=function(e){return null===e},Qe.isNumber=Fr,Qe.isObject=Br,Qe.isObjectLike=Nr,Qe.isPlainObject=zr,Qe.isRegExp=Us,Qe.isSafeInteger=function(e){return Or(e)&&e>=-Z&&e<=Z},Qe.isSet=xs,Qe.isString=Yr,Qe.isSymbol=Vr,Qe.isTypedArray=$s,Qe.isUndefined=function(e){return e===F},Qe.isWeakMap=function(e){return Nr(e)&&Bi(e)==be},Qe.isWeakSet=function(e){return Nr(e)&&"[object WeakSet]"==Vt(e)},Qe.join=function(e,t){return null==e?"":oi.call(e,t)},Qe.kebabCase=ol,Qe.last=yr,Qe.lastIndexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var r=o;return n!==F&&(r=(r=qr(n))<0?ai(o+r,0):ii(r,o-1)),t==t?function(e,t,n){for(var o=n+1;o--;)if(e[o]===t)return o;return o}(e,t,r):g(e,y,r,!0)},Qe.lowerCase=rl,Qe.lowerFirst=al,Qe.lt=Os,Qe.lte=Es,Qe.max=function(e){return e&&e.length?Ut(e,ua,Jt):F},Qe.maxBy=function(e,t){return e&&e.length?Ut(e,Yo(t,2),Jt):F},Qe.mean=function(e){return b(e,ua)},Qe.meanBy=function(e,t){return b(e,Yo(t,2))},Qe.min=function(e){return e&&e.length?Ut(e,ua,In):F},Qe.minBy=function(e,t){return e&&e.length?Ut(e,Yo(t,2),In):F},Qe.stubArray=fa,Qe.stubFalse=ga,Qe.stubObject=function(){return{}},Qe.stubString=function(){return""},Qe.stubTrue=function(){return!0},Qe.multiply=Cl,Qe.nth=function(e,t){return e&&e.length?jn(e,qr(t)):F},Qe.noConflict=function(){return tn._===this&&(tn._=Ua),this},Qe.noop=pa,Qe.now=ys,Qe.pad=function(e,t,n){e=Zr(e);var o=(t=qr(t))?O(e):0;if(!t||o>=t)return e;var r=(t-o)/2;return So(Xa(r),n)+e+So(Za(r),n)},Qe.padEnd=function(e,t,n){e=Zr(e);var o=(t=qr(t))?O(e):0;return t&&o<t?e+So(t-o,n):e},Qe.padStart=function(e,t,n){e=Zr(e);var o=(t=qr(t))?O(e):0;return t&&o<t?So(t-o,n)+e:e},Qe.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),li(Zr(e).replace(Ge,""),t||0)},Qe.random=function(e,t,n){if(n&&"boolean"!=typeof n&&Zo(e,t,n)&&(t=n=F),n===F&&("boolean"==typeof t?(n=t,t=F):"boolean"==typeof e&&(n=e,e=F)),e===F&&t===F?(e=0,t=1):(e=Wr(e),t===F?(t=e,e=0):t=Wr(t)),e>t){var o=e;e=t,t=o}if(n||e%1||t%1){var r=ui();return ii(e+r*(t-e+Qt("1e-"+((r+"").length-1))),t)}return Hn(e,t)},Qe.reduce=function(e,t,n){var o=Ds(e)?c:_,r=arguments.length<3;return o(e,Yo(t,4),n,r,ji)},Qe.reduceRight=function(e,t,n){var o=Ds(e)?d:_,r=arguments.length<3;return o(e,Yo(t,4),n,r,Pi)},Qe.repeat=function(e,t,n){return t=(n?Zo(e,t,n):t===F)?1:qr(t),Mn(Zr(e),t)},Qe.replace=function(){var e=arguments,t=Zr(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Qe.result=function(e,t,n){var o=-1,r=(t=no(t,e)).length;for(r||(r=1,e=F);++o<r;){var a=null==e?F:e[cr(t[o])];a===F&&(o=r,a=n),e=$r(a)?a.call(e):a}return e},Qe.round=Al,Qe.runInContext=k,Qe.sample=function(e){return(Ds(e)?bt:xn)(e)},Qe.size=function(e){if(null==e)return 0;if(Mr(e))return Yr(e)?O(e):e.length;var t=Bi(e);return t==de||t==he?e.size:kn(e).length},Qe.snakeCase=il,Qe.some=function(e,t,n){var o=Ds(e)?p:Nn;return n&&Zo(e,t,n)&&(t=F),o(e,Yo(t,3))},Qe.sortedIndex=function(e,t){return Fn(e,t)},Qe.sortedIndexBy=function(e,t,n){return zn(e,t,Yo(n,2))},Qe.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var o=Fn(e,t);if(o<n&&Hr(e[o],t))return o}return-1},Qe.sortedLastIndex=function(e,t){return Fn(e,t,!0)},Qe.sortedLastIndexBy=function(e,t,n){return zn(e,t,Yo(n,2),!0)},Qe.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Fn(e,t,!0)-1;if(Hr(e[n],t))return n}return-1},Qe.startCase=sl,Qe.startsWith=function(e,t,n){return e=Zr(e),n=null==n?0:Pt(qr(n),0,e.length),t=Gn(t),e.slice(n,n+t.length)==t},Qe.subtract=Sl,Qe.sum=function(e){return e&&e.length?I(e,ua):0},Qe.sumBy=function(e,t){return e&&e.length?I(e,Yo(t,2)):0},Qe.template=function(e,t,n){var o=Qe.templateSettings;n&&Zo(e,t,n)&&(t=F),e=Zr(e),t=Fs({},t,o,Uo);var r,a,i=Fs({},t.imports,o.imports,Uo),s=ta(i),l=S(i,s),u=0,c=t.interpolate||lt,d="__p += '",p=_a((t.escape||lt).source+"|"+c.source+"|"+(c===Be?et:lt).source+"|"+(t.evaluate||lt).source+"|$","g"),m="//# sourceURL="+(Da.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Wt+"]")+"\n";e.replace(p,(function(t,n,o,i,s,l){return o||(o=i),d+=e.slice(u,l).replace(ut,T),n&&(r=!0,d+="' +\n__e("+n+") +\n'"),s&&(a=!0,d+="';\n"+s+";\n__p += '"),o&&(d+="' +\n((__t = ("+o+")) == null ? '' : __t) +\n'"),u=l+t.length,t})),d+="';\n";var f=Da.call(t,"variable")&&t.variable;if(f){if(Ze.test(f))throw new ya("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(a?d.replace(Te,""):d).replace(Re,"$1").replace(He,"$1;"),d="function("+(f||"obj")+") {\n"+(f?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(r?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var g=cl((function(){return ba(s,m+"return "+d).apply(F,l)}));if(g.source=d,xr(g))throw g;return g},Qe.times=function(e,t){if((e=qr(e))<1||e>Z)return[];var n=ee,o=ii(e,ee);t=Yo(t),e-=ee;for(var r=L(o,t);++n<e;)t(n);return r},Qe.toFinite=Wr,Qe.toInteger=qr,Qe.toLength=Kr,Qe.toLower=function(e){return Zr(e).toLowerCase()},Qe.toNumber=Jr,Qe.toSafeInteger=function(e){return e?Pt(qr(e),-Z,Z):0===e?e:0},Qe.toString=Zr,Qe.toUpper=function(e){return Zr(e).toUpperCase()},Qe.trim=function(e,t,n){if((e=Zr(e))&&(n||t===F))return C(e);if(!e||!(t=Gn(t)))return e;var o=E(e),r=E(t);return oo(o,P(o,r),D(o,r)+1).join("")},Qe.trimEnd=function(e,t,n){if((e=Zr(e))&&(n||t===F))return e.slice(0,B(e)+1);if(!e||!(t=Gn(t)))return e;var o=E(e);return oo(o,0,D(o,E(t))+1).join("")},Qe.trimStart=function(e,t,n){if((e=Zr(e))&&(n||t===F))return e.replace(Ge,"");if(!e||!(t=Gn(t)))return e;var o=E(e);return oo(o,P(o,E(t))).join("")},Qe.truncate=function(e,t){var n=30,o="...";if(Br(t)){var r="separator"in t?t.separator:r;n="length"in t?qr(t.length):n,o="omission"in t?Gn(t.omission):o}var a=(e=Zr(e)).length;if(R(e)){var i=E(e);a=i.length}if(n>=a)return e;var s=n-O(o);if(s<1)return o;var l=i?oo(i,0,s).join(""):e.slice(0,s);if(r===F)return l+o;if(i&&(s+=l.length-s),Us(r)){if(e.slice(s).search(r)){var u,c=l;for(r.global||(r=_a(r.source,Zr(tt.exec(r))+"g")),r.lastIndex=0;u=r.exec(c);)var d=u.index;l=l.slice(0,d===F?s:d)}}else if(e.indexOf(Gn(r),s)!=s){var p=l.lastIndexOf(r);p>-1&&(l=l.slice(0,p))}return l+o},Qe.unescape=function(e){return(e=Zr(e))&&xe.test(e)?e.replace(Me,vn):e},Qe.uniqueId=function(e){var t=++Ta;return Zr(e)+t},Qe.upperCase=ll,Qe.upperFirst=ul,Qe.each=Cr,Qe.eachRight=Ar,Qe.first=vr,da(Qe,function(){var e={};return Ot(Qe,(function(t,n){Da.call(Qe.prototype,n)||(e[n]=t)})),e}(),{chain:!1}),Qe.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Qe[e].placeholder=Qe})),n(["drop","take"],(function(e,t){pt.prototype[e]=function(n){n=n===F?1:ai(qr(n),0);var o=this.__filtered__&&!t?new pt(this):this.clone();return o.__filtered__?o.__takeCount__=ii(n,o.__takeCount__):o.__views__.push({size:ii(n,ee),type:e+(o.__dir__<0?"Right":"")}),o},pt.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),n(["filter","map","takeWhile"],(function(e,t){var n=t+1,o=1==n||3==n;pt.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Yo(e,3),type:n}),t.__filtered__=t.__filtered__||o,t}})),n(["head","last"],(function(e,t){var n="take"+(t?"Right":"");pt.prototype[e]=function(){return this[n](1).value()[0]}})),n(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");pt.prototype[e]=function(){return this.__filtered__?new pt(this):this[n](1)}})),pt.prototype.compact=function(){return this.filter(ua)},pt.prototype.find=function(e){return this.filter(e).head()},pt.prototype.findLast=function(e){return this.reverse().find(e)},pt.prototype.invokeMap=Un((function(e,t){return"function"==typeof e?new pt(this):this.map((function(n){return on(n,e,t)}))})),pt.prototype.reject=function(e){return this.filter(Rr(Yo(e)))},pt.prototype.slice=function(e,t){e=qr(e);var n=this;return n.__filtered__&&(e>0||t<0)?new pt(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==F&&(n=(t=qr(t))<0?n.dropRight(-t):n.take(t-e)),n)},pt.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},pt.prototype.toArray=function(){return this.take(ee)},Ot(pt.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),o=/^(?:head|last)$/.test(t),r=Qe[o?"take"+("last"==t?"Right":""):t],a=o||/^find/.test(t);r&&(Qe.prototype[t]=function(){var t=this.__wrapped__,i=o?[1]:arguments,s=t instanceof pt,l=i[0],c=s||Ds(t),d=function(e){var t=r.apply(Qe,u([e],i));return o&&p?t[0]:t};c&&n&&"function"==typeof l&&1!=l.length&&(s=c=!1);var p=this.__chain__,m=!!this.__actions__.length,f=a&&!p,g=s&&!m;if(!a&&c){t=g?t:new pt(this);var h=e.apply(t,i);return h.__actions__.push({func:Lr,args:[d],thisArg:F}),new dt(h,p)}return f&&g?e.apply(this,i):(h=this.thru(d),f?o?h.value()[0]:h.value():h)})})),n(["pop","push","shift","sort","splice","unshift"],(function(e){var t=Ca[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",o=/^(?:pop|shift)$/.test(e);Qe.prototype[e]=function(){var e=arguments;if(o&&!this.__chain__){var r=this.value();return t.apply(Ds(r)?r:[],e)}return this[n]((function(n){return t.apply(Ds(n)?n:[],e)}))}})),Ot(pt.prototype,(function(e,t){var n=Qe[t];if(n){var o=n.name+"";Da.call(yi,o)||(yi[o]=[]),yi[o].push({name:t,func:n})}})),yi[Io(F,2).name]=[{name:"wrapper",func:F}],pt.prototype.clone=function(){var e=new pt(this.__wrapped__);return e.__actions__=co(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=co(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=co(this.__views__),e},pt.prototype.reverse=function(){if(this.__filtered__){var e=new pt(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},pt.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Ds(e),o=t<0,r=n?e.length:0,a=function(e,t,n){for(var o=-1,r=n.length;++o<r;){var a=n[o],i=a.size;switch(a.type){case"drop":e+=i;break;case"dropRight":t-=i;break;case"take":t=ii(t,e+i);break;case"takeRight":e=ai(e,t-i)}}return{start:e,end:t}}(0,r,this.__views__),i=a.start,s=a.end,l=s-i,u=o?s:i-1,c=this.__iteratees__,d=c.length,p=0,m=ii(l,this.__takeCount__);if(!n||!o&&r==l&&m==l)return Qn(e,this.__actions__);var f=[];e:for(;l--&&p<m;){for(var g=-1,h=e[u+=t];++g<d;){var v=c[g],y=v.iteratee,b=v.type,w=y(h);if(2==b)h=w;else if(!w){if(1==b)continue e;break e}}f[p++]=h}return f},Qe.prototype.at=us,Qe.prototype.chain=function(){return Ir(this)},Qe.prototype.commit=function(){return new dt(this.value(),this.__chain__)},Qe.prototype.next=function(){this.__values__===F&&(this.__values__=Gr(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?F:this.__values__[this.__index__++]}},Qe.prototype.plant=function(e){for(var t,n=this;n instanceof ct;){var o=mr(n);o.__index__=0,o.__values__=F,t?r.__wrapped__=o:t=o;var r=o;n=n.__wrapped__}return r.__wrapped__=e,t},Qe.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof pt){var t=e;return this.__actions__.length&&(t=new pt(this)),(t=t.reverse()).__actions__.push({func:Lr,args:[wr],thisArg:F}),new dt(t,this.__chain__)}return this.thru(wr)},Qe.prototype.toJSON=Qe.prototype.valueOf=Qe.prototype.value=function(){return Qn(this.__wrapped__,this.__actions__)},Qe.prototype.first=Qe.prototype.head,Ga&&(Qe.prototype[Ga]=function(){return this}),Qe}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(tn._=yn,define((function(){return yn}))):on?((on.exports=yn)._=yn,nn._=yn):tn._=yn}).call(this),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null},hasLHSMenu:{type:Boolean,required:!1,default:!0}},template:'\n        <div :class="[header.isMenuOpen ? \'menuOpen\' : \'\']">\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid" :class="[!hasLHSMenu ? \'noLHSMenu\' : \'\']">\n                <yuno-header-v2 \n                    @userInfo="onUserInfo" \n                    @isMini="onMini" \n                    v-if="loginStatus && hasPageHeader"\n                >\n                </yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(e){this.$emit("onUserInfo",e)},onMini(e){this.isMiniSidebar=e}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(e,t){e!==t&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(e,t){"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(e){this.updateActiveOrg(e.id)},orgUpdated(e){const t=e?.response?.data;201===t?.code?(sessionStorage.clear(),window.location.reload(!0)):t?.message&&console.log(t.message)},updateActiveOrg(e){this.$buefy.loading.open();const t={apiURL:YUNOCommon.config.academy("activeOrg",!1),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:e},callback:!0,callbackFunc:e=>this.orgUpdated(e)};this.dispatchData("postData",t)},dispatchData(e,t){this.$store.dispatch(e,t)},init(){if("org-admin"===this.userInfo.data.role){const e=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=e}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e}}});const YUNOCommon=function(e){const t={errorMsg:{common:"An error seems to have occurred. Please try again. ",notMapped:"The selected course doesn't mapped with any instructor. Please select other course.",enrollmentError:"It seems something went wrong with our servers. Our team has been notified. Please try again later.",sesstionExpired:"Your session has been expired. Please login again to resume your session. "},awsHost:function(){let e=window.location.hostname;return"localhost"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"www.yunolearning.com"===e?"https://api.yunolearning.com":"stage.yunolearning.com"===e||"dev.yunolearning.com"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"webcache.googleusercontent.com"===e?"https://api.yunolearning.com":void 0},addVerion:function(e){let t="";return t=e?"?buildVersion=1":"&buildVersion=1",t},pickHost:function(){return"http://localhost"===this.host()||"http://*************"===this.host()?"https://dev.yunolearning.com":"https://www.yunolearning.com"===this.host()||"https://webcache.googleusercontent.com"===this.host()?"https://www.yunolearning.com":"https://local.yunolearning.com"===this.host()?"https://dev.yunolearning.com":this.host()},laravelHost:function(){return"http://localhost"===this.host()||"https://dev.yunolearning.com"===this.host()?"https://dev-ext.yunolearning.com":"https://stage.yunolearning.com"===this.host()?"https://stage-ext.yunolearning.com":"https://www.yunolearning.com"===this.host()?"https://ai-laravel-yxdza.kinsta.app":void 0},host:function(){return window.location.protocol+"//"+window.location.hostname},footerAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/footer"},latesBlogAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/latest/blogs"},courseIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/short-detail"},allCoursesIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/detail"},headerMenuAPI:function(e,t){let n="";void 0!==t&&(n="?category="+t);return this.pickHost()+"/wp-json/yuno/v1/menu/"+e+n},headerMenuAPIV2:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/menu/"+e+"/"+t},userRoleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/role"},userProfileAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/profile"+n},studentResultsAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/results/"+e+"/"+t+"/"+n},faqAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/faq/"+e},courseAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/getdetail/"+e+"/"+t},scheduleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/schedule"},instructorAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/getUserDetail"},updateInstructorDetailAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUserDetail"},instructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/instructor/"+e+"/"+t},instructorBatchAPI:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/instructor/"+t+"/"+n+"/"+o+"/"+r+"/"+a},instructorNonBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructorSingleProductDetail?instructorID="+e},categoriesAPi:function(e){void 0===e&&(e="");return this.pickHost()+"/wp-json/yuno/v1/category"+e},featuredCoursesAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredCourses"},featuredInstructorAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredInstructor/"+e},batchAPi:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/"+e+"/"+t+"/"+n+"/"+o},classAPi:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v1/classes/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a},signUpAPi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},signUpV2APi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},isUserSignUpAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/signup/"+e},loginAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/loginWithGoogle"},createPaymentAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment"},updatePaymentAPi:function(e,t,n,o,r){return this.pickHost()+"/wp-json/yuno/v1/update/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/payment"},myLearnersAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/schedule/mylearners/mygroups/"+e},classTitleAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/class-schedule/titles/instructor/"+e},addClassTitleAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/add/class/title"},createClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/create/class/"+e},updateClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/update/class/"+e},classesAPi:function(e,t,n,o,r){return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+"/"+t+"/"+n+"/"+o+"/"+r},classesByViewAPi:function(e,t,n,r,a,i,s,l,u,c){let d="",p="",m="";c&&(d="?ver="+o()),void 0!==t&&!1!==t&&(p="/"+t),void 0!==i&&!1!==i&&(m="/"+i);return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+p+"/"+n+"/"+r+"/"+a+m+"/"+s+"/"+l+"/"+u+d},groupsAPi:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/get/groups/"+e+"/"+t+"/"+n+"/"+o},addLearnersToGroupAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUsersInGroup"},updateGroupTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/updateGroup"},createGroupsAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/create/group"},crmContacts:function(){return this.awsHost()+"/getCRMContacts"},instructorLearnersAPI:function(e,t,n,o,r){let a="";!1!==r&&(a="?filter="+r);return this.pickHost()+"/wp-json/yuno/v1/mylearners/"+e+"/"+t+"/"+n+"/"+o+a},instructorBatchesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/batches/"+e+"/"+t},learnerCoursesAPI:function(e,t,n){let r="";n&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/courses/"+t+r},enHeroCardsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/english-speaking/detail"},classDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/classDetail/"+e+"/"+t},classLearnerAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/classLearnerDetail/"+e+"/"+t+"/"+n},demoClassEnrollAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/enroll/demo/class"},editClassAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/class/detail/"+e+"/instructor/"+t},allCoursesAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/all/course/list"+t},allBatchesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/admin/batches/all"},enrollmentStatusAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/batch/"+e+"/"+t+"/"+n+"/status"},generatePaymentLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment/link"},reviewsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},paymentList:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v1/payments/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a},enrollmentList:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a},enrollmentsList:function(e,t,n,o,r,a,i,s,l,u,c,d,p,m){let f="";f=void 0!==p&&!1!==p?p:"v1";return this.pickHost()+"/wp-json/yuno/"+f+"/enrollments/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a+"/"+i+"/"+s+"/"+l+"/"+u+"/"+m+"/"+c+"/"+d},paymentsList:function(e,t,n,r,a,i,s,l,u,c){let d="";c&&(d="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/payment/"+e+"/"+t+"/"+n+"/"+r+"/"+a+"/"+i+"/"+s+"/"+l+"/"+u+d},updatePaymentLinkAPI:function(){return this.awsHost()+"/payments/updatelink"},updateLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateLink/payment"},instructorListAPI:function(e,t){let n="",o="";void 0!==t&&!1!==t&&(o="/"+t),void 0!==e&&!1!==e&&(n="/"+e);return this.pickHost()+"/wp-json/yuno/v1/instructor/list"+n+o},reviewsByTypeAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},batchToggleAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/status/toggle"},changeBatchAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/changeBatches"},blogListAPI:function(e,t){return this.pickHost()+"/wp-json/wp/v2/posts/?per_page="+e+"&offset="+t+"&_embed"},pageAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/pages/"+e+"?_embed"},blogAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/posts/"+e+"?_embed"},postCategoriesAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/?per_page="+e},courseBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batches/all/"+e+"/0/upcomingOngoing"},blogsByCategoryAPI:function(e,t,n){return this.pickHost()+"/wp-json/wp/v2/posts?categories="+e+"&per_page="+t+"&offset="+n+"&_embed"},blogCategoryAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/"+e},settingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings/"+e},updateSettingsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings"},addressAPI:function(e,t,n){let r="";n&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+"/address/"+t+r},updateAddressAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/user/address"},listOfCounsellorsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/counselor/list"},googleContactsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/getGoogleContacts/"+e},meetingAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/meeting/"+e+"/"+t+"/"+n+"/"+o},participantsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/add/zoom/participants/"+e},batchesGrid:function(e,t,n,o,r,a,i,s,l){let u="";void 0!==i&&!1!==i&&(u="/"+i);return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a+u+"/"+s+"/"+l},mapCoursesAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/instructor/courses/"+e+"/"+t+"/"+n},updateInstructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/add/instructor/"+e+"/course/"+t},relatedCoursesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/courses"},categoryListAPI:function(e){let t="";void 0!==e&&(t="?filter="+e);return this.pickHost()+"/wp-json/yuno/v1/all/category/signup"+t},categoryTaxonomyAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/taxonomy/course_category"+t},createEBookAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/ebook/create"},eBookListAPI:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v1/ebook/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a},deleteResourceAttachmentAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/"+t+"/attachment/delete/"+n},resourceEmailAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/send/email"},createDocAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/document/create"},docListAPI:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v1/document/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a},videoListAPI:function(e,t){let n="",o="";void 0!==e&&(n=e),void 0===o&&!1===o||(o=t);return this.pickHost()+"/wp-json/yuno/v1/videos/"+n},videoSearchAPI:function(e){let t="";void 0===t&&!1===t||(t=e);return this.pickHost()+"/wp-json/yuno/v1/videos/"+t},videoListByViewAPI:function(e,t,n,o,r){let a="";if(!1!==t)a=t;else{let t="";void 0!==n&&(t=n),a=e+"/"+t+"/"+o+"/"+r}return this.pickHost()+"/wp-json/yuno/v1/video/get/"+a},createVideoAPI:function(e){let t="";t=e?"update":"create";return this.pickHost()+"/wp-json/yuno/v1/video/"+t},userInfoAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v3/user/info/"+e+n},vcSettingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/admin/vc/settings/"+e},reviewAPI:function(e,t,n,r,a,i,s){let l="",u="",c="",d="",p="",m="";void 0!==e&&!1!==e&&(l="/"+e),void 0!==r&&!1!==r&&(d="/"+r),void 0!==a&&!1!==a&&(p="/"+a),void 0!==i&&!1!==i&&(m="/"+i),void 0!==n&&!1!==n&&(c="/"+n),s&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/review"+l+"/"+t+c+d+p+m+u},courseListAPI:function(e,t,n,r){let a="",i="",s="";r&&(a="?ver="+o()),void 0!==t&&!1!==t&&(i="/"+t),void 0!==n&&!1!==n&&(s="/"+n);return this.pickHost()+"/wp-json/yuno/v1/all/"+e+"/detail/list"+i+s+a},countriesListAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/countries"},stateListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/state/country/"+e},cityListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/city/state/"+e},languageListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/user/languages"},listOfMappedInstructorAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/instructor/course/batch"},batchCreateUpdateAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/batch"},batchDetailAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+n},learnerListAPI:function(e,t){let n="";t&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/list/"+e+n},instructorAvailabilityAPI:function(e,t,n){let r="",a="";n&&(r="?ver="+o()),void 0!==t&&!1!==t&&(a="/"+t);return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+a+r},createUpdateAvailabilityAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+n},timeSlotsAPI:function(e){let t="";e&&(t="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/slots"+t},availabilityGridAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/days/"+e+n},instructorsByCategoryAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/category/"+e+n},capabilitiesAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/forte/"+e+n},paymentLinkUpdateAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/payment/link"},getInviteURLAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/invitation/link/"+e},invitedByUserAPI:function(e,t){let n="";t&&(n="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+n},signInURLWithState(e){const t=["email","profile"],n=encodeURI(JSON.stringify(e));let o="";if(void 0!==yunoCognitoLoginURL){const e=new URL(yunoCognitoLoginURL);e.searchParams.set("state",n);o=e.toString()}else o="https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&state="+n+"&scope="+t.join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";return o},updateUserCategoryAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/user/insert/category"},learnerHistoryAPI:function(e,t,n){let r="";n&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/"+e+"/"+t+r},eventDetailAPI:function(e,t,n,r,a){let i="";a&&(i="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/event/history/detail/"+e+"/"+t+"/"+n+"?uuid="+r+i},profileDetailAPI:function(e,t,n){let r="";n&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/profile/"+e+"/"+t+r},apiTokenExpiry:function(){return this.pickHost()+"/wp-json/yuno/v1/user/expire/time"},apiTokenRefresh:function(){return this.pickHost()+"/wp-json/yuno/v1/google/refresh/token"},staticPageAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/page/"+e},resourcesListingAPI:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a},resourcesDetailAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+o},videoTestimonialAPI:function(e,t){let n="";t&&(n="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+n},createExamResultAPI:function(e,t,n,o){let r="",a="";void 0===o&&!1===o&&(o="v1"),void 0!==t&&!1!==t&&(r="/"+t),void 0!==n&&!1!==n&&(a="/"+n);return this.pickHost()+"/wp-json/yuno/"+o+"/examresult/"+e+r+a},deleteExamResultAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/examresult/delete/"+e},manageVideotestimonialAPI:function(e,t,n){let o="",r="";void 0!==t&&(o="/"+t),void 0!==n&&(r="/"+n);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+o+r},videotestimonialListAPI:function(e,t,n,o){let r="",a="";void 0!==n&&(r="/"+n),void 0!==o&&(a="/"+o);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+"/"+t+r+a},deleteVideotestimonialAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/delete/"+e},manageArticleAPI:function(e,t,n,o,r,a,i){let s="",l="",u="",c="",d="",p="",m="";void 0!==e&&!1!==e&&(u="/"+e),void 0!==t&&!1!==t&&(m="/"+t),void 0!==n&&!1!==n&&(c="/"+n),void 0!==o&&!1!==o&&(d="/"+o),void 0!==r&&!1!==r&&(p="/"+r),void 0!==a&&!1!==a&&(s="/"+a),void 0!==i&&!1!==i&&(l="/"+i);return this.pickHost()+"/wp-json/yuno/v1/article"+u+m+c+d+p+s+l},webinarSingleAPI:function(e,t,n){let r="";n&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+r},webinarListingAPI:function(e,t,n,r,a){let i="";a&&(i="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+r+i},deleteWebinarAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/delete/class/"+e+"/"+t+"/"+n},webinarEnrollmentAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/webinar/enrollment"},webinarInsightsAPI:function(e,t,n,o,r,a,i){return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a+"/"+i},notificationListAPI:function(e,t){void 0!==t||(t="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+t+"/"+e},notificationUpdateAPI:function(e){void 0!==e||(e="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+e+"/update"},manageNotificationAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/notification/"+e},searchResourceAPI:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+e+"?search="+o},managelearningContentAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+n},learningContentAPI:function(e,t,n,o,r){let a="",i="",s="",l="";void 0!==t&&!1!==t&&(a="/"+t),void 0!==n&&!1!==n&&(i="/"+n),void 0!==o&&!1!==o&&(s="/"+o),void 0!==r&&!1!==r&&(l="/"+r);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+a+i+s+l},learnerInsightsAPI:function(e,t,n,r,a,i,s,l,u){let c="";u&&(c="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/"+t+"/"+n+"/"+r+"/"+a+"/"+i+"/"+s+"/"+l+c},learnerInsightsClassAPI:function(e,t,n,r){let a="",i="";i=void 0!==n&&!1!==n?"v2":"v1",r&&(a="?ver="+o());return this.pickHost()+"/wp-json/yuno/"+i+"/learner/class/"+e+"/"+t+a},signupFormAPI:function(e,t,n){let r="",a="";n&&(r="?ver="+o()),void 0!==t&&!1!==t&&(a="/?state="+t);return this.pickHost()+"/wp-json/yuno/v1/signup/form/"+e+a+r},resourceTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/event/resources/?search="+e+"&item="+t},resourceDraftsAPI:function(e,t,n,r,a){let i="";a&&(i="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/drafts/"+e+"/"+t+"/"+n+"/"+r+i},resourceDraftsDeleteAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/drafts/delete/"+e},demoRequestAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/demo/class/enroll/request"},instructorProfileAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/instructor/profile"},subjectsListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/all/subjects/list"},campaignAudienceAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/campaign/audience"},createCampaignAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/campaign"},coursesFiltersAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/category/"+e+"/"+t},coursesResultsAPI:function(e,t,n,o){let r="";!1!==o&&(r="?filters="+encodeURI(JSON.stringify(o)));return this.pickHost()+"/wp-json/yuno/v2/courses/web/"+e+"/detail/"+t+"/"+n+"/"+r},resourcesResultsAPI:function(e,t,n,o,r,a,i){let s="";!1!==a&&(s="?filters="+encodeURI(JSON.stringify(a))),void 0!==i&&!1!==i||(i="web");return this.pickHost()+"/wp-json/yuno/v2/resources/"+i+"/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+s},instructorStatsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/getinstructorstats/"+e+"/attendance"},instructorCoursesV2API:function(e,t,n){let r="";n&&(r="?ver="+o());return this.pickHost()+"/wp-json/yuno/v2/instructor/courses/category/"+e+"/"+t+r},instructorInsightsAPI:function(e,t,n,r,a,i,s,l){let u="";l&&(u="?ver="+o());return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t+"/"+n+"/"+r+"/"+a+"/"+i+"/"+s+u},enableDisableInstructorAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t},vcPermissionAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/vc/settings/"+e},instructorProfileInsightsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/"+t+"/profile"},piiAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/pii/"+t},mappedCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/mapped/course/"+e+"/"+t},makeFeaturedAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/featured/"+e+"/"+t},dashboardLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/dashboard/user/enrollment?search="+e},manageDashboardAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/dashboard/"+e+"/report"+n},dashboardListAPI:function(e,t,n,o,r){let a="",i="",s="",l="";void 0!==n&&!1!==n&&(l="/"+n),void 0!==t&&!1!==t&&(s="/"+t),void 0!==o&&!1!==o&&(a="/"+o),void 0!==r&&!1!==r&&(i="/"+r);return this.pickHost()+"/wp-json/yuno/v1/dashboard/report/"+e+s+l+a+i},enrollmentDashboardAPI:function(e,t,n,o,r){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/"+e+"/"+t+"/"+n+"/"+o+"/"+r},usersListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/list/"+e},enrollmentClassDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+e+"/"+t},vimeoVideoAPI:function(e){return"https://api.vimeo.com/videos/"+e},batchLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/learners"},courseBatchLearners:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/"+t+"/learners"},blogCategoriesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/taxonomy/blog_category"},manageBlogAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/blog/"+e+n},publishedBlogsAPI:function(e,t,n,o,r,a){let i="",s="",l="",u="",c="",d="";void 0!==e&&!1!==e&&(d="/"+e),void 0!==t&&!1!==t&&(l="/"+t),void 0!==n&&!1!==n&&(u="/"+n),void 0!==o&&!1!==o&&(c="/"+o),void 0!==r&&!1!==r&&(i="/"+r),void 0!==a&&!1!==a&&(s="/"+a);return this.pickHost()+"/wp-json/yuno/v1/blog"+d+l+u+c+i+s},categoriesListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/category/"+e},vimeoUploadVideoAPI:function(){return"https://api.vimeo.com/me/videos"},vimeoVideoPrivacyAPI:function(e,t){return"https://api.vimeo.com/videos/"+e+"/privacy/domains/"+t},manageVideoClippingAPI:function(e,t,n,o,r,a){a=void 0!==a&&!1!==a?"clippings":"clipping",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",r=void 0!==r&&!1!==r?"/"+r:"";return this.pickHost()+"/wp-json/yuno/v1/"+a+"/"+e+t+n+o+r},instructorMyCourses:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/mycourses/instructor/"+e+"/"+t+"/"+n},instructorCourseBatches:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v1/mycourses/"+e+"/"+t+"/"+n+"/batches/"+o+"/"+r+"/"+a},manageBookmarkAPI:function(e,t,n,o,r,a){void 0!==t&&!1!==t||(t="v1"),n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"/"+o:"",r=void 0!==r&&!1!==r?"/"+r:"",a=void 0!==a&&!1!==a?"?filters="+encodeURI(JSON.stringify(a)):"";return this.pickHost()+"/wp-json/yuno/"+t+"/bookmark/"+e+n+o+r+a},availableCourses:function(e,t,n,o){t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/available-courses/"+e+t+n+o},availableBatches:function(e,t,n,o){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",o=void 0!==o&&!1!==o?"?params="+encodeURI(JSON.stringify(o)):"";return this.pickHost()+"/wp-json/yuno/v2/batches/upcomingOngoing"+e+t+n},courseEnrollmentStatus:function(e,t){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"";return this.pickHost()+"/wp-json/yuno/v2/enrollment-status"+e+t},courseOneToOne:function(e,t,n,o,r,a,i){return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a+"/"+i},cloudinaryImageUpload:function(e){return!!e&&{upload_URL:"https://api.cloudinary.com/v1_1/harman-singh/upload",upload_preset:"jg32bezo"}},imageUpload:function(){return this.pickHost()+"/wp-json/yuno/v1/image/upload/"},categorySearch:function(){return this.pickHost()+"/wp-json/yuno/v1/category/search"},categoryResources:function(e){e=void 0!==e&&!1!==e?"?ids="+encodeURI(JSON.stringify(e)):"";return this.pickHost()+"/wp-json/yuno/v2/resources/"+e},coursesList:function(){return this.pickHost()+"/wp-json/yuno/v2/courses/all"},upcomingOngoingBatchesList:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+e+"/"+t+"/"+n+"/"+o},pastBatchesList:function(e,t,n,o,r,a){return this.pickHost()+"/wp-json/yuno/v2/batches/past/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a},checkout:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/checkout/"+e+"/"+t},instructorInsights:function(e,t,n,o,r,a,i,s,l,u,c,d,p){return this.pickHost()+"/wp-json/yuno/v2/instructor/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a+"/"+i+"/"+s+"/"+l+"/"+u+"/"+c+"/"+d+"/"+p},updateNativelanguage:function(e){return this.pickHost()+"/wp-json/yuno/v2/instructor/nativelanguage/"+e},endBatch:function(e){return this.pickHost()+"/wp-json/yuno/v1/endbatch/"+e},collections:function(e,t,n,o,r){return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+"/"+t+"/"+n+"/"+o+"/"+r},instructorVideotestimonial:function(e,t,n,o,r){return this.pickHost()+"/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor"},courses:function(e,t,n,o,r,a,i){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/"+t+"/"+n+"/"+o+"/"+r+"/"+a+"/"+i},activityList:function(){return this.pickHost()+"/wp-json/yuno/v1/activity"},subCategoriyList:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/"+e+"/subcategories"},courseSchedule:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e},courseScheduleForm:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e+"/"+t},createCSV:function(){return this.pickHost()+"/wp-json/yuno/v1/export/csv"},downloadCSV:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t},courseDetail:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/detail/"+e+"/"+t},reviewIssues:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/items/"+e+"/issue/"+t+"/"+n},reviewPost:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/post"},formReview:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/get/"+e+"/"+t+"/"+n},classReviews:function(e,t,n,o,r){return this.pickHost()+"/wp-json/yuno/v2/review/average/"+e+"/"+t+"/"+n+"/"+o+"/"+r},classReviewsByInstructor:function(e,t,n,o,r){return this.pickHost()+"/wp-json/yuno/v2/review/classreviews/instructor/"+e+"/"+t+"/"+n+"/"+o+"/"+r},listOfUser:function(e,t,n){let r="";n&&(r="&ver="+o());return this.pickHost()+"/wp-json/yuno/v1/"+t+"/list/"+e+r},learnerActivity:function(e){return this.pickHost()+"/wp-json/yuno/v2/get-feedback/"+e},recentLearnerClass:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/get-feedback-information/"+e+"/"+t},enrollmentListByType:function(e,t,n,o,r,a,i,s,l,u,c){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/enrollments/"+t+"/"+n+"/"+o+"/"+r+"/"+a+"/"+i+"/"+s+"/"+l+"/"+u+"/"+c},courseEconomics:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e},courseEconomicsForm:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n},courseEconomicsSummary:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n+"/"+o},csvList:function(e,t,n,o,r){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t+"/"+n+"/"+o+"/"+r},orgList:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/org/"+e},referrerDetails:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/details"},referrerID:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/"+e+"/"+t},mappedInstructors:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/course/"+e},generateRefferralURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/get-referral-url?params="+encodeURI(JSON.stringify(e))},generateRefferralCode:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/code/generate"},referrerURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/url"},referralDetail:function(e,t,n,o){void 0===o&&(o="v1");return this.pickHost()+"/wp-json/yuno/"+o+"/referrer/user/"+e+"/"+t+"/"+n},referralReports:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v1/referrer/report/"+e+"/"+t+"/"+n+"/"+o},orgToken:function(e,t){let n="";return n="POST"===t?this.pickHost()+"/wp-json/yuno/v1/create/token":"PUT"===t?this.pickHost()+"/wp-json/yuno/v1/update/token":this.pickHost()+"/wp-json/yuno/v1/token/"+e,n},webhooks:function(e,t,n,o,r,a){let i="";return"grid"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/"+t+"/"+n+"/"+o+"/"+r+"/"+a:"create"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/create":"events"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/events":"update"===e&&(i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/update"),i},seo:function(e,t,n,o){let r="";return"status"===e?r=this.pickHost()+"/wp-json/yuno/v1/seo/status/"+t:"markNoIndex"===e?r=this.pickHost()+"/wp-json/yuno/v1/seo/mark-no-index":"pageSearch"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/seo/search-get/"+n+"/?search="+o),r},quiz:function(e,t,n,o){let r="";return void 0!==o&&!1!==o||(o=""),"create"===e?r=this.pickHost()+"/wp-json/yuno/v1/quiz/":"update"===e?r=this.pickHost()+"/wp-json/yuno/v1/quiz":"edit"===e?r=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t+"/"+o:"quizgrid"===e?r=this.pickHost()+"/wp-json/yuno/v1/quizzes":"quizgridV2"===e?r=this.pickHost()+"/wp-json/yuno/v3/category/practice":"attempt"===e?r=this.pickHost()+"/wp-json/yuno/v1/attempt/":"review"===e?r=this.pickHost()+"/wp-json/yuno/v1/attempt/answers/"+t+"/"+n:"delete"===e?r=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t:"quizReorder"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/quiz/reorder"),r},question:function(e,t,n,o,r){let a="";return"questions"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionbank/"+t+"/"+n:"create"===e?a=this.pickHost()+"/wp-json/yuno/v1/question":"single"===e||"delete"===e?a=this.pickHost()+"/wp-json/yuno/v1/question/"+o:"deleteQuestionSet"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset/"+r:"attempt"===e?a=this.pickHost()+"/wp-json/yuno/v1/question-attempt/"+o:"attemptQuestionSet"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset-attempt/"+r:"questionset"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset":"questionsetGET"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset/"+r:"questionsetQuestions"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset-questions":"questionsetQuestionsGET"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/"+r:"questionsetQuestionsList"===e?a=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder/"+r:"questionsetQuestionsReorder"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder"),a},enrollments:function(e,t,n,o){let r="";return"active"===e?r=this.pickHost()+"/wp-json/yuno/v2/batch/"+t+"/"+n+"/learners":"extendDate"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/enrollment/update"),r},blog:function(e,t,n,o){let r="";return"recentSingle"===e?r=this.pickHost()+"/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+t:"recentList"===e?r=this.pickHost()+"/wp-json/yuno/v1/blog/recent/"+n+"/"+o+"?is_list=true&category_id="+t:"categoriesList"===e?r=this.pickHost()+"/wp-json/yuno/v1/blog/categories":"detail"===e&&(r=this.pickHost()+"/wp-json/yuno/v1/blog/"+t),r},writingTask:function(e,t,n,o,r,a,i,s){let l="";return"type"===e?l=this.pickHost()+"/wp-json/yuno/v1/writingtask/type/"+t+"/"+n:"create"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/create":"update"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/update":"singleRecord"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o+"/"+r+"/"+a+"/"+i+"/"+s:"payload"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+o:"delete"===e&&(l=this.pickHost()+"/wp-json/yuno/v1/writingtask/delete/"+o),l},categoryLandingPage:function(e,t){let n="";return"category"===e?n=this.pickHost()+"/wp-json/yuno/v2/category/"+t:"practiceTests"===e&&(n=this.pickHost()+"/wp-json/yuno/v2/category/practice/"),n},examResults:function(e,t,n,o){return this.pickHost()+"/wp-json/yuno/v2/examresult/"+e+"/"+t+"/"+n+"/"+o},mainNav:function(e,t){return this.pickHost()+"/wp-json/yuno/v3/menu/"+e+"/"+t},org:function(e,t,n,o,r,a,i,s,l,u,c,d,p,m){return this.pickHost()+({info:`/wp-json/yuno/v2/org/${t}`,singleLearner:`/wp-json/yuno/v1/org/user/${t}/${n}`,industries:"/wp-json/yuno/v1/org/industries/details",detailsUpdate:"/wp-json/yuno/v1/org/update",create:"/wp-json/yuno/v1/org/create",settings:`/wp-json/yuno/v2/org/settings/${t}`,createCourseEconomics:`/wp-json/yuno/v3/org/course/economics/${o}`,courseEconomicsPersonalization:`/wp-json/yuno/v3/org/course/economics/${r}/${a}/${i}`,courseDetailForm:`/wp-json/yuno/v3/org/course/${r}`,courseDetailUpdate:"/wp-json/yuno/v3/org/course",orgAcademies:`/wp-json/yuno/v3/org/academies/${t}`,courseSchedule:`/wp-json/yuno/v3/org/course/schedule/${s}`,batchesUpcomingOngoing:"/wp-json/yuno/v3/org/batches/upcomingOngoing",createBatch:`/wp-json/yuno/v3/org/${l}/batch`,courses:`/wp-json/yuno/v3/org/course/${n}/${t}/${o}/${r}/${u}/${c}/${d}/${p}/${m}`,batchesPast:`/wp-json/yuno/v3/org/batches/past/${t}/${n}/${o}/${r}/${d}/${p}/${m}`,enrollments:"/wp-json/yuno/v3/org/academy/enrollments",academyDetails:`/wp-json/yuno/v1/academy/${t}`,academyInstructors:`/wp-json/yuno/v1/org/user/instructors/${t}`,createUpdateAcademy:"/wp-json/yuno/v1/academy",getAcademy:`/wp-json/yuno/v1/academy/${t}`}[e]||"")},leadForm:function(e,t,n){let o="";return"steps"===e?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/"+t+"/"+n:"postStep"===e?o=this.pickHost()+"/wp-json/yuno/v2/signup/form/update/"+t+"/"+n:"updateMobile"===e&&(o=this.pickHost()+"/wp-json/yuno/v1/admin/user/phone/update "),o},availableCoursesV2:function(e){let t="";return"listing"===e&&(t=this.pickHost()+"/wp-json/yuno/v3/available-courses/"),t},courseSearch:function(e,t){return this.pickHost()+({courseSearchFilters:"/wp-json/yuno/v4/courses/search/filters",courseList:"/wp-json/yuno/v4/courses/search"}[e]||"")},activeCategory:function(e){let t="";return"set"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/category/"),t},learners:function(e,t,n,o,r,a){let i="";switch(e){case"insights":i=`/wp-json/yuno/v2/users/${t}/learner/${n}/${o}/${r}`;break;case"demoRequests":i=`/wp-json/yuno/v1/demo-requests/${t}/list/${o}/${r}`;break;case"demoRequestsOrg":i=`/wp-json/yuno/v1/demo-requests/org-admin/${t}/${n}/${o}/${r}`;break;case"learnerDetailOrg":i=`/wp-json/yuno/v1/demo-requests/${t}/${a}`;break;case"learnerDetail":i=`/wp-json/yuno/v1/demo-requests/${a}`;break;case"instructorLearnerDetail":i=`/wp-json/yuno/v2/instructor/mylearner/${a}`;break;case"orgAdminLearners":i=`/wp-json/yuno/v2/orgadmin/learner/${n}/${o}/${r}`}return this.pickHost()+i},deleteUser:function(e){let t="";return"requested"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/delete/requests"),t},generic:function(e,t,n,o,r){let a="";switch(e){case"googleFonts":a=`https://www.googleapis.com/webfonts/v1/webfonts/?${t}`;break;case"courseSuggestions":a=`${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${t}`;break;case"contentSearch":a=`${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${t}`;break;case"userSearch":a=`${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${t}/${n}/${o}`;break;case"orgBatches":a=`${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${r}`;break;case"org":a=`${this.pickHost()}/wp-json/yuno/v2/org/${o}`;break;case"categories":a=`${this.pickHost()}/wp-json/yuno/v3/all/category/signup`}return a},course:function(e,t,n,o,r,a){let i="";switch(e){case"payload":i=`${this.pickHost()}/wp-json/yuno/v1/course/${t}`;break;case"updateCourse":i=`${this.pickHost()}/wp-json/yuno/v1/course`;break;case"mapInstructor":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;break;case"invitedInstructors":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${t}/${n}/${o}/${r}/${a}`;break;case"createBatchOrg":i=`${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;break;case"updateBatchOrg":i=`${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;break;case"mapCourses":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`}return i},learner:function(e,t,n,o,r){let a="";switch(e){case"enrolledCourses":a=`/wp-json/yuno/v3/learner/${t}/enrollments/${n}`;break;case"classes":a=`/wp-json/yuno/v4/classes/${n}/${o}/${t}?limit=${r.limit}&offset=${r.offset}&course=${r.course}&batch=${r.batch}&academy=${r.academy}`,a=`/wp-json/yuno/v4/classes/${n}/${o}/${t}?limit=${r.limit}&offset=${r.offset}&course=${r.course}&batch=${r.batch}&academy=${r.academy}`;break;case"filters":a=`/wp-json/yuno/v4/classes/filter/${o}/${t}`;break;case"getClassDetail":case"getClassDetail":a=`/wp-json/yuno/v4/classes/${r.classID}`}return this.pickHost()+a},classes:function(e,t,n,o,r,a,i,s,l,u,c){let d="";if("allClasses"===e)d=`/wp-json/yuno/v3/classes/${t}/${n}/${o}/${r}/${a}/${i}/${s}/${l}/${u}/${c}`;return this.pickHost()+d},instructor:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v1/instructor/mylearners/${t.instructorID}/${t.limit}/${t.offset}`,learnerDetail:`/wp-json/yuno/v2/instructor/mylearner/${t.learnerID}`,fetchMyLearners:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivateClass:"/wp-json/yuno/v4/classes/private",updatePrivateClass:`/wp-json/yuno/v4/classes/private/${t.classID}`,getClassDetail:`/wp-json/yuno/v4/classes/${t.classID}`,createAvailabilityV2:`/wp-json/yuno/v2/instructor/${t.id}/workinghours/create`,updateAvailabilityV2:`/wp-json/yuno/v2/instructor/${t.id}/workinghours/update`,getInstructorAvailability:`/wp-json/yuno/v4/working_hours/instructor/${t.id}`,learnersV2:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivate:"/wp-json/yuno/v4/class/schedule-private",freebusy:"/wp-json/yuno/v2/instructor/freebusy/batch",learnersV2:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`}[e]||"")},attendance:function(e,t,n,o,r){return this.pickHost()+({learners:`/wp-json/yuno/v1/attendance/${t}/${n}/${o}/${r}`}[e]||"")},user:function(e,t){return this.pickHost()+({region:`/wp-json/yuno/v3/user/region/${t.loggedinUserID}`,languages:"/wp-json/yuno/v3/user/languages",countries:"/wp-json/yuno/v1/countries",timezones:"/wp-json/yuno/v3/user/timezones",currencies:"/wp-json/yuno/v3/user/currencies",virtualClassRoom:`/wp-json/yuno/v3/virtual-classroom/${t.loggedinUserID}`,virtualClassRoomV4:`/wp-json/yuno/v4/settings/virtual-classrooms/${t.loggedinUserID}`,vcDisconnect:"/wp-json/yuno/v3/virtual-classroom",classLaunchStatus:`/wp-json/yuno/v2/class/updateLaunchStatus/${t.classID}`,classSchedule:"/wp-json/yuno/v4/classes/demo",slots:"/wp-json/yuno/v2/instructor/freebusy/slots",slotsV4:`/wp-json/yuno/v4/availability/free_slots/${t.params}`}[e]||"")},classInsights:function(e,t,n,o,r){return this.pickHost()+({yunoAdminPast:`/wp-json/yuno/v3/classes/past/${t}/${n}/${o}/${r}`,yunoAdminOngoingUpcoming:`/wp-json/yuno/v3/classes/ongoingUpcoming/${t}/${n}/${o}/${r}`,yunoOrgPast:`/wp-json/yuno/v3/org/classes/past/${t}/${n}/${o}/${r}`,yunoOrgOngoingUpcoming:`/wp-json/yuno/v3/org/classes/ongoingUpcoming/${t}/${n}/${o}/${r}`}[e]||"")},resource:function(e,t){return this.pickHost()+({batches:`/wp-json/yuno/v2/batches/upcomingOngoing/${t.role}/${t.userID}/${t.limit}/${t.offset}`,batchLearners:`/wp-json/yuno/v1/batch/${t.batchID}/${t.courseID}/learners`,sendResource:"/wp-json/yuno/v1/resources/send/resource"}[e]||"")},academy:function(e,t){return this.pickHost()+({academies:"/wp-json/yuno/v3/org/academies",fetchAcademiesFilters:`/wp-json/yuno/v4/academies/filters${t.params}`,academiesV2:`/wp-json/yuno/v4/academies/${t.view_type}${t.params}`,activeOrg:"/wp-json/yuno/v3/user/state",getOrgInstructors:`/wp-json/yuno/v3/org/instructor/completed/${t.id}/${t.org_id}/${t.academy_id}/${t.days}/${t.status}/${t.vc_status}/${t.course_id}/${t.category_id}/${t.is_featured}/${t.native_language}/${t.avg_rating}/${t.view_type}/${t.limit}/${t.offset}`,addDemoInstructors:"/wp-json/yuno/v3/academy/demo-instructors/add",getDemoInstructors:`/wp-json/yuno/v3/academy/demo-instructors/${t.id}/${t.org_id}/${t.academy_id}`,updateDemoInstructors:"/wp-json/yuno/v3/academy/demo-instructors/edit",fetchAcademyDetails:`/wp-json/yuno/v4/academies/${t.id}`,organizations:`/wp-json/yuno/v4/organizations/${t.org_id}`,fetchAllPlace:`/wp-json/yuno/v4/places/${t.view_type}?limit=${t.limit}&offset=${t.offset}`,fetchOrgPlace:`/wp-json/yuno/v4/places/${t.view_type}?org_id=${t.org_id}&limit=${t.limit}&offset=${t.offset}`,fetchPlace:`/wp-json/yuno/v4/places/${t.placeID}`}[e]||"")},googleMapLocation:function(e,t){return"https://maps.googleapis.com/maps/api"+({geoLocation:`/geocode/json?latlng=${t.latitude},${t.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${t.key}`}[e]||"")},google:function(e,t){return"https://www.google.com"+({openLocation:`/maps/search/?api=1&query=Google&query_place_id=${t.placeID}`}[e]||"")},Places:function(e,t){return this.pickHost()+({create:"/wp-json/yuno/v4/places",fetchAllPlaces:`/wp-json/yuno/v4/places/${t.view_type}`,fetchPlace:`/wp-json/yuno/v4/places/${t.placeID}`,fetchMapPlaceDetails:`/wp-json/yuno/v4/maps/places/details?place_id=${t.placeID}`}[e]||"")},classroom:function(e,t){return this.pickHost()+({createClassroom:"/wp-json/yuno/v4/classrooms"}[e]||"")},createCourse:function(e,t){return this.laravelHost()+({courseGPT:"/api/submit-chatgpt-request",schedules:"/api/generate-course-schedules",status:`/api/status/${t.loggedinUserID}/all/${t.jobID}`}[e]||"")},enrollmentsV4:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/enrollments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/enrollments/filters/${t.params}`,createLink:"/wp-json/yuno/v4/enrollments",changeBatch:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}`,enrollToggle:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}/unenroll`}[e]||"")},header:function(e,t){return this.pickHost()+({menu:`/wp-json/yuno/v4/menus/${t.userID}/${t.orgID}`}[e]||"")},payment:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/payments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/payments/filters/${t.params}`}[e]||"")},batch:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v4/enrollments/active/batch/${t.batchID}`}[e]||"")},courseV4:function(e,t){return this.pickHost()+({list:"/wp-json/yuno/v4/courses/search"}[e]||"")}},n=new Promise((function(e,t){try{if(navigator.userAgent.includes("Firefox")){var n=indexedDB.open("test");n.onerror=function(){e(!0)},n.onsuccess=function(){e(!1)}}else e(null)}catch(t){console.log(t),e(null)}})),o=function(){return performance.now()};return{config:t,findObjectByKey:(e,t,n)=>e.find((e=>e[t]===n))||null,heightOfEle:function(e,t){let n=e.offsetHeight;if(t){let t=getComputedStyle(e);return n+=parseInt(t.marginTop)+parseInt(t.marginBottom),n}return n},assignVValidationObj:function(e){const t=window.VeeValidate,n=window.VeeValidateRules,o=t.ValidationProvider,r=t.ValidationObserver;t.extend("minLength",{validate:(e,{length:t})=>e.length>=t,params:["length"],message:"At least {length} items must be selected"}),t.extend("maxLength",{validate:(e,{length:t})=>e.length<=t,params:["length"],message:"No more than {length} items must be selected"}),t.extend("isSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Custom title is not allowed"}),t.extend("isSelectedFromList",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the user from list"}),t.extend("isBatchSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the batch from list"}),t.extend("notAllowed",{validate:(e,{number:t})=>!1===/^0[0-9].*$/.test(e),params:["number"],message:"Phone number can't start with {number}"}),t.extend("greaterThen",{validate:(e,{number:t})=>e>t,params:["number"],message:"Value should be greater then {number}"}),t.extend("isOverlapping",{validate:e=>!e,message:"Time overlap with another set of time"}),t.extend("isEndTime",{validate:e=>!e,message:"Choose an end time later than the start time."}),t.extend("selectLearner",{validate:(e,{number:t})=>0!==t,params:["number"],message:"Please add at least 1 learner from list"}),t.extend("isEmpty",{validate:(e,{getValue:t})=>""!==t,params:["getValue"],message:"Field should not be blank"}),t.extend("isNotBlank",{validate:(e,{getValue:t})=>null!==t,params:["getValue"],message:"Please select the learner from list"}),t.extend("url",{validate:(e,{getValue:t})=>!!/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/.test(e),params:["getValue"],message:"Please enter valid URL"}),t.extend("httpsURL",{validate:(e,{getValue:t})=>!!/^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/.test(e),params:["getValue"],message:'Please make sure URL should start with "https" and should be valid'}),t.extend("email",{validate:e=>!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),message:"Please enter a valid email address"}),t.extend("hasCurlyBrackets",{validate:e=>/\{.+?\}/.test(e),message:"String must have curly brackets with content inside"});for(let o in e.messages)t.extend(o,n[o]);t.localize("validationMsg",e),Vue.component("ValidationProvider",o),Vue.component("ValidationObserver",r)},removeObjInArr:function(e,t,n){let o=e.length;for(;o--;)e[o]&&e[o].hasOwnProperty(t)&&arguments.length>2&&e[o][t]===n&&e.splice(o,1);return e},formatDate:function(e){var t=new Date(e);if(isNaN(t.getTime()))return e;return day=t.getDate(),day<10&&(day="0"+day),["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]+" "+day+" "+t.getFullYear()},dateTimeToArray:function(e){new Array;return e.split(" ")},timeConvert:function(e){return(e=e.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/)||[e]).length>1&&((e=e.slice(1))[5]=+e[0]<12?"AM":"PM",e[0]=+e[0]%12||12),e.join("")},getQueryParameter:function(e){for(var t=window.location.search.substring(1).split("&"),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return o[1]}return!1},countriesData:function(){return[{name:"Afghanistan",code:"AF"},{name:"Åland Islands",code:"AX"},{name:"Albania",code:"AL"},{name:"Algeria",code:"DZ"},{name:"American Samoa",code:"AS"},{name:"AndorrA",code:"AD"},{name:"Angola",code:"AO"},{name:"Anguilla",code:"AI"},{name:"Antarctica",code:"AQ"},{name:"Antigua and Barbuda",code:"AG"},{name:"Argentina",code:"AR"},{name:"Armenia",code:"AM"},{name:"Aruba",code:"AW"},{name:"Australia",code:"AU"},{name:"Austria",code:"AT"},{name:"Azerbaijan",code:"AZ"},{name:"Bahamas",code:"BS"},{name:"Bahrain",code:"BH"},{name:"Bangladesh",code:"BD"},{name:"Barbados",code:"BB"},{name:"Belarus",code:"BY"},{name:"Belgium",code:"BE"},{name:"Belize",code:"BZ"},{name:"Benin",code:"BJ"},{name:"Bermuda",code:"BM"},{name:"Bhutan",code:"BT"},{name:"Bolivia",code:"BO"},{name:"Bosnia and Herzegovina",code:"BA"},{name:"Botswana",code:"BW"},{name:"Bouvet Island",code:"BV"},{name:"Brazil",code:"BR"},{name:"British Indian Ocean Territory",code:"IO"},{name:"Brunei Darussalam",code:"BN"},{name:"Bulgaria",code:"BG"},{name:"Burkina Faso",code:"BF"},{name:"Burundi",code:"BI"},{name:"Cambodia",code:"KH"},{name:"Cameroon",code:"CM"},{name:"Canada",code:"CA"},{name:"Cape Verde",code:"CV"},{name:"Cayman Islands",code:"KY"},{name:"Central African Republic",code:"CF"},{name:"Chad",code:"TD"},{name:"Chile",code:"CL"},{name:"China",code:"CN"},{name:"Christmas Island",code:"CX"},{name:"Cocos (Keeling) Islands",code:"CC"},{name:"Colombia",code:"CO"},{name:"Comoros",code:"KM"},{name:"Congo",code:"CG"},{name:"Congo, The Democratic Republic of the",code:"CD"},{name:"Cook Islands",code:"CK"},{name:"Costa Rica",code:"CR"},{name:"Cote D'Ivoire",code:"CI"},{name:"Croatia",code:"HR"},{name:"Cuba",code:"CU"},{name:"Cyprus",code:"CY"},{name:"Czech Republic",code:"CZ"},{name:"Denmark",code:"DK"},{name:"Djibouti",code:"DJ"},{name:"Dominica",code:"DM"},{name:"Dominican Republic",code:"DO"},{name:"Ecuador",code:"EC"},{name:"Egypt",code:"EG"},{name:"El Salvador",code:"SV"},{name:"Equatorial Guinea",code:"GQ"},{name:"Eritrea",code:"ER"},{name:"Estonia",code:"EE"},{name:"Ethiopia",code:"ET"},{name:"Falkland Islands (Malvinas)",code:"FK"},{name:"Faroe Islands",code:"FO"},{name:"Fiji",code:"FJ"},{name:"Finland",code:"FI"},{name:"France",code:"FR"},{name:"French Guiana",code:"GF"},{name:"French Polynesia",code:"PF"},{name:"French Southern Territories",code:"TF"},{name:"Gabon",code:"GA"},{name:"Gambia",code:"GM"},{name:"Georgia",code:"GE"},{name:"Germany",code:"DE"},{name:"Ghana",code:"GH"},{name:"Gibraltar",code:"GI"},{name:"Greece",code:"GR"},{name:"Greenland",code:"GL"},{name:"Grenada",code:"GD"},{name:"Guadeloupe",code:"GP"},{name:"Guam",code:"GU"},{name:"Guatemala",code:"GT"},{name:"Guernsey",code:"GG"},{name:"Guinea",code:"GN"},{name:"Guinea-Bissau",code:"GW"},{name:"Guyana",code:"GY"},{name:"Haiti",code:"HT"},{name:"Heard Island and Mcdonald Islands",code:"HM"},{name:"Holy See (Vatican City State)",code:"VA"},{name:"Honduras",code:"HN"},{name:"Hong Kong",code:"HK"},{name:"Hungary",code:"HU"},{name:"Iceland",code:"IS"},{name:"India",code:"IN"},{name:"Indonesia",code:"ID"},{name:"Iran, Islamic Republic Of",code:"IR"},{name:"Iraq",code:"IQ"},{name:"Ireland",code:"IE"},{name:"Isle of Man",code:"IM"},{name:"Israel",code:"IL"},{name:"Italy",code:"IT"},{name:"Jamaica",code:"JM"},{name:"Japan",code:"JP"},{name:"Jersey",code:"JE"},{name:"Jordan",code:"JO"},{name:"Kazakhstan",code:"KZ"},{name:"Kenya",code:"KE"},{name:"Kiribati",code:"KI"},{name:"Korea, Democratic People'S Republic of",code:"KP"},{name:"Korea, Republic of",code:"KR"},{name:"Kuwait",code:"KW"},{name:"Kyrgyzstan",code:"KG"},{name:"Lao People'S Democratic Republic",code:"LA"},{name:"Latvia",code:"LV"},{name:"Lebanon",code:"LB"},{name:"Lesotho",code:"LS"},{name:"Liberia",code:"LR"},{name:"Libyan Arab Jamahiriya",code:"LY"},{name:"Liechtenstein",code:"LI"},{name:"Lithuania",code:"LT"},{name:"Luxembourg",code:"LU"},{name:"Macao",code:"MO"},{name:"Macedonia, The Former Yugoslav Republic of",code:"MK"},{name:"Madagascar",code:"MG"},{name:"Malawi",code:"MW"},{name:"Malaysia",code:"MY"},{name:"Maldives",code:"MV"},{name:"Mali",code:"ML"},{name:"Malta",code:"MT"},{name:"Marshall Islands",code:"MH"},{name:"Martinique",code:"MQ"},{name:"Mauritania",code:"MR"},{name:"Mauritius",code:"MU"},{name:"Mayotte",code:"YT"},{name:"Mexico",code:"MX"},{name:"Micronesia, Federated States of",code:"FM"},{name:"Moldova, Republic of",code:"MD"},{name:"Monaco",code:"MC"},{name:"Mongolia",code:"MN"},{name:"Montserrat",code:"MS"},{name:"Morocco",code:"MA"},{name:"Mozambique",code:"MZ"},{name:"Myanmar",code:"MM"},{name:"Namibia",code:"NA"},{name:"Nauru",code:"NR"},{name:"Nepal",code:"NP"},{name:"Netherlands",code:"NL"},{name:"Netherlands Antilles",code:"AN"},{name:"New Caledonia",code:"NC"},{name:"New Zealand",code:"NZ"},{name:"Nicaragua",code:"NI"},{name:"Niger",code:"NE"},{name:"Nigeria",code:"NG"},{name:"Niue",code:"NU"},{name:"Norfolk Island",code:"NF"},{name:"Northern Mariana Islands",code:"MP"},{name:"Norway",code:"NO"},{name:"Oman",code:"OM"},{name:"Pakistan",code:"PK"},{name:"Palau",code:"PW"},{name:"Palestinian Territory, Occupied",code:"PS"},{name:"Panama",code:"PA"},{name:"Papua New Guinea",code:"PG"},{name:"Paraguay",code:"PY"},{name:"Peru",code:"PE"},{name:"Philippines",code:"PH"},{name:"Pitcairn",code:"PN"},{name:"Poland",code:"PL"},{name:"Portugal",code:"PT"},{name:"Puerto Rico",code:"PR"},{name:"Qatar",code:"QA"},{name:"Reunion",code:"RE"},{name:"Romania",code:"RO"},{name:"Russian Federation",code:"RU"},{name:"RWANDA",code:"RW"},{name:"Saint Helena",code:"SH"},{name:"Saint Kitts and Nevis",code:"KN"},{name:"Saint Lucia",code:"LC"},{name:"Saint Pierre and Miquelon",code:"PM"},{name:"Saint Vincent and the Grenadines",code:"VC"},{name:"Samoa",code:"WS"},{name:"San Marino",code:"SM"},{name:"Sao Tome and Principe",code:"ST"},{name:"Saudi Arabia",code:"SA"},{name:"Senegal",code:"SN"},{name:"Serbia and Montenegro",code:"CS"},{name:"Seychelles",code:"SC"},{name:"Sierra Leone",code:"SL"},{name:"Singapore",code:"SG"},{name:"Slovakia",code:"SK"},{name:"Slovenia",code:"SI"},{name:"Solomon Islands",code:"SB"},{name:"Somalia",code:"SO"},{name:"South Africa",code:"ZA"},{name:"South Georgia and the South Sandwich Islands",code:"GS"},{name:"Spain",code:"ES"},{name:"Sri Lanka",code:"LK"},{name:"Sudan",code:"SD"},{name:"Suriname",code:"SR"},{name:"Svalbard and Jan Mayen",code:"SJ"},{name:"Swaziland",code:"SZ"},{name:"Sweden",code:"SE"},{name:"Switzerland",code:"CH"},{name:"Syrian Arab Republic",code:"SY"},{name:"Taiwan, Province of China",code:"TW"},{name:"Tajikistan",code:"TJ"},{name:"Tanzania, United Republic of",code:"TZ"},{name:"Thailand",code:"TH"},{name:"Timor-Leste",code:"TL"},{name:"Togo",code:"TG"},{name:"Tokelau",code:"TK"},{name:"Tonga",code:"TO"},{name:"Trinidad and Tobago",code:"TT"},{name:"Tunisia",code:"TN"},{name:"Turkey",code:"TR"},{name:"Turkmenistan",code:"TM"},{name:"Turks and Caicos Islands",code:"TC"},{name:"Tuvalu",code:"TV"},{name:"Uganda",code:"UG"},{name:"Ukraine",code:"UA"},{name:"United Arab Emirates",code:"AE"},{name:"United Kingdom",code:"GB"},{name:"United States",code:"US"},{name:"United States Minor Outlying Islands",code:"UM"},{name:"Uruguay",code:"UY"},{name:"Uzbekistan",code:"UZ"},{name:"Vanuatu",code:"VU"},{name:"Venezuela",code:"VE"},{name:"Viet Nam",code:"VN"},{name:"Virgin Islands, British",code:"VG"},{name:"Virgin Islands, U.S.",code:"VI"},{name:"Wallis and Futuna",code:"WF"},{name:"Western Sahara",code:"EH"},{name:"Yemen",code:"YE"},{name:"Zambia",code:"ZM"},{name:"Zimbabwe",code:"ZW"}]},isPrivateWindow:function(e){n.then((function(t){e(t)}))},setCookie:function(e,t,n){let o=new Date;void 0===n&&(n=30),o.setTime(o.getTime()+24*n*60*60*1e3);let r="expires="+o.toGMTString();document.cookie=e+"="+t+";"+r+";path=/"},deleteCookie:function(e){document.cookie=e+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"},getCookie:function(e){let t=("; "+document.cookie).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},timestamp:o,removeValInArr:function(e){let t,n,o=arguments,r=o.length;for(;r>1&&e.length;)for(t=o[--r];-1!==(n=e.indexOf(t));)e.splice(n,1);return e},hasInArray:function(e,t){return-1!=e.indexOf(t)},getFromString:function(e,t,n){let o=e.match(t);return null!=o&&(!0===n?o[1].replace(/\/$/,""):o[1])},encodeObj:function(e){return encodeURI(JSON.stringify(e))},detectQueryString:function(){const e=window.location.search;return e||!1},scrollToElement:function(e,t,n){let o=window.pageYOffset,r=(a=e,window.pageYOffset+document.querySelector(a).getBoundingClientRect().top);var a;targetY=document.body.scrollHeight-r<window.innerHeight?document.body.scrollHeight-window.innerHeight:r,customHeight=void 0!==n?n:74,diff=targetY-o-customHeight;let i="";diff&&window.requestAnimationFrame((function e(n){i||(i=n);let r=n-i,a=Math.min(r/t,1);var s;a=(s=a)<.5?4*s*s*s:(s-1)*(2*s-2)*(2*s-2)+1,window.scrollTo(0,o+diff*a),r<t&&window.requestAnimationFrame(e)}))},removeTagsFromString:function(e){return e.replace(/(<([^>]+)>)/gi,"")},findInArray:function(e,t){return void 0!==e.find((e=>e===t))},queryParameterNonWindow:function(e,t){for(var n=e.substring(1).split("&"),o=0;o<n.length;o++){var r=n[o].split("=");if(r[0]==t)return r[1]}return!1},getErrorMessage:function(e){return{virtualClassroom:{heading:"You’re not yet teaching any course.",message:"Once an organization maps you to a course, you can connect the organization's chosen virtual classroom."}}[e]||""},cleanTextAndTruncate:function(e,t){let n=e.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(n=n.replace(/\s\s+/g," "),n.length>t){const e=n.lastIndexOf(" ",t-1);return n.substring(0,e)+"..."}return n}}}(jQuery),YUNOStore=function(e){const t=function(e,t,n,o,r){if(o){if(void 0===t.addToModule||t.addToModule)if(void 0!==r&&r){for(let t=0;t<n.length;t++)e.data.push(n[t]);t.hasLoadmore&&(e.count=t.response.data.count,e.currentCount=e.data.length,e.offset=e.currentCount)}else e.data=n}else t.moduleTabs?(e.error=n,e.loading=!1):e.error=n;e.success=!0,e.loading=!1};return{init:function(){return new Vuex.Store({state:{pageLoader:!1,loader:{overlay:!1,isActive:!1},themeURL:themeURL,homeURL:homePage,config:{signInURL:"",yunoAPIToken:"undefined"!=typeof yunoAPIToken?yunoAPIToken:"",unauthorizedModal:!1,vimeoToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",vimeoVideoToken:"Bearer 878869c3fe96f7ec679b9455c539ee77",vimeoVideoEditToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",googleAPIKey:"AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",googleMapAPIKey:"AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",googleMapLocationAPIKey:"AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI"},user:{isLoggedin:!1,userID:isLoggedIn},userRole:{loading:!1,error:null,success:!1,data:[],response:[]},userProfile:{loading:!1,error:null,success:!1,data:[]},header:{loading:!1,error:null,errorData:[],success:!1,data:[],isMenuOpen:!1},footer:{loading:!1,error:null,errorData:[],success:!1,data:[]},blogList:{loading:!1,error:null,success:!1,data:[]},courseListIELTS:{loading:!1,error:null,errorData:[],success:!1,data:[]},allCourseListIELTS:{title:"All our courses are delivered by expert IELTS trainers",demoPageURL:"/demo-classes",loading:!1,error:null,errorData:[],success:!1,data:{single:[],multiple:[]}},resultsIELTS:{title:"Our students' results",isLoadMore:!1,count:"",currentCount:"",limit:8,offset:0,loading:!1,error:null,errorData:[],success:!1,data:[]},faqIELTS:{title:"FAQs",loading:!1,error:null,errorData:[],success:!1,data:[]},recordedClasses:{title:"Checkout actual classes that we recorded",videos:[{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",caption:"Subject Verb Agreement Class - Learn IELTS - Yuno Learning"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",caption:"IELTS Writing Task 2 Tips"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",caption:"How to Paraphrase - Learn IELTS - Yuno Learning"}]},course:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},courseV2:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},schedule:{loading:!1,error:null,errorData:[],success:!1,data:[]},courseBatches:{title:"",coursesCount:"",loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:4,offset:0},courseBatchesFilters:{currentCourse:"",tabs:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},courseTabs:[],instructor:{loading:!1,error:null,errorData:[],success:!1,data:[],tabs:[]},learnerCourses:{loading:!1,error:null,errorData:[],success:!1,tabs:[{title:"My Courses",tab:"Upcoming and Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You have not enrolled any course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0},{title:"My Courses",tab:"Past",url:"yunoPast",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You do not have any past course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0}]},instructorCourses:{loading:!1,error:null,errorData:[],success:!1,data:[]},instructorBasicDetails:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isUpdateDetail:!1,fluentInSelected:[],understandSelected:[],payload:{user_id:"",flat_house_number:"",street:"",landmark:"",pin_code:"",country:"",state:"",city:"",experience:"",fluent_in:[],understand:[],is_about:!1}},instructorAbout:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isAbout:!1,payload:{user_id:"",is_about:!0,about:""}},instructorDemoClasses:{loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:2,offset:0,isLoadMore:!1,data:[]},instructorMyCourses:{title:"My Courses",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},homeCategories:{title:"Top Courses on Yuno",data:[],loading:!1,error:null,errorData:[],success:!1},homeCarouselList:{title:"Featured Courses",data:[],loading:!1,error:null,errorData:[],success:!1},instructorslList:{title:"Meet Our Expert Instructors",description:"",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},whyLearn:{title:"Why Choose Yuno for Your Learning Journey",list:[{title:"Online classes with personalized attention",description:"All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",icon:"portrait",iconType:"material-icons-outlined"},{title:"Instructors who care for your success",description:"Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",icon:"emoji_events",iconType:"material-icons-outlined"},{title:"Best instructors but affordable pricing",description:"All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",icon:"account_balance_wallet",iconType:"material-icons-outlined"},{title:"Really smooth experience of technology",description:"We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",icon:"code",iconType:"material-icons-outlined"},{title:"Your counsellor is just a call away",description:"You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",icon:"call",iconType:"material-icons"}]},homeHero:{list:[{title:"Coding classes don’t have to cost so much. Try Yuno’s affordable classes",subTitle:"",cta:"Learn More",ctaURL:"/coding-for-kids",category:"",img:themeURL+"/assets/images/homeHero-kids.jpg"},{title:"Fluency builds confidence among children. Try our English speaking classes",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-English-Speaking.jpg"},{title:"Communicate. Succeed. English speaking classes for working professionals",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-professional.jpg"}]},chooseType:{title:"English Speaking Online Classes",subTitle:"Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",data:[],loading:!1,error:null,success:!1,list:[{title:"Working Professionals",type:"professionals",age:"22+",description:"We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",price:[{monthly:"1800",perClass:"150",level:"Intermediate"},{monthly:"2900",perClass:"242",level:"Advanced"}]},{title:"Students",type:"students",age:"15 - 22",description:"Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Kids",type:"kids",age:"8 to 14",description:"There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Homemakers",type:"homemakers",age:"25 to 55",description:"You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]}]},meetInstructor:{title:"Instructors who really care",description:"You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",data:[],loading:!1,error:null,errorData:[],success:!1},featuredTestimonials:{title:"So immersive that you’d want more classes",description:"We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",footer:{title:"Affordable pricing",description:"So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."}},spokenEnglishContentBlock:{title:"You don’t only get to practise, but you acquire English language skills for life",description:"The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",img:themeURL+"/assets/images/languages.svg"},kidsHero:{description:"I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",img:themeURL+"/assets/images/codingForKids.svg",author:{name:"Elon Musk",img:themeURL+"/assets/images/Elon-Musk.jpg",about:"Elon Musk, Founder of Tesla and SpaceX"}},kidsOfferBanner:{title:"Introducing programming foundation course for 7 - 16 years old",img:themeURL+"/assets/images/offerPrice.svg",productURL:"/course/learn-to-code-with-mit-scratch",list:["We make learning to code fun for your child","24 hours of live classes with homework assignments","3 classes per week","Rs. 349 per class","All classes by expert computer science instructors","Industry standard curriculum designed by MIT, USA","Certificate on successful completion"]},kidsTestimonials:{title:"What parents have to say"},kidsHighlights:{title:"Highlights",call:"Call us at <span>+91 62390 91798</span>",list:[{type:"liveClass",name:"Live Class",label:"21 hours of live classes"},{type:"oneToOne",name:"One to One",label:"one-to-one doubt clearing sessions"},{type:"price",name:"Price",label:"&#8377;349 per class"},{type:"game",name:"Game",label:"Game development by students"},{type:"programming",name:"Programming",label:"Programming fundamentals"},{type:"flexible",name:"Flexible",label:"Flexible timings with multiple batches"},{type:"certificate",name:"Certificate",label:"Certificate of completion"},{type:"demo",name:"Demo",label:"Free Demo Class"},{type:"cv",name:"CV",label:"Curriculum by MIT, USA"}]},gamesShowcase:{title:"Game Developed by Students",list:[{title:"Pop The Balloon Game",img:themeURL+"/assets/images/PopUPthebaloon.png",url:"https://scratch.mit.edu/projects/419275974/"},{title:"Apple Catcher",img:themeURL+"/assets/images/Applecatcher.png",url:"https://scratch.mit.edu/projects/423139061/"},{title:"Killing Zombies",img:themeURL+"/assets/images/Kill-Zombies.png",url:"https://scratch.mit.edu/projects/425774405/"},{title:"Pac-man",img:themeURL+"/assets/images/PACMAN.png",url:"https://scratch.mit.edu/projects/429660245/"}]},ieltsReviews:{title:"What our students say",description:"",data:[],loading:!1,error:null,success:!1},ourInstructors:{title:"Our Instructors",description:"You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",data:[],loading:!1,error:null,errorData:[],success:!1},curriculum:{title:"Best-in-class curriculum by MIT, USA",description:"The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",link:{label:"See curriculum",url:"/course/learn-to-code-with-mit-scratch"},img:themeURL+"/assets/images/scratch.svg",author:{name:"Mitchel Resnik",about:"PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",img:themeURL+"/assets/images/MitchellResnickThumb.jpg",link:{label:"Watch Mitchel Resnik’s Video on TED",url:"https://www.youtube.com/watch?v=Ok6LbV6bqaE"}}},signUpForm:{data:[],loading:!1,error:null,errorData:[],success:!1},loginWithGoogle:{isLoading:!1,data:[],payload:{State:loginState}},isUserSignUp:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollment:{isLoading:!1,isCourseEnrolled:!1,data:[],error:null,errorData:[],success:{username:"",productTitle:"",amount:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},payload:{id:"",receipt:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:""}},paymentDismiss:{data:[],loading:!1,error:null,errorData:[],success:!1},classSchedule:{isLoading:!1,modal:!1,successModal:!1,data:[],error:null,errorData:[],currentLearner:"",date:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",time:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",learnerSelected:"",relatedCourses:"",payload:{class_title:"",class_date_time:"",class_duration:"",instructor_id:parseInt(isLoggedIn,10),category_id:0,academy_id:0,learners:[],batch_id:0,course_id:0,classroom_id:0,place_id:0,is_online:!1,is_in_person:!1}},classEdit:{data:[],loading:!1,error:null,success:!1},classDelete:{data:[],loading:!1,error:null,success:!1},classTitle:{data:[],loading:!1,error:null,errorData:[],success:!1},addClassTitle:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{instructor_id:"",title:""}},myLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},learner:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,emptyStateCTA:{ctaLabel:"See past classes & recordings",tab:"yunoPast"},count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0,emptyStateCTA:!1}]},instructorHome:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0}]},instructorLearners:{scheduleClass:{modal:!1,data:[]},updateTitle:{modal:!1,modalData:null,isLoading:!1,payload:{title:"",group_id:""}},addLearner:{modal:!1,modalData:null,isLoading:!1,selectedLearner:"",deleteUser:[],newAddedUser:[],payload:{add_user_ids:[],delete_user_ids:[],group_id:""}},newGroupModal:{modal:!1,learners:[],selectedLearner:[],isLoading:!1,data:[],payload:{title:"",owner_id:"",role:"",access:"rw",user_ids:[]}},loading:!1,error:null,success:!1,tabs:[{title:"My Learners",tab:"All",url:"yunoAllLearners",isActive:!1,hasData:!1,type:"tableGrid-2",defaultSort:"name",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any learner",defaultFilters:[{type:"viewBy",val:"all"}],appliedFilters:[],filters:[{selected:"By Learner Type",default:"By Learner Type",type:"viewBy",module:"dropdown",isActive:!1,items:[{label:"All",val:"all",default:"all"},{label:"My Contacts",val:"myContacts",default:"all"},{label:"My Referrals",val:"myReferrals",default:"all"}]}],tableOptions:{isFluid:!0,pageLoading:!1,apiPaginated:!0,totalResult:"",perPage:20,currentPage:1,limit:100,offset:0},manageState:!0},{title:"My Groups",tab:"Groups",url:"yunoGroups",isActive:!1,hasData:!1,type:"groupCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any group",count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0}]},allLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},classDetail:{classType:"",data:[],tabs:[],loading:!1,error:null,errorMsg:"",errorData:[],success:!1},demoClassEnroll:{isLoading:!1,modal:!1,successModal:!1,error:null,errorData:[],data:[],payload:{class_id:"",instructor_id:"",user_id:"",start_date:"",end_date:"",class_title:"",class_description:""}},paymentLink:{isLoading:!1,data:[],successModal:!1,form:{amount:"",selectedUser:"",user:"",selectedBatch:"",batchID:"",batch:"",courseID:"",course:"",paymentType:"",noteForSelf:"",isInstallment:!1,installments:"",isNextSlide:!1,howManyInstallments:["2","3"]},payload:{customer_name:"",customer_email:"",customer_contact:"",type:"link",view_less:1,amount:"",currency:"INR",payment_description:"",receipt:"",partial_payment:0}},crmContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},allCourses:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},allBatches:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollmentStatus:{data:[],loading:!1,error:null,errorData:[],success:!1},createPayment:{data:[],payload:{id:"",receipt:"",Order_id:"",customer_id:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"generate_link",payment_mode:"online",amount:"",amount_due:"",total_instalments:"",instalment_amount:"",duration:"",status:"",description:"",self_notes:"",currency:"INR",entity:"invoice",counselor_id:"",short_url:"",org_id:"",org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:"",org_crm_id:"",org_cohort:"",org_programs:"",org_business_unit:"",org_parents:[]}},reviews:{data:[],loading:!1,error:null,errorData:[],success:!1},updatePaymentLink:{data:[],successModal:!1,payload:{id:""}},updateLink:{data:[],error:null,errorData:[],loading:!1,success:!1,successModal:!1,payload:{receipt_id:"",razerpay_invoice_id:"",short_url:""}},instructorList:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},profileReviews:{title:"Reviews"},reviewsByType:{data:[],loading:!1,error:null,success:!1},demoClasses:{loading:!1,error:null,success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,data:[],filters:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},manageEnroll:{data:[],loading:!1,error:null,errorData:[],success:!1},changeBatch:{data:[],loading:!1,error:null,errorData:[],success:!1},blogs:{data:[],headers:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1},blogDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},postCategories:{data:[],loading:!1,error:null,success:!1},anilLambaHero:{list:[{title:"Anil Lamba on finance",img:themeURL+"/assets/images/hero-anilLamba.png"}]},settings:{loading:!1,error:null,errorData:[],success:!1,tabs:[]},notifications:{data:[],loading:!1,error:null,errorData:[],success:!1},counsellorList:{data:[],refinedData:[],loading:!1,error:null,success:!1},pageDetail:{data:[],loading:!1,error:null,success:!1},googleContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},participants:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{login_email:"",first_name:"",last_name:"",user_id:""}},mapCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},relatedCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},categoryList:{data:[],loading:!1,error:null,errorData:[],success:!1,selected:""},categoryTaxonomy:{data:[],loading:!1,error:null,errorData:[],success:!1},createEBook:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedEBooks:{data:[],loading:!1,error:null,errorData:[],success:!1},deleteEBookAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},eBookEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},createResource:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedResources:{data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!1,defaultFilters:[],appliedFilters:[],filters:[]},deleteResourceAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},resourceEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},videoList:{data:[],loading:!1,error:null,errorData:[],success:!1},userInfo:{data:[],loading:!1,error:null,errorData:[],success:!1},paymentLinkList:{data:[],changeBatch:"",paymentDetail:"",createPayment:"",generateLink:"",loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorInsights:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorStats:{data:[],loading:!1,error:null,errorData:[],success:!1},goalsAchieved:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerRating:{data:[],loading:!1,error:null,errorData:[],success:!1},reviewVariations:{data:[],loading:!1,error:null,errorData:[],success:!1},completedEnrollments:{data:[],loading:!1,error:null,errorData:[],success:!1},classDelivered:{data:[],loading:!1,error:null,errorData:[],success:!1},activeEnrollment:{data:[],loading:!1,error:null,errorData:[],success:!1},qtRating:{data:[],loading:!1,error:null,errorData:[],success:!1},topIssuesCited:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorReviews:{data:[],loading:!1,error:null,errorData:[],success:!1},ratingBreakdown:{data:[],loading:!1,error:null,errorData:[],success:!1},countries:{data:[],loading:!1,error:null,errorData:[],success:!1},states:{data:[],loading:!1,error:null,errorData:[],success:!1},cities:{data:[],loading:!1,error:null,errorData:[],success:!1},languages:{data:[],loading:!1,error:null,errorData:[],success:!1},mappedInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},batchCreateUpdate:{data:[],loading:!1,error:null,errorData:[],success:!1},batchDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},timeSlots:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailability:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailabilityGrid:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorsByCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},capabilities:{data:[],loading:!1,error:null,errorData:[],success:!1},cancelPaymentLink:{data:[],loading:!1,error:null,errorData:[],success:!1},inviteLink:{data:[],loading:!1,error:null,errorData:[],success:!1},invitedByUser:{data:[],loading:!1,error:null,errorData:[],success:!1},updateUserCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenExpiryTime:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenRefresh:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfileHeader:{data:[],loading:!1,error:null,errorData:[],success:!1},staticPage:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfile:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resources:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resource:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:15,offset:0},learnerInsightsClass:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},videoTestimonials:{data:[],loading:!1,error:null,errorData:[],success:!1},ieltsResults:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:10,offset:0},deleteResource:{data:[],loading:!1,error:null,errorData:[],success:!1},eventDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},moduleWithoutTab:{data:[],loading:!1,error:null,errorData:[],success:!1,limit:20,offset:0,count:"",currentCount:"",isLoadMore:!1},moduleWithLoadMore:{data:[],other:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:3,offset:0},signupLastStep:{data:[],loading:!1,error:null,errorData:[],success:!1},subjectsList:{data:[],loading:!1,error:null,errorData:[],success:!1},filters:{data:[],loading:!1,error:null,errorData:[],success:!1,filters:null,payload:[]},filterResult:{data:[],additional:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,currentPage:1,isSidebar:!1,limit:20,offset:0,payload:[],modal:{isActive:!1,data:[]},tabs:[],refreshTable:!1},enrollmentV2:{data:[],loading:!1,error:null,errorData:[],success:!1},allReviews:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},enableDisableInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},module:{data:[],loading:!1,error:null,errorData:[],success:!1},drawer:{data:[],isActive:!1,loading:!1,error:null,errorData:[],success:!1},form:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],fields:[]},subform:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],calendarLoading:!1},subform2:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform3:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},orgAdmin:{data:[],loading:!1,error:null,errorData:[],success:!1},referralCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},generateCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},searchSuggestions:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},chooseAccountTypeModal:{modal:!1,data:[],loading:!1,error:null,errorData:[],success:!1},tabs:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1}},mutations:{gotData(e,n){if(n.isError){let o=n.response.response,r=!(void 0===n.pushData||!n.pushData),a=e[n.store],i="";i=void 0!==o&&void 0!==o.data&&void 0!==o.data.message?o.data.message:YUNOCommon.config.errorMsg.common,console.log(i),console.log(n.store),n.tabs?(t(a.tabs[n.tabIndex],n,o,!1,r),a.tabs[n.tabIndex].error=!0,a.tabs[n.tabIndex].errorData=i,n.callback&&n.callbackFunc(a.tabs[n.tabIndex].errorData)):(t(a,n,o,!1,r),a.error=!0,a.errorData=i,n.callback&&n.callbackFunc(a.errorData))}else{let o=n.response.data.data,r=!(void 0===n.pushData||!n.pushData),a=n.response.data,i=e[n.store];n.tabs?(204===a.code&&(i.tabs[n.tabIndex].error=!0,i.tabs[n.tabIndex].errorData=a.message,console.log(a.message),console.log(n.store)),401===a.code&&(i.tabs[n.tabIndex].error=!0,i.tabs[n.tabIndex].errorData=a.message,console.log(a.message),console.log(n.store)),t(i.tabs[n.tabIndex],n,o,!0,r)):(204===a.code&&(i.error=!0,i.errorData=a.message,console.log(a.message),console.log(n.store)),401===a.code&&(i.error=!0,i.errorData=a.message,console.log(a.message),console.log(n.store)),t(i,n,o,!0,r)),n.callback&&n.callbackFunc(n)}},thirdParty(e,t){module=e[t.store],module.error?t.callbackFunc(module):t.callbackFunc(t)},mapCourses(e,n){if(n.isError){let o=n.response;(void 0===n.overrideData||n.overrideData)&&t(e[n.module],n,o,!1),n.callback&&n.callbackFunc(e[n.module].errorData)}else{let o=n.response.data.data;(void 0===n.overrideData||n.overrideData)&&t(e[n.module],n,o,!0),n.callback&&n.callbackFunc(n)}},reviewsByType(e,n){if(n.isError){let o=n.response;t(e[n.module],n,o,!1)}else{let o=n.response.data.data;n.isTabAdded||e.instructor.data.tabs.push({tab:"Reviews",url:"yunoFeaturedTestimonials"}),t(e[n.module],n,o,!0),n.isTabAdded=!0,setTimeout((()=>{n.componentInstance.$refs.testimonialWrapper.initSlider()}),30)}},crmContacts(e,n){if(n.isError){let o=n.response;t(e[n.module],n,o,!1)}else{let r=n.response.data.data;for(var o=0;o<r.length;o++)r[o].username_email_phone=`${r[o].username_email} (${r[o].phone})`;t(e[n.module],n,r,!0)}},classDelete(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(e.loader.isActive=!1,e.loader.overlay=!1,void 0!==t.classID){let n=e.instructorHome.tabs[0].data;YUNOCommon.removeObjInArr(n,"id",t.classID),0===n.length&&(e.instructorHome.tabs[0].error=!0),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Class successfully deleted",position:"is-bottom"})}else t.componentInstance.$buefy.dialog.alert({title:"Delete",message:"Class successfully deleted",confirmText:"Ok",type:"is-danger",onConfirm:()=>window.location.href=YUNOCommon.config.host()+"/instructor"});e[t.module].data=t.response,e[t.module].success=!0,e[t.module].loading=!1}},demoClassEnroll(e,t){if(t.isError)e[t.module].isLoading=!1,void 0!==t.classIndex&&(e.loader.isActive=!1,e.loader.overlay=!1),t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(localStorage.removeItem("demoClassState"),e[t.module].isLoading=!1,e[t.module].data=t.response.data.data,void 0!==t.classIndex){let n=e[t.parentModule].data[t.classIndex];void 0!==n&&(n.isLoading=!1,n.is_enrolled=!0),e.loader.isActive=!1,e.loader.overlay=!1}else e[t.parentModule].data.is_enrolled=!0;t.componentInstance.$buefy.toast.open({duration:5e3,message:"You have successfully enrolled",position:"is-bottom"});const n=localStorage.getItem("userSignUp");null!==n&&"pending"===n&&(localStorage.setItem("oldUserState","/learner/"),localStorage.setItem("userState","/sign-up"),window.location.href=YUNOCommon.config.host()+"/sign-up")}},userRole(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].response=t.response.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.componentInstance&&t.componentInstance.getUserRole(t.response.data.data))},userProfile(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.instance&&void 0!==t.instance.gotUserProfile&&t.instance.gotUserProfile(t.response.data.data))},classDetail(e,n){if(n.isError){let o=n.response;"enrolled"===n.nestedTab?(e[n.module].tabs[n.tabIndex].loading=!1,e[n.module].tabs[n.tabIndex].pageLoading=!1,e[n.module].tabs[n.tabIndex].error=o):t(e[n.module],n,o,!1)}else n.callback&&n.callbackFunc(n,n.response)},instructorMyCourses(e,t){if(t.isError){if(t.batches){e[t.module].data[t.courseIndex].tabs[t.tabIndex].error=t.response}else e[t.module].error=t.response;e[t.module].success=!0,e[t.module].loading=!1}else{let n=t.response.data.data;if(t.batches){e[t.module].data[t.courseIndex].isBatches=!0;for(let o=0;o<n.length;o++)e[t.module].data[t.courseIndex].tabs[t.tabIndex].data.push(n[o]);let o=e[t.module].data[t.courseIndex].tabs[t.tabIndex];o.count=t.response.data.count,o.currentCount=o.data.length,o.offset=o.currentCount,o.isLoadMore=!1}else{for(let e=0;e<n.length;e++)n[e].isBatches=!1,n[e].isLoading=!1,n[e].tabs=[{tab:"Upcoming & Ongoing",isActive:!0,type:"upcomingOngoing",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null},{tab:"Past",isActive:!1,type:"past",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null}];e[t.module].data=n}e[t.module].success=!0,e[t.module].loading=!1}},allLearners(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,void 0!==t.nested&&(e[t.nested].tabs[0].error=!0);else{let o=t.response.data.data;if(void 0!==t.nested){let r=o.columns,a=o.rows,i={field:"actions",label:"Actions",sortable:!1};"Instructor"===t.userRole&&r.push(i);for(var n=0;n<a.length;n++)a[n].scheduleClass={active:!0,url:"/class-schedule/?learnerID="+a[n].id};e[t.nested].tabs[0].data=o,e[t.nested].tabs[0].totalResult=t.response.data.count,e[t.nested].tabs[0].pageLoading=!1}e[t.module].data=o.rows,e[t.module].success=!0,e[t.module].loading=!1}},instructorHome(e,t){const n=function(n){n?(e[t.module].tabs[t.index].hasData=!0,e[t.module].tabs[t.index].data=t.response.data.data):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0),e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1};t.isError?n(!1):n(!0)},instructorLearners(e,t){const n=function(n){if(n&&void 0!==t.response.data){let n=t.response.data.data;if(void 0!==t.form){if(e[t.module][t.form].data=n,e[t.module][t.form].modal=!1,"newGroupModal"===t.form){let o={date:YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n.group_created_time)[0]),group_created_time:n.group_created_time,group_id:""+n.group_id,group_name:t.payload.title,total_users:0,user:t.learners,scheduleClassURL:`/class-schedule/?groupID=${n.group_id}`};e[t.module].tabs[t.index].data.unshift(o),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group successfully created",position:"is-bottom"})}if("addLearner"===t.form){e[t.module].tabs[t.index].data.filter((function(e){return e.group_id===t.group.group_id}))[0].user=t.group.user;let n=e.instructorLearners.addLearner;n.selectedLearner="",n.payload.group_id="",n.payload.owner_id="",n.payload.user_ids=[],n.deleteUser=[],n.newAddedUser=[],n.payload.add_user_ids=[],n.payload.delete_user_ids=[],t.componentInstance.learnerAdded=!0,t.componentInstance.group=null,t.componentInstance.learnersList=[],t.componentInstance.$buefy.toast.open({duration:5e3,message:"Learners added successfully",position:"is-bottom"})}"updateTitle"===t.form&&t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group title updated successfully",position:"is-bottom"})}else{const r=e[t.module].tabs[t.index];r.hasData=!0,r.isLoadMore=!1;for(var o=0;o<n.length;o++)n[o].date=YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n[o].group_created_time)[0]),n[o].scheduleClassURL=`/class-schedule/?groupID=${n[o].group_id}`,r.data.push(n[o]);r.count=t.response.data.count,r.currentCount=r.data.length,r.offset=r.currentCount}}else void 0!==t.form?(e[t.module][t.form].modal=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${t.response.response.data.message}`,position:"is-bottom",type:"is-danger"})):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response);void 0!==t.form?e[t.module][t.form].isLoading=!1:(e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1)};t.isError?n(!1):(n(!0),204===t.response.data.code&&(e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response.data.message,console.log(t.response.data.message)))}},actions:{fetchThirdPartyData({commit:e,state:t},n){let o=t[n.store];o.loading=!0,axios.get(n.apiURL,{headers:void 0!==n.headers?n.headers:""}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)}))},postThirdPartyData({commit:e,state:t},n){let o=t[n.store];o.loading=!0,axios.defaults.timeout=void 0===n.timeout?0:n.timeout,"post"===n.method?axios.post(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"patch"===n.method?axios.patch(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"head"===n.method?axios.head(n.apiURL,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):"put"===n.method?axios.put(n.apiURL,n.payload,{headers:n.headers}).then((t=>{o.loading=!1,n.response=t,o.error=null,e(n.module,n)})).catch((t=>{o.loading=!1,o.errorData=t,n.response=t,o.error=!0,e(n.module,n)})):console.log("not defined")},fetchData({commit:e,state:t},n){let o="",r="";r="0"!==isLoggedIn?{authorization:t.config.yunoAPIToken}:{authorization:""},o=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0===n.moduleLoading||n.moduleLoading?o.loading=!0:o.loading=!1,n.moduleTabs&&n.isTabLoader&&(o.loading=!0),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.get(n.apiURL,{headers:r}).then((o=>{n.response=o,n.isError=!1,e(n.module,n),403===(n.response?.data?.data?.status??"")&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0))})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,t[n.store].loading=!1,t[n.store].success=!0;let r="";r=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0!==r.errorData&&void 0!==o.response&&(r.errorData=o.response),403===o.response.data.data.status&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0)),n.response=o,n.isError=!0,e(n.module,n)}))},putData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,n.payload,{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let r="";r=void 0!==n.store?t[n.store]:t[n.module],void 0!==r.errorData&&void 0!==o.response&&(r.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},awsPutData({commit:e,state:t},n){let o={accept:"application/json","content-type":"application/json",authorization:""};"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization="",axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,n.response=o,n.isError=!0,e(n.module,n)}))},postData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,n.payload,{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let r="";r=void 0!==n.store?t[n.store]:t[n.module],void 0!==r.errorData&&void 0!==o.response&&(r.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},awsPostData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={accept:"application/json","content-type":"application/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,JSON.stringify(n.payload),{headers:o}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0;let r="";r=void 0!==n.store?t[n.store]:t[n.module],void 0!==r.errorData&&void 0!==o.response&&(r.errorData=o.response),n.response=o,n.isError=!0,e(n.module,n)}))},deleteData({commit:e,state:t},n){let o="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",o=n.headers):(o={"content-type":"text/json"},"0"!==isLoggedIn?o.authorization=t.config.yunoAPIToken:o.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.delete(n.apiURL,{headers:o,data:n.payload}).then((t=>{n.response=t,n.isError=!1,e(n.module,n)})).catch((o=>{console.log(n.store),console.log(o),t[n.store].error=!0,n.response=o,n.isError=!0,e(n.module,n)}))}},getters:{getSignInURL:()=>"https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&scope="+["email","profile"].join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow",googleMeet(){const e=encodeURI(JSON.stringify({googleMeet:!0}));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/calendar.events","https://www.googleapis.com/auth/admin.reports.audit.readonly","https://www.googleapis.com/auth/drive.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},googleContacts(){const e=encodeURI(JSON.stringify("stateUpdate"));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/contacts.readonly","https://www.googleapis.com/auth/contacts.other.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},getSwitchAccountURL:e=>"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow"}})}}}(jQuery),YUNOPageLoader=(jQuery,{loader:function(){Vue.component("yuno-page-loader",{template:'\n                <div class="yunoPageLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOEmptyStates=(jQuery,{emptyStates:function(){Vue.component("yuno-empty-states",{props:["data","options","login"],template:'\n                <article :class="[\'emptyStates state\' + options.state, options.maxheight !== undefined && !options.maxheight ? \'noMaxHeight\' : \'\']">\n                    <figure>\n                        <img v-if="options.state !== \'notSelected\'" :src="wpThemeURL + \'/assets/images/\' + options.state + \'.png\'" :alt="options.state">\n                        <figcaption>\n                            <template v-if="options.state === \'404\'">\n                                <h1 class="stateTitle">Error (404)</h1>\n                                <p class="stateDescription">We can\'t find the page you\'re looking for.</p>\n                            </template>\n                            <template v-if="options.state === \'accountNotCreated\'">\n                                <h1 class="stateTitle">{{options.title}}</h1>\n                                <p class="stateDescription" v-html="options.description"></p>\n                            </template>\n                            <template v-if="options.state === \'notAuthorized\'">\n                                <template v-if="user.isLoggedin && options.type !== undefined && options.type === \'class\'">\n                                    <h1 class="stateTitle">You are not authorized to attend this class</h1>\n                                    <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag rounded v-if="false" type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                </template>\n                                <template v-else>\n                                    <template v-if="user.isLoggedin">\n                                        <h1 class="stateTitle">You are not authorized to view this page</h1>\n                                        <template v-if="data !== undefined">\n                                            <p class="stateDescription">{{data.errorMsg}}</p>\n                                        </template>\n                                        <template v-else>\n                                            <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag v-if="false" rounded type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                        </template>\n                                    </template>\n                                    <template v-else>\n                                        <h1 class="stateTitle">You need to log in to access</h1>\n                                        <b-button class="googleSignIn" @click="initGoogleSignIn($event)">\n                                            <span class="icnGoogle"></span> Sign in with Google \n                                        </b-button>\n                                    </template>\n                                </template>\n                            </template>\n                            <template v-if="options.state === \'dataNotFound\'">\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle" v-else>Data not found</h1>\n                                <p class="stateDescription">\n                                    <template v-if="data !== undefined">\n                                        {{data.errorMsg}}\n                                    </template>\n                                    <template v-else>\n                                        <template v-if="options.description !== undefined">\n                                            {{options.description}}\n                                        </template>\n                                        <template v-else>\n                                            The data you requested has not been found in the server.    \n                                        </template>\n                                    </template>\n                                </p>\n                            </template>\n                            <template v-if="options.state === \'notEnrolled\'">\n                                <h1 class="stateTitle marginBtm30" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle marginBtm30" v-else>You have not enrolled any course yet</h1>\n                                <b-button\n                                    v-if="options.isCTA === undefined || options.isCTA" \n                                    tag="a"\n                                    href="/learner-classes/"\n                                    class="yunoPrimaryCTA exploreCTA">\n                                    Explore Our Courses\n                                </b-button>\n                                <b-button\n                                    v-if="options.emptyStateCTA !== false" \n                                    @click="emptyStateCTA(options.emptyStateCTA, $event)"\n                                    class="yunoPrimaryCTA viewPast">\n                                    {{options.emptyStateCTA.emptyStateCTA.ctaLabel}}\n                                </b-button>\n                            </template>\n                            <template v-if="options.state === \'notSelected\'">\n                                <span v-if="options.iconType === \'material-icons\'" class="material-icons">{{options.icon}}</span>\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <p class="stateDescription" v-if="options.description">{{options.description}}</p>\n                            </template>\n                        </figcaption>\n                    </figure>\n                </article>\n            ',data:()=>({signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}}}),computed:{...Vuex.mapState(["user","userProfile","userRole"]),wpThemeURL(){return this.$store.state.themeURL},getSignInURL(){return this.$store.getters.getSwitchAccountURL}},mounted(){},methods:{emptyStateCTA(e,t){Event.$emit("emptyStateCTA",e,t)},setPayload(){let e=this.signIn,t="";t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",e.categoryURL=`/${t}`,e.landing_page.url=window.location.origin+window.location.pathname,e.landing_page.title=document.title,e.productCode="",e.leadStatus="",e.utmSource=YUNOCommon.getQueryParameter("utm_source"),e.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),e.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),e.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),e.adContent=YUNOCommon.getQueryParameter("ad_content"),e.utmTerm=YUNOCommon.getQueryParameter("utm_term"),e.gclid=YUNOCommon.getQueryParameter("gclid"),e.content.type="",e.content.id=""},initGoogleSignIn(e){void 0===this.$props.login?(this.setPayload(),localStorage.setItem("userState",window.location.pathname+window.location.search),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)):"modal"===this.$props.login&&Event.$emit("initLoginModal",e)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)}}})}}),YUNOHeader=function(e){const t=_.debounce(((e,t)=>{e.categories.isLoading=!0,e.categories.data=[],e.fetchCourseSuggestions(t)}),700);return{header:function(){Vue.component("yuno-header",{props:["isnav","scrollenabled","logoAlignment","postsignup","hassearchbar","options"],template:' \n                <div>\n                    <header id="yunoHeader" class="yunoHeader" :class="{\'noNav\': isnav === false, \'scrollEnabled\': scrollenabled, \'logoCenter\': logoAlignment}">\n                        <div class="container-fluid noOverflow">\n                            <nav class="navbar navbar-expand-lg" :class="[hassearchbar !== undefined && hassearchbar.isActive ? \'hasSearchBar\' : \'\',]">\n                                <figure class="logo navbar-brand">\n                                    <a :href="isnav !== false ? getHomeURL : \'#\'"><img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                </figure>\n                                \n                                <button v-if="isnav !== false" class="navbar-toggler" type="button" aria-controls="navbarSupportedContent" :aria-expanded="enabledSubmenu" aria-label="Toggle navigation" @click="toggleMenu">\n                                    <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                </button>\n                                <div v-if="isnav !== undefined && !isnav" class="yunoLogin">\n                                    <div v-if="false" class="yunoCallUs noSpacer" :class="{\'preLogin\': getUserStatus !== true}">\n                                        <a class="whatsapp" href="https://api.whatsapp.com/send?phone=+918847251466" target="_blank"><i class="fa fa-whatsapp" aria-hidden="true"></i></a>\n                                    </div>        \n                                </div>\n                                <div v-if="isnav !== false" class="collapse navbar-collapse yunoMainNav" id="yunoMainNav" :class="[!getUserStatus || getUserStatus && userRole.data === \'Learner\' ? \'hasSearch\' : \'\', userRole.data === \'yuno-admin\' ? \'hasAdmin\' : \'\', enabledSubmenu ? \'show\' : \'collapsed\']">\n                                    <ul class="navbar-nav align-items-center w-100">\n                                        <template v-if="headerMenu.loading" v-for="(menu, menuIndex) in 3">\n                                            <li :style="{ marginLeft: \'30px\' }">\n                                                <b-skeleton width="100px" height="32px" active></b-skeleton>\n                                            </li>\n                                        </template>\n                                        <template v-if="headerMenu.success" v-for="(menu, menuIndex) in headerMenu.data">\n                                            <li class="hasCTA" v-if="detectPlatform() === \'android\'">\n                                                <b-button tag="a"\n                                                    href="https://play.google.com/store/apps/details?id=com.yunolearning.learn"\n                                                    target="_blank"\n                                                    class="yunoPrimaryCTA small">\n                                                    Get the app\n                                                </b-button>\n                                            </li>\n                                            <template v-if="menu.type === \'menu_item\'">\n                                                <li \n                                                    class="nav-item" \n                                                    :class="{\'active\': menu.is_active === true, \'dropdown\': menu.items.length, \'isCTA\': menu.section === \'Invite\'}" \n                                                    :key="menuIndex">\n                                                    <template v-if="menu.items.length">\n                                                        <a \n                                                            :id="\'submenu\' + menuIndex"\n                                                            :class="{\'dropdownToggle\': menu.items.length}" \n                                                            @click="manageSubmenu($event, menu, \'submenu\' + menuIndex)"\n                                                            :ref="\'submenu\' + menuIndex"\n                                                            :href="menu.url">\n                                                            {{menu.section}}\n                                                        </a>\n                                                    </template>\n                                                    <a \n                                                        v-else\n                                                        :class="{\'dropdown-toggle\': menu.items.length}" \n                                                        @click="manageCustomLink($event, menu)"\n                                                        :href="menu.url">\n                                                            {{menu.section}}\n                                                    </a>\n                                                </li>\n                                            </template>\n                                            <template v-if="menu.type === \'search_item\'">\n                                                <li class="hasSearchBar">\n                                                    <div class="searchBarWrapper">\n                                                        <validation-observer \n                                                            tag="div" \n                                                            ref="searchObserver" \n                                                            v-slot="{ handleSubmit, invalid }">\n                                                            <form id="searchForm" @submit.prevent="handleSubmit(initForm)">\n                                                                <b-field class="searchFieldWrapper">\n                                                                    <validation-provider \n                                                                        tag="div"\n                                                                        class="searchField"\n                                                                        :customMessages="{ isNotBlank: errorMsg.subject }"\n                                                                        :rules="{required:true, isNotBlank:categories.selected}" \n                                                                        v-slot="{ errors, classes }">\n                                                                        <b-autocomplete\n                                                                            :class="classes"\n                                                                            v-model="categories.current"\n                                                                            :data="categories.data"\n                                                                            autocomplete="courseSearch"\n                                                                            :loading="categories.isLoading"\n                                                                            placeholder="Search..."\n                                                                            @typing="searchOnTyping"\n                                                                            @select="onSelect($event)"\n                                                                            :clearable="true"\n                                                                        >\n                                                                            <template slot-scope="props">\n                                                                                <template v-if="props.option.course_url">\n                                                                                    <div class="suggestion courseBlock">\n                                                                                        <figure>\n                                                                                            <div class="imageWrapper" v-if="false">\n                                                                                                <img :src="props.option.imageurl" :alt="props.option.title">\n                                                                                            </div>\n                                                                                            <figcaption>\n                                                                                                <p class="courseTitle">{{ props.option.title }}</p>\n                                                                                                <p class="courseDetail">\n                                                                                                    <span class="caption">Course</span>\n                                                                                                    <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>\n                                                                                                </p>\n                                                                                            </figcaption>\n                                                                                        </figure>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                                <template v-if="props.option.parent_cat_slug && props.option.course_count">\n                                                                                    <div class="suggestion categoryBlock">\n                                                                                        <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>\n                                                                                        <p class="courseDetail">\n                                                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>\n                                                                                        </p>\n                                                                                    </div>\n                                                                                </template>\n                                                                            </template>\n                                                                        </b-autocomplete>\n                                                                    </validation-provider>\n                                                                    <div class="ctaWrapper">\n                                                                        <b-button\n                                                                            native-type="submit"\n                                                                            class="doSearch">\n                                                                            <span class="material-icons-outlined">search</span>\n                                                                        </b-button>  \n                                                                    </div>\n                                                                </b-field>\n                                                            </form>\n                                                        </validation-observer>\n                                                    </div>\n                                                </li> \n                                            </template>\n                                        </template>\n                                    </ul>\n                                </div>\n                                <template v-if="isnav !== false">\n                                    <div class="yunoLogin" v-if="!getUserStatus">\n                                        <a \n                                            class="marginRight15"\n                                            @click="trackLoginLandingPage()"\n                                            href="/login/">\n                                            <span class="yuno-login-with-google-on-pages grey wired">Log in</span>\n                                        </a>\n                                        <a @click="trackLoginLandingPage()" href="/login/?type=signup">\n                                            <span class="yuno-login-with-google-on-pages">Sign up</span>\n                                        </a>\n                                        <div class="yunoCallUs" v-if="false" :class="{\'preLogin\': getUserStatus !== true}">\n                                            <template v-if="false">\n                                                <span class="caption">Call us at</span>\n                                                <a href="tel:+91-8847251466"><span class="material-icons">call</span> <span class="value">+91-8847251466</span></a>\n                                            </template>\n                                            <a href="#" @click.prevent="bookADemo()">Book a demo class</a>\n                                        </div>        \n                                    </div>\n                                </template>\n                                <template v-if="isnav !== false || hassearchbar !== undefined && hassearchbar.isActive">\n                                    <template v-if="getUserStatus">\n                                        <div class="dropdown yunoLoginDropdown" v-if="getUserProfile.success">\n                                            <a class="dropdown-toggle" href="#" role="button" id="userProfile" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">\n                                                <figure class="profilePic">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.first_name">\n                                                </figure>\n                                            </a>\n                                            <div class="dropdown-menu" aria-labelledby="userProfile">\n                                                <figure class="menuHeader">\n                                                    <img :src="getUserProfile.data.profile_img" :alt="getUserProfile.data.yuno_display_name">\n                                                    <figcaption>\n                                                        <p class="userName">{{ getUserProfile.data.yuno_display_name }}</p>\n                                                        <p class="userEmail">{{ getUserProfile.data.email }}</p>\n                                                    </figcaption>\n                                                </figure>\n                                                <ul class="userlinks">\n                                                    <li \n                                                        v-for="(item, l) in usermenu"\n                                                        :key="l"\n                                                        v-if="item.isActive"\n                                                        :class="[item.slug === \'switchAccount\' ? \'linksFooter\' : \'\']">\n                                                        <a \n                                                            v-if="item.callbackFunc !== false" \n                                                            class="dropdown-item" \n                                                            @click="item.callbackFunc" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                        <a \n                                                            v-else \n                                                            class="dropdown-item" \n                                                            :href="item.url">\n                                                            <span :class="item.iconType">{{item.icon}}</span> {{item.label}}\n                                                        </a>\n                                                    </li>\n                                                </ul>\n                                                <div class="additionalItems" v-if="isItemAvailable([\'Instructor\'])">\n                                                    <template v-if="referralCode.loading">\n                                                        <div class="loaderWrapper">\n                                                            <div class="smallLoader"></div>\n                                                        </div>\n                                                    </template>\n                                                    <template v-if="referralCode.success">\n                                                        <template v-if="referralCode.error === null">\n                                                            <div class="item">\n                                                                <b-field label="Referral code">\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="material-icons-outlined">content_copy</span>\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>\n                                                    \n                                                </div>\n                                            </div>\n                                        </div>\n                                    </template>\n                                </template>\n                            </nav>\n                        </div>\n                        <b-modal \n                            :active.sync="invite.modal" \n                            :width="500" \n                            :can-cancel="[\'escape\', \'x\']"\n                            class="yunoModal inviteModal">\n                                <div class="modalHeader">\n                                    <h2 class="modalTitle">Invite Link</h2>\n                                </div>\n                                <div class="modalBody">\n                                    <div class="wrapper">\n                                        <template v-if="inviteLink.loading">\n                                            <div class="loaderWrapper">\n                                                <div class="smallLoader"></div>\n                                            </div>\n                                        </template>\n                                        <template v-if="inviteLink.success">\n                                            <template v-if="inviteLink.error === null">\n                                                <ul class="classFields">\n                                                    <li>\n                                                        <div class="clipboard">\n                                                            <b-input id="inviteLink" :value="inviteLink.data.invitation_link" readonly></b-input>\n                                                            <i @click="copyToClipboard(\'inviteLink\')" class="fa trigger fa-clipboard" aria-hidden="true"></i>\n                                                        </div>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else>\n                                                <p>{{inviteLink.error}}</p>\n                                            </template>\n                                        </template>\n                                    </div>\n                                </div>\n                        </b-modal>\n                    </header>\n                    <div class="yunoSubmenu" \n                        :class="[submenu.isActive ? \'active\' : \'inActive\']"\n                        v-if="submenu.isActive && scrollenabled === undefined || submenu.isActive && scrollenabled !== undefined && !scrollenabled" \n                        v-closable="{exclude: [submenu.ref], handler: \'onClose\'}">\n                        <div class="container">\n                            <a href="#" class="closeSubmenu" @click.prevent="closeSubmenu()">\n                                <span class="material-icons">arrow_back_ios</span>\n                                {{ selectedSubmenu }}\n                            </a>\n                            <ul class="submenuList" :class="[submenu.data.items.length < 3 ? \'col2\' : \'\']">\n                                <li v-for="(nav, i) in exploreMenu" :key="i" v-if="nav.isVisible">\n                                    <p :class="[selectedSubmenu === nav.label ? \'active\' : \'\']">\n                                        <span class="material-icons-outlined">\n                                            {{ nav.icon }}\n                                        </span>\n                                        {{ nav.label }}\n                                        <span @click="manageSubmenuList(nav)" class="material-icons-outlined chevron">chevron_right</span>\n                                    </p>\n                                    <ul class="itemsList" :class="[nav.isItemsActive ? \'active\' : \'\']">\n                                        <li v-for="(item, j) in nav.items" :key="j">\n                                            <a :href="item.url">\n                                                {{ item.label }}\n                                            </a>\n                                        </li>\n                                    </ul>\n                                </li>\n                            </ul>\n                        </div>\n                    </div>\n                    <yuno-choose-account-type>\n                    </yuno-choose-account-type>\n                    <b-modal \n                        :active.sync="bookademo.modal" \n                        :width="388" \n                        :can-cancel="[\'escape\', \'x\']"\n                        :on-cancel="bookademoModalClose"\n                        class="yunoModal lightTheme">\n                            <template v-if="bookademo.modal">\n                                <div class="modalHeader">\n                                    <h3 class="modalTitle">\n                                        {{ bookademo.title }}\n                                    </h3>\n                                </div>\n                                <div class="modalBody">\n                                    <h2 class="modalCaption">{{ bookademo.subtitle }}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observerWrapper"\n                                        ref="bookademoobserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form @submit.prevent="handleSubmit(initBookADemo)">\n                                            <validation-provider \n                                                tag="div" \n                                                class="categoryWithImage" \n                                                :rules="{required:true}" \n                                                v-slot="{ errors, classes }"\n                                            >\n                                                <template v-for="(option, i) in bookademo.data">\n                                                    <div class="fieldWrapper">\n                                                        <div class="inner">\n                                                            <b-field :key="i" :style="categoryImg(option)">\n                                                                <b-radio-button \n                                                                    :class="classes"\n                                                                    v-model="bookademo.selected"\n                                                                    @input="initBookADemo()"\n                                                                    name="bookademo"\n                                                                    :native-value="option">\n                                                                    {{option.label}}\n                                                                </b-radio-button>\n                                                            </b-field>\n                                                            <div class="catLabel">{{option.label}}</div>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                                <p class="error">{{errors[0]}}</p>\n                                            </validation-provider>\n                                            <div class="ctaWrapper alignLeft" v-if="false">\n                                                <b-button native-type="submit" class="yunoSecondaryCTA">Submit</b-button>\n                                            </div>\n                                        </form>\n                                    </validation-observer>\n                                </div>\n                            </template>\n                    </b-modal>\n                \n                </div>\n            ',data(){return{enabledSubmenu:!1,bookademo:{modal:!1,title:"Book a Demo Class",subtitle:"Choose Subject Category",selected:"",data:[]},showSubmenu:!1,invite:{modal:!1},submenu:{isActive:!1,data:[],ref:""},usermenu:[{label:"Home",slug:"learnerDashboard",icon:"home",iconType:"material-icons-outlined",url:"/learner/",isActive:!1,callbackFunc:!1},{label:"Bookmarks",slug:"myBookmarks",icon:"bookmark_border",iconType:"material-icons-outlined",url:"/my-bookmarks/",isActive:!1,callbackFunc:!1},{label:"Enrolled Courses",slug:"myEnrollments",icon:"local_library",iconType:"material-icons-outlined",url:"/learner-courses/",isActive:!1,callbackFunc:!1},{label:"Subscribed Categories",slug:"categories",icon:"category",iconType:"material-icons-outlined",url:"/subscribed-categories",isActive:!1,callbackFunc:!1},{label:"My Profile",slug:"myProfile",icon:"account_circle",iconType:"material-icons-outlined",url:"",isActive:!1,callbackFunc:!1},{label:"Settings",slug:"settings",icon:"settings",iconType:"material-icons-outlined",url:"/settings/",isActive:!0,callbackFunc:!1},{label:"Switch account",slug:"switchAccount",icon:"swap_horiz",iconType:"material-icons-outlined",url:this.$store.getters.getSwitchAccountURL,isActive:!0,callbackFunc:()=>this.chooseAccountState()},{label:"Logout",slug:"logout",icon:"logout",iconType:"material-icons-outlined",url:"/logout/",isActive:!0,callbackFunc:!1}],errorMsg:{subject:"Please select the subject from list"},categories:{data:[],selected:null,current:"",isLoading:!1},payload:{search:""},searchParams:{limit:20,offset:0,personalization:"all",category:[],category_level_1:[],category_level_2:[],class_days_time:[{selected:[],slug:"class_days"},{selected:[],slug:"class_time"}],instructor_id:0,price_per_hour:1e4,total_duration:24},popularSearch:[],level3Menus:[],selectedSubmenu:null,exploreMenu:[{label:"Test Preparation",icon:"assignment_turned_in",isItemsActive:!1,isVisible:!0,items:[{label:"IELTS",url:"/ielts/courses/"},{label:"TOEFL",url:"/toefl/courses/"},{label:"SAT",url:"/sat/courses/"},{label:"GRE",url:"/gre/courses/"},{label:"GMAT",url:"/gmat/courses/"},{label:"CUET",url:"/cuet/courses/"},{label:"NEET",url:"/neet/courses/"},{label:"JEE",url:"/jee/courses/"},{label:"PTE",url:"/pte/courses/"},{label:"Duolingo",url:"/duolingo/courses/"}]},{label:"Languages",icon:"language",isItemsActive:!1,isVisible:!0,items:[{label:"English Speaking",url:"/english-speaking/courses/"},{label:"Hindi",url:"/hindi/courses/"},{label:"French",url:"/french/courses/"},{label:"German",url:"/german/courses/"},{label:"Spanish",url:"/spanish/courses/"},{label:"Italian",url:"/italian/courses/"}]},{label:"Professional Skills",icon:"work_outline",isItemsActive:!1,isVisible:!0,items:[{label:"Soft Skills",url:"/soft-skills/courses/"},{label:"Microsoft Excel",url:"/microsoft-excel/courses/"},{label:"Data Science and Analytics",url:"/data-science-and-analytics/courses/"},{label:"Artificial Intelligence (AI)",url:"/artificial-intelligence/courses/"},{label:"Design",url:"/design/courses/"},{label:"Anil Lamba on Finance",url:"/anil-lamba-on-finance/courses/"}]},{label:"Learning for Kids",icon:"lightbulb",isItemsActive:!1,isVisible:!0,items:[{label:"Vedic Maths",url:"/vedic-maths/courses/"},{label:"Coding for Kids",url:"/coding-for-kids/courses/"},{label:"Python Programming",url:"/python-programming/courses/"}]}]}},computed:{...Vuex.mapState(["loginWithGoogle","user","userRole","inviteLink","module","referralCode","searchSuggestions","chooseAccountTypeModal","header"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},getSignInURL(){return this.$store.getters.getSignInURL},switchAccountURL(){return this.$store.getters.getSwitchAccountURL},headerMenu:{get(){return this.$store.state.header}},getUserStatus:{get:()=>0!==Number(isLoggedIn)},getUserProfile:{get(){return this.$store.state.userProfile}}},async created(){this.emitEvents()},mounted(){this.logoGoogleSchema(),this.$nextTick((()=>{this.cacheLevel3Menus()})),this.getUserStatus?this.getState():(["userState","oldUserState","paymentState","demoClassState","skipSignUp","userSignUpPage","userSignUp","noSignupRequired","isWelcomePage","isReferrer","isQuiz"].forEach((e=>localStorage.removeItem(e))),sessionStorage.removeItem("activeUserV1"))},methods:{manageSubmenuList(e){this.selectedSubmenu=e.label,e.isItemsActive=!e.isItemsActive;this.exploreMenu.forEach((t=>{t.label!==e.label&&(t.isVisible=!t.isVisible)}))},cacheLevel3Menus(){this.level3Menus=Array.from(document.querySelectorAll(".dropdown-menu.level3"))},toggleMenu(){this.enabledSubmenu=!this.enabledSubmenu,this.header.isMenuOpen=this.enabledSubmenu},manageCustomLink(e,t){"Teach on Yuno"===t.section&&(e.preventDefault(),this.chooseAccountTypeModal.modal=!0)},isObjectBlank:e=>"object"==typeof e&&null!==e&&!Array.isArray(e)&&0===Object.keys(e).length,getQueryParams(e){const t=new URLSearchParams(e.split("?")[1]);let n={};for(const[e,o]of t)n[e]=o;return n},trackLoginLandingPage(){if(!this.user.isLoggedin){let e="",t="";e="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",t=this.isObjectBlank(this.getQueryParams(window.location.origin+window.location.pathname+window.location.search))?null:this.getQueryParams(window.location.origin+window.location.pathname+window.location.search);const n={url:window.location.origin+window.location.pathname,pageTitle:document.title,category:e,urlParams:t,zohoMeta:this.$props.options.zohoMeta};sessionStorage.setItem("landingPage",JSON.stringify(n))}},categoryImg:e=>({"background-image":`url(${e.image.replace(/ /g,"%20")})`}),bookademoModalClose(){this.bookademo.selected="",this.bookademo.data=[]},initBookADemo(){window.location.href=this.bookademo.selected.url},bookADemo(){this.bookademo.modal=!0;let e=JSON.parse(JSON.stringify(YUNOCommon.findObjectByKey(this.headerMenu.data,"label","Explore").submenu));const t={ielts:"/for-ads/ielts/ielts-for-all-v3/","english speaking":"/for-ads/english-speaking/english-for-all-v3/",pte:"/for-ads/pte/pte-for-all-v3/",duolingo:"/for-ads/duolingo-for-all/",toefl:"/for-ads/toefl-for-all/",french:"/for-ads/french/french-for-all-v3/"};e.forEach((e=>{e.slug=e.label.toLowerCase(),t[e.slug]&&(e.url=t[e.slug])})),this.bookademo.data=e},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},initForm(){const e=this.categories.selected,t={filter:"category",id:e.category_id,label:e.category,parent_id:0,slug:e.categorySlug},n={filter:"category"};void 0===this.$props.hassearchbar?window.location.href=YUNOCommon.config.host()+"/search/?state="+encodeURI(JSON.stringify(this.searchParams)):Event.$emit("initHeaderSearch",t,n)},onSelect(e){e&&(e.course_url?window.location.href=e.course_url:e.course_count&&(this.categories.selected=e,this.payload.search=e.id,this.searchParams.category=[e.id],this.searchParams.category_level_1=[],this.searchParams.category_level_2=[],e.parent_cat_slug&&(this.searchParams.category=[e.parent_cat_id],this.searchParams.category_level_1=[e.category_level_1],this.searchParams.category_level_2=[e.id]),this.initForm()))},gotCourseSuggestions(e){if(this.categories.isLoading=!1,200===e.response?.data?.code){const{course:t,category:n,sub_category:o}=e.response.data.data;n&&this.categories.data.push(...n),o&&this.categories.data.push(...o),t&&this.categories.data.push(...t)}},fetchCourseSuggestions(e){const t=this,n={apiURL:YUNOCommon.config.generic("courseSuggestions",e),module:"gotData",store:"searchSuggestions",callback:!0,callbackFunc:function(e){return t.gotCourseSuggestions(e)}};this.$store.dispatch("fetchData",n)},searchOnTyping(e){e.length>2?t(this,e):this.categories.data=[]},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},emitEvents(){Event.$on("updateHeaderSearch",(()=>{})),Event.$on("checkLoggedInState",(e=>{e&&Event.$on("gotUserRole",((e,t)=>{"Learner"===e&&(this.$store.state.homeURL="/learner"),"Instructor"===e&&this.fetchReferralCode()}))}))},structuredData(e){const t=document.createElement("script");t.setAttribute("type","application/ld+json");let n=document.createTextNode(JSON.stringify(e));t.appendChild(n),document.head.appendChild(t)},logoGoogleSchema(){let e={"@context":"https://schema.org","@type":"Organization",url:"https://www.yunolearning.com/",logo:YUNOCommon.config.host()+"/wp-content/themes/yunolearning-child/assets/images/yuno.jpeg"};this.structuredData(e)},manageLogin(e,t){e.preventDefault(),Event.$emit("manageLogin",e,t)},detectPlatform:()=>/iPhone|iPad|iPod/i.test(window.navigator.userAgent)?"ios":!!/Android/i.test(window.navigator.userAgent)&&"android",gotInviteLink(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code)this.inviteLink.data=e.response.data.data;else{const t=void 0!==e.response.data&&void 0!==e.response.data.message?e.response.data.message:YUNOCommon.config.errorMsg.common;this.inviteLink.error=t}},fetchInviteLink(){const e=this,t={apiURL:YUNOCommon.config.getInviteURLAPI(isLoggedIn),module:"gotData",store:"inviteLink",addToModule:!1,callback:!0,callbackFunc:function(t){return e.gotInviteLink(t)}};this.$store.dispatch("fetchData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},initInviteModal(e){e.preventDefault(),this.invite.modal=!0,0===this.inviteLink.data.length&&this.fetchInviteLink()},whichPage:()=>window.location.href.indexOf("open")>-1?"open":null,submenuClasses(){this.level3Menus.length||this.cacheLevel3Menus(),this.level3Menus.forEach((e=>e.classList.remove("show")))},closeSubmenu(){if("Explore"===this.selectedSubmenu)this.onClose();else{const e=this.exploreMenu;this.selectedSubmenu="Explore",e.forEach((e=>{e.isVisible=!0,e.isItemsActive=!1}))}},onClose(){this.submenu.isActive=!1,this.submenu.data=[],this.submenu.ref=""},manageSubmenu(e,t,n){e.preventDefault(),this.submenu.isActive=!this.submenu.isActive,this.submenu.data=t,this.submenu.ref=n,this.selectedSubmenu=t.section},manageSubmenuLevel3(e){this.level3Menus.length||this.cacheLevel3Menus();const t=this.level3Menus.find((t=>t.classList.contains(e)));t&&(this.submenuClasses(),t.classList.toggle("show"))},isPageHasCategory(){"undefined"!=typeof assignedCategory&&localStorage.setItem("userSignUpPage",assignedCategory)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},getState(){if(null!==localStorage.getItem("isChooseAccountState"))return["userState","isChooseAccountState"].forEach((e=>localStorage.removeItem(e))),!1}}})}}}(jQuery),YUNOHeaderRevamp=(jQuery,{headerRevamp:function(){let e;Vue.directive("closable",{bind(t,n,o){e=e=>{e.stopPropagation();const{handler:r,exclude:a}=n.value;let i=!1;a.forEach((t=>{if(!i){const n=o.context.$refs[t];i=n[0].contains(e.target)}})),t.contains(e.target)||i||o.context[r]()},document.addEventListener("click",e),document.addEventListener("touchstart",e)},unbind(){document.removeEventListener("click",e),document.removeEventListener("touchstart",e)}}),Vue.component("yuno-header-revamp",{props:["hasnav","hasscrollenabled","haslogoAlignment","postsignup","hassearchbar","fetchAPI","options"],template:'\n            <div class="isSticky">\n                <div>\n                    <section class="appPrompt" v-if="appInfo.platform === \'android\'">\n                        <div class="container">\n                            <ul class="colGrid">\n                                <li class="appIcon">\n                                    <div class="closePrompt">\n                                        <i @click="onPromptClose()" class="fa fa-times" aria-hidden="true"></i>\n                                    </div>\n                                    <figure class="appMedia">\n                                        <div class="iconWrap">\n                                            <img :src="appInfo.icon" :alt="appInfo.shortName">\n                                        </div>\n                                        <figcaption class="appInfo">\n                                            <p class="infoTitle">{{ appInfo.shortName }}</p>\n                                            <p class="infoCaption" v-if="appInfo.hasOS === \'android\'">{{ appInfo.androidCaption }}</p>\n                                        </figcaption>\n                                    </figure>\n                                </li>\n                                <li>\n                                    <b-button tag="a"\n                                        :href="appInfo.hasOS === \'android\' ? appInfo.androidURL : \'\'"\n                                        target="_blank"\n                                        rel="nofollow"\n                                        @click="onPromptView($event)"\n                                        class="yunoPrimaryCTA small">\n                                        Open\n                                    </b-button>\n                                </li>\n                            </ul>\n                        </div>\n                    </section>\n                    <template v-if="userInfo.error">\n                        <header id="yunoHeader" class="yunoHeader">\n                            <div class="container noOverflow">\n                                <nav class="navbar navbar-expand-lg">\n                                    <figure class="logo navbar-brand">\n                                        <a :href="getHomeURL"><img :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning"></a>\n                                    </figure>\n                                    <button class="navbar-toggler" data-toggle="collapse" data-target="#yunoMainNav" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">\n                                        <span class="navbar-toggler-icon"><i class="fa fa-bars" aria-hidden="true"></i></span>\n                                    </button>\n                                    <div class="collapse navbar-collapse yunoMainNav" id="yunoMainNav">\n                                        <ul class="navbar-nav justify-content-end align-items-center w-100">\n                                            <li class="nav-item">\n                                                <a \n                                                    href="/logout">\n                                                    Logout\n                                                </a>\n                                            </li>\n                                        </ul>\n                                    </div>\n                                </nav>\n                            </div>\n                        </header>\n                        <main id="yunoMain">\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </main>\n                    </template>\n                    <template v-else>\n                        <div class="notificationBar" :class="{ notificationHide: notification.isHidden, notificationShow: !notification.isHidden, notVisibleInDOM: !notification.isVisible }">\n                            <a :href="notification.url" target="_blank">\n                                {{ notification.label }}\n                            </a>\n                            <span class="material-icons-outlined" @click="hideNotificationBar">close</span>\n                        </div>\n                        <yuno-header \n                            :isnav="hasnav !== undefined ? hasnav : undefined" \n                            :logoAlignment="haslogoAlignment !== undefined ? haslogoAlignment : undefined" \n                            :scrollenabled="hasscrollenabled !== undefined ? hasscrollenabled : undefined"\n                            :postsignup="postsignup"\n                            :hassearchbar="hassearchbar"\n                            :options="{zohoMeta: options !== undefined ? options.zohoMeta : \'\'}">\n                        </yuno-header>\n                    </template>\n                    <b-modal \n                        :active.sync="config.unauthorizedModal" \n                        :width="450" \n                        :can-cancel="[\'escape\', \'x\']" \n                        :on-cancel="unauthorizedModalClose"\n                        class="yunoModal">\n                            <div class="modalHeader">\n                                <h2 class="modalTitle">Session Expired</h2>\n                            </div>\n                            <div class="modalBody">\n                                <div class="wrapper">\n                                    <p>{{sessionExpired}}</p>\n                                </div>\n                            </div>\n                            <div class="modalFooter">\n                                <div class="unauthorizedLogin">\n                                    <a \n                                        @click.prevent="setState()"\n                                        href="#">\n                                        <span class="g_icon"></span>\n                                        <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                    </a>\n                                </div>\n                            </div>\n                    </b-modal>\n                </div>\n                <b-modal \n                    :active.sync="loginModal.modal" \n                    :width="360" \n                    :can-cancel="[\'escape\', \'x\']"\n                    :on-cancel="loginModalClose"\n                    class="yunoModal loginSignupModal">\n                        <template v-if="loginModal.modal">\n                            <div class="modalHeader">\n                                <h3 class="modalTitle">\n                                    <template v-if="loginModal.active === \'login\'">\n                                        Log in\n                                    </template>\n                                    <template v-else>\n                                        Sign up\n                                    </template>\n                                </h3>\n                            </div>\n                            <div class="modalBody">\n                                <template v-if="loginModal.active === \'login\'">\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <div class="ctaWrapper">\n                                        <button class="googleLogin width70" @click="setState">\n                                            <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                        </button>\n                                    </div>\n                                    <p class="footerCaption">\n                                        {{loginModal[loginModal.active].footer.label}} <a @click="updateLoginState($event, \'signup\')" :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                    </p>\n                                </template>\n                                <template v-else>\n                                    <h2 class="modalCaption">{{loginModal[loginModal.active].title}}</h2>\n                                    <validation-observer \n                                        tag="div" \n                                        class="observer"\n                                        ref="commonSignupObserver" \n                                        v-slot="{ handleSubmit, invalid }">\n                                        <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">\n                                            <b-field label="Phone number">\n                                                <validation-provider :customMessages="{ required: \'Phone number is required\'}" tag="div" :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" v-slot="{ errors, classes }">\n                                                    <b-input placeholder="Enter your phone number" :class="classes" v-model="signIn.mobile"></b-input>\n                                                    <p class="error">{{errors[0]}}</p>\n                                                </validation-provider>    \n                                            </b-field>\n                                            <div class="ctaWrapper">\n                                                <button class="googleLogin" type="submit">\n                                                    <img :src="loginModal.googleIcnURL" alt="google"></img> {{loginModal[loginModal.active].cta}}\n                                                </button>\n                                            </div>\n                                            <p class="helperCaption">\n                                                {{loginModal[loginModal.active].helper.label}} <a @click="updateLoginState($event, \'login\')" :href="loginModal[loginModal.active].helper.actionURL">{{loginModal[loginModal.active].helper.actionLabel}}</a>\n                                            </p>\n                                            <p class="footerCaption" v-if="loginModal[loginModal.active].footer.isActive">\n                                                {{loginModal[loginModal.active].footer.label}} <a :href="loginModal[loginModal.active].footer.actionURL">{{loginModal[loginModal.active].footer.actionLabel}}</a>\n                                            </p>\n                                        </form>\n                                    </validation-observer>\n                                </template>\n                            </div>\n                        </template>\n                </b-modal>\n            </div>\n            ',data(){return{tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,appInfo:{shortName:"Yuno Learning",name:"Yuno Learning",icon:this.$store.state.themeURL+"/assets/images/yunoLogo.svg",androidURL:"https://play.google.com/store/apps/details?id=com.yunolearning.learn",hasOS:"android",androidCaption:"FREE - In the Google Play Store",platform:"",daysHidden:1,daysReminder:90},signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}},loginModal:{modal:!1,active:"",googleIcnURL:this.$store.state.themeURL+"/assets/images/google.svg",login:{title:"Log into your Yuno Learning account",hasMobile:!1,cta:"Login with Google",footer:{label:"Don't have an account yet?",actionLabel:"Sign Up",actionURL:"#"}},signup:{title:"Sign up to create a free account on Yuno Learning",hasMobile:!0,cta:"Sign up with Google",helper:{label:"Already a user?",actionLabel:"Login",actionURL:"#"},footer:{label:"Are you an instructor?",isActive:!0,actionLabel:"Sign up here",actionURL:"/ielts/become-an-instructor"}}},notification:{isHidden:!1,isVisible:!1,label:"Free summer online workshop for kids and teenagers",url:"/free-summer-workshops-for-kids",hideNotificationDate:""}}},computed:{...Vuex.mapState(["user","userRole","userInfo","userProfile","header","footer","config","apiTokenExpiryTime","apiTokenRefresh"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},emptyStates:()=>({state:"dataNotFound"}),getSignInURL(){return this.$store.getters.getSignInURL}},async created(){this.emitEvents()},mounted(){this.loginStatus()},methods:{hideNotificationBar(){this.notification.isVisible=!1,this.notification.isHidden=!0},setSigninProps(){localStorage.setItem("userState",window.location.pathname+window.location.search),void 0!==this.$props.postsignup&&localStorage.setItem("oldUserState",window.location.pathname+window.location.search)},setPayload(){const{zohoMeta:e={}}=this.$props.options||{},t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general";this.signIn={...this.signIn,categoryURL:`/${t}`,landing_page:{url:window.location.origin+window.location.pathname,title:document.title},productCode:e.productcode||"",leadStatus:e.leadstatus||"",utmSource:YUNOCommon.getQueryParameter("utm_source"),utmCampaign:YUNOCommon.getQueryParameter("utm_campaign"),utmMedium:YUNOCommon.getQueryParameter("utm_medium"),adGroupID:YUNOCommon.getQueryParameter("adgroupid"),adContent:YUNOCommon.getQueryParameter("ad_content"),utmTerm:YUNOCommon.getQueryParameter("utm_term"),gclid:YUNOCommon.getQueryParameter("gclid"),content:{type:e.content_type||"",id:e.content_id||""}}},isFirefoxPrivate(e){null!==e&&e?setTimeout((()=>{this.setSigninProps(),this.setPayload(),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),5e3):(this.setSigninProps(),this.setPayload(),setTimeout((()=>{window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),50))},setState(){YUNOCommon.isPrivateWindow(this.isFirefoxPrivate)},emitEvents(){void 0!==Event.$emit&&(Event.$on("manageLogin",((e,t)=>{this.manageLogin(e,t)})),Event.$on("initLoginModal",((e,t,n)=>{this.manageLogin(e,"signup"),this.loginModal[this.loginModal.active].footer.isActive=!1})),Event.$on("initSignupModal",(e=>{this.manageLogin(e,"signup")})))},updateLoginState(e,t){e.preventDefault(),this.loginModal.active=t},initCommonSignup(){this.setState()},loginModalClose(){setTimeout((()=>{this.loginModal.active=""}),300),localStorage.removeItem("paymentState"),localStorage.removeItem("isQuiz")},manageLogin(e,t){this.loginModal.modal=!0,this.loginModal.active=t},onPromptClose(){const e=this.appInfo.daysHidden;YUNOCommon.setCookie("yunoAppPrompt2",!0,e),this.detectPlatform()},onPromptView(e){this.detectPlatform()},detectPlatform(){if(/iPhone|iPad|iPod/i.test(window.navigator.userAgent))this.appInfo.platform="ios";else if(/Android/i.test(window.navigator.userAgent)){const e=YUNOCommon.getCookie("yunoAppPrompt2");this.appInfo.platform=void 0===e&&"android"}else this.appInfo.platform=!1},unauthorizedModalClose(){window.location.href="/logout"},roleSpecificAPI(e,t){"Learner"===e&&this.detectPlatform(),void 0!==Event.$emit&&Event.$emit("gotUserRole",e,t)},loginStatus(){0!==Number(isLoggedIn)?(this.user.isLoggedin=!0,this.fetchUserInfo(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):(this.user.isLoggedin=!1,this.detectPlatform(),void 0===this.$props.hasnav||this.$props.hasnav?(this.fetchPreLoginMenu(),void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin)):void 0!==Event.$emit&&Event.$emit("checkLoggedInState",this.user.isLoggedin))},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.userRole.data=t.role,this.userRole.success=!0,this.userProfile.data=t,this.userProfile.success=!0,(void 0===this.$props.hasnav||this.$props.hasnav)&&0!==this.header.data.length&&this.fetchPostLoginMenu(t.role),this.$props.fetchAPI&&this.fetchPostLoginMenu(t.role);const n=localStorage.getItem("skipSignUp"),o=localStorage.getItem("paymentState");if("Instructor"===t.role&&"de-active"===t.account_status&&"/account-disabled/"!==window.location.pathname)return window.location.href=YUNOCommon.config.host()+"/account-disabled",!1;"pending"===t.is_signup_completed&&"/sign-up/"!==window.location.pathname&&"/instructor-sign-up/"!==window.location.pathname&&null===n&&null===o&&setTimeout((()=>{"Instructor"===t.role?window.location.href=YUNOCommon.config.host()+"/instructor-sign-up":window.location.href=YUNOCommon.config.host()+"/sign-up"}),50),this.roleSpecificAPI(t.role,t)}else this.userInfo.error=!0,this.header.success=!0,this.footer.success=!0},fetchUserInfo(){const e={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:this.gotUserInfo};this.$store.dispatch("fetchData",e)},highlightCurrentPage(e){const t=e.response.data.data.map((e=>({...e,isActive:e.url===window.location.href})));this.header.data=t},gotPostLoginMenu(e){200===e?.response?.data?.code&&(this.highlightCurrentPage(e),Event.$emit("gotUserMenu"))},fetchPostLoginMenu(e){const t={apiURL:YUNOCommon.config.headerMenuAPIV2(isLoggedIn,e),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:this.gotPostLoginMenu};this.$store.dispatch("fetchData",t)},gotPreLoginMenu(e){200===e?.response?.data?.code&&(this.highlightCurrentPage(e),Event.$emit("gotPreLoginMenu"))},fetchPreLoginMenu(){const e={userID:isLoggedIn,orgID:0},t={apiURL:YUNOCommon.config.header("menu",e),module:"gotData",store:"header",callback:!0,callbackFunc:this.gotPreLoginMenu};this.$store.dispatch("fetchData",t)}}})}}),YUNOFooter=(jQuery,{footer:function(){Vue.component("yuno-footer",{props:["isnav","isLogo","whatsapp"],template:'\n                <footer id="yunoFooter" class="yunoFooter" :class="{\'noNav\': isnav === false, \'noBG\': isLogo !== undefined && !isLogo}">\n                    <div class="container noOverflow">\n                        <div class="row" v-if="isnav !== false">\n                            <template v-for="(primary, i) in footerData.primary">\n                                <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                    <template v-if="primary.type === \'withIcon\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :class="item.icon"\n                                                    :key="j">\n                                                    <a rel="nofollow" :href="item.url">\n                                                        <span>{{item.label}}</span>\n                                                    </a>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'basicNoLinks\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j"\n                                                    :class="[item.helper !== undefined && item.helper !== \'\' ? \'d-flex align-items-center\' : \'\']"\n                                                >\n                                                    <span>{{item.label}}</span>\n                                                    <small class="ml-2 helper" v-if="item.helper !== undefined && item.helper !== \'\'">{{item.helper}}</small>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'blocks\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j"\n                                                    :class="[item.helper !== undefined ? \'d-flex align-items-center\' : \'\']"\n                                                >\n                                                    <h4>{{item.label}}</h4>\n                                                    <p>{{item.description}}</p>\n                                                    <a :href="item.cta.url">{{item.cta.label}}</a>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'withOrderList\'">\n                                        <ul class="linkList checkList marginBtm30" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j">\n                                                    {{item.label}}\n                                                </li>\n                                            </template>\n                                        </ul>\n                                        <b-button tag="a"\n                                            :href="primary.cta.url"\n                                            target="_blank"\n                                            rel="nofollow noopener"\n                                            class="yunoSecondaryCTA">\n                                            {{ primary.cta.label }}\n                                        </b-button>\n                                    </template>\n                                    <template v-if="primary.type === \'stackBlock\'">\n                                        <template v-for="(item, j) in primary.items" v-if="item.type === \'appCTA\'">\n                                            <ul class="linkList marginBtm30" :class="primary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <template v-for="(subitem, k) in item.items">\n                                                    <li \n                                                        :class="subitem.icon"\n                                                        :key="k">\n                                                        <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                            <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                        </a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>            \n                                        <template v-for="(item, j) in primary.items" v-if="item.type === \'iconOnly\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <li class="iconsBlock">\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <div \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                        </div>\n                                                    </template>\n                                                </li>\n                                            </ul>\n                                        </template> \n                                    </template>\n                                </div>    \n                            </template>\n                        </div>\n                        <div class="row" v-if="isnav !== false">\n                            <div class="col-12">\n                                <div class="spacer"></div>\n                            </div>\n                        </div>\n                        <div class="row" v-if="isnav !== false">\n                            <template v-for="(secondary, i) in footerData.secondary">\n                                <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                    <template v-if="secondary.type === \'basic\'">\n                                        <ul class="linkList" :class="secondary.type">\n                                            <li v-if="secondary.title !== undefined" class="listTitle">\n                                                <h3>{{ secondary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in secondary.items">\n                                                <li \n                                                    :key="j"\n                                                >\n                                                    <template v-if="isContainsObject(item.url)">\n                                                        <a :href="getEnvironmentUrl(item.url)">{{item.label}}</a>\n                                                    </template>\n                                                    <template v-else>\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </template>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="secondary.type === \'stackBlock\'">\n                                        <template v-for="(item, j) in secondary.items" v-if="item.type === \'appCTA\'">\n                                            <ul class="linkList marginBtm30" :class="secondary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <template v-for="(subitem, k) in item.items">\n                                                    <li \n                                                        :class="subitem.icon"\n                                                        :key="k">\n                                                        <img class="appQR" alt="Yuno Android APP QR Code" width="96" height="96" :src="wpThemeURL + \'/assets/images/appQR.webp\'">\n                                                        <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                            <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                        </a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>            \n                                        <template v-for="(item, j) in secondary.items" v-if="item.type === \'iconOnly\'">\n                                            <ul class="linkList" :class="secondary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <li class="iconsBlock">\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <div \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                        </div>\n                                                    </template>\n                                                </li>\n                                            </ul>\n                                        </template> \n                                        <a :href="secondary.cta.url" target="_blank" class="link">{{ secondary.cta.label }}</a>\n                                    </template>\n                                </div>    \n                            </template>\n                        </div>\n                        <div class="row" v-if="!isnav">\n                            <div class="col-12">\n                                <ul class="footerBottom d-flex align-items-center justify-content-center">\n                                    <li class="copy">© Yunolearnung. {{currentYear}}</li>\n                                    <li class="copy">\n                                        <a href="/privacy-policy/" target="_blank">Privacy Policy</a>\n                                    </li>\n                                    <li class="copy">\n                                        <a href="/terms-of-use/" target="_blank">Terms of use</a>\n                                    </li>\n                                    <li v-if="false">\n                                        <figure class="logo">\n                                            <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                                        </figure>\n                                    </li>\n                                </ul>\n                            </div>\n                        </div>    \n                    </div>\n                    <div class="whatsappSticky" v-if="whatsapp === undefined || whatsapp ? true : false"><a :href="\'https://api.whatsapp.com/send?phone=\' + mobile" target="_blank"><span>Chat with us</span></a></div>\n                </footer>\n            ',data:()=>({currentYear:(new Date).getFullYear(),mobile:"************",footerData:{primary:[{title:!1,type:"withIcon",items:[{label:"Check internet speed",url:"http://yunolearning.speedtestcustom.com",icon:"wifiSpeed"},{label:"Zoom test",url:"https://zoom.us/test",icon:"zoomTest"}]},{title:"Teach Online",type:"blocks",items:[{label:"Instructor",description:"Create an instructor account and start teaching",cta:{label:"Sign up for instructor",url:"/become-an-instructor/"}},{label:"Academy",description:"Create your academy, publish courses and get new learners",cta:{label:"Create academy",url:"/create-academy/"}}]},{title:"Coming Soon",type:"basicNoLinks",items:[{label:"Developer Guide",helper:"",url:"#"},{label:"API Documentation",url:"#"},{label:"Postman Collection",url:"#"}]},{title:"Yuno Business",type:"basicNoLinks",items:[{label:"Yuno for Study Abroad Consultants",helper:"",url:"/for-study-abroad-and-immigration-companies/"},{label:"Yuno for Career Conselors",helper:"",url:"#"},{label:"Yuno for Skilling Companies",helper:"",url:"#"},{label:"Yuno for Learning & Development",helper:"",url:"#"},{label:"Yuno for IT Service Companies",helper:"",url:"#"},{label:"Yuno for Airlines & Hospitality",helper:"",url:"#"}]}],secondary:[{title:"IELTS",type:"basic",items:[{label:"IELTS Online Classes",url:"/ielts"},{label:"IELTS Free Study Material",url:"/ielts/collection/ielts-study-material-and-practice-tests/"},{label:"IELTS Reading",url:"/blog/how-to-prepare-for-ielts-reading-test"},{label:"IELTS Writing Task 1",url:"/blog/how-to-prepare-for-ielts-writing-task-1"},{label:"IELTS Writing Task 2",url:"/blog/how-to-prepare-for-ielts-writing-task-2"},{label:"IELTS Listening",url:"/blog/how-to-prepare-for-ielts-listening-test"},{label:"IELTS Speaking",url:"/blog/how-to-prepare-for-ielts-speaking-test"},{label:"IELTS Practice Tests",url:"/ielts/practice-tests"},{label:"Free IELTS Resources",url:"/ielts"}]},{title:"English Speaking",type:"basic",items:[{label:"Spoken English Classes",url:{dev:'/search/?state=%7B"limit":20,"offset":0,"personalization":"all","category":%5B3084%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"price_per_hour":10000,"total_duration":24%7D',stage:'/search/?state=%7B"limit":20,"offset":0,"personalization":"all","category":%5B3084%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"price_per_hour":10000,"total_duration":24%7D',prod:'/search/?state=%7B"category":%5B2811%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"limit":20,"offset":0,"personalization":"all","price_per_hour":10000,"total_duration":24%7D'}},{label:"English Speaking for Working Professionals",url:"/english-speaking/professionals/"},{label:"English Speaking for Students",url:"/english-speaking/students/"},{label:"Spoken English for Kids",url:"/english-speaking/kids/"},{label:"English Speaking for Teenagers",url:"#"},{label:"English Speaking for Home makers",url:"/english-speaking/homemakers/"},{label:"English Speaking for Interview",url:"/english-speaking/interview/"}]},{title:"More Resources",type:"basic",items:[{label:"Microsoft Excel Video Tutorials",url:"/microsoft-excel/resources/"},{label:"Python Programming Videos",url:"/python-programming/resources/"},{label:"Vedic Maths Tricks",url:"/vedic-maths/resources/"},{label:"Learn Vedic Maths",url:"/vedic-maths/"},{label:"Coding Classes for kids",url:"/coding-for-kids/"}]},{type:"stackBlock",items:[{title:"Our Mobile App",type:"appCTA",items:[{label:"Get the Android App",url:"https://play.google.com/store/apps/details?id=com.yunolearning.learn",icon:"android"}]},{title:"Social Media Channels",type:"iconOnly",items:[{label:"Facebook",url:"https://www.facebook.com/yunolearning",icon:"facebook"},{label:"Twitter",url:"https://twitter.com/YunoLearning",icon:"twitter"},{label:"Linkedin",url:"https://www.linkedin.com/company/yunolearning",icon:"linkedin"},{label:"Instagram",url:"https://www.instagram.com/yuno.learning/",icon:"instagram"},{label:"Youtube",url:"https://www.youtube.com/channel/UCTVIUtnoO5c103cSWXIBelQ",icon:"youtube"}]}],cta:{label:"Check out our blog",url:"/blog/"}}]}}),computed:{...Vuex.mapState(["user","userRole","footer"]),wpThemeURL(){return this.$store.state.themeURL},host:()=>YUNOCommon.config.pickHost(),getEnvironmentUrl(){return e=>{if(!this.isContainsObject(e))return e;const t={"https://dev.yunolearning.com":"dev","https://www.yunolearning.com":"prod",default:"stage"},n=t[this.host]||t.default;return this.host+e[n]}}},async created(){},methods:{isContainsObject:e=>"object"==typeof e&&null!==e}})}});Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",(()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()}))},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const e=sessionStorage.getItem("isLHSMenu");null===e||this.isMobile?this.reduce=!1:(this.reduce="true"===e,this.sidebarToggle(!0))},sidebarToggle(e){e||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout((()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}),n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},extractSlugFromURL(e){const t=e.replace(/\/$/,"").split("/");""===t[t.length-1]&&t.pop();return t[t.length-1]},manageCurrentPage(e){const t=e=>e.replace(/\/$/,""),n=t(window.location.origin+window.location.pathname);e.forEach((e=>{e.items.forEach((e=>{e.is_active=n===t(e.url);let o=!1;e.sub_items.forEach((r=>{r.is_active=n===t(r.url),r.is_active&&r.parent_id===e.id&&(o=!0)})),e.is_expended=!!o}))}))},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},gotPostLoginMenu(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";e?t=e.response.data.data:(t=this.header.data,this.header.success=!0),this.manageCurrentPage(t),this.header.data=t,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(e){const t={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,o={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPostLoginMenu(e)}};this.$store.dispatch("fetchData",o)},manageOrgAdmin(e){const{host:t}=YUNOCommon.config,{has_org:n,org_id:o}=e;null===sessionStorage.getItem("activeOrg")&&(n?o.length>1?(window.location.href=`${t()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(o[0].id)):window.location.href=`${t()}/create-organization-account`)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";if(e?t=e.response.data.data:(t=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(t.role),this.userRole.data=t.role,this.userProfile.data=t,this.userProfile.success=!0,t.role,"Learner"===t.role&&this.fetchReferralCode(),"Learner"===t.role&&"pending"===t.is_signup_completed){const e=localStorage.getItem("userState");window.location.pathname+window.location.search!==e&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout((()=>{localStorage.removeItem("skipSignUp")}),10))}t.role,this.$emit("userInfo",t)}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},getStorage(){const e=this.storage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.header.data=e.menu}this.loginStatus()},setStorage(){const e=this.storage,t={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(e.name+"V"+e.version,JSON.stringify(t))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const e=this.storage;sessionStorage.removeItem(e.name+"V"+e.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(e){if(this.generateCode.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;Event.$emit("fetchReferralCode")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("postData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(e,t){0!==t.sub_items.length&&e.preventDefault(),"generate-code"===t.slug&&e.preventDefault(),"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.referralCode.data=t}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},gotCode(e){if(this.moduleWithoutTab.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;this.fetchReferralCode()}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotCode(t)}};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Referral":"Referral Earnings",isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)}}}),Vue.component("yuno-empty-state-v6",{props:{wrapperClass:{type:String,required:!1},heading:{type:String,required:!1},errorMessage:{type:String,required:!1},hasImg:{type:Boolean,required:!1,default:!0},iconImg:{type:String,required:!1},hasBorder:{type:Boolean,required:!1,default:!1}},template:'\n        <section class="emptyStateV6" :class="[wrapperClass, { hasBorder: hasBorder }]">\n            <figure>\n                <img \n                    v-if="hasImg"\n                    width="80" \n                    height="59" \n                    :src="iconImg ? wpThemeURL + iconImg : wpThemeURL + \'/assets/images/noBatch.png\'" \n                    :alt="errorMessage"\n                >\n                <figcaption>\n                    <h3 v-if="heading" class="subtitle1 onSurface">{{ heading }}</h3>\n                    {{ errorMessage }}\n                </figcaption>\n            </figure>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},mounted(){},methods:{}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required ",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-list-places",{template:'\n        <yuno-page-grid :authorizedRoles="authorizedRoles" @onUserInfo="onUserInfo" :hasSearchBar="false">\n\t\t\t<template v-slot:main>\n\t\t\t\t<div class="yunoListPlaces">\n\t\t\t\t\t<div class="container">\n\t\t\t\t\t\t<div class="mainHeader">\n\t\t\t\t\t\t\t<div class="block">\n\t\t\t\t\t\t\t\t<h1>{{ pageHeader.title }}</h1>\n\t\t\t\t\t\t\t\t<div class="action">\n\t\t\t\t\t\t\t\t\t<b-button tag="a"\n\t\t\t\t\t\t\t\t\t\t:href="pageHeader.button.url"\n\t\t\t\t\t\t\t\t\t\tclass="yunoPrimaryCTA ">\n\t\t\t\t\t\t\t\t\t\t{{pageHeader.button.label}}\n\t\t\t\t\t\t\t\t\t</b-button>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="listPlaces">\n\t\t\t\t\t\t\t<h2 class="onSurface subtitle1 m-bottom-large-times-2">Places & Classrooms</h2>\n\t\t\t\t\t\t\t<template v-if="filterResult.loading">\n\t\t\t\t\t\t\t\t<b-skeleton width="100%" height="200px"></b-skeleton>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t<template v-else-if="filterResult.success && filterResult.error === null">\n\t\t\t\t\t\t\t\t<b-table \n\t\t\t\t\t\t\t\t\tref="table" \n\t\t\t\t\t\t\t\t\t:data="filterResult.data"\n\t\t\t\t\t\t\t\t\tdetailed\n\t\t\t\t\t\t\t\t\tdetail-key="id" \n\t\t\t\t\t\t\t\t\t:detail-transition="transitionName"\n\t\t\t\t\t\t\t\t\t:paginated="true"\n\t\t\t\t\t\t\t\t\t:backend-pagination="true"\n\t\t\t\t\t\t\t\t\t:show-detail-icon="false" \n\t\t\t\t\t\t\t\t\t:loading="filterResult.isLoadMore" \n\t\t\t\t\t\t\t\t\t:total="filterResult.count"\n\t\t\t\t\t\t\t\t\t:per-page="filterResult.limit" \n\t\t\t\t\t\t\t\t\t:current-page="filterResult.currentPage"\n\t\t\t\t\t\t\t\t\t@details-open="onDetailOpen"\n\t\t\t\t\t\t\t\t\t@details-close="onDetailClose"\n\t\t\t\t\t\t\t\t\t@page-change="pageChange($event)"\n\t\t\t\t\t\t\t\t>\n\n\t\t\t\t\t\t\t\t\t<b-table-column v-slot="props">\n\t\t\t\t\t\t\t\t\t\t<span class="material-icons-outlined arrowIcon" @click="props.toggleDetails(props.row)">\n\t\t\t\t\t\t\t\t\t\t\t{{ openRows.has(props.row.id) ? \'expand_more\' : \'chevron_right\' }}\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t</b-table-column>\n\n\t\t\t\t\t\t\t\t\t<b-table-column field="name" label="Place Name" v-slot="props">\n\t\t\t\t\t\t\t\t\t\t<span class="rowLabel" @click="props.toggleDetails(props.row)">\n\t\t\t\t\t\t\t\t\t\t\t{{ props.row.name }} {{ props.row.classrooms.length > 1 ? "(" +\n\t\t\t\t\t\t\t\t\t\t\tprops.row.classrooms.length + ")" : \'\' }}\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t</b-table-column>\n\n\t\t\t\t\t\t\t\t\t<b-table-column field="type" label="Type" v-slot="props">\n\t\t\t\t\t\t\t\t\t\t<span class="rowLabel capitalize">{{ formatLabel(props.row.type) }}</span>\n\t\t\t\t\t\t\t\t\t</b-table-column>\n\n\t\t\t\t\t\t\t\t\t<b-table-column field="facilities" label="Facilities" v-slot="props">\n\t\t\t\t\t\t\t\t\t\t<span class="rowLabel">{{ placeFacilties(props.row.facilities) }}</span>\n\t\t\t\t\t\t\t\t\t</b-table-column>\n\n\t\t\t\t\t\t\t\t\t<template #detail="props">\n\t\t\t\t\t\t\t\t\t\t<div class="content">\n\t\t\t\t\t\t\t\t\t\t\t<p class="subtitle2 secondary m-bottom-large-times-2">{{ props.row.name }}</p>\n\t\t\t\t\t\t\t\t\t\t\t<div v-for="classroom in props.row.classrooms" :key="classroom.id" class="box">\n\t\t\t\t\t\t\t\t\t\t\t\t<p class="secondary subtitle2 m-bottom-large-times-1">{{ classroom.title }}</p>\n\t\t\t\t\t\t\t\t\t\t\t\t<ul class="wrapper">\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1">Floor: </span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1 noBold"> {{ formatLabel(classroom.floor.type) }}</span></li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1">Area (Sqft): </span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1 noBold"> {{ classroom.area }}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1">Capacity: </span> \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1 noBold"> {{ classroom.seating_capacity }}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1">Computer Terminal: </span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1 noBold"> {{ classroom.facilities.computer_terminals }}</span> \n\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<li class="facilities m-top-small-times-1">\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1">Facilities: </span> \n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<span class="onSurfaceVariant caption1 noBold">{{ classroomFacilties(classroom.facilities) }}</span>\n\t\t\t\t\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t</b-table>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t<template v-else-if="filterResult.success">\n\t\t\t\t\t\t\t\t<yuno-empty-state-v6 :errorMessage="filterResult.errorData"\n\t\t\t\t\t\t\t\t\ticonImg="/assets/images/enrollment.png" :hasBorder="true">\n\t\t\t\t\t\t\t\t</yuno-empty-state-v6>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</template>\n\t\t</yuno-page-grid>\n   ',computed:{...Vuex.mapState(["user","userInfo","userRole","filterResult"])},data:()=>({authorizedRoles:["yuno-admin","org-admin"],pageHeader:{title:"My Classrooms",button:{url:"/add-classroom/",label:"Add Clasrooms"}},transitionName:"fade",openRows:new Set}),methods:{onUserInfo(e){this.fetchAllPlaces(!0)},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},formatLabel(e){switch(e){case"COMMERCIAL_BUILDING":return"Commercial Building";case"OFFICE":return"Office";case"SCHOOL":return"School";case"ONE_PLUS":return"One Plus";case"GROUND_PLUS":return"Ground Floor";default:return e}},onDetailClose(e){this.openRows.delete(e.id)},onDetailOpen(e){this.openRows.add(e.id)},placeFacilties(e){if(!e)return"";return[e.car_parking?.self_parking&&"Self Car Parking",e.car_parking?.valet_service&&"Valet Car Parking",e.bike_parking&&"Bike Parking"].filter(Boolean).join(", ")},classroomFacilties(e){if(!e)return"";return[e.wifi&&"Wifi",e.blackboard&&"Blackboard",e.whiteboard&&"Whiteboard",e.projector&&"Projector",e.lcd_monitor&&"LCD Monitor",e.air_conditioning&&"Air Conditioning",e.power_backup&&"Power Backup"].filter(Boolean).join(", ")},pageChange(e){this.filterResult.currentPage=e,this.filterResult.isLoadMore=!0,this.filterResult.offset=Math.floor(this.filterResult.limit*e-this.filterResult.limit),this.fetchAllPlaces(!1)},gotPlaces(e){this.filterResult.loading=!1,this.filterResult.isLoadMore=!1;const t=e?.response?.data;if(200===t?.code){let e=t.data;this.filterResult.count=t.count,this.filterResult.data=e;e[0].id}},fetchAllPlaces(e){this.filterResult.loading=e;const t="org-admin"===this.userRole.data?"fetchOrgPlace":"fetchAllPlace",n={view_type:"list",limit:this.filterResult.limit,offset:this.filterResult.offset,..."org-admin"===this.userRole.data&&{org_id:this.activeOrg()}},o={apiURL:YUNOCommon.config.academy(t,n),module:"gotData",store:"filterResult",callback:!0,callbackFunc:this.gotPlaces};this.$store.dispatch("fetchData",o)}}});