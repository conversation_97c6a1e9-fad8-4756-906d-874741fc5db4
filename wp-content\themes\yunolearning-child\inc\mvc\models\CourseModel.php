<?php

namespace V4;

/**
 * Class AcademyModel
 * Handles Academy-related database interactions and business logic.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */

class CourseModel extends Model
{

    /**
     * Constructor for AcademyModel.
     * Loads required libraries.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadLibary('locale');
        $this->loadModel('locale');
        $this->loadModel('invoice');
        $this->loadModel('user'); // Added to fix User_Minimal schema issues
    }

    /**
     * Retrieves a list of courses based on the provided query and optional filters.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the courses.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns a list of formatted courses or false if no courses are found or the query is invalid.
     * <AUTHOR>
     */
    public function getCourses($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if ($query['custom']) {
            $coursesCntResponse = $this->es->count('course', $query['custom']);

            if ($coursesCntResponse['status_code'] == 200) {

                if (ynSchemaType($filter, 'Course_Minimal')) {
                    $query['custom']['_source'] = [
                        'data.details.record_id',
                        'data.details.title',
                        'data.details.url'
                    ];
                } else {
                    $query['custom']['_source'] = [
                        'data.details.record_id',
                        'data.details.title',
                        'data.details.url',
                        'data.details.course_category',
                        'data.details.type',
                        'data.details.media_url',
                        'data.details.short_description',
                        'data.details.post_description',
                        'data.details.successful_students',
                        'data.details.active_enrollments',
                        'data.details.parent_taxonomy',
                        'data.details.academies',
                        'data.details.group_type',
                        'data.details.availability_summary',
                        'data.details.duration_weeks',
                        'data.details.highlight_1',
                        'data.details.highlight_2',
                        'data.details.highlight_3',
                        'data.details.highlight_4',
                        'data.details.highlight_5',
                        'data.details.highlight_6',
                        'data.details.highlight_7',
                        'data.details.highlight_8',
                        'data.details.highlight_9',
                        'data.details.highlight_10',
                        'data.details.cancellation_policy'
                    ];
                }

                if (isset($query['_source']) && is_array($query['_source']) && !empty($query['_source'])) {
                    $query['custom']['_source'] = $query['_source'];
                }

                $coursesDataResponse = $this->es->customQuery($query['custom'], 'course', $query['qryStr'] ?? null);
            } else {
                return false;
            }
        } else {
            return false;
        }

        if ($coursesDataResponse['status_code'] == 200) {
            $responseCount = $coursesCntResponse['body']['count'];
            $courses = $coursesDataResponse['body']['hits']['hits'];
            if ($responseCount > 0 && is_countable($courses) && count($courses) > 0) {
                foreach ($courses as $courseDataResponse) {
                    $course = $courseDataResponse['_source']['data']['details'];

                    $learningOutcomes = [];
                    for ($i = 1; $i <= 10; $i++) {
                        if (isset($course['highlight_' . $i]) && !empty($course['highlight_' . $i])) {
                            $learningOutcomes[] = $course['highlight_' . $i];
                        }
                    }

                    // Build the structured response
                    $responseData[] = array(
                        'id' =>  $course['record_id'] ?? '',  // Unique number of course
                        'title' => $course['title'] ?? '',  // Label of course
                        'url' => $course['url'] ?? '',  // URL of course (assuming format URI)
                        'cta' => [
                            'type' => 'BOOKADEMO',
                            'title' => 'Book a Demo Class',
                            'url' => isset($course['course_category'][0]['id']) ? get_field('book_demo_class_url', 'course_category_' . $course['course_category'][0]['id']) : '',
                        ],
                        'featured_media' => [
                            'video' => [
                                'id' => '', // Unique video ID on the streaming platform
                                'title' => '', // Title of the video on the streaming platform
                                'url' => isset($course['type']) && strtolower($course['type']) != "image" ? $course['media_url'] : "", // URL of the video on the streaming platform
                                'thumbnail' => [
                                    [
                                        "url" => "",
                                        "alt_text" => ""
                                    ]
                                ]
                            ],
                            'image' => [
                                [
                                    'url' => isset($course['type']) && strtolower($course['type']) == "image" ? $course['media_url'] : "",
                                    'alt_text' => ""
                                ]
                            ]
                        ],
                        'resources_summary' => $this->load->subData('course', 'getCourseResourcesSummary', ['cat_id' => isset($course['course_category'][0]['id']) ? $course['course_category'][0]['id'] : 0]),
                        'short_description' => $course['short_description'] ?? '',  // Short description
                        'long_description' => $course['post_description'] ?? '',  // Long description
                        'past_learners' => $course['successful_students'] ?? [],  // Learners who've completed the course
                        'active_learners' => $course['active_enrollments'] ?? [],  // Active enrollments
                        'category' => $this->load->subData('category', 'getCategory', ['slug' => $course['parent_taxonomy'] ?? ''], ['schema' => 'Category_Minimal', 'noResponse' => true]),  // Category object
                        'academy' => $this->load->subData('academy', 'getAcademy', $course['academies'][0] ?? 0, ['schema' => 'Academy_Minimal']),  // Academy object
                        'personalization' => array_map(function ($type) {
                            return [
                                'type' => $type === "1-1" ? "1TO1" : "GROUP", // Set '1TO1' for "1-1" and 'GROUP' for "1-Many"
                                'name' => $type === "1-1" ? "1-to-1" : "Group", // Readable name
                                'slug' => $type, // Use the value directly as the slug
                                'description' => $type === "1-1"
                                    ? "Classes will be taught in a 1-to-1 setting, consisting of only one learner."
                                    : "Classes will be taught in a group setting, consisting of multiple learners.",
                                'value' => true // Set true or false as needed for personalization
                            ];
                        }, $course['group_type'] ?? []),  // Personalization details
                        //'availability' => $this->load->subData('course', 'getCourseAvailability', ['id' => $course['record_id']]),
                        'duration' => array(
                            'label' => 'WEEKS',  // Duration unit: WEEKS or DAYS
                            'value' => $course['duration_weeks'] ?? 0  // Numeric value of duration
                        ),
                        'teaching_mode' => [
                            'online' => TRUE,
                            'in_person' => FALSE
                        ],  // Teaching mode: ONLINEONLY, INPERSONONLY, HYBRID
                        'enrollment_type' => $course['enrollment_type'] ?? '',  // Enrollment type 'fixed' or 'rolling'
                        'learning_outcomes' => isset($learningOutcomes) && !empty($learningOutcomes) ? $learningOutcomes : false,
                        'schedule' => $this->load->subData('course', 'getCourseSchedule', ['id' => $course['record_id'] ?? 0], ['noResponse' => ['id'=>0, 'activity'=>false]]),  // Schedule object
                        'economics' => $this->load->subData('course', 'getCourseEconomics', ['id' => $course['record_id'] ?? 0]),  // Economics/financial details
                        'instructors' => $this->load->subData('course', 'getCourseInstructors', ['id' => $course['record_id'] ?? 0]),  // Instructors
                        'cancellation_policy' => $course['cancellation_policy'] ?? '',  // Cancellation policy
                        "in_crm" => [
                            "platform" => "Zoho",
                            "id" => get_post_meta($course['record_id'] ?? '', 'id', true) ?? ''
                        ]
                    );
                } //foreach end courses


                if (isset($filter['schema'])) {
                    $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
                }
                return $this->schema->validate(['count' => $responseCount, 'data' => $responseData], ['count' => 'integer', 'data' => ['Refer#Course']], $filter);
            } //if end
        }
        return false;
    }

    /**
     * Retrieves detailed course data based on the provided course ID or custom query.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID.
     * @param array $filter Optional filters for schema validation and formatting.
     * @return array|false Returns formatted course data or false if the course is not found.
     * @throws \InvalidArgumentException If the input query is invalid.
     * <AUTHOR>
     */
    public function getCourse($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            // Fetch course data from Elasticsearch (or any other data source)
            if (ynSchemaType($filter, 'Course_Minimal')) {
                $qryStr['_source'] = implode(',', [
                    'data.details.record_id',
                    'data.details.title',
                    'data.details.url'
                ]);
            } else {
                $qryStr['_source'] = implode(',', [
                    'data.details.record_id',
                    'data.details.title',
                    'data.details.url',
                    'data.details.course_category',
                    'data.details.type',
                    'data.details.media_url',
                    'data.details.short_description',
                    'data.details.post_description',
                    'data.details.successful_students',
                    'data.details.active_enrollments',
                    'data.details.parent_taxonomy',
                    'data.details.academies',
                    'data.details.group_type',
                    'data.details.availability_summary',
                    'data.details.duration_weeks',
                    'data.details.highlight_1',
                    'data.details.highlight_2',
                    'data.details.highlight_3',
                    'data.details.highlight_4',
                    'data.details.highlight_5',
                    'data.details.highlight_6',
                    'data.details.highlight_7',
                    'data.details.highlight_8',
                    'data.details.highlight_9',
                    'data.details.highlight_10',
                    'data.details.cancellation_policy',
                    'data.details.enrollment_type'
                ]);
            }

            if (isset($query['_source']) && is_array($query['_source']) && !empty($query['_source'])) {
                $qryStr['_source'] = implode(',', $query['_source']);
            }

            $courseDataResponse = $this->es->read('course', 'course-' . $query['id'], $qryStr);
        } elseif (isset($query['custom'])) {
            $courseDataResponse = $this->es->customQuery($query['custom'], 'course', $query['qryStr'] ?? null);
        } else {
            return false;
        }

        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];

            $learningOutcomes = [];
            for ($i = 1; $i <= 10; $i++) {
                if (isset($course['highlight_' . $i]) && !empty($course['highlight_' . $i])) {
                    $learningOutcomes[] = $course['highlight_' . $i];
                }
            }


            // Build the structured response
            $courseResponse = array(
                'id' =>  $course['record_id'] ?? '',  // Unique number of course
                'title' => $course['title'] ?? '',  // Label of course
                'url' => $course['url'] ?? '',  // URL of course (assuming format URI)
                'cta' => [
                    'type' => 'BOOKADEMO',
                    'title' => 'Book a Demo Class',
                    'url' => isset($course['course_category'][0]['id']) ? get_field('book_demo_class_url', 'course_category_' . $course['course_category'][0]['id']) : '',
                ],
                'featured_media' => [
                    'video' => [
                        'id' => '', // Unique video ID on the streaming platform
                        'title' => '', // Title of the video on the streaming platform
                        'url' => isset($course['type']) && strtolower($course['type']) != "image" ? $course['media_url'] : "", // URL of the video on the streaming platform
                        'thumbnail' => [
                            [
                                "url" => "",
                                "alt_text" => ""
                            ]
                        ]
                    ],
                    'image' => [
                        [
                            'url' => isset($course['type']) && strtolower($course['type']) == "image" ? $course['media_url'] : "",
                            'alt_text' => ""
                        ]
                    ]
                ],
                'resources_summary' => $this->load->subData('course', 'getCourseResourcesSummary', ['cat_id' => isset($course['course_category'][0]['id']) ? $course['course_category'][0]['id'] : 0]),
                'short_description' => $course['short_description'] ?? '',  // Short description
                'long_description' => $course['post_description'] ?? '',  // Long description
                'past_learners' => $course['successful_students'] ?? [],  // Learners who've completed the course
                'active_learners' => $course['active_enrollments'] ?? [],  // Active enrollments
                'category' => $this->load->subData('category', 'getCategory', ['slug' => $course['parent_taxonomy'] ?? ''], ['schema' => 'Category_Minimal', 'noResponse' => true]),  // Category object
                'academy' => $this->load->subData('academy', 'getAcademy', $course['academies'][0] ?? 0, ['schema' => 'Academy_Minimal']),  // Academy object
                'personalization' => array_map(function ($type) {
                    return [
                        'type' => $type === "1-1" ? "1TO1" : "GROUP", // Set '1TO1' for "1-1" and 'GROUP' for "1-Many"
                        'name' => $type === "1-1" ? "1-to-1" : "Group", // Readable name
                        'slug' => $type, // Use the value directly as the slug
                        'description' => $type === "1-1"
                            ? "Classes will be taught in a 1-to-1 setting, consisting of only one learner."
                            : "Classes will be taught in a group setting, consisting of multiple learners.",
                        'value' => true // Set true or false as needed for personalization
                    ];
                }, $course['group_type'] ?? []),  // Personalization details
                //'availability' => $this->load->subData('course', 'getCourseAvailability', ['id' => $course['record_id']]),
                'duration' => array(
                    'label' => 'WEEKS',  // Duration unit: WEEKS or DAYS
                    'value' => $course['duration_weeks'] ?? 0  // Numeric value of duration
                ),
                'teaching_mode' => [
                    'online' => TRUE,
                    'in_person' => FALSE
                ],  // Teaching mode: ONLINEONLY, INPERSONONLY, HYBRID
                'enrollment_type' => $course['enrollment_type'] ?? '',  // Enrollment type 'fixed' or 'rolling'
                'learning_outcomes' => isset($learningOutcomes) && !empty($learningOutcomes) ? $learningOutcomes : false,
                'schedule' => $this->load->subData('course', 'getCourseSchedule', ['id' => $course['record_id'] ?? 0], ['noResponse' => ['id'=>0, 'activity'=>false]]),  // Schedule object
                'economics' => $this->load->subData('course', 'getCourseEconomics', ['id' => $course['record_id'] ?? 0]),  // Economics/financial details
                'instructors' => $this->load->subData('course', 'getCourseInstructors', ['id' => $course['record_id'] ?? 0]),  // Instructors
                'cancellation_policy' => $course['cancellation_policy'] ?? '',  // Cancellation policy
                // "in_crm" => [
                //     "platform" => "Zoho",
                //     "id" => get_post_meta($course['record_id'] ?? '', 'id', true) ?? ''
                // ]
            );
            // Validate the formatted response against the 'Course' schema
            return $this->schema->validate($courseResponse, 'Course', $filter);
        } else {
            return false;
        }
    }

    public function getCourseResourcesSummary($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['cat_id' => $query];
        if (isset($query['cat_id'])) {

            $query['custom'] = [
                "query" => [
                    "constant_score" => [
                        "filter" => [
                            "bool" => [
                                "must" => [
                                    [
                                        "terms" => [
                                            "data.details.resource_type" => ["video", "article", "ebook", "document"]
                                        ]
                                    ],
                                    [
                                        "terms" => [
                                            "data.details.taxonomy" => [$query['cat_id']]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                "fields" => [
                    "data.details.record_id",
                    "data.details.resource_type"
                ],
                "_source" => false
            ];

            $resoursesDataResponse = $this->es->customQuery($query['custom'], 'resourceevent', $query['qryStr'] ?? null);


            if ($resoursesDataResponse['status_code'] == 200) {

                $resources = $resoursesDataResponse['body']['hits']['hits'];
                $collections = [];
                $videoCount = 0;
                $articleCount = 0;
                $documentCount = 0;
                $ebookCount = 0;
                if (is_countable($resources) && count($resources) > 0) {
                    foreach ($resources as $resource) {
                        $resourceData = $resource['fields'];

                        $currentPostStatus = get_post_status($resourceData['data.details.record_id'][0]);
                        if ($currentPostStatus == "publish") {
                            if ($resourceData['data.details.resource_type'][0] == "video") {
                                $videoCount++;
                            }

                            if ($resourceData['data.details.resource_type'][0] == "article") {
                                $articleCount++;
                            }

                            if ($resourceData['data.details.resource_type'][0] == "document") {
                                $documentCount++;
                            }

                            if ($resourceData['data.details.resource_type'][0] == "ebook") {
                                $ebookCount++;
                            }
                        } //if end publish
                    } //foreach end resources


                    $totalRecords = $videoCount + $articleCount + $ebookCount + $documentCount;
                    if ($videoCount == 1) {
                        $videoName = "Video";
                    } else {
                        $videoName = "Videos";
                    }

                    if ($articleCount == 1) {
                        $articleName = "article";
                    } else {
                        $articleName = "Articles";
                    }

                    if ($ebookCount == 1) {
                        $ebookName = "eBook";
                    } else {
                        $ebookName = "eBooks";
                    }

                    if ($documentCount == 1) {
                        $documentName = "Document";
                    } else {
                        $documentName = "Documents";
                    }
                    $video = array("type" => strtoupper($videoName), "title" => $videoName, "count" => $videoCount);
                    $article = array("type" => strtoupper($articleName), "title" => $articleName, "count" => $articleCount);
                    $ebook = array("type" => strtoupper($ebookName), "title" => $ebookName, "count" => $ebookCount);
                    $document = array("type" => strtoupper($documentName), "title" => $documentName, "count" => $documentCount);
                    if ($videoCount > 0) {
                        $collections[] = $video;
                    }
                    if ($articleCount > 0) {
                        $collections[] = $article;
                    }
                    if ($ebookCount > 0) {
                        $collections[] = $ebook;
                    }
                    if ($documentCount > 0) {
                        $collections[] = $document;
                    }


                    $response = [
                        "total" => $totalRecords,
                        "resources" => ($totalRecords>0) ? $collections : false
                    ];

                    $schema = [
                        'total' => 'integer',
                        'resources' => [
                            [
                                'type' => 'string',
                                'title' => 'string',
                                'count' => 'integer'
                            ]
                        ]
                    ];


                    return $this->schema->validate($response, $schema, $filter);
                } //if end resources count
            } //if end resoursesDataResponse
        } //if end cat_id

        return false;
    }

    /**
     * Retrieves course availability information including group and 1-to-1 availability details.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch availability information.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns formatted course availability data including:
     *                     - summary: Overall availability summary
     *                     - group: Group availability details with personalization and batch information
     *                     - 1_to_1: 1-to-1 availability details with personalization and instructor information
     *                     Returns false if the course is not found or the query is invalid.
     * <AUTHOR>
     */
    /*public function getCourseAvailability($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $qryStr['_source'] = implode(',', [
                'data.details.availability_summary',
                'data.details.batch_details',
                'data.details.mapped_instructors'
            ]);
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id'], $qryStr);
        }

        $schema = [
            'summary' => 'string',  // Summary of the course availability
            'group' => [
                'personalization' => 'Refer#Personalization',
                'batch' => ['Refer#Batch_Minimal'],
            ],  // Group availability details (array of objects)
            '1_to_1' => [
               'personalization' => 'Refer#Personalization',
               'instructor' => ['Refer#User_Minimal'],
            ]  // 1-to-1 availability details (array of objects)
        ];

        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];

            $availability = [
                'summary' => $course['availability_summary'] ?? '',
                'group' => [
                    'personalization' => [
                        'type' => 'GROUP',  // Assuming GROUP type for example
                        'name' => 'Group',
                        'description' => 'Group-based availability with personalization',
                        'value' => true
                    ],
                    'batch' => array_map(function ($batch) {
                        return [
                            'id' => $batch['batch_id'],  // Use the unique batch ID
                            'title' => $batch['batch_title'],  // Title of the batch
                            'temporal_state' => $batch['temporal_state'] ?? ''  // Whether the batch is in the past or upcoming/ongoing enum ['PAST', 'UPCOMINGONGOING'] example xor-batch
                        ];
                    }, $course['batch_details'] ?? []),
                ],
                '1_to_1' => [
                    'personalization' => [
                        'type' => '1TO1',  // Assuming 1TO1 type for example
                        'name' => '1-to-1',
                        'description' => '1-to-1 availability with personalization',
                        'value' => true
                    ],
                    'instructor' => array_map(function ($instructor) {
                        return [
                                'id' => $instructor['id'],
                                'role' => ["instructor"],
                                'full_name' => $instructor['name'],
                                'image_url' => $instructor['image_url']
                            ];
                    }, $course['mapped_instructors'] ?? []),
                ],
            ];
            
            return $this->schema->validate($availability, $schema, $filter);
        } else {
            return false;
        }
    }*/

    /**
     * Retrieves the schedule of a course from Elasticsearch based on the provided query parameters.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to retrieve the course schedule.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns the formatted course schedule or false if no schedule is found or the query is invalid.
     * <AUTHOR>
     */
    public function getCourseSchedule($query, $filter = [])
    {
        // Ensure query is an array
        $query = is_array($query) ? $query : ['id' => $query];

        // Fetch course schedule data
        if (isset($query['id'])) {
            $qryStr['_source'] = implode(',', [
                'data.details.course_schedule',
                'data.details.course_schedule_id'
            ]);
            $scheduleDataResponse = $this->es->read('course', 'course-' . $query['id'], $qryStr);
        } else {
            return false;
        }

        // Check if the response from Elasticsearch is valid
        if ($scheduleDataResponse['status_code'] == 200) {

            // Extract the course schedule from the response
            $courseSchedule = $scheduleDataResponse['body']['_source']['data']['details']['course_schedule'] ?? null;
            $courseScheduleId = $scheduleDataResponse['body']['_source']['data']['details']['course_schedule_id'] ?? null;

            if (isset($courseSchedule) && !empty($courseSchedule)) {
                // Initialize an empty array for the formatted response
                $formattedResponse = [];
                $formattedResponse['id'] = $courseScheduleId;
                $formattedResponse['activity'] = isset($courseSchedule) && !empty($courseSchedule) ? array_map(function ($schedule) {
                    return [
                        'type' => $schedule['activity']['label'] ?? '', // Extract 'label' as 'type'
                        'slug' => $schedule['activity']['slug'] ?? '', // Extract 'slug'
                        'id' => $schedule['id'] ?? '', // Unique ID for the activity
                        'order' => $schedule['order'] ?? '', // The order of the activity
                        'title' => $schedule['title'] ?? '', // Title of the activity
                        'icon_url' => $schedule['icon_url'] ?? '', // URL to the activity icon
                        'short_description' => $schedule['excerpt'] ?? '', // Short description of the activity
                        'long_description' => $schedule['description'] ?? '', // Long description of the activity
                        'duration_in_minutes' => $schedule['duration'] ?? '', // Duration in minutes
                        'sub_cat' => isset($schedule['sub_cat']) && !empty($schedule['sub_cat']) ? array_map(function ($subCat) {
                            return [
                                'id' => $subCat['id'], // ID of the sub-category
                                'name' => $subCat['name'], // Name of the sub-category
                                'slug' => $subCat['slug'], // Slug of the sub-category
                                'sub_cat' => isset($subCat['sub_cat']) && !empty($subCat['sub_cat']) ? array_map(function ($nestedSubCat) {
                                    return [
                                        'id' => $nestedSubCat['id'], // ID of the nested sub-category
                                        'name' => $nestedSubCat['name'], // Name of the nested sub-category
                                        'slug' => $nestedSubCat['slug'] // Slug of the nested sub-category
                                    ];
                                }, $subCat['sub_cat']) : false // Nested sub-categories
                            ];
                        }, $schedule['sub_cat']) : false // Sub-categories for the activity
                    ];
                }, $courseSchedule) : false; // Initialize 'activity' as an array
                // Return the formatted response
                return $this->schema->validate($formattedResponse, 'Course_Schedule', $filter);
            }
        }
        return false;
    }

    /**
     * Retrieves course economics information including pricing, personalization options, and time investment details.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch economics information.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns formatted course economics data including:
     *                     - personalization: Course type (GROUP or 1-1) with description
     *                     - price: Pricing information with variants (list_price and selling_price)
     *                     - what_you_get: Course features (diagnostic test, live classes, assignments, etc.)
     *                     - expected_time_investment: Time investment details for various activities
     *                     Returns false if the course is not found or the query is invalid.
     * <AUTHOR>
     */
    public function getCourseEconomics($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $qryStr['_source'] = implode(',', [
                'data.details.one_to_one_price',
                'data.details.group_price',
                'data.details.prices',
                'data.details.group_type',
                'data.details.course_economics',
                'data.details.course_economics_one_to_one'
            ]);
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id'], $qryStr);
        }

        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];
            $courseEconomics = [];

            $currencyCode = $this->locale->activeCurrency('code');
            $oneToOnePrice = $course['one_to_one_price'] ?? 0;
            $groupPrice = $course['group_price'] ?? 0;

            if (isset($course['prices']) && !empty($course['prices'])) {
                foreach ($course['prices'] as $currency) {
                    if ($currency['code'] == $currencyCode) {

                        $oneToOnePriceOC = $currency['1_to_1_price'] ?? $currency['1_to_1_Price'];
                        $groupPriceOC = $currency['group_price'] ?? $currency['Group_Price'];

                        break;
                    }
                }
            }


            $course = $courseDataResponse['body']['_source']['data']['details'];
            //for group course economics
            if (in_array("1-Many", $course['group_type'])) {

                //for group price
                if (isset($groupPriceOC) && !empty($groupPriceOC)) {
                    $groupPrice = $groupPriceOC;
                } else {
                    $exchangeRate = $this->localeModel->getExchangeRate($currencyCode);
                    $groupPrice = $groupPrice / $exchangeRate;
                }

                $whatYouGetGroupClasses = [];
                $expectedTimeInvestmentGroupClasses = [];

                if (isset($course['course_economics']) && !empty($course['course_economics'])) {
                    //copy course economics one to many
                    $ceOnetoMany = $course['course_economics'][0];
                    // Prepare "what_you_get"

                    //what we get for group course economics
                    //diagnostic test
                    $diagnosticTest = isset($ceOnetoMany['is_diagnostic_test']) && ($ceOnetoMany['is_diagnostic_test'] === '1' || $ceOnetoMany['is_diagnostic_test'] === 1 || $ceOnetoMany['is_diagnostic_test'] === true);

                    //post test
                    $postTest = isset($ceOnetoMany['is_there_a_post_test']) && ($ceOnetoMany['is_there_a_post_test'] === '1' || $ceOnetoMany['is_there_a_post_test'] === 1 || $ceOnetoMany['is_there_a_post_test'] === true);

                    //assignments
                    $assignments = $ceOnetoMany['number_of_assignments_per_student_that_require_correction'] + $ceOnetoMany['number_of_assignments_per_student_that_not_require_correction'];

                    //mock tests
                    $mockTests = $ceOnetoMany['number_of_mock_exams'] ?? 0;

                    //group classes duration
                    $groupClassesDurationMins = (30 * $ceOnetoMany['group_class_30_min_duartion']) +
                        (45 * $ceOnetoMany['group_class_45_min_duartion']) +
                        (60 * $ceOnetoMany['group_class_60_min_duartion']);
                    //diagnostic test duration
                    $diagnosticTestDurationMins = $ceOnetoMany['duration_diagnostic_test'] ?? 0;
                    //post test duration
                    $postTestDurationMins = $ceOnetoMany['time_investment_per_student_on_post_test'] ?? 0;

                    //total time duration
                    $totalTimeMins = $groupClassesDurationMins + $diagnosticTestDurationMins + $postTestDurationMins;
                    $totalTimeHoursFloat = $totalTimeMins / 60;

                    // Format durations in hours and minutes
                    $groupClassesDuration = intdiv($groupClassesDurationMins, 60) . " hours " . ($groupClassesDurationMins % 60) . " mins";
                    $diagnosticTestDuration = intdiv($diagnosticTestDurationMins, 60) . " hours " . ($diagnosticTestDurationMins % 60) . " mins";
                    $postTestDuration = intdiv($postTestDurationMins, 60) . " hours " . ($postTestDurationMins % 60) . " mins";
                    $totalTimeDuration = intdiv($totalTimeMins, 60) . " hours " . ($totalTimeMins % 60) . " mins";

                    //diagnostic test
                    if ($diagnosticTest) {
                        $whatYouGetGroupClasses[] = [
                            "label" => "Diagnostic Test",
                            "subtitle" => "",
                            "slug" => "diagnostic_test",
                            "value" => $diagnosticTest,
                            "message" => "We will check your current level of skills so that your instructor can help you make improvements in your weak areas",
                            "items" => false
                        ];
                    }

                    //group classes
                    $numberGroupOfClasses = $ceOnetoMany['group_class_30_min_duartion'] + $ceOnetoMany['group_class_45_min_duartion'] + $ceOnetoMany['group_class_60_min_duartion'];
                    if ($groupClassesDurationMins > 0) {
                        $whatYouGetGroupClasses[] = [
                            "label" => "of live classes",
                            "subtitle" => "$numberGroupOfClasses group classes",
                            "slug" => "live_classes",
                            "value" => $groupClassesDuration,
                            "value_float" => $groupClassesDurationMins / 60,
                            "message" => "",
                            "items" => [
                                [
                                    "label" => "of group classes",
                                    "subtitle" => "$numberGroupOfClasses group classes",
                                    "slug" => "group_classes",
                                    "value" => $groupClassesDuration,
                                    "value_float" => $groupClassesDurationMins / 60,
                                    "message" => "",
                                ]
                            ],
                        ];
                    }

                    //assignments
                    if ($assignments > 0) {
                        $whatYouGetGroupClasses[] = [
                            "label" => "Assignments",
                            "subtitle" => "",
                            "slug" => "assignments",
                            "value" => $assignments,
                            "message" => "",
                            "items" => false
                        ];
                    }

                    //post test
                    if ($postTest) {
                        $whatYouGetGroupClasses[] = [
                            "label" => "Post test",
                            "subtitle" => "",
                            "slug" => "post_test",
                            "value" => $postTest,
                            "message" => "",
                            "items" => false
                        ];
                    }

                    //mock tests
                    if ($mockTests > 0) {
                        $whatYouGetGroupClasses[] = [
                            "label" => "Mock tests",
                            "subtitle" => "",
                            "slug" => "mock_tests",
                            "value" => $mockTests,
                            "message" => "After the test, your instructor will check your answers and give you feedback in a live class as to how to improve your performance",
                            "items" => false
                        ];
                    }

                    // Prepare "expected_time_investment"
                    $expectedTimeInvestmentGroupClasses[] = [
                        "title" => "$totalTimeDuration of time investment",
                        "title_float" => $totalTimeHoursFloat,
                        "subtitle" => " (less than 1 hour per week)",
                        "subtitle_float" => $totalTimeHoursFloat / 6, // Assuming 6 weeks
                        "message" => "You are expected to spend these many hours in completing all activities (classes, assignments, tests, etc.)",
                        "items" => [
                            [
                                "label" => "in diagnostic test",
                                "slug" => "diagnostic_test",
                                "hours" => $diagnosticTestDuration,
                                "hours_float" => $diagnosticTestDurationMins / 60,
                                "items" => false
                            ],
                            [
                                "label" => "in live classes",
                                "slug" => "live_classes",
                                "hours" => $groupClassesDuration,
                                "hours_float" => $groupClassesDurationMins / 60,
                                "items" => [
                                    [
                                        "label" => "in group classes",
                                        "slug" => "group_classes",
                                        "value" => $groupClassesDuration,
                                        "value_float" => $groupClassesDurationMins / 60,
                                        "message" => "",
                                    ]
                                ],
                            ],
                            [
                                "label" => "in post test",
                                "slug" => "post_test",
                                "hours" => $postTestDuration,
                                "hours_float" => $postTestDurationMins / 60,
                                "items" => false
                            ],
                        ],
                    ];
                }
                //prepare course economics one to many
                $courseEconomics[] = [
                    "id" => $ceOnetoMany['id'] ?? 0,
                    "personalization" => [
                        'type' => 'GROUP',
                        'name' => 'Group',
                        'description' => 'Classes will be taught in a group setting, consisting of multiple learners.',
                        'value' => true,    //Pending logic
                    ],
                    "price" => [
                        'variants' => [
                            [
                                'type' => [
                                    'based_on' => 'Personalization',
                                    'value' => '1-Many'
                                ],
                                'name' => 'Course',
                                'list_price' => $this->invoiceModel->getListPrice(['ccCode' => $currencyCode, 'basePrice' => $groupPrice]),
                                'hourly_list_price' => $this->invoiceModel->getListPrice(['ccCode' => $currencyCode, 'basePrice' => intval($totalTimeHoursFloat ?? 0) > 0 ? ($groupPrice / $totalTimeHoursFloat) : 0]),
                                'selling_price' => $this->invoiceModel->getSellPrice(['ccCode' => $currencyCode, 'basePrice' => $groupPrice]),
                            ]
                        ]
                    ],
                    "what_you_get" => !empty($whatYouGetGroupClasses) ? $whatYouGetGroupClasses : false,
                    "expected_time_investment" => !empty($expectedTimeInvestmentGroupClasses) ? $expectedTimeInvestmentGroupClasses : false,
                ];
            }
            //for one to one course economics
            if (in_array("1-1", $course['group_type'])) {

                //for one to one price
                if (isset($oneToOnePriceOC) && !empty($oneToOnePriceOC)) {
                    $oneToOnePrice = $oneToOnePriceOC;
                } else {
                    $exchangeRate = $this->localeModel->getExchangeRate($currencyCode);
                    $oneToOnePrice = $oneToOnePrice / $exchangeRate;
                }

                // Prepare "what_you_get"
                $whatYouGetOneToOne = [];
                $expectedTimeInvestmentOneToOne = [];
                if (isset($course['course_economics_one_to_one']) && !empty($course['course_economics_one_to_one'])) {
                    //copy course economics one to one
                    $ceOnetoOne = $course['course_economics_one_to_one'][0];


                    //what we get for one to one course economics
                    //diagnostic test
                    $diagnosticTest = isset($ceOnetoOne['is_diagnostic_test']) && ($ceOnetoOne['is_diagnostic_test'] === '1' || $ceOnetoOne['is_diagnostic_test'] === 1 || $ceOnetoOne['is_diagnostic_test'] === true);

                    //post test
                    $postTest = isset($ceOnetoOne['is_there_a_post_test']) && ($ceOnetoOne['is_there_a_post_test'] === '1' || $ceOnetoOne['is_there_a_post_test'] === 1 || $ceOnetoOne['is_there_a_post_test'] === true);

                    //assignments
                    $assignments = $ceOnetoOne['number_of_assignments_per_student_that_require_correction'] + $ceOnetoOne['number_of_assignments_per_student_that_not_require_correction'];

                    //mock tests
                    $mockTests = $ceOnetoOne['number_of_mock_exams'] ?? 0;

                    //one to one classes duration
                    $oneToOneClassesDurationMins =  (30 * $ceOnetoOne['one_to_one_class_30_min_duartion']) +
                        (45 * $ceOnetoOne['one_to_one_class_45_min_duartion']) +
                        (60 * $ceOnetoOne['one_to_one_class_60_min_duartion']) +
                        (75 * $ceOnetoOne['one_to_one_class_75_min_duartion']) +
                        (90 * $ceOnetoOne['one_to_one_class_90_min_duartion']) +
                        (120 * $ceOnetoOne['one_to_one_class_120_min_duartion']) +
                        (150 * $ceOnetoOne['one_to_one_class_150_min_duartion']) +
                        (180 * $ceOnetoOne['one_to_one_class_180_min_duartion']);

                    //total assignments
                    $totalAssignmentsOnetoOne = $ceOnetoOne['number_of_assignments_per_student_that_not_require_correction'] + $ceOnetoOne['number_of_assignments_per_student_that_require_correction'];
                    //assignments duration
                    $assignmentsDurationMins = $ceOnetoOne['time_investment_per_student_on_assignment_that_require_correction'] * $ceOnetoOne['number_of_assignments_per_student_that_require_correction'] +
                        $ceOnetoOne['time_investment_per_student_on_assignment_that_not_require_correction'];
                    //diagnostic test duration
                    $diagnosticTestDurationMins = $ceOnetoOne['duration_diagnostic_test'] ?? 0;
                    //post test duration
                    $postTestDurationMins = $ceOnetoOne['time_investment_per_student_on_post_test'] ?? 0;

                    $totalTimeMins = $oneToOneClassesDurationMins + $assignmentsDurationMins + $diagnosticTestDurationMins + $postTestDurationMins;
                    $totalTimeHoursFloat = $totalTimeMins / 60;

                    // Format durations in hours and minutes
                    $oneToOneClassesDuration = intdiv($oneToOneClassesDurationMins, 60) . " hours " . ($oneToOneClassesDurationMins % 60) . " mins";
                    $assignmentsDuration = intdiv($assignmentsDurationMins, 60) . " hours " . ($assignmentsDurationMins % 60) . " mins";
                    $diagnosticTestDuration = intdiv($diagnosticTestDurationMins, 60) . " hours " . ($diagnosticTestDurationMins % 60) . " mins";
                    $postTestDuration = intdiv($postTestDurationMins, 60) . " hours " . ($postTestDurationMins % 60) . " mins";
                    $totalTimeDuration = intdiv($totalTimeMins, 60) . " hours " . ($totalTimeMins % 60) . " mins";

                    //diagnostic test
                    if ($diagnosticTest) {
                        $whatYouGetOneToOne[] = [
                            "label" => "Diagnostic Test",
                            "subtitle" => "",
                            "slug" => "diagnostic_test",
                            "value" => $diagnosticTest,
                            "message" => "We will check your current level of skills so that your instructor can help you make improvements in your weak areas",
                            "items" => false
                        ];
                    }

                    $numberOnetoOneOfClasses =  $ceOnetoOne['one_to_one_class_30_min_duartion'] +
                        $ceOnetoOne['one_to_one_class_45_min_duartion'] +
                        $ceOnetoOne['one_to_one_class_60_min_duartion'] +
                        $ceOnetoOne['one_to_one_class_75_min_duartion'] +
                        $ceOnetoOne['one_to_one_class_90_min_duartion'] +
                        $ceOnetoOne['one_to_one_class_120_min_duartion'] +
                        $ceOnetoOne['one_to_one_class_150_min_duartion'] +
                        $ceOnetoOne['one_to_one_class_180_min_duartion'];

                    if ($oneToOneClassesDurationMins > 0) {
                        $whatYouGetOneToOne[] = [
                            "label" => "of live classes",
                            "subtitle" => "$numberOnetoOneOfClasses live classes",
                            "slug" => "live_classes",
                            "value" => $oneToOneClassesDuration,
                            "value_float" => $oneToOneClassesDurationMins / 60,
                            "message" => "",
                            "items" => [
                                [
                                    "label" => "of 1-to-1 classes",
                                    "subtitle" => "$numberOnetoOneOfClasses 1-to-1 classes",
                                    "slug" => "one_to_one_classes",
                                    "value" => $oneToOneClassesDuration,
                                    "value_float" => $oneToOneClassesDurationMins / 60,
                                    "message" => "",
                                ],
                            ],
                        ];
                    }

                    //assignments
                    if ($assignments > 0) {
                        $whatYouGetOneToOne[] = [
                            "label" => "Assignments",
                            "subtitle" => "",
                            "slug" => "assignments",
                            "value" => $totalAssignmentsOnetoOne,
                            "message" => "",
                            "items" => false
                        ];
                    }

                    //post test
                    if ($postTest) {
                        $whatYouGetOneToOne[] = [
                            "label" => "Post test",
                            "subtitle" => "",
                            "slug" => "post_test",
                            "value" => $postTest,
                            "message" => "",
                            "items" => false
                        ];
                    }

                    //mock tests
                    if ($mockTests > 0) {
                        $whatYouGetOneToOne[] = [
                            "label" => "Mock tests",
                            "subtitle" => "",
                            "slug" => "mock_tests",
                            "value" => $mockTests,
                            "message" => "After the test, your instructor will check your answers and give you feedback in a live class as to how to improve your performance",
                            "items" => false
                        ];
                    }

                    // Prepare "expected_time_investment"
                    $expectedTimeInvestmentOneToOne[] = [
                        "title" => "$totalTimeDuration of time investment",
                        "title_float" => $totalTimeHoursFloat,
                        "subtitle" => " (less than 1 hour per week)",
                        "subtitle_float" => $totalTimeHoursFloat / 6, // Assuming 6 weeks
                        "message" => "You are expected to spend these many hours in completing all activities (classes, assignments, tests, etc.)",
                        "items" => [
                            [
                                "label" => "in diagnostic test",
                                "slug" => "diagnostic_test",
                                "hours" => $diagnosticTestDuration,
                                "hours_float" => $diagnosticTestDurationMins / 60,
                                "items" => false
                            ],
                            [
                                "label" => "in live classes",
                                "slug" => "live_classes",
                                "hours" => $oneToOneClassesDuration,
                                "hours_float" => $oneToOneClassesDurationMins / 60,
                                "items" => [
                                    [
                                        "label" => "in 1-to-1 classes",
                                        "slug" => "one_to_one_classes",
                                        "value" => $oneToOneClassesDuration,
                                        "value_float" => $oneToOneClassesDurationMins / 60,
                                        "message" => "",
                                    ],
                                ],
                            ],
                            [
                                "label" => "in assignments",
                                "slug" => "assignments",
                                "hours" => $assignmentsDuration,
                                "hours_float" => $assignmentsDurationMins / 60,
                                "items" => false
                            ],
                            [
                                "label" => "in post test",
                                "slug" => "post_test",
                                "hours" => $postTestDuration,
                                "hours_float" => $postTestDurationMins / 60,
                                "items" => false
                            ],
                        ],
                    ];
                }
                //prepare course economics one to one
                $courseEconomics[] = [
                    "id" => $ceOnetoOne['id'] ?? 0,
                    "personalization" => [
                        'type' => '1-1',
                        'name' => '1-1',
                        'description' => 'Classes will be taught in a 1-to-1 setting, consisting of only one learner.',
                        'value' => true,    //Pending logic
                    ],
                    "price" => [
                        'variants' => [
                            [
                                'type' => [
                                    'based_on' => 'Personalization',
                                    'value' => '1-1'
                                ],
                                'name' => 'Course',
                                'list_price' => $this->invoiceModel->getListPrice(['ccCode' => $currencyCode, 'basePrice' => $oneToOnePrice]),
                                'hourly_list_price' => $this->invoiceModel->getListPrice(['ccCode' => $currencyCode, 'basePrice' => intval($totalTimeHoursFloat ?? 0) > 0 ? ($oneToOnePrice / $totalTimeHoursFloat) : 0]),
                                'selling_price' => $this->invoiceModel->getSellPrice(['ccCode' => $currencyCode, 'basePrice' => $oneToOnePrice]),
                            ]
                        ]
                    ],
                    "what_you_get" => !empty($whatYouGetOneToOne) ? $whatYouGetOneToOne : false,
                    "expected_time_investment" => !empty($expectedTimeInvestmentOneToOne) ? $expectedTimeInvestmentOneToOne : false,
                ];
            }

            return $this->schema->validate($courseEconomics, ['Refer#Course_Economics'], $filter);
        }

        return false;
    }

    /**
     * Retrieves the instructors for a specific course.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the instructors.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array Returns an array of instructors for the course.
     * <AUTHOR>
     */
    public function getCourseInstructors($query, $filter = [])
    {
        // Ensure query is an array
        $query = is_array($query) ? $query : ['id' => $query];

        // Fetch course schedule data
        if (isset($query['id'])) {
            $qryStr['_source'] = implode(',', [
                'data.details.mapped_instructors',
            ]);
            $instructorsDataResponse = $this->es->read('course', 'course-' . $query['id'], $qryStr);
        } else {
            return false;
        }

        // Check if the response from Elasticsearch is valid
        if ($instructorsDataResponse['status_code'] == 200) {
            $mappedInstructors = $instructorsDataResponse['body']['_source']['data']['details']['mapped_instructors'] ?? [];

            $formattedResponse = [];

            if (!empty($mappedInstructors)) {
                foreach ($mappedInstructors as $instructor) {
                    $instructorId = $instructor['id'] ?? 0;
                    if ($instructorId > 0) {
                        $userData = $this->userModel->getUser($instructorId, ['schema' => 'User_Minimal']);
                        if ($userData) {
                            $formattedResponse[] = $userData;
                        }
                    }
                }

                return $this->schema->validate($formattedResponse, ['Refer#User_Minimal'], $filter);
            }
        }

        return false;
    }
    /**
     * Retrieves the academy ID associated with a specific course.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the academy.
     * @param array $filter Additional filters for response formatting (optional).
     * @return mixed Returns the academy ID or false if the course is not found or the query is invalid.
     * <AUTHOR>
     */
    public function getAcademyId($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if (isset($query['id'])) {
            // Fetch batch data from your data source (like a database or Elasticsearch)
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id']);
        } else {
            return false;
        }

        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];
            return $course['academies'][0];
        }
    }



    /**
     * Retrieves a list of courses taught by a specific instructor.
     *
     * @since 1.0.0
     * @access public
     * @param array|string $query Query parameters or course ID to fetch the courses.
     * @param int $instructorId The ID of the instructor whose courses are to be fetched.
     * @return array|false Returns an array of courses associated with the instructor or false if no courses are found.
     * <AUTHOR>
     */
    public function getInstructorCourses($query, $instructorId)
    {
        if (!is_array($query)) {
            $query = ['id' => $query];
        }

        if (isset($query['id'])) {
            $courseSearchResponse = $this->es->read('course', 'course-' . $query['id']);
            if ($courseSearchResponse['status_code'] == 200) {
                $res = $courseSearchResponse['body'];
                $res = [$res];
            } else {
                return false;
            }
        } elseif (!empty($query['custom'])) {
            $courseSearchResponse = $this->es->customQuery($query['custom'], 'course');
            if ($courseSearchResponse['status_code'] == 200) {
                $res = $courseSearchResponse['body']['hits']['hits'];
            } else {
                return false;
            }
        } else {
            return false;
        }

        $courseResponse = [];
        $row = ['items' => []];

        if ($res) {
            foreach ($res as $course) {
                $source = isset($course['_source']) ? $course['_source'] : $course;

                if (isset($source['data'])) {
                    $data = $source['data'];
                    $courseResponse[] = $data;

                    if (
                        isset($data['details']) &&
                        isset($data['details']['batch_details']) &&
                        is_array($data['details']['batch_details'])
                    ) {
                        foreach ($data['details']['batch_details'] as $batch) {
                            if (
                                isset($batch['instructor_id'], $batch['active_batch']) &&
                                $batch['instructor_id'] == $instructorId
                            ) {
                                $batch_name = isset($batch['batch_name']) ? $batch['batch_name'] : '';
                                $batch_id   = isset($batch['batch_id']) ? $batch['batch_id'] : 0;

                                // Ensure uniqueness in the items array.
                                $exists = array_filter($row['items'], function ($item) use ($batch_id) {
                                    return $item['id'] == $batch_id;
                                });
                                if (empty($exists)) {
                                    $row['items'][] = [
                                        'id'     => $batch_id,
                                        'label'  => $batch_name,
                                        'filter' => 'batch',
                                    ];
                                }
                            }
                        }
                    }
                }
            }

            return $row;
        }

        return false;
    }

    /**
     * Generates filters for a course based on the course ID and instructor ID.
     *
     * @since 1.0.0
     * @access public
     * @param int $courseId The ID of the course to generate filters for.
     * @param int $instructorId The ID of the instructor.
     * @return array Returns a filter structure for courses based on the instructor.
     * <AUTHOR>
     */
    public function generateCourseFiltersInstructor($courseId, $instructorId)
    {
        $row = [
            'filter' => 'course',
            'title' => 'course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($courseId)) {
            $courseData = $this->getCourse($courseId, ['schema' => 'Course_Minimal']);

            if ($courseData) {
                $courseName = $courseData['title'] ?? '';
                $selCourseId = $courseData['id'] ?? 0;

                $selectedCourse = [
                    'id' => $selCourseId,
                    'label' => $courseName,
                    'filter' => 'course',
                ];
                $row['selected'] = $selectedCourse['id'];
            }
        }

        $customQuery = [
            'custom' => [
                "query" => [
                    "nested" => [
                        "path"  => "data.details",
                        "query" => [
                            "constant_score" => [
                                "filter" => [
                                    "bool" => [
                                        "must" => [
                                            "terms" => [
                                                "data.details.mapped_instructor_ids" => [$instructorId]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $courses = $this->getCourses($customQuery, ['key' => 'data']);

        if (!empty($courses)) {
            foreach ($courses as $course) {
                $courseId = $course['id'] ?? 0;

                if (!empty($courseId)) {
                    $courseTitle = $course['title'] ?? '';
                    $row['items'][] = [
                        'id' => $courseId,
                        'label' => $courseTitle,
                        'filter' => 'course',
                    ];
                }
            }
        }
        return $row;
    }

    /**
     * Generates filters for a course based on the course ID and learner ID.
     *
     * @since 1.0.0
     * @access public
     * @param int $courseId The ID of the course to generate filters for.
     * @param int $learnerId The ID of the learner.
     * @return array Returns a filter structure for courses based on the learner.
     * <AUTHOR>
     */
    public function generateCourseFiltersLearner($courseId, $learnerId)
    {
        $row = [
            'filter' => 'course',
            'title' => 'course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'dropdown',
            'selected' => 0,
            'items' => []
        ];

        if (isset($courseId)) {
            $courseData = $this->getCourse($courseId, ['schema' => 'Course_Minimal']);

            if ($courseData) {
                $courseName = $courseData['title'] ?? '';
                $selCourseId = $courseData['id'] ?? 0;

                $selectedCourse = [
                    'id' => $selCourseId,
                    'label' => $courseName,
                    'filter' => 'course',
                ];
                $row['selected'] = $selectedCourse['id'];
            }
        }

        $customQuery = [
            'query' => [
                'bool' => [
                    'must' => [
                        [
                            'term' => [
                                'data.details.user_id' => $learnerId
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $courseRecords = $this->es->customQuery($customQuery, 'batchenrollmentevent', []);

        if ($courseRecords['status_code'] == 200) {
            $courses = $courseRecords['body']['hits']['hits'];
        } else {
            return false;
        }
        $existingBatchIds = [];
        if (!empty($courses)) {
            foreach ($courses as $record) {
                $details = $record['_source']['data']['details'] ?? [];
                $course_id = $details['course_id'] ?? 0;
                $course_name = $details['course_name'] ?? '';

                if ($course_id && !in_array($course_id, $existingBatchIds)) {
                    $row['items'][] = [
                        'id' => $course_id,
                        'label' => $course_name,
                        'filter' => 'course',
                    ];
                    $existingBatchIds[] = $course_id;
                }
            }
        }
        return $row;
    }

    /**
     * Generates course filters based on the user's role and associated entity.
     *
     * @since 1.0.0
     * @access public
     * @param int $userId The ID of the user.
     * @param int $orgId The ID of the organization (for org-admins).
     * @param int $instructorId The ID of the instructor (for filtering instructor-specific courses).
     * @param int $learnerId The ID of the learner (for filtering learner-specific courses).
     * @param int $counselorId The ID of the counselor.
     * @param int $courseId The selected course ID (if any).
     * @return array Returns an array containing course filter data.
     * <AUTHOR>
     */
    public function generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId)
    {
        return [
            'filter' => 'course_id',
            'title' => 'Course',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Course',
            'ui_control_type' => 'query_suggestion',
            'selected' => $courseId,
            'current' => '',
            'loading' => false,
            'success' => false,
            'items' => []  // Ensure `items` remains empty
        ];
    }
    /**
     * Retrieves course records associated with one or more academies.
     *
     * This method accepts an academy ID (or an array of IDs) and constructs an Elasticsearch query
     * to find courses that belong to the specified academies. It then extracts the course record IDs
     * from the Elasticsearch response.
     *
     * @since 1.0.0
     * @access public
     * @param mixed $query Academy ID, or an array/associative array with an 'id' key containing the academy IDs.
     * @return array|bool Returns an array of course record IDs on success, an empty array if no courses are found,
     * or false if the query fails or input is invalid.
     * <AUTHOR>
     */
    public function getCoursesByAcademyId($query)
    {
        $query = is_array($query) ? $query : ['id' => $query];

        if (is_array($query)) {
            if (isset($query['id'])) {
                $academyIds = is_array($query['id']) ? $query['id'] : [$query['id']];
            } else {
                $academyIds = $query;
            }
        } else {
            $academyIds = [$query];
        }

        if (!empty($academyIds)) {
            $academyQuery = [
                'query' => [
                    'nested' => [
                        'path' => 'data.details',
                        'query' => [
                            'terms' => [
                                'data.details.academies' => $academyIds
                            ]
                        ]
                    ]
                ]
            ];

            $courseDataResponse = $this->es->customQuery($academyQuery, 'course', ['size' => 100]);
            if ($courseDataResponse['status_code'] != 200) {
                return false;
            }
        } else {
            return false;
        }
        $courseData = [];

        if (isset($courseDataResponse['status_code']) && $courseDataResponse['status_code'] == 200) {
            if (
                isset($courseDataResponse['body']['hits']) &&
                isset($courseDataResponse['body']['hits']['hits']) &&
                is_array($courseDataResponse['body']['hits']['hits'])
            ) {
                $hits = $courseDataResponse['body']['hits']['hits'];

                foreach ($hits as $hit) {
                    if (
                        isset($hit['_source']['data']['details']['record_id']) &&
                        !empty($hit['_source']['data']['details']['record_id'])
                    ) {
                        $courseData[] = $hit['_source']['data']['details']['record_id'];
                    }
                }
                return $courseData;
            } else {
                return [];
            }
        } else {
            return false;
        }
    }

    /**
     * Retrieves a list of courses based on search criteria.
     *
     * @since 1.0.0
     * @access public
     * @param array $query Query parameters containing custom ElasticSearch query.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns a list of formatted courses matching the search criteria or false if no courses are found.
     * <AUTHOR>
     */
    public function getCourseSearch($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];

        if ($query['custom']) {
            $coursesCntResponse = $this->es->count('course', $query['custom']);

            if ($coursesCntResponse['status_code'] == 200) {

                $query['custom']['_source'] = [
                    'data.details.record_id',
                    'data.details.title',
                    'data.details.url',
                    'data.details.type',
                    'data.details.mapped_instructor_count',
                    'data.details.short_description',
                    'data.details.post_description',
                    'data.details.successful_students',
                    'data.details.active_enrollments',
                    'data.details.parent_taxonomy',
                    'data.details.academies',
                    'data.details.group_type',
                    'data.details.duration_weeks'
                ];

                if (isset($query['_source']) && is_array($query['_source']) && !empty($query['_source'])) {
                    $query['custom']['_source'] = $query['_source'];
                }

                $coursesDataResponse = $this->es->customQuery($query['custom'], 'course', $query['qryStr'] ?? null);
            } else {
                return false;
            }
        } else {
            return false;
        }

        if ($coursesDataResponse['status_code'] == 200) {
            $responseCount = $coursesCntResponse['body']['count'];
            $courses = $coursesDataResponse['body']['hits']['hits'];
            if ($responseCount > 0 && is_countable($courses) && count($courses) > 0) {
                foreach ($courses as $courseDataResponse) {
                    $course = $courseDataResponse['_source']['data']['details'];

                    // Build the structured response
                    $responseData[] = [
                        'id' => $course['record_id'] ?? 0,
                        'title' => $course['data.details.title'][0] ?? $course['title'] ?? '',
                        'category' => $this->load->subData('category', 'getCategory', ['slug' => $course['parent_taxonomy'] ?? ''], ['schema' => 'Category_Minimal', 'noResponse' => true]),  // Category object
                        'url' => $course['url'] ?? '',
                        'cta' => [
                            'type' => 'BOOKADEMO',
                            'title' => 'Book a Demo Class',
                            'url' => isset($course['course_category'][0]['id']) ? get_field('book_demo_class_url', 'course_category_' . $course['course_category'][0]['id']) : '',
                        ],
                        'short_description' => $course['short_description'] ?? $course['post_description'] ?? '',
                        'price' => $this->load->subData('course', 'getCourseSearchPrice', ['id' => $course['record_id'] ?? 0]),
                        'academy' => $this->load->subData('academy', 'getAcademy', $course['academies'][0] ?? 0, ['schema' => 'Academy_Basic']),
                        'instructors' => [
                            'mapped_instructor_count' => $course['mapped_instructor_count'] ?? 0,
                            'mapped_instructors' => $this->load->subData('course', 'getCourseInstructors', ['id' => $course['record_id'] ?? 0])
                        ],
                        'duration' => [
                            'label' => 'WEEKS',
                            'value' => $course['duration_weeks'] ?? 0
                        ],
                        'active_learners' => $course['active_enrollments'] ?? [],
                        'past_learners' => $course['successful_students'] ?? [],
                        'personalizations' => array_map(function ($type) {
                            return [
                                'type' => $type === "1-1" ? "1TO1" : "GROUP", // Set '1TO1' for "1-1" and 'GROUP' for "1-Many"
                                'name' => $type === "1-1" ? "1-to-1" : "Group", // Readable name
                                'slug' => $type, // Use the value directly as the slug
                                'description' => $type === "1-1"
                                    ? "Classes will be taught in a 1-to-1 setting, consisting of only one learner."
                                    : "Classes will be taught in a group setting, consisting of multiple learners.",
                                'value' => true // Set true or false as needed for personalization
                            ];
                        }, $course['group_type'] ?? []),  // Personalization details
                        'teaching_mode' => [
                            'online' => TRUE,
                            'in_person' => FALSE
                        ],  // Teaching mode: ONLINEONLY, INPERSONONLY, HYBRID
                    ];
                }

                if (isset($filter['schema'])) {
                    $filter['schema'] = ['count' => 'integer', 'data' => [$filter['schema']]];
                }

                return $this->schema->validate(
                    ['count' => $responseCount, 'data' => $responseData],
                    ['count' => 'integer', 'data' => ['Refer#Course_Search']],
                    $filter
                );
            }
        }

        return false;
    }

    /**
     * Retrieves price information for courses based on search criteria.
     *
     * @since 1.0.0
     * @access public
     * @param array $query Query parameters containing custom ElasticSearch query.
     * @param array $filter Additional filters for response formatting (optional).
     * @return array|false Returns price information for courses matching the search criteria or false if no courses are found.
     * <AUTHOR>
     */
    public function getCourseSearchPrice($query, $filter = [])
    {
        is_array($query) ? $query : $query = ['id' => $query];
        if (isset($query['id'])) {
            $qryStr['_source'] = implode(',', [
                'data.details.one_to_one_price',
                'data.details.group_price',
                'data.details.prices',
                'data.details.group_type',
                'data.details.course_economics',
                'data.details.course_economics_one_to_one'
            ]);
            $courseDataResponse = $this->es->read('course', 'course-' . $query['id'], $qryStr);
        }

        if ($courseDataResponse['status_code'] == 200) {
            $course = $courseDataResponse['body']['_source']['data']['details'];
            $courseEconomics = [];

            $currencyCode = $this->locale->activeCurrency('code');
            $oneToOnePrice = $course['one_to_one_price'] ?? 0;
            $groupPrice = $course['group_price'] ?? 0;

            if (isset($course['prices']) && !empty($course['prices'])) {
                foreach ($course['prices'] as $currency) {
                    if ($currency['code'] == $currencyCode) {

                        $oneToOnePriceOC = $currency['1_to_1_price'] ?? $currency['1_to_1_Price'];
                        $groupPriceOC = $currency['group_price'] ?? $currency['Group_Price'];

                        break;
                    }
                }
            }


            $course = $courseDataResponse['body']['_source']['data']['details'];

            //for group course economics
            if (in_array("1-Many", $course['group_type'])) {
                //for group price
                if (isset($groupPriceOC) && !empty($groupPriceOC)) {
                    $groupPrice = $groupPriceOC;
                } else {
                    $exchangeRate = $this->localeModel->getExchangeRate($currencyCode);
                    $groupPrice = $groupPrice / $exchangeRate;
                }

                if (isset($course['course_economics']) && !empty($course['course_economics'])) {
                    $ceOnetoMany = $course['course_economics'][0];

                    //group classes duration
                    $groupClassesDurationMins = (30 * $ceOnetoMany['group_class_30_min_duartion']) +
                        (45 * $ceOnetoMany['group_class_45_min_duartion']) +
                        (60 * $ceOnetoMany['group_class_60_min_duartion']);
                    //diagnostic test duration
                    $diagnosticTestDurationMins = $ceOnetoMany['duration_diagnostic_test'] ?? 0;
                    //post test duration
                    $postTestDurationMins = $ceOnetoMany['time_investment_per_student_on_post_test'] ?? 0;

                    //total time duration
                    $totalTimeMins = $groupClassesDurationMins + $diagnosticTestDurationMins + $postTestDurationMins;
                    $totalTimeHoursFloat = $totalTimeMins / 60;
                }

                //prepare course economics one to many
                $courseEconomics[] = [
                    'list_price' => $this->invoiceModel->getSellPrice(['ccCode' => $currencyCode, 'basePrice' => $groupPrice]),
                    'price_per_hour' => $this->invoiceModel->getListPrice(['ccCode' => $currencyCode, 'basePrice' => intval($totalTimeHoursFloat ?? 0) > 0 ? ($groupPrice / $totalTimeHoursFloat) : 0]),
                    'live_hours' => intval($totalTimeHoursFloat)
                ];
            }

            //for one to one course economics
            if (in_array("1-1", $course['group_type'])) {
                //for one to one price
                if (isset($oneToOnePriceOC) && !empty($oneToOnePriceOC)) {
                    $oneToOnePrice = $oneToOnePriceOC;
                } else {
                    $exchangeRate = $this->localeModel->getExchangeRate($currencyCode);
                    $oneToOnePrice = $oneToOnePrice / $exchangeRate;
                }

                if (isset($course['course_economics_one_to_one']) && !empty($course['course_economics_one_to_one'])) {
                    $ceOnetoOne = $course['course_economics_one_to_one'][0];
                    //one to one classes duration
                    $oneToOneClassesDurationMins =  (30 * $ceOnetoOne['one_to_one_class_30_min_duartion']) +
                        (45 * $ceOnetoOne['one_to_one_class_45_min_duartion']) +
                        (60 * $ceOnetoOne['one_to_one_class_60_min_duartion']) +
                        (75 * $ceOnetoOne['one_to_one_class_75_min_duartion']) +
                        (90 * $ceOnetoOne['one_to_one_class_90_min_duartion']) +
                        (120 * $ceOnetoOne['one_to_one_class_120_min_duartion']) +
                        (150 * $ceOnetoOne['one_to_one_class_150_min_duartion']) +
                        (180 * $ceOnetoOne['one_to_one_class_180_min_duartion']);

                    //assignments duration
                    $assignmentsDurationMins = $ceOnetoOne['time_investment_per_student_on_assignment_that_require_correction'] * $ceOnetoOne['number_of_assignments_per_student_that_require_correction'] +
                        $ceOnetoOne['time_investment_per_student_on_assignment_that_not_require_correction'];
                    //diagnostic test duration
                    $diagnosticTestDurationMins = $ceOnetoOne['duration_diagnostic_test'] ?? 0;
                    //post test duration
                    $postTestDurationMins = $ceOnetoOne['time_investment_per_student_on_post_test'] ?? 0;

                    $totalTimeMins = $oneToOneClassesDurationMins + $assignmentsDurationMins + $diagnosticTestDurationMins + $postTestDurationMins;
                    $totalTimeHoursFloat = $totalTimeMins / 60;
                }

                //prepare course economics one to one
                $courseEconomics[] = [
                    'list_price' => $this->invoiceModel->getSellPrice(['ccCode' => $currencyCode, 'basePrice' => $oneToOnePrice]),
                    'price_per_hour' => $this->invoiceModel->getListPrice(['ccCode' => $currencyCode, 'basePrice' => intval($totalTimeHoursFloat ?? 0) > 0 ? ($oneToOnePrice / $totalTimeHoursFloat) : 0]),
                    'live_hours' => intval($totalTimeHoursFloat)
                ];
            }
            
            
            // Build the price response
            $responseData = !empty($courseEconomics) ? [array_reduce($courseEconomics, function ($carry, $item) {
                if ($carry === null || $item['list_price']['exclusive_tax'] < $carry['list_price']['exclusive_tax']) {
                    return $item;
                }
                return $carry;
            })] : false;

            return $this->schema->validate(
                $responseData[0],
                [
                    'list_price' => 'Refer#Price',
                    'price_per_hour' => 'Refer#Price_List',
                    'live_hours' => 'integer'
                ],
                $filter
            );
        }

        return false;
    }

    /**
     * Generates course suggestions based on search text.
     *
     * @since 1.0.0
     * @access public
     * @param string $searchTerm The search text to filter courses
     * @param array $highlightFields The fields to highlight in the search results
     * @param int $limit The maximum number of suggestions to return
     * @return array Returns an array containing suggestions and match counts
     */

    public function getCourseSuggestions($query, $searchText)
    {
        $defaultImage = "https://dev.yunolearning.com/wp-content/themes/yunolearning-child/inc/img/videoDefault.jpg";

        $response = $this->es->customQuery($query, 'course');
        $hits = $response['body']['hits']['hits'] ?? [];

        $courses = [];
        $categories = [];
        $subcategories = [];

        foreach ($hits as $hit) {
            $source = $hit['_source']['data']['details'] ?? [];
            $highlight = $hit['highlight'] ?? [];

            $recordId = $source['record_id'] ?? null;
            $defaultImage = "https://dev.yunolearning.com/wp-content/themes/yunolearning-child/inc/img/videoDefault.jpg";

            $imageUrl = $source['media_url'] ?: ($source['featuredImage']['url'] ?? $defaultImage);
            
            $courses[] = [
                'id' => $recordId,
                'title' => strip_tags($highlight['data.details.title'][0] ?? $source['title']),
                'featured_media' => [
                    'image' => $imageUrl ? [['url' => $imageUrl, 'alt_text' => $source['title']]] : [],
                ],
                'duration' => [
                    'label' => 'weeks',
                    'value' => (int) ($source['duration_weeks'] ?? 0)
                ],
                'url' => $source['url'] ?? ''
            ];

            foreach ($source['course_category'] ?? [] as $cat) {
                $catId = $cat['id'] ?? null;
                if (!$catId || empty($cat['name']) || empty($cat['slug'])) {
                    continue;
                }

                $catName = strip_tags($highlight['data.details.course_category.name'][0] ?? $cat['name']);

                $categories[$catId] = [
                    'id' => $catId,
                    'name' => $catName,
                    'slug' => $cat['slug'],
                    'course_count' => ($categories[$catId]['course_count'] ?? 0) + 1
                ];

                foreach ($cat['sub_category'] ?? [] as $subCat) {
                    $subCatId = $subCat['id'] ?? null;
                    if (!$subCatId || empty($subCat['name']) || empty($subCat['slug'])) {
                        continue;
                    }

                    $subCatName = strip_tags($highlight['data.details.course_category.sub_category.name'][0] ?? $subCat['name']);

                    $subcategories[$subCatId] = [
                        'id' => $subCatId,
                        'name' => $subCatName,
                        'slug' => $subCat['slug'],
                        'parent' => [
                            'id' => $catId,
                            'name' => $catName,
                            'slug' => $cat['slug']
                        ],
                        '1up_level_category_id' => $catId,
                        'course_count' => ($subcategories[$subCatId]['course_count'] ?? 0) + 1
                    ];

                    foreach ($subCat['sub_category'] ?? [] as $subSubCat) {
                        $subSubCatId = $subSubCat['id'] ?? null;
                        if (!$subSubCatId || empty($subSubCat['name']) || empty($subSubCat['slug'])) {
                            continue;
                        }

                        $subSubCatName = strip_tags($highlight['data.details.course_category.sub_category.sub_category.name'][0] ?? $subSubCat['name']);

                        $subcategories[$subSubCatId] = [
                            'id' => $subSubCatId,
                            'name' => $subSubCatName,
                            'slug' => $subSubCat['slug'],
                            'parent' => [
                                'id' => $catId,
                                'name' => $catName,
                                'slug' => $cat['slug']
                            ],
                            '1up_level_category_id' => $subCatId,
                            'course_count' => ($subcategories[$subSubCatId]['course_count'] ?? 0) + 1
                        ];
                    }
                }
            }
        }

        return [
            'course' => array_values($courses),
            'category' => array_values($categories),
            'sub_category' => array_values($subcategories)
        ];
    }
}
