@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.fontAwesomeIcon {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@font-face {
  font-family: "Material Icons Outlined";
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/material-Icons.woff2?6qrc5l") format("woff2");
}

@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/material-Icons-filled.woff2?8qrc5l")
    format("woff2");
}

@font-face {
  font-family: "FontAwesome";
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/fontawesome-webfont.woff2?v=4.7.0")
    format("woff2");
}

%headline5 {
  @include setFont($headline5, 28px, 500, 0);
}
#app {
  .secondary {
    color: $secondary;
  }
  .capitalize {
    text-transform: capitalize;
  }
  .yunoListPlaces {
    .mainHeader {
      margin-bottom: $gapSmall * 2;

      .block {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0;
    }

      h1 {
        @extend %headline5;
        margin: 14px 0 0 20px;

        @media (min-width: 768px) {
          margin: 0;
        }
      }
    }
    .listPlaces {
      .b-table {
        margin-bottom: $gapLargest;
        .table-wrapper {
          .table {
            thead {
              tr {
                th {
                  color: $onSurface;
                  @include setFont($subtitle2, 18px, 500, 0);
                  letter-spacing: 0.1px;
                }
              }
            }
            tbody {
              tr {
                td {
                  // border: none;
                  color: $onSurfaceVariant;
                  @include setFont($subtitle2, 18px, 400, 0);
                  padding: 5px 8px;
                  letter-spacing: 0.25px;

                  .rowLabel {
                    display: flex;
                    margin-top: $gapSmaller;
                  }

                  .arrowIcon {
                    cursor: pointer;
                    margin-top: 2px;
                  }
                }
              }
            }
            tr {
              &.detail {
                background-color: $bg;
                box-shadow: none;
                td {
                  padding: 0;
                }

                .detail-container {
                  border: 1px solid $grey;
                  padding: 25px 48px;
                  .box {
                    box-shadow: none;
                    background: none;
                    padding: 0;

                    &:not(:last-child) {
                      border-bottom: 1px solid $grey;
                      padding-bottom: 19px;
                      margin-bottom: 19px;
                    }

                    .wrapper {
                      display: flex;
                      flex-wrap: wrap;

                      li {
                        position: relative;
                        white-space: nowrap;

                        &::before {
                          content: "|";
                          color: $grey;
                          margin: 0 8px;
                        }
                        &:first-child::before,
                        &:last-child::before {
                          content: "";
                          margin: 0;
                        }
                      }
                    }

                    .facilities {
                      width: 100%;
                    }
                  }
                }
              }
            }
          }
        }

        .pagination-link {
          color: $primaryCopyColor;

          &:hover {
            text-decoration: none;
          }

          &.is-current {
            background-color: $primary;
            border-color: $primary;
            color: $secondaryCopyColor;
          }

          &.pagination-previous,
          &.pagination-next {
            .icon {
              .mdi-chevron-left,
              .mdi-chevron-right {
                &:after {
                  content: "\f060";
                  @extend .fontAwesomeIcon;
                }
              }

              .mdi-chevron-right {
                &:after {
                  content: "\f061";
                }
              }
            }
          }
        }
      }
    }
  }
}
