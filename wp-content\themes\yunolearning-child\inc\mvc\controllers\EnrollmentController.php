<?php

namespace V4;

/**
 * Class EnrollmentController
 * Handles enrollment-related operations.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class EnrollmentController extends Controller
{
    /**
     * Constructor for EnrollmentController.
     * Loads required libraries and models.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadLibary('dateTime', 'dt');
        $this->loadLibary('validate');
        $this->loadModel('category');
        $this->loadModel('user');
        $this->loadModel('batch');
        $this->loadModel('enrollment');
        $this->loadModel('payment');
        $this->loadModel('referral');
        $this->loadModel('learner');
        $this->loadModel('course');
        $this->loadModel('org');
        $this->loadModel('academy');
        $this->loadModel('instructor');
        $this->loadModel('counselor');
    }

    /**
     * Adds a new enrollment.
     *
     * @param object $request HTTP request object.
     * @return object JSON response.
     * @throws Exception If an error occurs during enrollment.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function addEnrollment($request)
    {
        try {
            $request = json_decode($request->get_body(), true);
            ynLog("addEnrollment - Request: " . json_encode($request), 'addEnrollment');
            // Validation required fields update with V4 validate with nested array
            $validationChecks = [
                'user_id' => 'numeric',
                'course_id' => 'numeric',
                'batch_id' => 'numeric',
                'payment_gateway' => 'string',
                'payment_mode' => 'string',
                'amount' => 'numeric',
                'currency_code' => '/^(INR|USD|AED)$/',
                'enrolled_by' => 'numeric'
            ];

            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($request, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            $userId = $request['user_id'];
            $courseId = $request['course_id'];
            $batchId = $request['batch_id'];

            $referralCodeStatus = $request['referral_code_status'];
            // Validate referral code
            ynLog("addEnrollment - referralCodeStatus: " . $referralCodeStatus, 'addEnrollment');
            $referrerId = $this->referralModel->validateReferralCode($referralCodeStatus, $userId);
            ynLog("addEnrollment - referrerId: " . $referrerId, 'addEnrollment');

            if ($referralCodeStatus === "applied" && $referrerId === false) {
                // Referral code is applied but invalid or disabled
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Invalid or disabled referral code']);
            }

            ynLog("addEnrollment - isUserAlreadyEnrolled", 'addEnrollment');
            $isAlreadyEnrolled = $this->enrollmentModel->isUserAlreadyEnrolled($userId, $courseId, $batchId);
            ynLog("addEnrollment - isAlreadyEnrolled: " . $isAlreadyEnrolled, 'addEnrollment');

            // Check if user is already enrolled using the model
            // Question @chandresh : make query to check if user is already enrolled in this course and batch optimize query ?
            if ($isAlreadyEnrolled) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'User is already enrolled in this course and batch.']);
            }

            // Fetch all batches in which the user is already enrolled for this course (from CPT and Elasticsearch)
            // Question @chandresh : What if user want to enroll in same course but different batch again ? test 
            ynLog("addEnrollment - isUserEnrolledEarlier", 'addEnrollment');
            $isEarlierEnrolled = $this->enrollmentModel->isUserEnrolledEarlier($userId, $courseId);
            ynLog("addEnrollment - isEarlierEnrolled: " . $isEarlierEnrolled, 'addEnrollment');

            // If the user is enrolled in ANY batch for this course, block enrollment
            if ($isEarlierEnrolled) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'You are already enrolled in this course and cannot enroll again.']);
            }

            // Fetch batch details
            // Question @chandresh : Why can we use getBatch here ? ask disscuded later change with query
            ynLog("addEnrollment - getBatch", 'addEnrollment');
            $batchData = $this->batchModel->getBatch($batchId); // models
            ynLog("addEnrollment - batchData: " . $batchData, 'addEnrollment');
            if ($batchData === false) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Batch not found']);
            } else {
                // Validate batch duration
                if (empty($batchData['class_time']['duration']) || !is_numeric($batchData['class_time']['duration'])) {
                    return $this->response->error('POST_INSERT_FAIL', ['message' => 'Invalid duration']);
                }
            }

            // check batch locked
            ynLog("addEnrollment - check batch locked", 'addEnrollment');
            if ($batchData !== false && $batchData['is_locked'] == true) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'This batch is locked, please select another batch']);
            }

            ynLog("addEnrollment - getBatchSQL", 'addEnrollment');
            $wpBatches = $this->batchModel->getBatchSQL($batchId); //update when migrate to elasticsearch course>batch_details index
            ynLog("addEnrollment - wpBatches: " . $wpBatches, 'addEnrollment');
            $errorMessage = ($wpBatches['enrollmentType'] === 'fixed') ? 'Batch has already started, please select another batch and try again' : 'Enrollment is not allowed in this batch. Please update the end date of the batch and try again';

            // Validate batch enrollment tobe migrate to elasticsearch course>batch_details index
            $isValid = $this->batchModel->verifyBatchEnrollment($wpBatches, $batchId);
            ynLog("addEnrollment - isValid: " . $isValid, 'addEnrollment');
            if (!$isValid) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => $errorMessage]);
            }


            // Insert Enrollment into Elasticsearch
            ynLog("addEnrollment - addEnrollment", 'addEnrollment');
            $enrollmentId = $this->enrollmentModel->addEnrollment($request, $wpBatches, $referrerId);
            ynLog("addEnrollment - enrollmentId: " . $enrollmentId, 'addEnrollment');
            if ($enrollmentId === false) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Enrollment Failed. (E) Try Again!']);
            }


            ynLog("addEnrollment - retrivePayment", 'addEnrollment');
            $existingEsPayment = $this->paymentModel->retrivePayment($userId, $courseId, $batchId);
            ynLog("addEnrollment - existingEsPayment: " . $existingEsPayment, 'addEnrollment');

            // Step 1: Check for Existing Document
            if (!empty($existingEsPayment)) {
                return $this->response->error('DUPLICATE_PAYMENT', ['message' => 'Payment already exists.']);
            } else {
                ynLog("addEnrollment - addPayment", 'addEnrollment');
                $paymentId = $this->paymentModel->addPayment($request, $wpBatches, $enrollmentId);
                ynLog("addEnrollment - paymentId: " . $paymentId, 'addEnrollment');

                if ($paymentId === false) {
                    return $this->response->error('POST_INSERT_FAIL', ['message' => 'Payment Failed (E). Try Again !']);
                }
            }

            $paymentGateway = $request['payment_gateway'];
            // Payment gateway ( e.g., RazorPay )
            $paymentMode = $request['payment_mode'];
            $enrolledBy = $request['enrolled_by'];
            //yuno admin // org admin 

            if ($paymentId > 0) {

                if ($paymentMode === 'offline' && $paymentGateway === 'offline') {
                    ynLog("addEnrollment - handleOfflinePayment", 'addEnrollment');
                    $offlinePaymentResult = $this->paymentModel->handleOfflinePayment($request, $wpBatches, $paymentId, $enrollmentId);
                    ynLog("addEnrollment - offlinePaymentResult: " . $offlinePaymentResult, 'addEnrollment');
                    if ($offlinePaymentResult) {
                        ynLog("addEnrollment - event_details", 'addEnrollment');
                        $event_details = [
                            'user_id'   => $enrolledBy,
                            'action'    => 'enrollment',
                            'target'    => 'enrollment',
                            'target_id' => $enrollmentId,
                            'timestamp' => $this->dt->currentSystemDT()
                        ];

                        trigger_custom_event($event_details);
                        ynLog("addEnrollment - postEnrollmentActions", 'addEnrollment');
                        $enrollmentActionsResponse = $this->postEnrollmentActions($request, $wpBatches, $enrollmentId);

                        return $this->response->success('POST_INSERT', ['enrollment_id' => $enrollmentId], ['message' => 'Offline payment processed successfully.']);
                    } else {

                        return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to process offline payment.']);
                    }
                } elseif ($paymentMode === 'online' && $paymentGateway === 'generate_link') {
                    ynLog("addEnrollment - handleOnlinePayment", 'addEnrollment');
                    $paymentLink = $this->paymentModel->handleOnlinePayment($request, $wpBatches, $enrollmentId, $paymentId);
                    ynLog("addEnrollment - paymentLink: " . $paymentLink, 'addEnrollment');
                    if ($paymentLink) {
                        return $this->response->success('POST_INSERT', ['enrollment_id' => $enrollmentId, 'payment_link' => $paymentLink], ['message' => 'Payment link generated successfully.']);
                    } else {
                        return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to generate payment link.']);
                    }
                } elseif ($paymentMode === 'online' && $paymentGateway === 'direct_payment') {
                    ynLog("addEnrollment - handleDirectOnlinePayment", 'addEnrollment');
                    $razorpayCheckoutPayload  = $this->paymentModel->handleDirectOnlinePayment($request, $wpBatches, $enrollmentId, $paymentId);
                    ynLog("addEnrollment - razorpayCheckoutPayload: " . $razorpayCheckoutPayload, 'addEnrollment');
                    if ($razorpayCheckoutPayload) {
                        return $this->response->success('POST_INSERT', $razorpayCheckoutPayload, ['message' => 'Payment Payload generated successfully.']);
                    } else {
                        return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to generate payload for Checkout.']);
                    }
                }
            } else {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Enrollment failed.']);
            }
        } catch (Exception $e) {
            return $this->response->error('POST_INSERT_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Performs post-enrollment actions like notifications and linking.
     *
     * @param object $request HTTP request object.
     * @param array  $wpBatches Batch details.
     * @param int    $enrollmentTblId Enrollment table ID.
     * @return bool Success or failure.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function postEnrollmentActions($request, $wpBatches, $enrollmentId)
    {
        // Update Org Details
        $orgLink = $this->learnerModel->linkOrgDetails($request);

        ynLog("addEnrollment - inside postEnrollmentActions -- orgLink  === Offline: " . json_encode($orgLink), 'addEnrollment_V4');

        // Link Learner to Instructor
        $isLearnerAdded = $this->learnerModel->addLearnerToInstructor($request, $wpBatches);
        ynLog("addEnrollment - Debugging isLearnerAdded: postEnrollmentActions " . var_export($isLearnerAdded, true) . $isLearnerAdded, 'addEnrollment_V4');

        // if (!$isLearnerAdded) {
        //     return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to add learner to instructor or relationship already exists.']);
        // }

        // Trigger external API calls (e.g., Segment)
        $segmentCall = $this->learnerModel->triggerSegmentCall($enrollmentId);
        ynLog("addEnrollment - Debugging segmentCall: postEnrollmentActions " . var_export($segmentCall, true) . $segmentCall, 'addEnrollment_V4');
        return true;
    }

    /**
     * Retrieves enrollments based on filters.
     *
     * @param object $request HTTP request object.
     * @return object JSON response.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function getEnrollments($request)
    {
        // Extract query string parameters
        $userId = isset($_GET['user_id']) ? (int) $_GET['user_id'] : 0;
        $instructorId = isset($_GET['instructor_id']) ? (int) $_GET['instructor_id'] : 0;
        $status = isset($_GET['status']) ? $_GET['status'] : 'all';
        $counselorId = isset($_GET['counselor_id']) ? (int) $_GET['counselor_id'] : 0;
        $learnerId = isset($_GET['learner_id']) ? (int) $_GET['learner_id'] : 0;
        $courseId = isset($_GET['course_id']) ? (int) $_GET['course_id'] : 0;
        $organizationId = isset($_GET['org_id']) ? (int) $_GET['org_id'] : 0;
        $academyId = isset($_GET['academy_id']) ? (int) $_GET['academy_id'] : 0;
        $referrals = isset($_GET['referrals']) ? $_GET['referrals'] : 'all';
        $paymentStatus = isset($_GET['payment_status']) ? $_GET['payment_status'] : 'all';
        $limit = isset($_GET['limit']) ? (int) $_GET['limit'] : 10; // Default limit
        $offset = isset($_GET['offset']) ? (int) $_GET['offset'] : 0; // Default offset

        $viewType = isset($request['viewType']) ? $request['viewType'] : 'all';

        // Validation rules
        $validationChecks = [
            'user_id' => '/^(?!0$)\d+$/[LABEL:User Id]', // Must be numeric
            //'instructor_id' => '/^(?!0$)\d+$/[REQ:NOT][LABEL:Instrcutor Id]',            // Must be numeric
            // 'org_id' => 'regex_match[/^(?!0$)\d+$/][REQ:NOT][LABEL:Org Id]',                   // Must be numeric
            // 'referrals' => 'string',   // Must match one of these values
            // 'learner_id' => 'regex_match[/^(?!0$)\d+$/][REQ:NOT][LABEL:Learner Id]',               // Must be numeric
            // 'status' => 'string', // Must match one of these values
            // 'counselor_id' => 'numeric',             // Must be numeric
            // 'payment_status' => 'string', // Must match one of these values
            // 'course_id' => 'regex_match[/^(?!0$)\d+$/][LABEL:Course Id]',                // Must be numeric
            'limit' => '/^(?!0$)\d+$/[LABEL:Limit]'                   // Must be numeric
        ];

        // Perform validation on each field
        foreach ($validationChecks as $key => $type) {
            $result = $this->validate->validateRequired($request, $key, $type);
            if (is_wp_error($result)) {
                return $result; // Return the error immediately if validation fails
            }
        }

        if ($userId <= 0) {
            return $this->response->error('USER_ID_FAIL', ['message' => 'Invalid User']);
        }

        // Fetch User Role
        $this->loadModel('user');
        $role = $this->userModel->getUserRole($userId);

        // Role-Based Validation
        if ($this->userModel->checkRole($role, $this->userModel->yn_Org_Admin) !== false) {
            if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $referrals !== 'all') {
                return $this->response->error('ACCESS_DENIED', ['message' => 'You are not allowed to use these filters']);
            }
        } else if ($this->userModel->checkRole($role, $this->userModel->yn_Counselor) !== false) {
            if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $learnerId !== 0) {
                return $this->response->error('ACCESS_DENIED', ['message' => 'Counselors can only filter by course, learners, referrals, and enrollment status']);
            }
        } else if ($this->userModel->checkRole($role, $this->userModel->yn_Instructor) !== false) {
            if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $organizationId !== 0 || $referrals !== 'all') {
                return $this->response->error('ACCESS_DENIED', ['message' => 'Instructors can only filter by course, learners, and enrollment status']);
            }
        } else if ($this->userModel->checkRole($role, $this->userModel->yn_Yuno_Admin) !== false) {
            // Yuno Admin - full access, no restrictions
        } else {
            return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
        }

        // Validation checks
        if ($userId <= 0) {
            return $this->response->error('USER_ID_FAIL', ['message' => 'Invalid User']);
        }

        $mustConditions = [];
        $mustNotConditions = [];

        if (isset($instructorId) && $instructorId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.instructor_id" => $instructorId
                ]
            ];
        }

        if (isset($academyId) && $academyId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.academy_id" => $academyId
                ]
            ];
        }

        if ($this->userModel->checkRole($role, $this->userModel->yn_Org_Admin) !== false) {
            // get active org
            $activeOrganizationId = get_user_meta($userId, 'active_org', true);
            if ($activeOrganizationId && $activeOrganizationId != 0) {
                $mustConditions[] = [
                    "match_phrase" => [
                        "data.details.org_admin.id" => $activeOrganizationId
                    ]
                ];
            }
        }

        if ($this->userModel->checkRole($role, $this->userModel->yn_Org_Admin) === false && $organizationId === 0) {
            // If not Org Admin and no org_id provided, return error
            if (isset($organizationId) && $organizationId != 0) {
                $mustConditions[] = [
                    "match_phrase" => [
                        "data.details.org_admin.id" => $organizationId
                    ]
                ];
            }
        }

        if (isset($courseId) && $courseId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.course_id" => $courseId
                ]
            ];
        }

        if (isset($learnerId) && $learnerId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.user_id" => $learnerId
                ]
            ];
        }

        if ($referrals !== "all") {
            if ($instructorId === 0) {
                $mustNotConditions[] = [
                    "match" => [
                        "data.details.refer.referrer.id" => 0
                    ]
                ];
            } else {
                $referrerId = $instructorId;
                $mustConditions[] = [
                    "match" => [
                        "data.details.refer.referrer.id" => $referrerId
                    ]
                ];
            }
        }

        if (isset($counselorId) && $counselorId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.counselor_id" => $counselorId
                ]
            ];
        }

        if ($status !== "all") {
            $enrollmentStatus = ($status === "active") ? "ACTIVE" : "INACTIVE";
            $mustConditions[] = [
                "match" => [
                    "data.details.enrollment_status" => $enrollmentStatus
                ]
            ];
        }

        if ($paymentStatus !== "all") {
            $paymentCondition = "Paid in full";
            if ($paymentStatus === "full") {
                $mustConditions[] = [
                    "match_phrase" => [
                        "data.details.payment_status" => "*" . $paymentCondition . "*"
                    ]
                ];
            } else {
                $mustNotConditions[] = [
                    "match_phrase" => [
                        "data.details.payment_status" => "*" . $paymentCondition . "*"
                    ]
                ];
            }
        }

        $mustNotConditions[] = [
            "match" => [
                "data.details.course_name" => false
            ]
        ];

        $elasticQuery = [
            "_source" => [
                "inner_hits",
                "type",
                "data.details.instructor_id",
                "data.details.counselor_id",
                "data.details.payment_status",
                "data.details.user_id",
                "data.details.enrolled_end",
                "data.details.enrolled_days_left",
                "data.details.crm_contact_id",
                "data.details.payment_method",
                "data.details.payment",
                "data.details.payment_description",
                "data.details.amount_pending",
                "data.details.amount_received",
                "data.details.total_amount",
                "data.details.course_id",
                "data.details.enrollment_status",
                "data.details.course_name",
                "data.details.batch_id",
                "data.details.user_id",
                "data.details.event_date",
                "data.details.enrolled_classes_counter",
                "data.details.batch_end_date",
                "data.details.enrollment_type",
                "data.details.personalization",
                "data.details.enrollment_id",
                "data.details.attended_classes_counter",
                "data.details.enrolled_classes_collection",
                "data.details.attended_classes_collection",
                "data.details.enrolled_on",
                "data.details.event_date",
                "data.details.org_admin.id",
                "data.details.org_admin.image",
                "data.details.org_admin.name",
                "data.details.refer.referrer.id",
                "data.details.refer.discount_amount.referrer_discount_percentage",
                "data.details.selling_price",
                "data.details.listing_price",
                "data.details.enrolled_by",
                "data.details.full_part",
                "data.details.is_unenroll",
                "data.details.is_change_batch",
            ],
            "query" => [
                "bool" => [
                    "must" => $mustConditions,
                    "must_not" => $mustNotConditions
                ]
            ],
            "sort" => [
                [
                    "data.details.event_date" => "desc"
                ]
            ]
        ];

        $query['custom'] = $elasticQuery;
        $query['qryStr'] = [
            "from" => $offset,
            "size" => $limit
        ];
        //$query['cache'] = true;

        // Step 1: Decode the request body
        ynLog("get_enrollment - Request received with query " . json_encode($elasticQuery), 'get_enrollment');

        $enrollmentDataResponse = $this->enrollmentModel->getEnrollments($query);
        // Debugging the value and type of $enrollmentDataResponse

        if (!empty($enrollmentDataResponse)) {

            if ($viewType === "list") {
                // Return success response with place details
                return $this->response->success('GET_SUCCESS', $enrollmentDataResponse, ['message' => 'Enrollments found']);
            } elseif ($viewType === "grid") {

                if ($role === 'org admin' && isset($request['academy_id'])) {
                    // Override or modify $columns as needed
                    $columns = $this->getColumnsForOrgAdminWithAcademy(); // replace with your actual method
                }

                $columns = $this->getColumnsBasedOnRole($userRole ?? null);
                // Check for org admin and presence of academy_id in the request (even if it's 0)

                $enrollmentData = [
                    "rows" =>  $enrollmentDataResponse['data'],
                    "columns" => $columns
                ];
                return $this->response->success('GET_SUCCESS', ['data' => $enrollmentData, 'count' => $enrollmentDataResponse['count']], ['message' => 'Enrollments found']);
            }
        }

        return $this->response->error('GET_FAIL', ['message' => "As your learners enroll in courses, you'll see their enrollments here"]);
    }

    /**
     * Retrieves the column configuration for enrollments based on user role.
     *
     * @since 1.0.0
     * @access private
     * @param string|null $role The role of the user.
     * @return array An array of column configurations for the specified role.
     * <AUTHOR>
     */
    private function getColumnsBasedOnRole($role)
    {
        $columns = [];

        if (isset($role) && $role == 'Instructor') {
            $columns = [
                [
                    "field" => "id",
                    "label" => "ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "status",
                    "label" => "Status",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "start_date.time, start_date.timezone",
                    "label" => "Enrolled On",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "end_date.time, end_date.timezone",
                    "label" => "Enrolment Ends",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "days_left",
                    "label" => "Days Left",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "learner.id, learner.role, learner.full_name, learner.image_url",
                    "label" => "Learner",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "enrolled_by.id, enrolled_by.role, enrolled_by.full_name, enrolled_by.image_url",
                    "label" => "Enrolled By",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "counselor.id, counselor.role, counselor.full_name, counselor.image_url",
                    "label" => "Counselor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "batch.id, batch.title, batch.temporal_state",
                    "label" => "Batch Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "instructor.id, instructor.role, instructor.full_name, instructor.image_url",
                    "label" => "Instructor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "course.id, course.title, course.url",
                    "label" => "Course",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "academy.id, academy.name",
                    "label" => "Academy",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "of_org.id, of_org.name",
                    "label" => "Organization",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.exclusive_tax, list_price.inclusive_tax",
                    "label" => "List Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.exclusive_tax, selling_price.inclusive_tax",
                    "label" => "Selling Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "full_part.type, full_part.total_installments",
                    "label" => "Enrolment Method",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "referral.referrer.id, referral.referrer.role, referral.referrer.full_name, referral.referrer.image_url",
                    "label" => "Referrer",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "payment.id, payment.date.time, payment.date.timezone, payment.mode, payment.status, payment.full_part, payment.amount, payment.amount_due",
                    "label" => "Payment Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "classes.attended, classes.total",
                    "label" => "Classes Attended",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "attendance.percentage, attendance.status",
                    "label" => "Attendance",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "actions",
                    "label" => "Actions",
                    "tooltip" => "",
                    "sortable" => true
                ]
            ];
        } elseif (isset($role) && $role == 'org-admin') {
            $columns = [
                [
                    "field" => "id",
                    "label" => "ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "status",
                    "label" => "Status",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "start_date.time, start_date.timezone",
                    "label" => "Enrolled On",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "end_date.time, end_date.timezone",
                    "label" => "Enrolment Ends",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "days_left",
                    "label" => "Days Left",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "learner.id, learner.role, learner.full_name, learner.image_url",
                    "label" => "Learner",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "enrolled_by.id, enrolled_by.role, enrolled_by.full_name, enrolled_by.image_url",
                    "label" => "Enrolled By",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "counselor.id, counselor.role, counselor.full_name, counselor.image_url",
                    "label" => "Counselor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "batch.id, batch.title, batch.temporal_state",
                    "label" => "Batch Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "instructor.id, instructor.role, instructor.full_name, instructor.image_url",
                    "label" => "Instructor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "course.id, course.title, course.url",
                    "label" => "Course",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "academy.id, academy.name",
                    "label" => "Academy",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "of_org.id, of_org.name",
                    "label" => "Organization",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.inclusive_tax",
                    "label" => "List Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.inclusive_tax",
                    "label" => "Selling Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "full_part.type, full_part.total_installments",
                    "label" => "Enrolment Method",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "in_crm.id, in_crm.platform",
                    "label" => "CRM Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "referral.referrer.id, referral.referrer.role, referral.referrer.full_name, referral.referrer.image_url",
                    "label" => "Referrer",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "payment.id, payment.date.time, payment.date.timezone, payment.mode, payment.status, payment.full_part, payment.amount, payment.amount_due",
                    "label" => "Payment Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "classes.attended, classes.total",
                    "label" => "Classes Attended",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "attendance.percentage, attendance.status",
                    "label" => "Attendance",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "actions",
                    "label" => "Actions",
                    "tooltip" => "",
                    "sortable" => true
                ]
            ];
        } else {
            $columns = [
                [
                    "field" => "id",
                    "label" => "ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "status",
                    "label" => "Status",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "start_date.time, start_date.timezone",
                    "label" => "Enrolled On",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "end_date.time, end_date.timezone",
                    "label" => "Enrolment Ends",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "days_left",
                    "label" => "Days Left",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "learner.id, learner.role, learner.full_name, learner.image_url",
                    "label" => "Learner",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "enrolled_by.id, enrolled_by.role, enrolled_by.full_name, enrolled_by.image_url",
                    "label" => "Enrolled By",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "counselor.id, counselor.role, counselor.full_name, counselor.image_url",
                    "label" => "Counselor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "batch.id, batch.title, batch.temporal_state",
                    "label" => "Batch Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "instructor.id, instructor.role, instructor.full_name, instructor.image_url",
                    "label" => "Instructor",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "course.id, course.title, course.url",
                    "label" => "Course",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "academy.id, academy.name",
                    "label" => "Academy",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "of_org.id, of_org.name",
                    "label" => "Organization",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "list_price.inclusive_tax",
                    "label" => "List Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "selling_price.inclusive_tax",
                    "label" => "Selling Price",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "full_part.type, full_part.total_installments",
                    "label" => "Enrolment Method",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "referral.referrer.id, referral.referrer.role, referral.referrer.full_name, referral.referrer.image_url",
                    "label" => "Referrer",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "payment.id, payment.date.time, payment.date.timezone, payment.mode, payment.status, payment.full_part, payment.amount, payment.amount_due",
                    "label" => "Payment Details",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "classes.attended, classes.total",
                    "label" => "Classes Attended",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "attendance.percentage, attendance.status",
                    "label" => "Attendance",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "actions",
                    "label" => "Actions",
                    "tooltip" => "",
                    "sortable" => true
                ]
            ];
        }

        return $columns;
    }

    private function getColumnsForOrgAdminWithAcademy($role)
    {
        $columns = [
            [
                "field" => "id",
                "label" => "ID",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "status",
                "label" => "Status",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "start_date.time, start_date.timezone",
                "label" => "Enrolled On",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "end_date.time, end_date.timezone",
                "label" => "Enrolment Ends",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "days_left",
                "label" => "Days Left",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "learner.id, learner.role, learner.full_name, learner.image_url",
                "label" => "Learner",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "enrolled_by.id, enrolled_by.role, enrolled_by.full_name, enrolled_by.image_url",
                "label" => "Enrolled By",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_id",
                "label"    => "DFO ID",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_name",
                "label"    => "DFO Learner Name",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_email",
                "label"    => "DFO Learner Email",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_phone",
                "label"    => "DFO Learner Phone",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_created_at",
                "label"    => "DFO Learner Signedup Date",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_cohort",
                "label"    => "DFO Learner Cohort",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_programs",
                "label"    => "DFO Learner Programs",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_business_unit",
                "label"    => "DFO Learner Business unit",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field"    => "details_from_org_parents",
                "label"    => "DFO Learner Parents",
                "tooltip" => "",
                "sortable" => true
            ]
        ];

        return $columns;
    }

    /**
     * Handles Razorpay webhooks.
     *
     * @return void
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function handleRazorpayWebhook()
    {
        $this->paymentModel->handleWebhook();
    }

    /**
     * Updates the batch for an enrollment.
     *
     * @param object $request HTTP request object.
     * @return object JSON response.
     * @throws Exception If an error occurs while updating.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function updEnrollment($request)
    {
        try {
            // Step 1: Decode the request body
            ynLog("updEnrollment - Request received with body " . $request->get_body() . "\n\n", 'updEnrollment');
            $enrollmentId = (int)$request['enrollmentId']; // enrollment id
            $payload = json_decode($request->get_body(), true);
            // Step 2: Initialize necessary variables from the payload with validation checks
            $validationChecks = [
                'enrollmentId' => 'numeric'
            ];

            $prmBody = [
                'enrollmentId' => $enrollmentId
            ];

            // Perform validation on each field
            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result; // Return the error immediately if validation fails
                }
            }

            // Validate payload
            if (empty($payload)) {
                // Return error if payload is empty
                return $this->response->error('PAYLOAD_FAIL', ['message' => "Payload is empty."]);
            }

            $action = $payload['action']; //CHG_BATCH

            // Validate each field using the common functions
            $validation_checks = [
                "action" => '/^(CHG_BATCH|UNENROLL)$/'
            ];

            $errors = $this->validate->validateData($payload, $validation_checks);

            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            if ($action !== 'CHG_BATCH' && $action !== 'UNENROLL') {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Invalid action provided']);
            }

            if ($action === 'UNENROLL') {
                $isUnenroll = $payload['is_unenroll']; //UNENROLL
                $validation_checks = [
                    "is_unenroll" => 'boolean'
                ];

                $errors = $this->validate->validateData($payload, $validation_checks);

                if (!empty($errors)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
                }

                $data = [
                    'is_unenroll' => $isUnenroll
                ];

                $response = $this->enrollmentModel->unEnrollLearner($enrollmentId, $data);
                ynLog("updEnrollment - unEnrollLearner - response " . json_encode($response), 'updEnrollment');

                if ($response === false) {
                    return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Enrollment not found']);
                } else {
                    // Success
                    return $this->response->success('PUT_UPDATE', ['enrollment_id' => $enrollmentId], ['message' => 'Enrollment updated successfully.']);
                }
            }

            // If action is CHG_BATCH, handle that case
            if ($action === 'CHG_BATCH') {
                $newBatchId = $payload['new_batch_id'] ?? null;

                // Validate the fields needed for CHG_BATCH
                $validation_checks = [
                    "new_batch_id" => '/^[0-9]+$/' // Batch ID
                ];

                $errors = $this->validate->validateData($payload, $validation_checks);

                if (!empty($errors)) {
                    return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
                }

                // Proceed with batch change logic
                $response = $this->enrollmentModel->updateBatch($enrollmentId, $newBatchId);
                ynLog("updEnrollment - updateBatch - response " . json_encode($response), 'updEnrollment');

                if ($response === false) {
                    return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Enrollment not found']);
                } else {
                    // Success
                    return $this->response->success('PUT_UPDATE', ['enrollment_id' => $enrollmentId], ['message' => 'Enrollment updated successfully.']);
                }
            }
        } catch (Exception $e) {
            // Return error if an exception occurs
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Retrieves enrollment filters based on request parameters.
     *
     * @since 1.0.0
     * @access public
     * @param object $request HTTP request object containing query parameters.
     * @return object JSON response with filters data.
     * @throws Exception If an error occurs while fetching filters.
     * <AUTHOR>
     */
    public function getEnrollmentsFilters($request)
    {
        try {
            // Log the request
            ynLog("getEnrollmentsFilters - Request received with query " . json_encode($request->get_query_params()), 'getEnrollmentsFilters');

            // Extract query parameters
            $queryParams = $request->get_query_params();

            $userId = (int) $queryParams['user_id'] ?? 0;
            $orgId = (int)$queryParams['org_id'] ?? 0;
            $instructorId = (int)$queryParams['instructor_id'] ?? 0;
            $learnerId = (int)$queryParams['learner_id'] ?? 0;
            $courseId = (int)$queryParams['course_id'] ?? 0;
            $status = $queryParams['status'] ?? 'all';
            $paymentStatus = $queryParams['payment_status'] ?? 'all';
            $counselorId = (int)$queryParams['counselor_id'] ?? 0;
            $referrals = $queryParams['referrals'] ?? 'all';

            // Get user role based on role_id
            $userRole = $this->userModel->getUserRole($userId);

            // Validation: Restrict access to unauthorized filters based on user role
            switch ($userRole) {
                case 'org-admin':
                    if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $referrals !== 'all') {
                        return $this->response->error('ACCESS_DENIED', ['message' => 'You are not allowed to use these filters']);
                    }
                    break;

                case 'counselor':
                    if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $orgId !== 0) {
                        return $this->response->error('ACCESS_DENIED', ['message' => 'Counselors can only filter by course, learners, referrals, and enrollment status']);
                    }
                    break;

                case 'instructor':
                    if ($instructorId !== 0 || $counselorId !== 0 || $paymentStatus !== 'all' || $orgId !== 0 || $referrals !== 'all') {
                        return $this->response->error('ACCESS_DENIED', ['message' => 'Instructors can only filter by course, learners, and enrollment status']);
                    }
                    break;

                case 'yuno-admin':
                    // No restrictions, yuno-admin has full access
                    break;

                default:
                    return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
            }

            // Initialize filters based on role
            $filters = [];

            switch ($userRole) {
                case 'yuno-admin':
                    $filters = [
                        $this->courseModel->generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId),
                        $this->orgModel->generateEnrollmentOrgFilters($userId, $orgId),
                        $this->learnerModel->generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->instructorModel->generateEnrollmentInstructorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->counselorModel->generateEnrollmentCounselorFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->paymentModel->generateEnrollmentPaymentFilters($paymentStatus),
                        $this->enrollmentModel->generateEnrollmentStatusFilters($status),
                        $this->referralModel->generateEnrollmentReferralFilters($referrals)
                    ];
                    break;
                case 'counselor':
                    $filters = [
                        $this->courseModel->generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId),
                        $this->learnerModel->generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->referralModel->generateEnrollmentReferralFilters($referrals),
                        $this->enrollmentModel->generateEnrollmentStatusFilters($status)
                    ];
                    break;
                case 'org-admin':
                    $filters = [
                        $this->courseModel->generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId),
                        $this->learnerModel->generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->enrollmentModel->generateEnrollmentStatusFilters($status)
                    ];
                    break;
                case 'instructor':
                    $filters = [
                        $this->courseModel->generateEnrollmentCourseFilters($userId, $orgId, $instructorId, $learnerId, $counselorId, $courseId),
                        $this->learnerModel->generateEnrollmentLearnerFilters($userId, $orgId, $instructorId, $learnerId, $counselorId),
                        $this->enrollmentModel->generateEnrollmentStatusFilters($status)
                    ];
                    break;
            }

            // Step 4: Return 204 if no data is found
            if (empty(array_filter($filters))) {
                return $this->response->error('GET_FAIL', ['message' => 'No filters found']);
            }

            return $this->response->success('GET_SUCCESS', $filters, ['message' => 'Filters retrieved successfully']);
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    }

    public function changeEnrollmentBatch($request)
    {
        try {
            ynLog("changeEnrollmentBatch - Request received " . json_encode($request), 'changeEnrollmentBatch');
            ynLog("changeEnrollmentBatch - Request received with params " . json_encode($request->get_params()), 'changeEnrollmentBatch');
            ynLog("changeEnrollmentBatch - Request received with query " . json_encode($request->get_query_params()), 'changeEnrollmentBatch');
            ynLog("changeEnrollmentBatch - Request received with body " . $request->get_body(), 'changeEnrollmentBatch');
            ynLog("changeEnrollmentBatch - Request: " . $request->get_body(), 'changeEnrollmentBatch');

            $enrollmentId = $request->get_params()['enrollmentId'];
            ynLog("changeEnrollmentBatch - enrollmentId: " . $enrollmentId, 'changeEnrollmentBatch');

            $payload = json_decode($request->get_body(), true);
            $newBatchId = $payload['new_batch_id'] ?? null;

            if (!$enrollmentId || !$newBatchId) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Missing enrollmentId or new_batch_id']);
            }

            $validationChecks = [
                'new_batch_id' => '/^[0-9]+$/'
            ];

            $errors = $this->validate->validateData($payload, $validationChecks);
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            $response = $this->enrollmentModel->updateBatch($enrollmentId, $newBatchId);
            ynLog("changeEnrollmentBatch - updateBatch - response: " . json_encode($response), 'changeEnrollmentBatch');

            if ($response === false) {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Failed to change batch for enrollment']);
            }

            return $this->response->success('PUT_UPDATE', ['enrollment_id' => $enrollmentId], ['message' => 'Batch changed successfully.']);
        } catch (Exception $e) {
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }

    public function updEnrollmentUnenroll($request)
    {
        try {
            ynLog("unenrollLearner - Request: " . $request->get_body(), 'updEnrollmentUnenroll');

            $payload = json_decode($request->get_body(), true);
            $isUnenroll = $payload['is_unenroll'] ?? null;
            $enrollmentId = (int)($request['enrollmentId'] ?? 0);

            if (!isset($isUnenroll)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Missing is_unenroll']);
            }

            $validationChecks = [
                'is_unenroll' => 'boolean'
            ];

            $errors = $this->validate->validateData($payload, $validationChecks);
            if (!empty($errors)) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => implode(', ', $errors)]);
            }

            $response = $this->enrollmentModel->unEnrollLearner($enrollmentId, ['is_unenroll' => $isUnenroll]);
            ynLog("unenrollLearner - response: " . json_encode($response), 'updEnrollmentUnenroll');

            if ($response === false) {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Enrollment not found']);
            }

            return $this->response->success('PUT_UPDATE', ['enrollment_id' => $enrollmentId], ['message' => 'Enrollment updated successfully.']);
        } catch (Exception $e) {
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Fetches enrollments from the database based on course and batch IDs.
     *
     * @param int $courseId Course ID.
     * @param int $batchId Batch ID.
     * @return array Array of enrollment objects.
     * <AUTHOR>
     */
    public function getActiveEnrollmentsByEntity($request)
    {

        try {
            ynLog("getActiveEnrollmentsByEntity - Request: " . json_encode($request->get_query_params()), 'getActiveEnrollmentsByEntity');
            // Extract course and batch IDs from the request
            $entityType = $request['entityType'];
            if (!$entityType) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Missing entityType']);
            }
            $entityId = $request['entityId'];

            if ($entityType !== 'batch') {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Invalid entity type']);
            }
            $entityId = $request['entityId'];
            if (!$entityId) {
                return $this->response->error('PAYLOAD_FAIL', ['message' => 'Missing entityId']);
            }
            // Validate course and batch IDs
            $validationChecks = [
                'entityId' => 'numeric',
                'entityType' => '/^(batch)$/'
            ];
            $prmBody = [
                'entityId' => $entityId,
                'entityType' => $entityType
            ];
            // Perform validation on each field
            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result; // Return the error immediately if validation fails
                }
            }
            // Fetch enrollments from the es database
            $enrollments = $this->enrollmentModel->getActiveEnrollmentsByBatchId($entityId);

            ynLog("getActiveEnrollmentsByEntity - Enrollments: " . json_encode($enrollments), 'getActiveEnrollmentsByEntity');
            if (empty($enrollments)) {
                return $this->response->error('GET_FAIL', ['message' => 'No enrollments found']);
            }

            return $this->response->success('GET_SUCCESS', $enrollments, ['message' => 'Active Enrollments retrieved successfully']);
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    }
}
