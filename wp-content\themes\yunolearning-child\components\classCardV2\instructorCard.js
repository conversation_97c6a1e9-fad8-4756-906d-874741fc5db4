Vue.component("yuno-instructor-card", {
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  template: `
		<div class="cardContent">
			<div class="cardContentWrapper">
				<div class="actionList mobile">
					<b-dropdown aria-role="list" @click.native.stop position="is-bottom-left" :can-close="['escape', 'outside']">
						<template #trigger>
							<div class="d-flex align-items-center" style="cursor: pointer;">
								<span class="material-icons onSurfaceVariant">more_vert</span>
							</div>
						</template>

						<b-dropdown-item 
							aria-role="listitem" 
							tag="a" 
							href="/class-schedule"
							@click="editClass(data)"
						>
							Edit
						</b-dropdown-item>
						<b-dropdown-item 
							v-if="data?.virtual_classroom?.meeting_url && data.virtual_classroom.meeting_url.trim() !== ''" 
							aria-role="listitem"
						>
							Delete
						</b-dropdown-item>
					</b-dropdown>
				</div>
				<div class="classStatus" v-if="!isPast && data.temporal_status != 'upcoming'">
					<span class="dot"></span>
					<span class="overline">{{ data.temporal_status }}</span>
				</div>
				<span class="onSurfaceVariant caption1">{{ data.course.title }}</span>
				<div class="d-flex flex-wrap">
                    <span :class="['classType overline mr-2', 'onSurfaceVariant', data.type == 'WEBINAR' ? 'lightPeach' : 'bgLightBlue']">
                        {{ data.type }}
                    </span>
                    <span 
                        class="classType overline onSurfaceVariant bgLightGreen" 
                        v-if="isPast && data?.recording?.url == ''"
                    >
                        RECORDING NOT AVAILABLE
                    </span>
                    <span 
                        v-if="isPast && data?.recording?.url != ''" 
                        class="classType overline onSurfaceVariant bgLightGreen"
                    >
                        RECORDING AVAILABLE
                    </span>
                </div>
				<div class="d-flex flex-column">
					<span class="classTitle subtitle1 onSurface">{{ data.class_title.title }}</span>
					<div class="classDetails">
						<span class="onSurfaceVariant">
							{{ formatedSchedule(data) }}
						</span>
						<span class="separator">|</span>
						<span class="onSurfaceVariant">BATCH: {{ data.batch.title }}</span>
						<span class="separator">|</span>
						<span class="onSurfaceVariant">ID: {{ data.batch.id }}</span>
						<span v-if="isPast" class="separator">|</span>
						<div class="d-flex" v-if="isPast && data?.recording?.url != ''">
							<div class="recordingDuration">
								<div class="playIcon">
									<span class="material-icons">play_arrow</span>
								</div>
							<span>{{ data?.recording?.duration }} minutes</span>
							</div>
						</div>
					</div>
				</div>
				<div class="userProfile">
					<template v-if="isPast">
					    <div class="learnerAttendence" v-if="false">
							<ul class="learnerList pl-3" v-if="validEnrollments.length">
								<li v-for="(enrollment, index) in limitedEnrollments" :key="index">
									<figure class="learnerMapped">
										<img v-if="enrollment.image.url != ''" :src="enrollment.image.url" alt="enrollment.image.alt_text" />
										<span v-else class="material-icons">account_circle</span>
									</figure>
								</li>
							</ul>
							<span class="onSurfaceVariant subtitle2 noBold"
							>
								{{ data?.attendance_of_each?.length || 0 }} of {{ validEnrollments.length || 0 }} attended
							</span>
							<span class="onSurfaceVariant subtitle2 noBold">({{ progressValue }}%)</span>
						</div>
						<div class="progressWrapper" v-if="false">
							<b-progress
								:type="attendanceClass"
								:value="progressValue"
								style="flex-grow: 1;"
							>
							</b-progress>
						</div>
					</template>
					<template v-else>
						<ul class="learnerList pl-3" v-if="validEnrollments.length">
							<li v-for="(enrollment, index) in limitedEnrollments" :key="index">
								<template v-if="enrollment.id > 0">
									<figure class="learnerMapped">
										<img v-if="enrollment.image_url != ''" :src="enrollment.image.url" alt="Learner Image" />
										<span v-else class="material-icons">account_circle</span>
									</figure>
								</template>
							</li>
						</ul>
						<div>
							<span class="onSurfaceVariant subtitle2 noBold">
								{{ validEnrollments.length ? data.enrollments.length + ' Students enrolled' : '' }}
							</span>
						</div>
					</template>
				</div>
			</div>
			<div class="buttonWrapper">
				<div class="hasFlex">
					<div class="academyLabel">
						<span class="academyLogo" v-if="academy?.logo_url?.url !== ''">
							<img :src="academy.logo_url.url" 
								:alt="academy.alt_text"
							/>
						</span>
						<span v-if="data?.academy?.name" class="onSurfaceVariant caption1">
							{{ formattedAcademyName(data.academy.name) }}
						</span>
					</div>
					<div class="actionList desktop" v-if="!isPast">
						<b-dropdown aria-role="list" @click.native.stop position="is-bottom-left" >
							<template #trigger>
								<div class="d-flex align-items-center">
									<span class="material-icons onSurfaceVariant">more_vert</span>
								</div>
							</template>

							<b-dropdown-item 
								aria-role="listitem" 
								tag="a" 
								href="/class-schedule"
								@click="editClass(data)"
							>
								Edit
							</b-dropdown-item>
							<b-dropdown-item 
								v-if="data?.virtual_classroom?.meeting_url && data.virtual_classroom.meeting_url.trim() !== ''" 
								aria-role="listitem"
								@click="deleteClass(data)"
							>
								Delete
							</b-dropdown-item>
						</b-dropdown>
					</div>
				</div>
				<div class="cta" v-if="isLive">
					<b-button
						tag="a"
						class="yunoPrimaryCTA"
						:disabled="!data.virtual_classroom.meeting_url || data.virtual_classroom.meeting_url.trim() === ''"
						:href="data.virtual_classroom.meeting_url"
					>
						Launch Class
					</b-button>
				</div>
				<div class="cta" v-if="false">
					<b-button
						tag="a"
						class="secondaryCTA"
						@click.stop="openScheduleModal(data)"
					>
						Schedule Class
					</b-button>
				</div>
			</div>
		</div>
    `,
  computed: {
    ...Vuex.mapState(["user", "userRole", "classDelete"]),

    academy() {
      return this.data.academy;
    },
	validEnrollments() {
  		return this.data?.enrollments?.filter(e => e.id > 0) || [];
	},
    limitedEnrollments() {
      return this.validEnrollments.slice(0, 5) || [];
    },
    attendanceClass() {
      const attendanceCount = this.data?.attendance_of_each?.length || 0;
      const enrollmentCount = this.validEnrollments.length || 1; // Avoid division by zero
      const p = (attendanceCount * 100) / enrollmentCount;
      if (p <= 30) return "is-red";
      if (p <= 50) return "is-orange";
      if (p <= 70) return "is-yellow";
      if (p <= 80) return "is-lightGreen";
      if (p <= 90) return "is-blue";
      return "is-green";
    },
    progressValue() {
      const attendanceCount = this.data?.attendance_of_each?.length || 0;
      const enrollmentCount = this.data?.enrollments.length || 1; // Avoid division by zero
      return (attendanceCount * 100) / enrollmentCount;
    },
    isLive() {
      return (
        this.data.temporal_status === "live" ||
        this.data.temporal_status === "upcoming"
      );
    },
    isPast() {
      return this.data.temporal_status === "past";
    },
  },
  data() {
    return {
      academyLogo:
        this.$store.state.themeURL + "/assets/images/academy_logo.png",
    };
  },
  methods: {
    editClass(data) {
      localStorage.removeItem("classEditState");

      localStorage.setItem("classEditState", JSON.stringify(data));
    },
    deleteClass(data) {
      this.$buefy.dialog.confirm({
        title: "Deleting Class",
        message: "Are you sure you want to delete this class?",
        confirmText: "Delete Class",
        cancelText: "Cancel",
        customClass: "deleteClassDialog",
        type: "is-danger",
        onConfirm: () => {
          this.deleteClassAPI(data.id, data.academy.id);
        },
      });
    },
    deleteClassAPI(id, academyID) {
      const options = {
        apiURL: YUNOCommon.config.deleteWebinarAPI(id, isLoggedIn, academyID),
        module: "gotData",
        store: "classDelete",
        callback: true,
        callbackFunc: (options) => this.gotDeleteClass(options),
      };
      this.$store.dispatch("deleteData", options);
    },
    gotDeleteClass(options) {
      const response = options?.response?.data;
      if (response?.code === 200) {
        this.showToastMessage(response.message);
      } else if (response?.message) {
        this.showToastMessage(response.message);
      }
    },
    showToastMessage(message) {
      this.$buefy.toast.open({
        duration: 5000,
        message: `${message}`,
        position: "is-bottom",
      });
    },
    formattedAcademyName(academy) {
      return academy
        .split("-") // Convert into array by splitting at '-'
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
        .join(" "); // Join back as a string with spaces
    },
    formatedSchedule(data) {

      const startTime =
        data?.temporal_status == "past"
          ? data?.actual?.start?.time
          : data?.scheduled?.start?.time;

      const endTime =
        data?.temporal_status == "past"
          ? data?.actual?.end?.time
          : data?.scheduled?.end?.time;

      if (
        !startTime ||
        !endTime ||
        startTime.trim() === "" ||
        endTime.trim() === ""
      ) {
        return "";
      }

      try {
        const startDate = new Date(startTime);
        const endDate = new Date(endTime);

        return this.formatDuration(startTime, endTime);
      } catch (error) {
        console.error("Error formatting schedule:", error);
        return "";
      }
    },
    formatDuration(start, end) {
      const startTime = new Date(start);
      const endTime = new Date(end);
      return `${startTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })} - ${endTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`;
    },
  },
});
