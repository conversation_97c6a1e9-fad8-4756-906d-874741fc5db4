<?php

namespace V4;

/**
 * Batch<PERSON>ontroller
 *
 * This class handles batch-related functionalities.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class BatchController extends Controller
{
    /**
     * BatchController Constructor
     *
     * Initializes the controller by loading required libraries and models 
     * needed for batch-related functionalities.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadModel('batch');
        $this->loadLibary('validate');
        $this->loadModel('course');
        $this->loadModel('instructor');
        $this->loadModel('user');
        $this->loadModel('academy');
        $this->loadModel('batch');
        $this->loadFilter('course');
        $this->loadFilter('instructor');
        $this->loadFilter('batch');
    }
    /**
     * Get a batch by ID
     *
     * Retrieves all info of batch from the es and returns them in a JSON format.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function getBatch($request)
    {
        try {
            ynLog("getBatch - Request: " . json_encode($request), 'getBatch');
            $batchId = (int)$request['batchId'];


            ynLog("getBatch - Batch ID: $batchId", 'getBatch');

            // Validate each field using the common functions
            $validation_checks = [
                'batchId' => 'numeric'
            ];

            $prmBody = [
                'batchId' => $batchId
            ];

            foreach ($validation_checks as $key => $type) {
                $result = $this->validate->validateRequired($prmBody, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }
            $batch = $this->batchModel->getBatch($batchId);
            ynLog("getBatch - Batch: " . json_encode($batch), 'getBatch');

            if (!empty($batch)) {
                return $this->response->success('GET_SUCCESS', $batch, ['message' => 'Batch retrieved successfully']);
            } else {
                return $this->response->error('GET_FAIL', ['message' => 'Batch not found']);
            }
        } catch (\Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Handles the creation of a new batch.
     * @since 1.0.0
     * <AUTHOR>
     *
     * This controller method receives a JSON HTTP request, validates the payload,
     * and delegates the actual creation to the `createBatchPost` method of the BatchModel.
     * It also performs detailed logging and structured error/success responses.
     *
     * ### Request Payload (JSON)
     * {
     *   "title": "Batch Title",                           // (string) Required
     *   "course_id": [123, 456],                          // (array of numeric IDs) Required
     *   "instructor_id": 1001,                            // (numeric) Required
     *   "created_by": 501,                                // (numeric) Required
     *   "start_date": "2025-07-01",                       // (ISO 8601 date string) Required
     *   "end_date": "2025-08-01",                         // (ISO 8601 date string) Required
     *   "class_days": ["Mon", "Wed", "Fri"],              // (array of strings) Required
     *   "class_time": "10:30",                            // (string, HH:MM format) Required
     *   "personalisation": "one_to_many",                 // (enum: "one_to_many" | "one_to_one") Required
     *   "teaching_mode": "online",                        // (enum: "online" | "in_person") Required
     *   "seats_max": 20,                                    // (numeric) Required
     *   "is_locked": false                                // (boolean) Required
     * }
     *
     * @param WP_REST_Request $request WordPress REST API request object.
     *
     * @return WP_REST_Response JSON response with:
     *   - success: { "batch_id": <int>, "message": "Batch created successfully" }
     *   - failure: { "message": "Failed to create batch" } or error details
     *
     * ### Error Codes:
     * - POST_INSERT_FAIL: Returned when validation fails or batch creation fails.
     *
     * @throws Exception Catches and returns any exception as POST_INSERT_FAIL.
     */

    public function addBatch($request)
    {
        try {
            ynLog("createBatch - Request: " . json_encode($request), 'createBatch');
            // Parse JSON payload
            $data = json_decode($request->get_body(), true);
            ynLog("createBatch - Data: " . json_encode($data), 'createBatch');
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Invalid JSON payload']);
            }
            // Define all required fields with expected type or pattern
            $validationChecks = [
                'title'                => 'string',
                'course_id'            => 'array',
                'instructor_id'        => 'numeric',
                'created_by'           => 'numeric',
                'start_date'           => 'string', // Can add regex for ISO date if needed
                'end_date'             => 'string',
                'class_days'           => 'array',
                'class_time'           => 'string',
                'personalisation' => '/^(one_to_many|one_to_one)$/',
                'teaching_mode'        => '/^(online|in_person)$/',
                // 'place_id'             => 'numeric',
                // 'classroom_id'         => 'numeric',
                'seats_max'            => 'numeric',
                'is_locked'            => 'boolean',
                'created_by'           => 'numeric'
            ];

            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($request, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Call the model's function
            $batchId = $this->batchModel->createBatchPost($data);
            ynLog("createBatch - Batch ID: " . $batchId, 'createBatch');

            if (!$batchId) {
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Failed to create batch']);
            } else if ($batchId === 'duplicate_title') {
                ynLog("createBatch - Duplicate batch title", 'createBatch');
                return $this->response->error('POST_INSERT_FAIL', ['message' => 'Batch with this title already exists']);
            } else {
                ynLog("createBatch - Batch created with ID: $batchId", 'createBatch');
                return $this->response->success('POST_INSERT', ['batch_id' => $batchId], ['message' => 'Batch created successfully']);
            }
        } catch (\Exception $e) {
            // Handle any exceptions that occur
            return $this->response->error('POST_INSERT_FAIL', ['message' => $e->getMessage()]);
        }
    }

    /**
     * Update a batch by ID
     *
     * Updates the batch with the given ID and returns the updated batch in JSON format.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function updBatch($request)
    {
        try {
            ynLog("updateBatch - Request: " . json_encode($request), 'updBatch');

            // Parse incoming JSON payload
            $data = json_decode($request->get_body(), true);
            ynLog("updateBatch - Data: " . json_encode($data), 'updBatch');
            $batchId = isset($request['batchId']) ? (int)$request['batchId'] : 0;

            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Invalid JSON payload']);
            }

            // Validate required key for update
            if (empty($batchId) || !is_numeric($batchId)) {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Invalid or missing batch_id']);
            }

            // Check if the post exists and is of type 'batch'
            if (get_post_type($batchId) !== 'batch') {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => "Batch ID $batchId is not a valid batch"]);
            }

            ynLog("updateBatch - Updated batch post_id: $batchId", 'updBatch');
            // Call the model's function to update the batch
            $updBatchId = $this->batchModel->updateBatchPost($batchId, $data);
            ynLog("updateBatch - Updated Batch ID: " . $updBatchId, 'updBatch');
            if (!$updBatchId) {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Failed to update batch']);
            }

            return $this->response->success(
                'PUT_UPDATE',
                ['batch_id' => $updBatchId],
                ['message' => 'Batch updated successfully']
            );
        } catch (\Exception $e) {
            ynLog("updateBatch - Exception: " . $e->getMessage(), 'updBatch');
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }
    /**
     * Delete a batch by ID
     *
     * Deletes the batch with the given ID and returns a success message.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function deleteBatch($request)
    {
        try {
            ynLog("deleteBatch - Request: " . json_encode($request), 'deleteBatch');

            $batchId = $request['batchId'];
            ynLog("deleteBatch - Batch ID: $batchId", 'deleteBatch');

            // Validate batch ID
            if (empty($batchId) || !is_numeric($batchId)) {
                return $this->response->error('DELETE_FAIL', ['message' => 'Invalid batch ID']);
            }

            // Call the model's function to delete the batch
            $result = $this->batch->deleteBatch($batchId);
            ynLog("deleteBatch - Result: " . json_encode($result), 'deleteBatch');

            if ($result) {
                return $this->response->success('DELETE_SUCCESS', [], ['message' => 'Batch deleted successfully']);
            } else {
                return $this->response->error('DELETE_FAIL', ['message' => 'Failed to delete batch']);
            }
        } catch (\Exception $e) {
            ynLog("deleteBatch - Exception: " . $e->getMessage(), 'deleteBatch');
            return $this->response->error('DELETE_FAIL', ['message' => $e->getMessage()]);
        }
    }
    /**
     * Get all batches
     *
     * Retrieves all batches and returns them in a JSON format.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function getBatches($request)
    {
        try {
            ynLog("getAllBatches - Request: " . json_encode($request), 'getAllBatches');
            // Extract query string parameters
            $userId = isset($_GET['user_id']) ? (int) $_GET['user_id'] : 0;
            $instructorId = isset($_GET['instructor_id']) ? (int) $_GET['instructor_id'] : 0;
            $courseId = isset($_GET['course_id']) ? (int) $_GET['course_id'] : 0;
            $academyId = isset($_GET['academy_id']) ? (int) $_GET['academy_id'] : 0;
            $limit = isset($_GET['limit']) ? (int) $_GET['limit'] : 10; // Default limit
            $offset = isset($_GET['offset']) ? (int) $_GET['offset'] : 0; // Default offset
            $viewType = isset($request['viewType']) ? $request['viewType'] : 'all';
            $batchDays = isset($_GET['batch_days']) && trim($_GET['batch_days']) !== ''
                ? array_filter(array_map('trim', explode(',', $_GET['batch_days'])))
                : [];
            $batchTime = isset($_GET['batch_time']) && trim($_GET['batch_time']) !== ''
                ? array_filter(array_map('trim', explode(',', $_GET['batch_time'])))
                : [];
            $personalisation = isset($_GET['personalisation']) ? $_GET['personalisation'] : 'all'; // e.g., 'IELTS'
            $onlyEnrollable = isset($_GET['only_enrollable']) ? (bool) $_GET['only_enrollable'] : false;
            $onlyLocked     = isset($_GET['only_locked']) ? (bool) $_GET['only_locked'] : false;
            $activeBatch = isset($_GET['active_batch']) ? $_GET['active_batch'] : 'all'; // e.g., 'upcoming', 'inactive', 'all'
            $teachingMode = isset($_GET['teaching_mode']) ? $_GET['teaching_mode'] : 'all'; // e.g., 'online', 'in_person', 'all'
            $enrollmentRequestDate = isset($_GET['enrollment_request_date']) ? $_GET['enrollment_request_date'] : 'all'; // e.g., '2023-10-01'

            // Validation rules
            $validationChecks = [
                'user_id' => 'numeric',                  // Must be numeric
                'course_id' => 'numeric',                // Must be numeric
                'limit' => 'numeric',                    // Must be numeric
            ];

            // Perform validation on each field
            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($request, $key, $type);
                if (is_wp_error($result)) {
                    return $result; // Return the error immediately if validation fails
                }
            }

            if ($userId <= 0) {
                return $this->response->error('USER_ID_FAIL', ['message' => 'Invalid User']);
            }

            // Fetch User Role
            $this->loadModel('user');
            $role = $this->userModel->getUserRole($userId);

            // Role-Based Validation
            if ($this->userModel->checkRole($role, $this->userModel->yn_Org_Admin) !== false) {
                if ($instructorId !== 0 || $courseId !== 0 || $academyId !== 0 || $personalisation !== 'all' || $teachingMode !== 'all' || $onlyLocked !== 'all' || $activeBatch !== 'all' || $onlyEnrollable !== 'all') {
                    return $this->response->error('ACCESS_DENIED', ['message' => 'You are not allowed to use these filters']);
                }
            } else if ($this->userModel->checkRole($role, $this->userModel->yn_Yuno_Admin) !== false) {
                // Yuno Admin - full access, no restrictions
            } else {
                return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
            }
            $mustConditions = [];
            $mustNotConditions = [];

            // Course ID filter
            if (!empty($courseId)) {
                $mustConditions[] = [
                    "match_phrase" => [
                        "data.details.batch_details.course_id" => $courseId
                    ]
                ];
            }

            // Instructor filter
            if (!empty($instructorId)) {
                $mustConditions[] = [
                    "match_phrase" => [
                        "data.details.batch_details.instructor_id" => $instructorId
                    ]
                ];
            }

            // Academy filter
            if (!empty($academyId)) {
                $mustConditions[] = [
                    "term" => [
                        "data.details.academies" => $academyId
                    ]
                ];
            }

            // Personalisation
            if ($personalisation !== "all") {
                $mustConditions[] = [
                    "match" => [
                        "data.details.batch_details.personalisation" => $personalisation === "one_to_one" ? 1 : 0
                    ]
                ];
            }
            // Class Mode
            if ($teachingMode !== "all") {
                $mustConditions[] = [
                    "match" => [
                        "data.details.batch_details.mode" => $teachingMode === "in_person" ? 1 : 0
                    ]
                ];
            }

            // Active Batch
            if ($activeBatch !== "all") {
                $today = date('Y-m-d'); // Format as per your ES mapping

                if ($activeBatch === "active") {
                    // Active batch flag = 1
                    $mustConditions[] = [
                        "match" => [
                            "data.details.batch_details.active_batch" => 1
                        ]
                    ];
                } elseif ($activeBatch === "upcoming") {
                    // batch_start_date >= today
                    $mustConditions[] = [
                        "range" => [
                            "data.details.batch_details.batch_start_date" => [
                                "gte" => $today
                            ]
                        ]
                    ];
                } elseif ($activeBatch === "inactive") {
                    // batch_end_date < today
                    $mustConditions[] = [
                        "range" => [
                            "data.details.batch_details.batch_end_date" => [
                                "lt" => $today
                            ]
                        ]
                    ];
                }
            }
            
            // Exclude deleted batches
            $mustNotConditions[] = [
                "term" => [
                    "data.details.batch_details.batch_deleted_status" => 1
                ]
            ];

            // Enrollable filter
            if (isset($onlyEnrollable)) {
                $mustConditions[] = [
                    "term" => [
                        "data.details.batch_details.enrollable_status" => $onlyEnrollable ? 1 : 0
                    ]
                ];
            }

            // Locked Batch filter
            if (isset($onlyLocked)) {
                $mustConditions[] = [
                    "term" => [
                        "data.details.batch_details.locked_batch" => $onlyLocked ? 1 : 0
                    ]
                ];
            }


            // Enrollment Request Date filter
            if (!empty($enrollmentRequestDate)) {
                $mustConditions[] = [
                    "range" => [
                        "data.details.batch_details.batch_start_date" => [
                            "gte" => $enrollmentRequestDate
                        ]
                    ]
                ];
            }


            // Add filter for batch_days if not 'all'
            if (!empty($batchDays) && !(count($batchDays) === 1 && strtolower($batchDays[0]) === 'all')) {
                $dayConditions = array_map(function ($day) {
                    return [
                        'match' => [
                            'data.details.batch_details.days_of_week' => $day
                        ]
                    ];
                }, $batchDays);

                $mustConditions[] = [
                    'bool' => [
                        'should' => $dayConditions,
                        'minimum_should_match' => 1
                    ]
                ];
            }

            // Add filter for batch_time if not 'all'
            if (!empty($batchTime) && !(count($batchTime) === 1 && strtolower($batchTime[0]) === 'all')) {
                $timeConditions = array_map(function ($time) {
                    return [
                        'match' => [
                            'data.details.batch_details.batch_type' => $time
                        ]
                    ];
                }, $batchTime);

                $mustConditions[] = [
                    'bool' => [
                        'should' => $timeConditions,
                        'minimum_should_match' => 1
                    ]
                ];
            }

            $elasticQuery = [
                "_source" => false, // set to true if you want to return the full parent document
                "query" => [
                    "nested" => [
                        "path" => "data.details.batch_details",
                        "query" => [
                            "bool" => [
                                "must" => $mustConditions,
                                "must_not" => $mustNotConditions
                            ]
                        ],
                        "inner_hits" => [
                            "_source" => [
                                "includes" => [
                                    "data.details.batch_details.batch_id",
                                    "data.details.batch_details.batch_name",
                                    "data.details.batch_details.batch_title",
                                    "data.details.batch_details.course_id",
                                    "data.details.batch_details.instructor_id",
                                    "data.details.batch_details.mode",
                                    "data.details.batch_details.locked_batch",
                                    "data.details.batch_details.active_batch",
                                    "data.details.batch_details.personalisation",
                                    "data.details.batch_details.enrollment_counter",
                                    "data.details.batch_details.days_of_week",
                                    "data.details.batch_details.batch_type",
                                    "data.details.batch_details.batch_start_date",
                                    "data.details.batch_details.batch_end_date",
                                    "data.details.batch_details.vacancy",
                                    "data.details.batch_details.batch_deleted_status",
                                    "data.details.batch_details.batch_post_url",
                                    "data.details.batch_details.batch_post_id",
                                    "data.details.batch_details.batch_display_end_date",
                                    "data.details.batch_details.batch_display_start_date",
                                    "data.details.batch_details.batch_label",
                                    "data.details.batch_details.class_start_time",
                                    "data.details.batch_details.class_end_time",
                                    "data.details.batch_details.teaching_mode",
                                    "data.details.batch_details.max_seats",
                                    "data.details.batch_details.place",
                                    "data.details.batch_details.classroom",
                                    "data.details.batch_details.duration",
                                    "data.details.batch_details.any_time",
                                    "data.details.batch_details.batch_visibility",
                                    "data.details.batch_details.enrollable_status",
                                    "data.details.batch_details.enrollment_start_date",
                                    "data.details.batch_details.enrollment_type",
                                    "data.details.batch_details.group_type",
                                    "data.details.batch_details.image",
                                    "data.details.batch_details.instructor_completed_enrollment",
                                    "data.details.batch_details.instructor_name",
                                    "data.details.batch_details.instructor_profile_url",
                                    "data.details.batch_details.instructor_rating",
                                    "data.details.batch_details.last_enrollment_end_date",
                                    "data.details.batch_details.ongoing_batch",
                                    "data.details.batch_details.price",
                                    "data.details.batch_details.publisher_id",
                                    "data.details.batch_details.reviews_count",
                                    "data.details.batch_details.term_id",
                                    "data.details.batch_details.time",
                                    "data.details.batch_details.user_id",
                                    "data.details.batch_details.zoho_batch_id"

                                ]
                            ],
                            "size" => 1000,
                            "sort" => [
                                [
                                    "data.details.batch_details.batch_start_date" => [
                                        "order" => "asc"
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                "sort" => [
                    [
                        "_score" => [
                            "order" => "desc"
                        ]
                    ]
                ]
            ];

            ynLog("get_batch_list - Elastic Query: " . json_encode($elasticQuery), 'get_batches');

            // Plug into the query wrapper structure
            $query['custom'] = $elasticQuery;
            $query['qryStr'] = [
                "from" => $offset,
                "size" => $limit
            ];

            // Optional debug logging
            ynLog("get_batch_list - Final ES Query: " . json_encode($elasticQuery), 'get_batches');
            ynLog("get_batch_list - Query String: " . json_encode($query['qryStr']), 'get_batches');

            // Call the model's function to get all batches
            $batches = $this->batchModel->getBatches($query);
            ynLog("getAllBatches - Batches: " . json_encode($batches), 'getAllBatches');

            if (!empty($batches)) {
                if ($viewType === "list") {
                    // Return success response with place details
                    return $this->response->success('GET_SUCCESS', $batches, ['message' => 'Batches retrieved successfully']);
                } elseif ($viewType === "grid") {
                    if ($role === 'org admin' && isset($request['academy_id'])) {
                        // Override or modify $columns as needed
                        $columns = $this->getColumnsForOrgAdmin($role); // replace with your actual method
                    }
                    $columns = $this->getColumnsForYunoAdmin($userRole ?? null);
                    // Check for yuno admin 

                    $BatchesData = [
                        "rows" =>  $batches['data'],
                        "columns" => $columns
                    ];
                    return $this->response->success('GET_SUCCESS', ['data' => $BatchesData, 'count' => $batches['count']], ['message' => 'Batches retrieved successfully']);
                }
            } else {
                return $this->response->error('GET_FAIL', ['message' => 'No batches found']);
            }
        } catch (\Exception $e) {
            ynLog("getAllBatches - Exception: " . $e->getMessage(), 'getAllBatches');
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    }
    /**
     * Get filters for batches
     *
     * Retrieves all filters for batches and returns them in a JSON format.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function getBatchesFilters($request)
    {
        try {
            ynLog("getBatchesFilters - Request: " . json_encode($request), 'getBatchesFilters');
            // Extract query parameters
            $queryParams = $request->get_query_params();
            ynLog("getBatchesFilters - Query Params: " . json_encode($queryParams), 'getBatchesFilters');
            // Validate user role
            $userId = isset($queryParams['user_id']) ? (int)$queryParams['user_id'] : 0;
            $orgId = (int)$queryParams['org_id'] ?? 0;
            $academyId = (int)$queryParams['academy_id'] ?? 0;
            $instructorId = (int)$queryParams['instructor_id'] ?? 0;
            $courseId = (int)$queryParams['course_id'] ?? 0;
            //$batchDays = isset($queryParams['batch_days']) ? explode(',', $queryParams['batch_days']) : [];
            //$batchTime = isset($queryParams['batch_time']) ? explode(',', $queryParams['batch_time']) : [];
            $personalisation = isset($queryParams['personalisation']) ? $queryParams['personalisation'] : '';
            //$batchDays = isset($queryParams['batch_days']) ? json_decode($queryParams['batch_days'], true) : [];
            //$batchTime = isset($queryParams['batch_time']) ? json_decode($queryParams['batch_time'], true) : [];

            $batchDays = isset($_GET['batch_days']) && trim($_GET['batch_days']) !== ''
                ? array_filter(array_map('trim', explode(',', $_GET['batch_days'])))
                : [];

            $batchTime = isset($_GET['batch_time']) && trim($_GET['batch_time']) !== ''
                ? array_filter(array_map('trim', explode(',', $_GET['batch_time'])))
                : [];

            $teachingMode = isset($queryParams['teaching_mode']) ? $queryParams['teaching_mode'] : '';
            $onlyEnrollable = isset($queryParams['only_enrollable']) ? (bool)$queryParams['only_enrollable'] : false;
            $onlyLocked = isset($queryParams['only_locked']) ? (bool)$queryParams['only_locked'] : false;

            ynLog("getBatchesFilters - User ID: $userId", 'getBatchesFilters');
            if ($userId <= 0) {
                return $this->response->error('USER_ID_FAIL', ['message' => 'Invalid User']);
            }
            // Fetch User Role
            $this->loadModel('user');
            $role = $this->userModel->getUserRole($userId);
            ynLog("getBatchesFilters - User Role: $role", 'getBatchesFilters');

            // Role-Based Validation
            if ($this->userModel->checkRole($role, $this->userModel->yn_Org_Admin) !== false) {
                // Org Admin - limited access, no filters
                // return $this->response->error('ACCESS_DENIED', ['message' => 'You are not allowed to use these filters']);
            } else if ($this->userModel->checkRole($role, $this->userModel->yn_Yuno_Admin) !== false) {
                // Yuno Admin - full access, no restrictions
            } else {
                return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
            }

            if ($this->userModel->checkRole($role, $this->userModel->yn_Org_Admin) !== false) {
                $filters = [
                    $this->courseFilter->generateBatchCourseFilters($userId, $orgId, $academyId, $courseId),
                    $this->batchFilter->generateBatchDayFilters($batchDays),
                    $this->batchFilter->generateBatchTimeFilters($batchTime),
                    $this->batchFilter->generateBatchPersonalisationFilters($personalisation),
                    $this->batchFilter->generateBatchTeachingModeFilters($teachingMode),
                    $this->batchFilter->generateBatchEnrollableFilters($onlyEnrollable),
                    $this->batchFilter->generateBatchLockedFilters($onlyLocked)
                ];
            } else if ($this->userModel->checkRole($role, $this->userModel->yn_Yuno_Admin) !== false) {
                // Yuno Admin - full access, no restrictions
                $filters = [
                    $this->courseFilter->generateBatchCourseFilters($userId, $orgId, $academyId, $courseId),
                    $this->instructorFilter->generateBatchInstructorFilters($userId, $instructorId),
                    $this->batchFilter->generateBatchDayFilters($batchDays),
                    $this->batchFilter->generateBatchTimeFilters($batchTime),
                    $this->batchFilter->generateBatchPersonalisationFilters($personalisation),
                    $this->batchFilter->generateBatchTeachingModeFilters($teachingMode),
                    $this->batchFilter->generateBatchEnrollableFilters($onlyEnrollable),
                    $this->batchFilter->generateBatchLockedFilters($onlyLocked)
                ];
            } else {
                return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
            }

            ynLog("getBatchesFilters - Filters: " . json_encode($filters), 'getBatchesFilters');

            if (!empty($filters)) {
                return $this->response->success('GET_SUCCESS', $filters, ['message' => 'Batch filters retrieved successfully']);
            } else {
                return $this->response->error('GET_FAIL', ['message' => 'No batch filters found']);
            }
        } catch (\Exception $e) {
            ynLog("getBatchesFilters - Exception: " . $e->getMessage(), 'getBatchesFilters');
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    }

    private function getColumnsForOrgAdmin($role)
    {
        $columns =  [
            [
                "field" => "id",
                "label" => "Batch ID",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "title",
                "label" => "Label",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "instructor.full_name",
                "label" => "Instructor",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "class_days",
                "label" => "Days",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "active_enrollments",
                "label" => "Active Enrollments",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "start_end.start_date",
                "label" => "Start Date",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "class_time",
                "label" => "Class Time",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "start_end.end_date",
                "label" => "End Date",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "class_time.duration",
                "label" => "Duration",
                "tooltip" => "",
                "sortable" => true
            ]
        ];

        return $columns;
    }
    private function getColumnsForYunoAdmin($userRole = null)
    {
        $columns = [
            [
                "field" => "id",
                "label" => "Batch ID",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "title",
                "label" => "Label",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "instructor.full_name",
                "label" => "Instructor",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "class_days",
                "label" => "Days",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "active_enrollments",
                "label" => "Active Enrollments",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "start_end.start_date",
                "label" => "Start Date",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "class_time",
                "label" => "Class Time",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "start_end.end_date",
                "label" => "End Date",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "class_time.duration",
                "label" => "Duration",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "checkout_url",
                "label" => "Batch URL",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => "teaching_mode",
                "label" => "Teaching Mode",
                "tooltip" => "",
                "sortable" => true
            ],
            [
                "field" => 'seats.max',
                'label' => 'Max Seats',
                'tooltip' => '',
                'sortable' => true
            ],
            [
                "field" => "actions",
                "label" => "Actions",
                "tooltip" => "",
                "sortable" => true
            ]
        ];

        return $columns;
    }

    public function endBatch($request)
    {
        try {
            ynLog("endBatch - Request: " . json_encode($request), 'endBatch');

            // Parse incoming JSON payload
            $data = json_decode($request->get_body(), true);
            ynLog("endBatch - Data: " . json_encode($data), 'endBatch');
            $batchId = isset($request['batchId']) ? (int)$request['batchId'] : 0;
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Invalid JSON payload']);
            }
            // Validate required key for update
            $validationChecks = [
                'batchId'        => 'numeric'
            ];

            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($request, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Check if the post exists and is of type 'batch'
            if (get_post_type($batchId) !== 'batch') {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => "Batch ID $batchId is not a valid batch"]);
            }

            ynLog("endBatch - Batch ID: $batchId", 'endBatch');
            // Call the model's function to end the batch
            $result = $this->batchModel->endBatch($batchId, $data);
            ynLog("endBatch - Result: " . json_encode($result), 'endBatch');
            if ($result) {
                return $this->response->success(
                    'PUT_UPDATE',
                    ['batch_id' => $batchId],
                    ['message' => 'Batch ended successfully']
                );
            } else {
                return $this->response->error('PUT_UPDATE_FAIL', ['message' => 'Failed to end batch']);
            }
        } catch (\Exception $e) {
            ynLog("endBatch - Exception: " . $e->getMessage(), 'endBatch');
            return $this->response->error('PUT_UPDATE_FAIL', ['message' => $e->getMessage()]);
        }
    }
}
