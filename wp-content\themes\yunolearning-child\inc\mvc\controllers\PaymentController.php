<?php

namespace V4;

/**
 * PaymentController
 *
 * This class handles payment-related functionalities, including retrieving payments, 
 * filtering payments, and managing payment-related actions.
 *
 * @package V4
 * @since 1.0.0
 * <AUTHOR>
 */
class PaymentController extends Controller
{
    /**
     * PaymentController Constructor
     *
     * Initializes the controller by loading required libraries and models 
     * needed for payment-related functionalities.
     *
     * @since 1.0.0
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadModel('category');
        $this->loadModel('user');
        $this->loadModel('batch');
        $this->loadLibary('validate');
        $this->loadModel('payment');
        $this->loadModel('counselor');
    }

    /**
     * Retrieves payment records based on filters such as user role, date range, status, and counselor ID.
     *
     * This function constructs an Elasticsearch query based on the provided filters, executes it,
     * and returns payment data. The response format can be either a **list** or a **structured dataset**
     * containing columns and rows.
     *
     * @package V4
     * @since 1.0.0
     * <AUTHOR>
     *
     * @param WP_REST_Request $request The incoming request containing query parameters.
     *
     * Query parameters:
     * - `referrer_id` (int)     : The ID of the referring user.
     * - `limit` (int)           : The maximum number of records to return (default: 20).
     * - `offset` (int)          : The offset for pagination.
     * - `user_id` (int)         : The ID of the user to filter payments for.
     * - `days` (int)            : The number of days for filtering transactions.
     * - `status` (string)       : The payment status ('all' by default).
     * - `counselor_id` (int)    : The ID of the assigned counselor.
     * - `viewType` (string)     : Determines whether the response should be a **list** or a structured **table**.
     *
     * @return WP_REST_Response JSON response containing filtered payment records.
     *
     * The response may include:
     * - `data` (array)          : An array of payment records.
     * - `count` (int)           : The total number of matched records.
     * - `columns` (array)       : If `viewType` is "table", column definitions for UI rendering.
     *
     * @throws WP_Error If validation fails or an unexpected error occurs.
     */
    public function getPayments($request)
    {
        global $wpdb;
        $query = [];
        // Extract parameters from the request
        $limit = isset($_GET['limit']) ? (int) $_GET['limit'] : 20;
        $offset = isset($_GET['offset']) ? (int) $_GET['offset'] : 0;
        $userId = isset($_GET['user_id']) ? (int) $_GET['user_id'] : 0;
        $days = isset($_GET['days']) ? (int) $_GET['days'] : 0;
        $status = isset($_GET['status']) ? $_GET['status'] : 'all';
        $counselorId = isset($_GET['counselor_id']) ? (int) $_GET['counselor_id'] : 0;
        $referrerId = isset($_GET['referrer_id']) ? (int) $_GET['referrer_id'] : 0;
        $mustConditions = [];
        $mustNotConditions = [];
        $viewType = isset($request['viewType']) ? $request['viewType'] : null;

        // Validation rules
        $validationChecks = [
            'user_id' => 'numeric',
            'status' => 'string',
            'limit' => 'numeric'
        ];

        // Perform validation on each field
        foreach ($validationChecks as $key => $type) {
            $result = $this->validate->validateRequired($request, $key, $type);
            if (is_wp_error($result)) {
                return $result; // Return the error immediately if validation fails
            }
        }

        // Get user role based on userId
        $userRole = $this->userModel->getUserRole($userId);

        // Restrict access to filters based on role
        if ($userRole !== 'yuno-admin' && $userRole != 'org-admin') {
            return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
        }

        // if (isset($userId) && $userId != 0) {
        //     $mustConditions[] = [
        //         "match_phrase" => [
        //             "data.details.user_id" => $userId
        //         ]
        //     ];
        // }

        if (isset($counselorId) && $counselorId != 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.counselor_id" => $counselorId
                ]
            ];
        }

        // Add date range condition
        if ($days > 0) {
            $previousDate = date("Y-m-d", strtotime("-$days days"));
            $mustConditions[] = [
                "range" => [
                    "data.details.created_at" => [
                        "gte" => $previousDate
                    ]
                ]
            ];
        }

        // Add status condition
        if ($status !== 'all') {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.link_status" => strtolower($status)
                ]
            ];
        }

        // Add referrer_id condition
        if ($referrerId > 0) {
            $mustConditions[] = [
                "match_phrase" => [
                    "data.details.referrer_id" => $referrerId
                ]
            ];
        }

        // Ensure that the mustConditions array is not empty
        if (empty($mustConditions)) {
            $mustConditions[] = [
                "match_all" => (object)[]
            ];
        }

        $elasticQuery = [
            "_source" => [
                "inner_hits",
                "type",
                "data.details.batch_name",
                "data.details.category",
                "data.details.counselor_image",
                "data.details.counselor_name",
                "data.details.course_id",
                "data.details.course_name",
                "data.details.created_on",
                "data.details.event_date",
                "data.details.event_label",
                "data.details.event_type",
                "data.details.generate_link",
                "data.details.image",
                "data.details.instructor_image",
                "data.details.instructor_name",
                "data.details.invoice_id",
                "data.details.link_amount",
                "data.details.link_status",
                "data.details.name",
                "data.details.org_admin.id",
                "data.details.org_admin.image",
                "data.details.org_admin.name",
                "data.details.payment_link",
                "data.details.payment_method",
                "data.details.receipt_id",
                "data.details.user_id",
                "data.details.payment_id",
                "data.details.enrollment_id",
                "data.details.uuid"
            ],
            "query" => [
                "bool" => [
                    "must" => $mustConditions,
                    //"must_not" => $mustNotConditions
                ]
            ],
            "sort" => [
                [
                    "data.details.event_date" => "desc"
                ]
            ] // need to change the mapping of this key for sorting
        ];

        $query['custom'] = $elasticQuery;
        $query['qryStr'] = [
            "from" => $offset,
            "size" => $limit
        ];

        error_log("getPaymentsFilters - query  " . date("Y-m-d H:i:s") . " === ID: " . json_encode($elasticQuery) . "\n\n", 3, ABSPATH . "error-logs/payment_insights.log");

        $paymentDataResponse = $this->paymentModel->getPayments($query);

        if (is_array($paymentDataResponse) && count($paymentDataResponse)) {
            if ($viewType === "list") {
                return $this->response->success('GET_SUCCESS', $paymentDataResponse, ['message' => 'Payments found']);
            }

            // Define columns
            $columns = [
                [
                    "field" => "id",
                    "label" => "Transaction ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "date.time",
                    "label" => "Transaction Time",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "date.timezone",
                    "label" => "Timezone",
                    "tooltip" => "",
                    "sortable" => false
                ],
                [
                    "field" => "mode",
                    "label" => "Payment Mode",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "full_part",
                    "label" => "Payment Type",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "item.type",
                    "label" => "Item Type",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "item.item_id",
                    "label" => "Item ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "gateway.platform",
                    "label" => "Gateway Platform",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "gateway.name",
                    "label" => "Gateway Name",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "gateway.fav_icon.url",
                    "label" => "Fav Icon URL",
                    "tooltip" => "",
                    "sortable" => false
                ],
                [
                    "field" => "gateway.fav_icon.alt_text",
                    "label" => "Fav Icon Alt Text",
                    "tooltip" => "",
                    "sortable" => false
                ],
                [
                    "field" => "gateway.transaction_id",
                    "label" => "Gateway Transaction ID",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "currency.name",
                    "label" => "Currency",
                    "tooltip" => "",
                    "sortable" => false
                ],
                [
                    "field" => "amount",
                    "label" => "Amount",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "tax.type",
                    "label" => "Tax Type",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "tax.label",
                    "label" => "Tax Label",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "tax.percentage",
                    "label" => "Tax Percentage",
                    "tooltip" => "",
                    "sortable" => true
                ],
                [
                    "field" => "tax.amount",
                    "label" => "Tax Amount",
                    "tooltip" => "",
                    "sortable" => true
                ]
            ];

            $paymentData = [
                "rows" => $paymentDataResponse['data'],
                "columns" => $columns
            ];

            return $this->response->success('GET_SUCCESS', ['data' => $paymentData, 'count' => $paymentDataResponse['count']], ['message' => 'Payments found']);
        }

        return $this->response->error('GET_FAIL', ['message' => "You'll see their payments here"]);
    }

    /**
     * Retrieves and applies payment filters based on the user's role and query parameters.
     *
     * This function ensures that only authorized users can access specific filters 
     * based on their role. It logs all requests for debugging purposes.
     *
     * @package V4
     * @since 1.0.0
     * <AUTHOR>
     *
     * @param WP_REST_Request $request The incoming request containing query parameters.
     * 
     * Query parameters:
     * - `user_id` (int)       : The ID of the requesting user.
     * - `referrals` (string)  : Referral status (default: 'all').
     * - `days` (int)          : The number of days for payment filtering.
     * - `status` (string)     : Payment status (default: 'all').
     * - `counselor_id` (int)  : The ID of the assigned counselor.
     *
     * @return WP_REST_Response JSON response containing available payment filters 
     *                          based on the user's role.
     * @throws Exception If an unexpected error occurs.
     */
    public function getPaymentsFilters($request)
    {
        try {
            $logFile = ABSPATH . "error-logs/get_payments_filters.log";

            // Log the request
            error_log("getPaymentsFilters - Request received at " . date("Y-m-d H:i:s") . " === " . json_encode($request->get_query_params()) . "\n\n", 3, $logFile);

            // Extract query parameters
            $queryParams = $request->get_query_params();

            $userId = (int) $queryParams['user_id'] ?? 0;
            $days = (int) $queryParams['days'] ?? 0;
            $status = $queryParams['status'] ?? 'all';
            $counselorId = (int) $queryParams['counselor_id'] ?? 0;

            // Get user role based on userId
            $userRole = $this->userModel->getUserRole($userId);

            // Restrict access to filters based on role
            switch ($userRole) {
                case 'yuno-admin':
                    //  Yuno Admin: Has access to all payment filters
                    break;

                case 'org-admin':
                    if ($counselorId !== 0) {
                        return $this->response->error('ACCESS_DENIED', ['message' => 'Org Admins cannot filter by referrer or counselor']);
                    }
                    break;

                default:
                    return $this->response->error('ACCESS_DENIED', ['message' => 'Unauthorized user role']);
            }

            // Initialize filters based on role
            $filters = [];

            switch ($userRole) {
                case 'yuno-admin':
                    $filters = [
                        $this->counselorModel->generatePaymentmentCounselorFilters($userId, $counselorId),
                        $this->paymentModel->generatePaymentDaysFilters($days),
                        $this->paymentModel->generateEnrollmentPaymentFilters($status)
                    ];
                    break;

                case 'org-admin':
                    $filters = [
                        $this->paymentModel->generateEnrollmentPaymentFilters($status),
                        $this->paymentModel->generatePaymentDaysFilters($days)
                    ];
                    break;
            }

            // Step 4: Return 204 if no data is found
            if (empty(array_filter($filters))) {
                return $this->response->error('GET_FAIL', ['message' => 'No filters found']);
            }

            return $this->response->success('GET_SUCCESS', $filters, ['message' => 'Payment filters retrieved successfully']);
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $e->getMessage()]);
        }
    }
    /**
     * Retrieves payment details by payment ID.
     *
     * This function fetches payment details based on the provided payment ID.
     * It returns a success response with the payment details or an error response if not found.
     *
     * @package V4
     * @since 1.0.0
     * <AUTHOR>
     *
     * @param WP_REST_Request $request The incoming request containing the payment ID.
     *
     * @return WP_REST_Response JSON response containing the payment details or an error message.
     */
    public function getPayment($request)
    {
        try {
            // Log the request
            ynLog("getPaymentDetails - Request received at " . date("Y-m-d H:i:s") . " === " . json_encode($request->get_query_params()), 'getPaymentDetails');
            // Extract payment ID from the request
            $paymentId = isset($request['id']) ? (int) $request['id'] : 0;

            // Validation required fields update with V4 validate with nested array
            $validationChecks = [
                'id' => 'numeric'
            ];

            foreach ($validationChecks as $key => $type) {
                $result = $this->validate->validateRequired($request, $key, $type);
                if (is_wp_error($result)) {
                    return $result;
                }
            }

            // Fetch payment details using the model
            $paymentDetails = $this->paymentModel->getPayment($paymentId);

            // Check if payment details were found
            if (!empty($paymentDetails)) {
                return $this->response->success('GET_SUCCESS', $paymentDetails, ['message' => 'Payment details retrieved successfully']);
            } else {
                return $this->response->error('NOT_FOUND', ['message' => 'Payment not found']);
            }
        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => 'Error logging request: ' . $e->getMessage()]);
        }
    }
}
