Vue.component('yuno-autocomplete-search', {
    props: {
        data: {
            type: Object,
            required: true
        },
        defaultFilters: {
            type: Object,
            required: true
        },
        payload: {
            type: Object,
            required: true
        }
    },
    template: `
        <div class="yunoAutocompleteSearch" :class="[data.filter]">
            <b-field>
                <b-autocomplete
                    v-model="data.current"
                    :clearable="true"
                    :placeholder="data.placeholder"
                    :data="data.items"
                    :loading="data.loading"
                    :field="data.search_field"
                    :disabled="data.is_disabled"
                    autocomplete="search"
                    @typing="onSearchType($event, data)"
                    @select="onSearchItemSelect($event, data)"
                >
                    <template slot="empty">No results for {{selectedOption}}</template>
                    <template slot-scope="props">
                        {{props.option[data.search_field]}}
                    </template>
                </b-autocomplete>  
            </b-field>
        </div>
    `,
    data() {
        return {
            selectedOption: ""
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'filterResult'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onSearchType: _.debounce(function (name, filter) {
            if (name.length > 2) {
                filter.loading = true;
                filter.items = [];
                this.$emit('onQuerySearch', name, filter, false);
            } else {
                filter.items = []
                return
            }
        }, 500),
        onSearchItemSelect(e, filter) {
            if (e !== null) {
                filter.selected = e[this.$props.data.id_field];
            } else {
                filter.selected = null;
                this.filterResult.payload[filter.filter] = this.defaultFilters[filter.filter];
            };
            
            this.$emit('onQuerySearch', name, filter, true);
        },
        searchFocusToggle(filter, type) {
            // if (type === 'focus') {
            //     filter.is_focus = true;

            //     if (filter.current === "" && filter.selected !== null) {
            //         filter.selected = null;
            //         // Event.$emit('onSearchItemSelect', filter);
            //     }
                
            // } else {
            //     filter.is_focus = false;
            // }
        },
    }
});