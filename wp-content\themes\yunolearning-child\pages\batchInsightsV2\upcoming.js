Vue.component('yuno-upcoming', {
    props: {
        storage: {
            type: Object,
            required: true
        },
        apiURL: {
            type: Object,
            required: true,
        },
        filterAPIURL: {
            type: Object,
            required: true,
        }
    },
    template: `
        <div>   
            <b-loading :is-full-page="true" v-model="isLoading" :can-cancel="false"></b-loading>
            <yuno-table-grid
                :apiURL="apiURL"
                :filterAPIURL="filterAPIURL"
                apiMethodType="GET"
                :payload="filterResult.payload"
                :sessionStorage="storage"
                emptyStateImg="/assets/images/group.png"
                @onRefineFilters="onRefineFilters"
                @onQuerySearch="onQuerySearch"
                @onSearchInput="onSearchInput"
                @manageDrawer="manageDrawer"
                @gotFilters="gotFilters"
                @onPageChange="onPageChange"
                @gotGrid="onGotGrid"
                @initAction="initAction"
                recordFoundCaption="Batches found"
                :errorSlot="true"
            >
                <template v-slot:stats>
                    <yuno-date 
                        v-if="filterResult.payload.course_id !== 0" 
                        :data="dateRange" 
                        :options="filterResult.payload"
                        @onDateChange="onDateChange"
                    >
                    </yuno-date>
                </template>
                <template v-slot:emptyState>
                    <yuno-empty-state-v2 
                        v-if="filterResult.errorData === 'course_id is required.'"
                        :options="{'type': 'noDataFound', 'message': 'Choose a course above to see its batches'}"
                        image="/assets/images/choose.png"
                    >
                    </yuno-empty-state-v2>
                    <yuno-empty-state-v2 
                        v-else
                        :options="{'type': 'noDataFound', 'message': filterResult.errorData}"
                        image="/assets/images/group.png"
                    >
                    </yuno-empty-state-v2>
                </template>
            </yuno-table-grid>
            <b-modal 
                :active.sync="drawerModal.modal" 
                :width="drawerModal.width" 
                animation="slide-out"
                :can-cancel="['escape', 'x']"
                :on-cancel="closeDrawer"
                class="yunoModal drawerModal">
                    <template v-if="drawerModal.modal">
                        <div class="scrollable">
                            <template v-if="drawerModal.type === 'id'">
                                <template v-if="drawer.loading">
                                    <div class="loaderWrapper">
                                        <div class="smallLoader"></div>
                                    </div>
                                </template>
                                <template v-if="drawer.success && drawerModal.modal">
                                    <template v-if="drawer.error">
                                        <h2 class="drawerTitle">Active Learners</h2>
                                        <yuno-empty-states :options="{'state': 'dataNotFound', 'description': drawer.errorData}"></yuno-empty-states>
                                    </template>
                                    <template v-else>
                                        <div class="scrollable">
                                            <h2 class="drawerTitle">Active Learners ({{ drawer.data.length }})</h2>
                                            <div class="learners">
                                                <template v-for="(user, u) in drawer.data">
                                                    <figure :key="u" class="userImg">
                                                        <img :src="user.image_url" :alt="user.full_name">
                                                        <figcaption>
                                                            <h3 class="primaryTitle">{{ user.full_name }}</h3>
                                                        </figcaption>
                                                    </figure>
                                                </template>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                            </template>
                        </div>
                    </template>
            </b-modal>
        </div>
    `,
    data() {
        return {
            isLoading: false,
            drawerModal: {
                modal: false,
                data: [],
                width: 1000,
                type: ""
            },
            dateRange: [],
            academies: [],
        }
    },
    computed: {
        ...Vuex.mapState([
            'userInfo',
            'filters',
            'filterResult',
            'userRole',
            'drawer',
            'orgAdmin',
            'counsellorList',
            'allCourses',
            'allBatches',
            'learnerInsightsClass'
        ]),
    },
    mounted() {
        this.fetchModules();
    },
    methods: {
        gotLearners(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            };
        },
        fetchLearners(batchID) {
            this.drawer.data = [];
            this.drawer.error = null;
            this.drawer.success = false;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.batch("learners", {batchID: batchID}),
                module: "gotData",
                store: "drawer",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotLearners(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        onTabChanged() {
            
        },
        onDateChange(data) {
            this.filterResult.payload.enrollment_request_date = data.slug;
            this.filterResult.refreshTable = true;
        },
        addDays(date, days) {
            const result = new Date(date);
            result.setDate(result.getDate() + days);
            return result;
        },
        manageDateRange() {
            const startDate = this.addDays(new Date(), 0);
            const endDate = this.addDays(startDate, 10);
            
            const current = new Date(startDate.getTime());
            
            const dates = [];
            while (current < endDate) {
                const year = current.getFullYear();
                const month = String(current.getMonth() + 1).padStart(2, '0');
                const day = String(current.getDate()).padStart(2, '0');
                
                // Note: toDateString() format can be inconsistent across JS engines.
                // 'ddd MMM DD' format is recreated manually for consistency.
                const weekDay = current.toDateString().substring(0, 3);
                const monthName = current.toDateString().substring(4, 7);

                dates.push({
                    label: `${weekDay} ${monthName} ${day}`,
                    slug: `${year}-${month}-${day}`
                });
                current.setDate(current.getDate() + 1);
            };

            this.dateRange = dates;

            if (this.dateRange.length > 1) {
                this.filterResult.payload.enrollment_request_date = this.dateRange[1].slug;
            }
        },
        fetchModules() {
            this.manageDateRange();

            if (this.userInfo.data.role === "org-admin") {
                this.fetchAcademies();
            }
        },
        endBatchDone(options, row, action) {
            this.isLoading = false;

            const response = options.response?.data;
            const message = response?.message;
            const isSuccess = response?.code === 201;

            this.$buefy.toast.open({
                duration: 5000,
                message: message,
                position: 'is-bottom',
                type: isSuccess ? 'is-success' : 'is-danger'
            });
        },            
        initEndBatch(row, action) {
            this.isLoading = true;

            const options = {
                apiURL: YUNOCommon.config.endBatch(row.id),
                module: "gotData",
                store: "updateLink",
                callback: true,
                callbackFunc: (options) => this.endBatchDone(options, action, row)
            };

            this.$store.dispatch('putData', options);
        },
        initAction(row, action) {
            switch (action.slug) {
                case "endBatch":
                    let title = "End Batch",
                        msg = "Are you sure you want to end this batch";

                    this.$buefy.dialog.confirm({
                        title: title,
                        message: "Are you sure you want to end this batch",
                        cancelText: 'Cancel',
                        confirmText: 'Yes',
                        type: 'is-danger',
                        onConfirm: () => this.initEndBatch(row, action)
                    });    
                    break;
            }
        },
        isFilterAvailable(data) {
            return YUNOCommon.findInArray(data, this.userRole.data);
        },
        getPersonalisation(personalisation) {
            if (personalisation === "1-to-1") {
                return "one_to_one";
            } else if (personalisation === "group") {
                return "one_to_many";
            } else {
                return "";
            }
        },
        onGotGrid(data) {
            const rows = data.rows;
            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];
                
                row.actions = [
                    {
                        label: "Edit Batch",
                        is_active: this.isFilterAvailable(["yuno-admin", "org-admin"]),
                        slug: "editBatch",
                        active_class: "material-icons-outlined",
                        url: "/create-batch/?classSize="+ this.getPersonalisation(row.personalisation.name) +"&isEdit="+ row.id +"",
                        link_target: "_blank",
                        icon: {
                            type: "mdl",
                            class: "material-icons-outlined",
                            hover: "material-icons",
                            font: "edit"
                        }
                    },
                    {
                        label: "New Enrollment",
                        is_active: this.isFilterAvailable(["yuno-admin", "org-admin"]),
                        slug: "newEnrollment",
                        active_class: "material-icons-outlined",
                        url: `/generate-link/?courseID=${row.course[0].id}-${row.id}`,
                        link_target: "_blank",
                        icon: {
                            type: "mdl",
                            class: "material-icons-outlined",
                            hover: "material-icons",
                            font: "person_add_alt_1"
                        }
                    },
                    {
                        label: "End batch",
                        is_active: this.isFilterAvailable(["yuno-admin"]),
                        slug: "endBatch",
                        active_class: "material-icons-outlined",
                        url: false,
                        link_target: "",
                        icon: {
                            type: "mdl",
                            class: "material-icons-outlined",
                            hover: "material-icons",
                            font: "unpublished"
                        }
                    }
                ];
            }
        },
        onPageChange() {
            this.$emit("onPageChange");
        },
        onRefineFilters(filters) {
            // Implementation if needed
        },
        onQuerySearch(name, filter, isSelect) {
            if (isSelect) {
                const payload = this.filterResult.payload;
                const filters = this.filters.data;
                payload[filter.filter] = filter.selected ?? 0;

                if (payload.course_id !== 0) {
                    filters.forEach(filter => {
                        filter.is_disabled = false;
                    });
                } else {
                    filters.forEach(filter => {
                        filter.is_disabled = true;

                        if (filter.filter === "course_id") {
                            filter.is_disabled = false;
                        }
                    });
                }

            } else {
                switch (filter.filter) {
                    case "course_id":
                        if (this.userInfo.data.role === "org-admin") {
                            const payload = {
                                "category": [0],
                                "academy": this.academies.map(academy => academy.id),
                                "text": name
                            }
                            this.fetchCourseSuggestions(name, filter, payload);
                        } else {
                            const payload = {
                                "category": [0],
                                "academy": [0],
                                "text": name
                            }
                            this.fetchCourseSuggestions(name, filter, payload);
                        }
                        break;
                    case "instructor_id":
                        this.fetchInstructorSuggestions(name, filter);
                        break;
                }
            }
        },
        onSearchInput(name, filter) {
            // Implementation if needed
        },
        manageDrawer(row, col) {
            this.drawerModal.data = row;
            this.drawerModal.modal = true;    
            this.drawerModal.type = "";

            if (col === "id") {
                this.drawerModal.type = "id";
                this.fetchLearners(row.id);
            }
        },
        closeDrawer() {
            this.drawerModal.modal = false;
            this.drawerModal.data = [];
            this.drawerModal.type = "";
        },
        activeOrg() {
            return this.userInfo.data.current_state.org_id || 0;
        },
        gotFilters(data) {
            const filters = data;

            filters.forEach(filter => {
                filter.search_field = "";
                filter.current = "";
                filter.id_field = "";
                filter.is_disabled = true;
        
                switch (filter.filter) {
                    case "course_id":
                        filter.search_field = "title";
                        filter.current = filter.items.find(item => item.id === filter.selected)?.title || "";
                        filter.id_field = "id";
                        filter.is_disabled = false;
                        break;
                    case "instructor_id":
                        filter.search_field = "name";
                        filter.current = filter.selected ? filter.selected.name : "";
                        filter.id_field = "user_id";
                        break;
                }
            });


            const payload = this.filterResult.payload;

            if (payload.course_id !== 0) {
                filters.forEach(filter => {
                    filter.is_disabled = false;
                });
            } else {
                filters.forEach(filter => {
                    filter.is_disabled = true;

                    if (filter.filter === "course_id") {
                        filter.is_disabled = false;
                    }
                });
            }
            

            // if (this.userRole.data.includes("yuno-admin")) {
            //     this.fetchOrgAdminList(organization, "all");
            //     this.fetchCounsellorList(counselor);    
            // }

            
        },
        gotAcademies(options) {
            if (options.response?.data?.code === 200) {
                const { data } = options.response.data;
                this.academies = data;
            }
        },
        fetchAcademies() {
            const props = {
                view_type: "list",
                params: `?orgId=${this.activeOrg()}`
            }
            const options = {
                apiURL: YUNOCommon.config.academy("academiesV2", props),
                module: "gotData",
                store: "updateLink",
                callback: true,
                callbackFunc: (options) => this.gotAcademies(options)
            };
        
            this.$store.dispatch('fetchData', options);
        },
        gotCourseSuggestions(options, filter) {
            filter.loading = false;
            if (options.response?.data?.code === 200) {
                const { course } = options.response.data.data;
                if (course) {
                    filter.items.push(...course);
                }

                console.log(filter);
            }
        },
        fetchCourseSuggestions(name, filter, payload) {
            const options = {
                apiURL: YUNOCommon.config.courseV4("suggestions", false),
                module: "gotData",
                store: "searchSuggestions",
                payload: payload,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callback: true,
                callbackFunc: (options) => this.gotCourseSuggestions(options, filter)
            };
        
            this.$store.dispatch('postData', options);
        },
        gotInstructorSuggestions(options, filter) {
            filter.loading = false;
            if (options.response?.data?.code === 200) {
                filter.items = options.response.data.data;
            }
        },
        fetchInstructorSuggestions(name, filter) {
            const options = {
                apiURL: YUNOCommon.config.generic("userSearch", name, "instructor", this.activeOrg()),
                module: "gotData",
                store: "searchSuggestions",
                callback: true,
                callbackFunc: (options) => this.gotInstructorSuggestions(options, filter)
            };
        
            this.$store.dispatch('fetchData', options);
        },
    }
});