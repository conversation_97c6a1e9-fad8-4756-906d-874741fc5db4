.fontAwesomeIcon, #app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-previous .icon .mdi-chevron-left:after,
#app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-next .icon .mdi-chevron-left:after,
#app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@font-face {
  font-family: "Material Icons Outlined";
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/material-Icons.woff2?6qrc5l") format("woff2");
}
@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/material-Icons-filled.woff2?8qrc5l") format("woff2");
}
@font-face {
  font-family: "FontAwesome";
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/fontawesome-webfont.woff2?v=4.7.0") format("woff2");
}
#app .yunoListPlaces .mainHeader h1 {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .secondary {
  color: #000000;
}
#app .capitalize {
  text-transform: capitalize;
}
#app .yunoListPlaces .mainHeader {
  margin-bottom: 20px;
}
#app .yunoListPlaces .mainHeader .block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
}
#app .yunoListPlaces .mainHeader h1 {
  margin: 14px 0 0 20px;
}
@media (min-width: 768px) {
  #app .yunoListPlaces .mainHeader h1 {
    margin: 0;
  }
}
#app .yunoListPlaces .listPlaces .b-table {
  margin-bottom: 30px;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table thead tr th {
  color: #201A19;
  font-size: 14px;
  line-height: 18px;
  font-weight: 500;
  margin-bottom: 0;
  letter-spacing: 0.1px;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tbody tr td {
  color: #534342;
  font-size: 14px;
  line-height: 18px;
  font-weight: 400;
  margin-bottom: 0;
  padding: 5px 8px;
  letter-spacing: 0.25px;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tbody tr td .rowLabel {
  display: flex;
  margin-top: 5px;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tbody tr td .arrowIcon {
  cursor: pointer;
  margin-top: 2px;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail {
  background-color: #FFFBFF;
  box-shadow: none;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail td {
  padding: 0;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container {
  border: 1px solid #E6E6E6;
  padding: 25px 48px;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container .box {
  box-shadow: none;
  background: none;
  padding: 0;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container .box:not(:last-child) {
  border-bottom: 1px solid #E6E6E6;
  padding-bottom: 19px;
  margin-bottom: 19px;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container .box .wrapper {
  display: flex;
  flex-wrap: wrap;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container .box .wrapper li {
  position: relative;
  white-space: nowrap;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container .box .wrapper li::before {
  content: "|";
  color: #E6E6E6;
  margin: 0 8px;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container .box .wrapper li:first-child::before, #app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container .box .wrapper li:last-child::before {
  content: "";
  margin: 0;
}
#app .yunoListPlaces .listPlaces .b-table .table-wrapper .table tr.detail .detail-container .box .facilities {
  width: 100%;
}
#app .yunoListPlaces .listPlaces .b-table .pagination-link {
  color: #000;
}
#app .yunoListPlaces .listPlaces .b-table .pagination-link:hover {
  text-decoration: none;
}
#app .yunoListPlaces .listPlaces .b-table .pagination-link.is-current {
  background-color: #A81E22;
  border-color: #A81E22;
  color: #FFF;
}
#app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-previous .icon .mdi-chevron-left:after,
#app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-next .icon .mdi-chevron-left:after,
#app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  content: "\f060";
}
#app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoListPlaces .listPlaces .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  content: "\f061";
}/*# sourceMappingURL=listPlaces.css.map */