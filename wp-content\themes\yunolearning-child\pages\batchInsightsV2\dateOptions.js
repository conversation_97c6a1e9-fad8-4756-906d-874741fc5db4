Vue.component('yuno-date', {
    props: ["data", "options"],
    template: `
        <section class="dateOptions">
            <b-field>
                <template v-for="(date, i) in data">
                    <b-radio-button 
                        v-model="options.enrollment_request_date"
                        @input="onDateChange(date)"
                        :key="i"
                        :native-value="date.slug">
                        <b-tooltip label="Enrollments start date"
                            type="is-dark"
                            position="is-right">
                            <span>{{ date.label }}</span>    
                        </b-tooltip>
                    </b-radio-button>
                </template>
            </b-field>
        </section>
    `,
    data() {
        return {
            radioButton: ''
        }
    },
    computed: {
        
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onDateChange(data) {
            this.$emit('onDateChange', data);
        }
    }
});