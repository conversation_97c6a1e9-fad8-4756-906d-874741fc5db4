@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";



#app {

    .dark87 {
        @include setFontColor($primaryCopyColor, 0.87);
    }
    
    .dark60 {
        @include setFontColor($primaryCopyColor, 0.6); 
    }

    .dark38 {
        @include setFontColor($primaryCopyColor, 0.38);
    }

    .pageGrid .mainBody {
        position: relative;
        z-index: 7;
    }

    .dateOptions {
        overflow-y: auto;
        margin-top: $gap15;

        @media (min-width: 768px) {
            overflow-y: visible;
        }

        .button {
            font-size: $caption1;
            &.is-primary {
                background-color: $primary;
            }
        }

        .field.has-addons .control:first-child:not(:only-child) .button {
            border-bottom-right-radius: 0;
            border-top-right-radius: 0;
        }

        .field.has-addons .control:not(:first-child):not(:last-child) .button {
            border-radius: 0;
        }
    }

    .mainHeader {
        margin: $gap15 0;
        background-color: #FFF;
        position: sticky;
        top: 0;
        z-index: 2;

        @media (max-width: 767px) {
            margin: 30px 0 15px 0;
            padding-left: 18px;
        }

        .block {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0;
        }

        .pageTitle {
            @include setFont($headline5, 28px, 500, 0);
            display: flex;
            align-items: center;
    
            a { 
                position: relative;
                top: 4px;
                font-size: 18px;
                margin-left: $gapSmaller;
    
                &:hover {
                    text-decoration: none;
                }
            }
        }

        .gridInfo {
            display: flex;
            justify-content: space-between;
            margin-top: $gap15;
            align-items: center;
    
            .note {
                @include setFont($subtitle2, normal, 400, 0);
                @extend .dark60;
    
                span {
                    font-weight: 500;
                    @extend .dark87;
                }
            }
    
            .actions {
                @extend .dark87;
                text-decoration: none;
                display: flex;
                margin: 0 (-$gapSmaller);

                li {
                    padding: 0 $gapSmaller;

                    a {
                        @extend .dark87;
                    }
                }
            }
        }
    }

    
    
}