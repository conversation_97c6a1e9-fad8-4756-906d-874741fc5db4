window.Event = new Vue();

const validationMsg = {
  messages: {
    required: "This field is required",
    numeric: "Numbers only",
    min: "Minimum 10 numbers required",
    max: "Maximum 15 numbers required ",
    is_not: "New batch shouldn't be same as current batch",
  },
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component("yuno-add-classroom", {
  template: `
        <yuno-page-grid
            :authorizedRoles="authorizedRoles"
            @onUserInfo="onUserInfo"
            :hasSearchBar="false"
			:hasLHSMenu="false"
        >
            <template v-slot:main>
                <div class="container-fluid">
                    <template v-if="moduleWithoutTab.loading">
                        <figure class="infiniteSpinner">
                            <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                        </figure>
                    </template>
                    <template v-if="moduleWithoutTab.success">
                        <div class="add-classroom">
                            <div class="container">
								<div class="wrapper" :class="{ hideSearchField }">
									<div class="mainHeader">
										<h1>Add Classroom</h1>
									</div>
									<div class="classroomContainer" :class="{'details': hideSearchField}">
										<template v-if="!hideSearchField">
											<yuno-search-location 
												:map="map"
												@confirmLocation="showClassroomDetail"
												@updateSelectedLocation="updateSelectedLocation"
											>
											</yuno-search-location>
										</template>
										<template v-else>
											<yuno-classroom-detail
												ref="classroomDetail"
												@hideClassroomDetail="hideClassroomDetail"
											>
											</yuno-classroom-detail>
										</template>
										<div class="mapWrapper" ref="googleMap"></div>
									</div>
								</div>
                            </div>
                        </div>
                    </template>
                </div>
            </template>
        </yuno-page-grid>
    `,
  data() {
    return {
      apiURL: null,
      isMiniSidebar: false,
      pageHeader: {
        title: "Org Settings",
      },
      authorizedRoles: ["org-admin"],
      storage: {
        name: "orgSettings",
        version: 1,
      },
      hideSearchField: false,
      map: null,
      marker: null,
      geocoder: null,
    };
  },
  computed: {
    ...Vuex.mapState([
      "user",
      "userInfo",
      "header",
      "userProfile",
      "userRole",
      "filters",
      "filterResult",
      "moduleWithoutTab",
      "form",
      "timeSlots",
    ]),
    wpThemeURL() {
      return this.$store.state.themeURL;
    },
    isUserAuthorized: {
      get() {
        if (YUNOCommon.findInArray(this.authorizedRoles, this.userRole.data)) {
          return true;
        } else {
          return false;
        }
      },
    },
    emptyStates() {
      return {
        state: "notAuthorized",
      };
    },
    isPageLoading: {
      get() {
        const module = this.userInfo.loading;

        return module;
      },
    },
    isPageReady: {
      get() {
        let module = "";

        if (this.user.isLoggedin) {
          module = this.userInfo.success;
        } else {
          module = true;
        }

        return module;
      },
    },
  },
  methods: {
    initGoogleMap() {
      if (!this.$refs.googleMap) {
        console.error("Map container not found");
        return;
      }

      // Initialize Google Maps
      const mapOptions = {
        zoom: 12,
        center: { lat: 28.6139, lng: 77.209 }, // Default to New Delhi coordinates
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: true,
        draggable: true, // Disable map dragging
        zoomControl: true, // Disable zoom control
      };

      try {
        this.map = new google.maps.Map(this.$refs.googleMap, mapOptions);
        this.geocoder = new google.maps.Geocoder();
      } catch (error) {
        console.error("Error initializing Google Maps:", error);
      }
    },
    updateSelectedLocation(place) {
      // Update map center and zoom
      this.map.setCenter(place.geometry.location);
      this.map.setZoom(15);

      // Clear existing marker if any
      if (this.marker) {
        this.marker.setMap(null);
      }
    },
    showClassroomDetail() {
      this.hideSearchField = true;
    },
    hideClassroomDetail() {
      this.hideSearchField = false;
    },
    /**
     * Handles the user information.
     *
     * @param {Object} data - The user information data.
     */
    onUserInfo(data) {
      if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
        // this.setupTabs();
        this.fetchOrgInfo();
      }
    },
    setupForm() {
      const defaultFieldProperties = {
        isRequired: false,
        isDisabled: false,
        isLoading: false,
      };

      this.form.fields = [
        {
          label: "School / College / Commercial Building ",
          placeholder: "Select",
          type: "dropdown",
          name: "type",
          options: [
            {
              label: "School",
              value: "SCHOOL",
            },
            {
              label: "College",
              value: "COLLEGE",
            },
            {
              label: "Commercial Building ",
              value: "COMMERCIAL_BUILDING",
            },
          ],
          required: true,
        },
        {
          label: "Name of Place",
          placeholder: "Enter the name of place",
          type: "text",
          name: "name",
          required: true,
        },
        {
          label: "Short Description",
          placeholder: "Enter short description here",
          type: "textarea",
          name: "short_description",
		  min: 10,
		  max: 150,
          required: true,
        },
        {
          label: "Long Description",
          placeholder: "Enter long description here",
          type: "textarea",
          name: "long_description",
		  min: 20,
		  max: 5000,
          required: true,
        },
        {
          label: "Complete Address",
          placeholder: "Enter address",
          type: "text",
          name: "formatted_address",
          readonly: true,
          ...defaultFieldProperties,
        },
        {
          label: "Address",
          placeholder: "Enter address",
          type: "text",
          name: "address_1",
          required: true,
        },
        {
          label: "Floor (Optional)",
          placeholder: "Enter floor",
          type: "text",
          name: "floor",
          ...defaultFieldProperties,
        },
        {
          label: "Landmark (Optional)",
          placeholder: "Enter landmark",
          type: "text",
          name: "landmark",
          ...defaultFieldProperties,
        },
        {
          label: "Area / Sector / Locality (Optional)",
          placeholder: "Enter area / sector / locality",
          type: "text",
          name: "address_2",
          ...defaultFieldProperties,
        },
        {
          title: "Facilities",
          class: "faciltiesCheckbox",
          isGrouped: true,
          fields: [
            {
              label: "Car Parking",
              placeholder: "",
              type: "checkbox",
              name: "car_parking",
              hasChildren: true,
              ...defaultFieldProperties,
            },
            {
              label: "Self Parking",
              placeholder: "",
              type: "checkbox",
              name: "self_parking",
              class: "addMargin",
              ...defaultFieldProperties,
            },
            {
              label: "Valet Parking",
              placeholder: "",
              type: "checkbox",
              name: "valet_parking",
              class: "addMargin",
              ...defaultFieldProperties,
            },
            {
              label: "Bike Parking",
              placeholder: "",
              type: "checkbox",
              name: "bike_parking",
              ...defaultFieldProperties,
            },
          ],
        },
        {
          title: "Open Hours (Only of places)",
          type: "timeSlots",
        },
        {
          title: "Classroom Detail",
          type: "classDetailForm",
          class: "classDetailForm",
          classrooms: [
            {
              fields: [
                {
                  label: "Class Title",
                  placeholder: "Enter class title",
                  type: "text",
                  name: "class_title",
                  required: true,
                },
                {
                  type: "groupedTextDropdown",
                  class: "classroomFloor",
                  groups: [
                    {
                      label: "Floor",
                      placeholder: "GroundPlus/OnePlus",
                      type: "dropdown",
                      name: "classroom_floor_type",
                      options: [
                        {
                          label: "GroundPlus",
                          value: "GROUND_PLUS",
                        },
                        {
                          label: "OnePlus",
                          value: "ONE_PLUS",
                        },
                      ],
                      required: true,
                    },
                    {
                      label: "Floor Number",
                      placeholder: "Enter floor number",
                      type: "text",
                      name: "classroom_floor_number",
                      required: true,
                    },
                  ],
                },
                {
                  label: "Classroom area (Sqft)",
                  placeholder: "Select",
                  type: "dropdown",
                  name: "classroom_area",
                  options: Array.from({ length: 50 }, (_, i) => ({
                    label: `${i + 1}`,
                    value: `${i + 1}`,
                  })),
                  required: true,
                },
                {
                  label: "Seating Capacity",
                  placeholder: "Enter seating capacity",
                  type: "text",
                  name: "seating_capacity",
                  required: true,
                },
                {
                  title: "Facilities",
                  class: "groupCheckbox",
                  isGrid: "true",
                  type: "gridCheckbox",
                  fields: [
                    {
                      label: "Wifi",
                      type: "checkbox",
                      name: "wifi",
                    },
                    {
                      label: "Whiteboard",
                      type: "checkbox",
                      name: "whiteboard",
                    },
                    {
                      label: "Blackboard",
                      type: "checkbox",
                      name: "blackboard",
                    },
                    {
                      label: "Projector",
                      type: "checkbox",
                      name: "projector",
                    },
                    {
                      label: "LCD Monitor",
                      type: "checkbox",
                      name: "lcd_monitor",
                    },
                    {
                      label: "Air Conditioning",
                      type: "checkbox",
                      name: "air_conditioning",
                    },
                    {
                      label: "Power Backup",
                      type: "checkbox",
                      name: "power_backup",
                    },
                  ],
                },
                {
                  label: "Computer Terminal",
                  placeholder: "Computer Terminal",
                  type: "text",
                  name: "computer_terminals",
                  required: true,
                },
              ],
            },
          ],
        },
      ];

      const payload = {
        org_id: this.activeOrg(),
        type: null,
        name: "",
        short_description: "",
        long_description: "",
        formatted_address: this.searchQuery,
        address_1: "",
        address_2: "",
        floor: "",
        floor_type: "",
        landmark: "",
        city: "",
        state_name: "",
        state_code: "",
        country_name: "",
        country_code: "",
        postal_code: "",
        car_parking: false,
        self_parking: false,
        valet_parking: false,
        bike_parking: false,
        class_title: "",
        classroom_floor_type: "",
        classroom_floor_number: "",
        seating_capacity: "",
        classroom_area: "",
        wifi: false,
        whiteboard: false,
        blackboard: false,
        projector: false,
        lcd_monitor: false,
        air_conditioning: false,
        power_backup: false,
        computer_terminals: 0,
        google_map: null,
      };
      this.form.payload = payload;
      this.timeSlots.data = [
        {
          day: "Monday",
          availability: [],
          isDayOff: true,
        },
        {
          day: "Tuesday",
          availability: [],
          isDayOff: true,
        },
        {
          day: "Wednesday",
          availability: [],
          isDayOff: true,
        },
        {
          day: "Thursday",
          availability: [],
          isDayOff: true,
        },
        {
          day: "Friday",
          availability: [],
          isDayOff: true,
        },
        {
          day: "Saturday",
          availability: [],
          isDayOff: true,
        },
        {
          day: "Sunday",
          availability: [],
          isDayOff: true,
        },
      ];
    },
    activeOrg() {
      const activeOrg = this.userInfo.data.current_state.org_id;

      if (activeOrg) {
        return activeOrg;
      }
    },
    gotOrgInfo(options) {
      const { response: { data: { code, data } = {} } = {} } = options;
      if (code === 200) {
        setTimeout(() => {
          this.initGoogleMap();
        }, 100);
        this.setupForm();
      }
    },
    fetchOrgInfo() {
      const options = {
        apiURL: YUNOCommon.config.generic(
          "org",
          false,
          false,
          this.activeOrg()
        ),
        module: "gotData",
        store: "moduleWithoutTab",
        callback: true,
        callbackFunc: this.gotOrgInfo,
      };

      this.$store.dispatch("fetchData", options);
    },
  },
});
