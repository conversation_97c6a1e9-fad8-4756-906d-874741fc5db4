/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function e(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function t(e,t,n,a){for(var o=-1,s=null==e?0:e.length;++o<s;){var i=e[o];t(a,i,n(i),e)}return a}function n(e,t){for(var n=-1,a=null==e?0:e.length;++n<a&&!1!==t(e[n],n,e););return e}function a(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function o(e,t){for(var n=-1,a=null==e?0:e.length;++n<a;)if(!t(e[n],n,e))return!1;return!0}function s(e,t){for(var n=-1,a=null==e?0:e.length,o=0,s=[];++n<a;){var i=e[n];t(i,n,e)&&(s[o++]=i)}return s}function i(e,t){return!(null==e||!e.length)&&g(e,t,0)>-1}function r(e,t,n){for(var a=-1,o=null==e?0:e.length;++a<o;)if(n(t,e[a]))return!0;return!1}function l(e,t){for(var n=-1,a=null==e?0:e.length,o=Array(a);++n<a;)o[n]=t(e[n],n,e);return o}function c(e,t){for(var n=-1,a=t.length,o=e.length;++n<a;)e[o+n]=t[n];return e}function u(e,t,n,a){var o=-1,s=null==e?0:e.length;for(a&&s&&(n=e[++o]);++o<s;)n=t(n,e[o],o,e);return n}function d(e,t,n,a){var o=null==e?0:e.length;for(a&&o&&(n=e[--o]);o--;)n=t(n,e[o],o,e);return n}function p(e,t){for(var n=-1,a=null==e?0:e.length;++n<a;)if(t(e[n],n,e))return!0;return!1}function m(e){return e.match(Ke)||[]}function f(e,t,n){var a;return n(e,function(e,n,o){if(t(e,n,o))return a=n,!1}),a}function h(e,t,n,a){for(var o=e.length,s=n+(a?1:-1);a?s--:++s<o;)if(t(e[s],s,e))return s;return-1}function g(e,t,n){return t==t?function(e,t,n){for(var a=n-1,o=e.length;++a<o;)if(e[a]===t)return a;return-1}(e,t,n):h(e,y,n)}function v(e,t,n,a){for(var o=n-1,s=e.length;++o<s;)if(a(e[o],t))return o;return-1}function y(e){return e!=e}function b(e,t){var n=null==e?0:e.length;return n?I(e,t)/n:X}function w(e){return function(t){return null==t?B:t[e]}}function _(e){return function(t){return null==e?B:e[t]}}function k(e,t,n,a,o){return o(e,function(e,o,s){n=a?(a=!1,e):t(n,e,o,s)}),n}function I(e,t){for(var n,a=-1,o=e.length;++a<o;){var s=t(e[a]);s!==B&&(n=n===B?s:n+s)}return n}function C(e,t){for(var n=-1,a=Array(e);++n<e;)a[n]=t(n);return a}function S(e){return e?e.slice(0,F(e)+1).replace(qe,""):e}function D(e){return function(t){return e(t)}}function L(e,t){return l(t,function(t){return e[t]})}function A(e,t){return e.has(t)}function j(e,t){for(var n=-1,a=e.length;++n<a&&g(t,e[n],0)>-1;);return n}function T(e,t){for(var n=e.length;n--&&g(t,e[n],0)>-1;);return n}function R(e){return"\\"+Qt[e]}function P(e){return Yt.test(e)}function O(e){return zt.test(e)}function x(e){var t=-1,n=Array(e.size);return e.forEach(function(e,a){n[++t]=[a,e]}),n}function M(e,t){return function(n){return e(t(n))}}function $(e,t){for(var n=-1,a=e.length,o=0,s=[];++n<a;){var i=e[n];i!==t&&i!==z||(e[n]=z,s[o++]=n)}return s}function U(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}function H(e){return P(e)?function(e){for(var t=Bt.lastIndex=0;Bt.test(e);)++t;return t}(e):fn(e)}function E(e){return P(e)?function(e){return e.match(Bt)||[]}(e):function(e){return e.split("")}(e)}function F(e){for(var t=e.length;t--&&We.test(e.charAt(t)););return t}function N(e){return e.match(Vt)||[]}var B,V="Expected a function",Y="__lodash_hash_undefined__",z="__lodash_placeholder__",q=16,W=32,G=64,J=128,Q=256,K=1/0,Z=9007199254740991,X=NaN,ee=4294967295,te=ee-1,ne=ee>>>1,ae=[["ary",J],["bind",1],["bindKey",2],["curry",8],["curryRight",q],["flip",512],["partial",W],["partialRight",G],["rearg",Q]],oe="[object Arguments]",se="[object Array]",ie="[object Boolean]",re="[object Date]",le="[object Error]",ce="[object Function]",ue="[object GeneratorFunction]",de="[object Map]",pe="[object Number]",me="[object Object]",fe="[object Promise]",he="[object RegExp]",ge="[object Set]",ve="[object String]",ye="[object Symbol]",be="[object WeakMap]",we="[object ArrayBuffer]",_e="[object DataView]",ke="[object Float32Array]",Ie="[object Float64Array]",Ce="[object Int8Array]",Se="[object Int16Array]",De="[object Int32Array]",Le="[object Uint8Array]",Ae="[object Uint8ClampedArray]",je="[object Uint16Array]",Te="[object Uint32Array]",Re=/\b__p \+= '';/g,Pe=/\b(__p \+=) '' \+/g,Oe=/(__e\(.*?\)|\b__t\)) \+\n'';/g,xe=/&(?:amp|lt|gt|quot|#39);/g,Me=/[&<>"']/g,$e=RegExp(xe.source),Ue=RegExp(Me.source),He=/<%-([\s\S]+?)%>/g,Ee=/<%([\s\S]+?)%>/g,Fe=/<%=([\s\S]+?)%>/g,Ne=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Be=/^\w*$/,Ve=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ye=/[\\^$.*+?()[\]{}|]/g,ze=RegExp(Ye.source),qe=/^\s+/,We=/\s/,Ge=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Je=/\{\n\/\* \[wrapped with (.+)\] \*/,Qe=/,? & /,Ke=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Ze=/[()=,{}\[\]\/\s]/,Xe=/\\(\\)?/g,et=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,tt=/\w*$/,nt=/^[-+]0x[0-9a-f]+$/i,at=/^0b[01]+$/i,ot=/^\[object .+?Constructor\]$/,st=/^0o[0-7]+$/i,it=/^(?:0|[1-9]\d*)$/,rt=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,lt=/($^)/,ct=/['\n\r\u2028\u2029\\]/g,ut="\\ud800-\\udfff",dt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",pt="\\u2700-\\u27bf",mt="a-z\\xdf-\\xf6\\xf8-\\xff",ft="A-Z\\xc0-\\xd6\\xd8-\\xde",ht="\\ufe0e\\ufe0f",gt="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vt="['’]",yt="["+ut+"]",bt="["+gt+"]",wt="["+dt+"]",_t="\\d+",kt="["+pt+"]",It="["+mt+"]",Ct="[^"+ut+gt+_t+pt+mt+ft+"]",St="\\ud83c[\\udffb-\\udfff]",Dt="[^"+ut+"]",Lt="(?:\\ud83c[\\udde6-\\uddff]){2}",At="[\\ud800-\\udbff][\\udc00-\\udfff]",jt="["+ft+"]",Tt="\\u200d",Rt="(?:"+It+"|"+Ct+")",Pt="(?:"+jt+"|"+Ct+")",Ot="(?:['’](?:d|ll|m|re|s|t|ve))?",xt="(?:['’](?:D|LL|M|RE|S|T|VE))?",Mt="(?:"+wt+"|"+St+")"+"?",$t="["+ht+"]?",Ut=$t+Mt+("(?:"+Tt+"(?:"+[Dt,Lt,At].join("|")+")"+$t+Mt+")*"),Ht="(?:"+[kt,Lt,At].join("|")+")"+Ut,Et="(?:"+[Dt+wt+"?",wt,Lt,At,yt].join("|")+")",Ft=RegExp(vt,"g"),Nt=RegExp(wt,"g"),Bt=RegExp(St+"(?="+St+")|"+Et+Ut,"g"),Vt=RegExp([jt+"?"+It+"+"+Ot+"(?="+[bt,jt,"$"].join("|")+")",Pt+"+"+xt+"(?="+[bt,jt+Rt,"$"].join("|")+")",jt+"?"+Rt+"+"+Ot,jt+"+"+xt,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",_t,Ht].join("|"),"g"),Yt=RegExp("["+Tt+ut+dt+ht+"]"),zt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,qt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Wt=-1,Gt={};Gt[ke]=Gt[Ie]=Gt[Ce]=Gt[Se]=Gt[De]=Gt[Le]=Gt[Ae]=Gt[je]=Gt[Te]=!0,Gt[oe]=Gt[se]=Gt[we]=Gt[ie]=Gt[_e]=Gt[re]=Gt[le]=Gt[ce]=Gt[de]=Gt[pe]=Gt[me]=Gt[he]=Gt[ge]=Gt[ve]=Gt[be]=!1;var Jt={};Jt[oe]=Jt[se]=Jt[we]=Jt[_e]=Jt[ie]=Jt[re]=Jt[ke]=Jt[Ie]=Jt[Ce]=Jt[Se]=Jt[De]=Jt[de]=Jt[pe]=Jt[me]=Jt[he]=Jt[ge]=Jt[ve]=Jt[ye]=Jt[Le]=Jt[Ae]=Jt[je]=Jt[Te]=!0,Jt[le]=Jt[ce]=Jt[be]=!1;var Qt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Kt=parseFloat,Zt=parseInt,Xt="object"==typeof global&&global&&global.Object===Object&&global,en="object"==typeof self&&self&&self.Object===Object&&self,tn=Xt||en||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,an=nn&&"object"==typeof module&&module&&!module.nodeType&&module,on=an&&an.exports===nn,sn=on&&Xt.process,rn=function(){try{var e=an&&an.require&&an.require("util").types;return e||sn&&sn.binding&&sn.binding("util")}catch(e){}}(),ln=rn&&rn.isArrayBuffer,cn=rn&&rn.isDate,un=rn&&rn.isMap,dn=rn&&rn.isRegExp,pn=rn&&rn.isSet,mn=rn&&rn.isTypedArray,fn=w("length"),hn=_({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),gn=_({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),vn=_({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),yn=function _(We){function Ke(e){if(No(e)&&!Tr(e)&&!(e instanceof pt)){if(e instanceof dt)return e;if(Ts.call(e,"__wrapped__"))return mo(e)}return new dt(e)}function ut(){}function dt(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=B}function pt(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ee,this.__views__=[]}function mt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}function ft(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}function ht(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}function gt(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new ht;++t<n;)this.add(e[t])}function vt(e){this.size=(this.__data__=new ft(e)).size}function yt(e,t){var n=Tr(e),a=!n&&jr(e),o=!n&&!a&&Pr(e),s=!n&&!a&&!o&&Ur(e),i=n||a||o||s,r=i?C(e.length,Is):[],l=r.length;for(var c in e)!t&&!Ts.call(e,c)||i&&("length"==c||o&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Qa(c,l))||r.push(c);return r}function bt(e){var t=e.length;return t?e[On(0,t-1)]:B}function wt(e,t){return lo(ua(e),jt(t,0,e.length))}function _t(e){return lo(ua(e))}function kt(e,t,n){(n===B||Oo(e[t],n))&&(n!==B||t in e)||Lt(e,t,n)}function It(e,t,n){var a=e[t];Ts.call(e,t)&&Oo(a,n)&&(n!==B||t in e)||Lt(e,t,n)}function Ct(e,t){for(var n=e.length;n--;)if(Oo(e[n][0],t))return n;return-1}function St(e,t,n,a){return Ai(e,function(e,o,s){t(a,e,n(e),s)}),a}function Dt(e,t){return e&&da(t,ts(t),e)}function Lt(e,t,n){"__proto__"==t&&Gs?Gs(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function At(e,t){for(var n=-1,a=t.length,o=gs(a),s=null==e;++n<a;)o[n]=s?B:Xo(e,t[n]);return o}function jt(e,t,n){return e==e&&(n!==B&&(e=e<=n?e:n),t!==B&&(e=e>=t?e:t)),e}function Tt(e,t,a,o,s,i){var r,l=1&t,c=2&t,u=4&t;if(a&&(r=s?a(e,o,s,i):a(e)),r!==B)return r;if(!Fo(e))return e;var d=Tr(e);if(d){if(r=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Ts.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!l)return ua(e,r)}else{var p=Fi(e),m=p==ce||p==ue;if(Pr(e))return oa(e,l);if(p==me||p==oe||m&&!s){if(r=c||m?{}:Ga(e),!l)return c?function(e,t){return da(e,Ei(e),t)}(e,function(e,t){return e&&da(t,ns(t),e)}(r,e)):function(e,t){return da(e,Hi(e),t)}(e,Dt(r,e))}else{if(!Jt[p])return s?e:{};r=function(e,t,n){var a=e.constructor;switch(t){case we:return sa(e);case ie:case re:return new a(+e);case _e:return function(e,t){return new e.constructor(t?sa(e.buffer):e.buffer,e.byteOffset,e.byteLength)}(e,n);case ke:case Ie:case Ce:case Se:case De:case Le:case Ae:case je:case Te:return ia(e,n);case de:return new a;case pe:case ve:return new a(e);case he:return function(e){var t=new e.constructor(e.source,tt.exec(e));return t.lastIndex=e.lastIndex,t}(e);case ge:return new a;case ye:return function(e){return Si?_s(Si.call(e)):{}}(e)}}(e,p,l)}}i||(i=new vt);var f=i.get(e);if(f)return f;i.set(e,r),$r(e)?e.forEach(function(n){r.add(Tt(n,t,a,n,e,i))}):xr(e)&&e.forEach(function(n,o){r.set(o,Tt(n,t,a,o,e,i))});var h=d?B:(u?c?Fa:Ea:c?ns:ts)(e);return n(h||e,function(n,o){h&&(n=e[o=n]),It(r,o,Tt(n,t,a,o,e,i))}),r}function Rt(e,t,n){var a=n.length;if(null==e)return!a;for(e=_s(e);a--;){var o=n[a],s=t[o],i=e[o];if(i===B&&!(o in e)||!s(i))return!1}return!0}function Pt(e,t,n){if("function"!=typeof e)throw new Cs(V);return Vi(function(){e.apply(B,n)},t)}function Ot(e,t,n,a){var o=-1,s=i,c=!0,u=e.length,d=[],p=t.length;if(!u)return d;n&&(t=l(t,D(n))),a?(s=r,c=!1):t.length>=200&&(s=A,c=!1,t=new gt(t));e:for(;++o<u;){var m=e[o],f=null==n?m:n(m);if(m=a||0!==m?m:0,c&&f==f){for(var h=p;h--;)if(t[h]===f)continue e;d.push(m)}else s(t,f,a)||d.push(m)}return d}function xt(e,t){var n=!0;return Ai(e,function(e,a,o){return n=!!t(e,a,o)}),n}function Mt(e,t,n){for(var a=-1,o=e.length;++a<o;){var s=e[a],i=t(s);if(null!=i&&(r===B?i==i&&!zo(i):n(i,r)))var r=i,l=s}return l}function $t(e,t){var n=[];return Ai(e,function(e,a,o){t(e,a,o)&&n.push(e)}),n}function Ut(e,t,n,a,o){var s=-1,i=e.length;for(n||(n=Ja),o||(o=[]);++s<i;){var r=e[s];t>0&&n(r)?t>1?Ut(r,t-1,n,a,o):c(o,r):a||(o[o.length]=r)}return o}function Ht(e,t){return e&&Ti(e,t,ts)}function Et(e,t){return e&&Ri(e,t,ts)}function Bt(e,t){return s(t,function(t){return Uo(e[t])})}function Vt(e,t){for(var n=0,a=(t=na(t,e)).length;null!=e&&n<a;)e=e[co(t[n++])];return n&&n==a?e:B}function Yt(e,t,n){var a=t(e);return Tr(e)?a:c(a,n(e))}function zt(e){return null==e?e===B?"[object Undefined]":"[object Null]":Ws&&Ws in _s(e)?function(e){var t=Ts.call(e,Ws),n=e[Ws];try{e[Ws]=B;var a=!0}catch(e){}var o=Os.call(e);return a&&(t?e[Ws]=n:delete e[Ws]),o}(e):function(e){return Os.call(e)}(e)}function Qt(e,t){return e>t}function Xt(e,t){return null!=e&&Ts.call(e,t)}function en(e,t){return null!=e&&t in _s(e)}function nn(e,t,n){for(var a=n?r:i,o=e[0].length,s=e.length,c=s,u=gs(s),d=1/0,p=[];c--;){var m=e[c];c&&t&&(m=l(m,D(t))),d=ii(m.length,d),u[c]=!n&&(t||o>=120&&m.length>=120)?new gt(c&&m):B}m=e[0];var f=-1,h=u[0];e:for(;++f<o&&p.length<d;){var g=m[f],v=t?t(g):g;if(g=n||0!==g?g:0,!(h?A(h,v):a(p,v,n))){for(c=s;--c;){var y=u[c];if(!(y?A(y,v):a(e[c],v,n)))continue e}h&&h.push(v),p.push(g)}}return p}function an(t,n,a){var o=null==(t=oo(t,n=na(n,t)))?t:t[co(yo(n))];return null==o?B:e(o,t,a)}function sn(e){return No(e)&&zt(e)==oe}function rn(e,t,n,a,o){return e===t||(null==e||null==t||!No(e)&&!No(t)?e!=e&&t!=t:function(e,t,n,a,o,s){var i=Tr(e),r=Tr(t),l=i?se:Fi(e),c=r?se:Fi(t);l=l==oe?me:l,c=c==oe?me:c;var u=l==me,d=c==me,p=l==c;if(p&&Pr(e)){if(!Pr(t))return!1;i=!0,u=!1}if(p&&!u)return s||(s=new vt),i||Ur(e)?Ua(e,t,n,a,o,s):function(e,t,n,a,o,s,i){switch(n){case _e:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case we:return!(e.byteLength!=t.byteLength||!s(new Es(e),new Es(t)));case ie:case re:case pe:return Oo(+e,+t);case le:return e.name==t.name&&e.message==t.message;case he:case ve:return e==t+"";case de:var r=x;case ge:var l=1&a;if(r||(r=U),e.size!=t.size&&!l)return!1;var c=i.get(e);if(c)return c==t;a|=2,i.set(e,t);var u=Ua(r(e),r(t),a,o,s,i);return i.delete(e),u;case ye:if(Si)return Si.call(e)==Si.call(t)}return!1}(e,t,l,n,a,o,s);if(!(1&n)){var m=u&&Ts.call(e,"__wrapped__"),f=d&&Ts.call(t,"__wrapped__");if(m||f){var h=m?e.value():e,g=f?t.value():t;return s||(s=new vt),o(h,g,n,a,s)}}return!!p&&(s||(s=new vt),function(e,t,n,a,o,s){var i=1&n,r=Ea(e),l=r.length;if(l!=Ea(t).length&&!i)return!1;for(var c=l;c--;){var u=r[c];if(!(i?u in t:Ts.call(t,u)))return!1}var d=s.get(e),p=s.get(t);if(d&&p)return d==t&&p==e;var m=!0;s.set(e,t),s.set(t,e);for(var f=i;++c<l;){var h=e[u=r[c]],g=t[u];if(a)var v=i?a(g,h,u,t,e,s):a(h,g,u,e,t,s);if(!(v===B?h===g||o(h,g,n,a,s):v)){m=!1;break}f||(f="constructor"==u)}if(m&&!f){var y=e.constructor,b=t.constructor;y!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof b&&b instanceof b)&&(m=!1)}return s.delete(e),s.delete(t),m}(e,t,n,a,o,s))}(e,t,n,a,rn,o))}function fn(e,t,n,a){var o=n.length,s=o,i=!a;if(null==e)return!s;for(e=_s(e);o--;){var r=n[o];if(i&&r[2]?r[1]!==e[r[0]]:!(r[0]in e))return!1}for(;++o<s;){var l=(r=n[o])[0],c=e[l],u=r[1];if(i&&r[2]){if(c===B&&!(l in e))return!1}else{var d=new vt;if(a)var p=a(c,u,l,e,t,d);if(!(p===B?rn(u,c,3,a,d):p))return!1}}return!0}function bn(e){return!(!Fo(e)||function(e){return!!Ps&&Ps in e}(e))&&(Uo(e)?$s:ot).test(uo(e))}function wn(e){return"function"==typeof e?e:null==e?cs:"object"==typeof e?Tr(e)?Dn(e[0],e[1]):Sn(e):ms(e)}function _n(e){if(!eo(e))return oi(e);var t=[];for(var n in _s(e))Ts.call(e,n)&&"constructor"!=n&&t.push(n);return t}function kn(e){if(!Fo(e))return function(e){var t=[];if(null!=e)for(var n in _s(e))t.push(n);return t}(e);var t=eo(e),n=[];for(var a in e)("constructor"!=a||!t&&Ts.call(e,a))&&n.push(a);return n}function In(e,t){return e<t}function Cn(e,t){var n=-1,a=xo(e)?gs(e.length):[];return Ai(e,function(e,o,s){a[++n]=t(e,o,s)}),a}function Sn(e){var t=za(e);return 1==t.length&&t[0][2]?no(t[0][0],t[0][1]):function(n){return n===e||fn(n,e,t)}}function Dn(e,t){return Za(e)&&to(t)?no(co(e),t):function(n){var a=Xo(n,e);return a===B&&a===t?es(n,e):rn(t,a,3)}}function Ln(e,t,n,a,o){e!==t&&Ti(t,function(s,i){if(o||(o=new vt),Fo(s))!function(e,t,n,a,o,s,i){var r=so(e,n),l=so(t,n),c=i.get(l);if(c)return kt(e,n,c),B;var u=s?s(r,l,n+"",e,t,i):B,d=u===B;if(d){var p=Tr(l),m=!p&&Pr(l),f=!p&&!m&&Ur(l);u=l,p||m||f?Tr(r)?u=r:Mo(r)?u=ua(r):m?(d=!1,u=oa(l,!0)):f?(d=!1,u=ia(l,!0)):u=[]:Vo(l)||jr(l)?(u=r,jr(r)?u=Ko(r):Fo(r)&&!Uo(r)||(u=Ga(l))):d=!1}d&&(i.set(l,u),o(u,l,a,s,i),i.delete(l)),kt(e,n,u)}(e,t,i,n,Ln,a,o);else{var r=a?a(so(e,i),s,i+"",e,t,o):B;r===B&&(r=s),kt(e,i,r)}},ns)}function An(e,t){var n=e.length;if(n)return Qa(t+=t<0?n:0,n)?e[t]:B}function jn(e,t,n){t=t.length?l(t,function(e){return Tr(e)?function(t){return Vt(t,1===e.length?e[0]:e)}:e}):[cs];var a=-1;return t=l(t,D(Va())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(Cn(e,function(e,n,o){return{criteria:l(t,function(t){return t(e)}),index:++a,value:e}}),function(e,t){return function(e,t,n){for(var a=-1,o=e.criteria,s=t.criteria,i=o.length,r=n.length;++a<i;){var l=ra(o[a],s[a]);if(l)return a>=r?l:l*("desc"==n[a]?-1:1)}return e.index-t.index}(e,t,n)})}function Tn(e,t,n){for(var a=-1,o=t.length,s={};++a<o;){var i=t[a],r=Vt(e,i);n(r,i)&&Hn(s,na(i,e),r)}return s}function Rn(e,t,n,a){var o=a?v:g,s=-1,i=t.length,r=e;for(e===t&&(t=ua(t)),n&&(r=l(e,D(n)));++s<i;)for(var c=0,u=t[s],d=n?n(u):u;(c=o(r,d,c,a))>-1;)r!==e&&Ys.call(r,c,1),Ys.call(e,c,1);return e}function Pn(e,t){for(var n=e?t.length:0,a=n-1;n--;){var o=t[n];if(n==a||o!==s){var s=o;Qa(o)?Ys.call(e,o,1):Gn(e,o)}}return e}function On(e,t){return e+Xs(ci()*(t-e+1))}function xn(e,t){var n="";if(!e||t<1||t>Z)return n;do{t%2&&(n+=e),(t=Xs(t/2))&&(e+=e)}while(t);return n}function Mn(e,t){return Yi(ao(e,t,cs),e+"")}function $n(e){return bt(os(e))}function Un(e,t){var n=os(e);return lo(n,jt(t,0,n.length))}function Hn(e,t,n,a){if(!Fo(e))return e;for(var o=-1,s=(t=na(t,e)).length,i=s-1,r=e;null!=r&&++o<s;){var l=co(t[o]),c=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(o!=i){var u=r[l];(c=a?a(u,l,r):B)===B&&(c=Fo(u)?u:Qa(t[o+1])?[]:{})}It(r,l,c),r=r[l]}return e}function En(e){return lo(os(e))}function Fn(e,t,n){var a=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(n=n>o?o:n)<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var s=gs(o);++a<o;)s[a]=e[a+t];return s}function Nn(e,t){var n;return Ai(e,function(e,a,o){return!(n=t(e,a,o))}),!!n}function Bn(e,t,n){var a=0,o=null==e?a:e.length;if("number"==typeof t&&t==t&&o<=ne){for(;a<o;){var s=a+o>>>1,i=e[s];null!==i&&!zo(i)&&(n?i<=t:i<t)?a=s+1:o=s}return o}return Vn(e,t,cs,n)}function Vn(e,t,n,a){var o=0,s=null==e?0:e.length;if(0===s)return 0;for(var i=(t=n(t))!=t,r=null===t,l=zo(t),c=t===B;o<s;){var u=Xs((o+s)/2),d=n(e[u]),p=d!==B,m=null===d,f=d==d,h=zo(d);if(i)var g=a||f;else g=c?f&&(a||p):r?f&&p&&(a||!m):l?f&&p&&!m&&(a||!h):!m&&!h&&(a?d<=t:d<t);g?o=u+1:s=u}return ii(s,te)}function Yn(e,t){for(var n=-1,a=e.length,o=0,s=[];++n<a;){var i=e[n],r=t?t(i):i;if(!n||!Oo(r,l)){var l=r;s[o++]=0===i?0:i}}return s}function zn(e){return"number"==typeof e?e:zo(e)?X:+e}function qn(e){if("string"==typeof e)return e;if(Tr(e))return l(e,qn)+"";if(zo(e))return Di?Di.call(e):"";var t=e+"";return"0"==t&&1/e==-K?"-0":t}function Wn(e,t,n){var a=-1,o=i,s=e.length,l=!0,c=[],u=c;if(n)l=!1,o=r;else if(s>=200){var d=t?null:$i(e);if(d)return U(d);l=!1,o=A,u=new gt}else u=t?[]:c;e:for(;++a<s;){var p=e[a],m=t?t(p):p;if(p=n||0!==p?p:0,l&&m==m){for(var f=u.length;f--;)if(u[f]===m)continue e;t&&u.push(m),c.push(p)}else o(u,m,n)||(u!==c&&u.push(m),c.push(p))}return c}function Gn(e,t){return null==(e=oo(e,t=na(t,e)))||delete e[co(yo(t))]}function Jn(e,t,n,a){return Hn(e,t,n(Vt(e,t)),a)}function Qn(e,t,n,a){for(var o=e.length,s=a?o:-1;(a?s--:++s<o)&&t(e[s],s,e););return n?Fn(e,a?0:s,a?s+1:o):Fn(e,a?s+1:0,a?o:s)}function Kn(e,t){var n=e;return n instanceof pt&&(n=n.value()),u(t,function(e,t){return t.func.apply(t.thisArg,c([e],t.args))},n)}function Zn(e,t,n){var a=e.length;if(a<2)return a?Wn(e[0]):[];for(var o=-1,s=gs(a);++o<a;)for(var i=e[o],r=-1;++r<a;)r!=o&&(s[o]=Ot(s[o]||i,e[r],t,n));return Wn(Ut(s,1),t,n)}function Xn(e,t,n){for(var a=-1,o=e.length,s=t.length,i={};++a<o;)n(i,e[a],a<s?t[a]:B);return i}function ea(e){return Mo(e)?e:[]}function ta(e){return"function"==typeof e?e:cs}function na(e,t){return Tr(e)?e:Za(e,t)?[e]:zi(Zo(e))}function aa(e,t,n){var a=e.length;return n=n===B?a:n,!t&&n>=a?e:Fn(e,t,n)}function oa(e,t){if(t)return e.slice();var n=e.length,a=Fs?Fs(n):new e.constructor(n);return e.copy(a),a}function sa(e){var t=new e.constructor(e.byteLength);return new Es(t).set(new Es(e)),t}function ia(e,t){return new e.constructor(t?sa(e.buffer):e.buffer,e.byteOffset,e.length)}function ra(e,t){if(e!==t){var n=e!==B,a=null===e,o=e==e,s=zo(e),i=t!==B,r=null===t,l=t==t,c=zo(t);if(!r&&!c&&!s&&e>t||s&&i&&l&&!r&&!c||a&&i&&l||!n&&l||!o)return 1;if(!a&&!s&&!c&&e<t||c&&n&&o&&!a&&!s||r&&n&&o||!i&&o||!l)return-1}return 0}function la(e,t,n,a){for(var o=-1,s=e.length,i=n.length,r=-1,l=t.length,c=si(s-i,0),u=gs(l+c),d=!a;++r<l;)u[r]=t[r];for(;++o<i;)(d||o<s)&&(u[n[o]]=e[o]);for(;c--;)u[r++]=e[o++];return u}function ca(e,t,n,a){for(var o=-1,s=e.length,i=-1,r=n.length,l=-1,c=t.length,u=si(s-r,0),d=gs(u+c),p=!a;++o<u;)d[o]=e[o];for(var m=o;++l<c;)d[m+l]=t[l];for(;++i<r;)(p||o<s)&&(d[m+n[i]]=e[o++]);return d}function ua(e,t){var n=-1,a=e.length;for(t||(t=gs(a));++n<a;)t[n]=e[n];return t}function da(e,t,n,a){var o=!n;n||(n={});for(var s=-1,i=t.length;++s<i;){var r=t[s],l=a?a(n[r],e[r],r,n,e):B;l===B&&(l=e[r]),o?Lt(n,r,l):It(n,r,l)}return n}function pa(e,n){return function(a,o){var s=Tr(a)?t:St,i=n?n():{};return s(a,e,Va(o,2),i)}}function ma(e){return Mn(function(t,n){var a=-1,o=n.length,s=o>1?n[o-1]:B,i=o>2?n[2]:B;for(s=e.length>3&&"function"==typeof s?(o--,s):B,i&&Ka(n[0],n[1],i)&&(s=o<3?B:s,o=1),t=_s(t);++a<o;){var r=n[a];r&&e(t,r,a,s)}return t})}function fa(e,t){return function(n,a){if(null==n)return n;if(!xo(n))return e(n,a);for(var o=n.length,s=t?o:-1,i=_s(n);(t?s--:++s<o)&&!1!==a(i[s],s,i););return n}}function ha(e){return function(t,n,a){for(var o=-1,s=_s(t),i=a(t),r=i.length;r--;){var l=i[e?r:++o];if(!1===n(s[l],l,s))break}return t}}function ga(e){return function(t){var n=P(t=Zo(t))?E(t):B,a=n?n[0]:t.charAt(0),o=n?aa(n,1).join(""):t.slice(1);return a[e]()+o}}function va(e){return function(t){return u(rs(is(t).replace(Ft,"")),e,"")}}function ya(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Li(e.prototype),a=e.apply(n,t);return Fo(a)?a:n}}function ba(t,n,a){var o=ya(t);return function s(){for(var i=arguments.length,r=gs(i),l=i,c=Ba(s);l--;)r[l]=arguments[l];var u=i<3&&r[0]!==c&&r[i-1]!==c?[]:$(r,c);return(i-=u.length)<a?Ta(t,n,ka,s.placeholder,B,r,u,B,B,a-i):e(this&&this!==tn&&this instanceof s?o:t,this,r)}}function wa(e){return function(t,n,a){var o=_s(t);if(!xo(t)){var s=Va(n,3);t=ts(t),n=function(e){return s(o[e],e,o)}}var i=e(t,n,a);return i>-1?o[s?t[i]:i]:B}}function _a(e){return Ha(function(t){var n=t.length,a=n,o=dt.prototype.thru;for(e&&t.reverse();a--;){var s=t[a];if("function"!=typeof s)throw new Cs(V);if(o&&!i&&"wrapper"==Na(s))var i=new dt([],!0)}for(a=i?a:n;++a<n;){var r=Na(s=t[a]),l="wrapper"==r?Ui(s):B;i=l&&Xa(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?i[Na(l[0])].apply(i,l[3]):1==s.length&&Xa(s)?i[r]():i.thru(s)}return function(){var e=arguments,a=e[0];if(i&&1==e.length&&Tr(a))return i.plant(a).value();for(var o=0,s=n?t[o].apply(this,e):a;++o<n;)s=t[o].call(this,s);return s}})}function ka(e,t,n,a,o,s,i,r,l,c){var u=t&J,d=1&t,p=2&t,m=24&t,f=512&t,h=p?B:ya(e);return function g(){for(var v=arguments.length,y=gs(v),b=v;b--;)y[b]=arguments[b];if(m)var w=Ba(g),_=function(e,t){for(var n=e.length,a=0;n--;)e[n]===t&&++a;return a}(y,w);if(a&&(y=la(y,a,o,m)),s&&(y=ca(y,s,i,m)),v-=_,m&&v<c)return Ta(e,t,ka,g.placeholder,n,y,$(y,w),r,l,c-v);var k=d?n:this,I=p?k[e]:e;return v=y.length,r?y=function(e,t){for(var n=e.length,a=ii(t.length,n),o=ua(e);a--;){var s=t[a];e[a]=Qa(s,n)?o[s]:B}return e}(y,r):f&&v>1&&y.reverse(),u&&l<v&&(y.length=l),this&&this!==tn&&this instanceof g&&(I=h||ya(I)),I.apply(k,y)}}function Ia(e,t){return function(n,a){return function(e,t,n,a){return Ht(e,function(e,o,s){t(a,n(e),o,s)}),a}(n,e,t(a),{})}}function Ca(e,t){return function(n,a){var o;if(n===B&&a===B)return t;if(n!==B&&(o=n),a!==B){if(o===B)return a;"string"==typeof n||"string"==typeof a?(n=qn(n),a=qn(a)):(n=zn(n),a=zn(a)),o=e(n,a)}return o}}function Sa(t){return Ha(function(n){return n=l(n,D(Va())),Mn(function(a){var o=this;return t(n,function(t){return e(t,o,a)})})})}function Da(e,t){var n=(t=t===B?" ":qn(t)).length;if(n<2)return n?xn(t,e):t;var a=xn(t,Zs(e/H(t)));return P(t)?aa(E(a),0,e).join(""):a.slice(0,e)}function La(t,n,a,o){var s=1&n,i=ya(t);return function n(){for(var r=-1,l=arguments.length,c=-1,u=o.length,d=gs(u+l),p=this&&this!==tn&&this instanceof n?i:t;++c<u;)d[c]=o[c];for(;l--;)d[c++]=arguments[++r];return e(p,s?a:this,d)}}function Aa(e){return function(t,n,a){return a&&"number"!=typeof a&&Ka(t,n,a)&&(n=a=B),t=Wo(t),n===B?(n=t,t=0):n=Wo(n),function(e,t,n,a){for(var o=-1,s=si(Zs((t-e)/(n||1)),0),i=gs(s);s--;)i[a?s:++o]=e,e+=n;return i}(t,n,a=a===B?t<n?1:-1:Wo(a),e)}}function ja(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=Qo(t),n=Qo(n)),e(t,n)}}function Ta(e,t,n,a,o,s,i,r,l,c){var u=8&t;t|=u?W:G,4&(t&=~(u?G:W))||(t&=-4);var d=[e,t,o,u?s:B,u?i:B,u?B:s,u?B:i,r,l,c],p=n.apply(B,d);return Xa(e)&&Bi(p,d),p.placeholder=a,io(p,e,t)}function Ra(e){var t=ws[e];return function(e,n){if(e=Qo(e),(n=null==n?0:ii(Go(n),292))&&ni(e)){var a=(Zo(e)+"e").split("e");return+((a=(Zo(t(a[0]+"e"+(+a[1]+n)))+"e").split("e"))[0]+"e"+(+a[1]-n))}return t(e)}}function Pa(e){return function(t){var n=Fi(t);return n==de?x(t):n==ge?function(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=[e,e]}),n}(t):function(e,t){return l(t,function(t){return[t,e[t]]})}(t,e(t))}}function Oa(e,t,n,a,o,s,i,r){var l=2&t;if(!l&&"function"!=typeof e)throw new Cs(V);var c=a?a.length:0;if(c||(t&=-97,a=o=B),i=i===B?i:si(Go(i),0),r=r===B?r:Go(r),c-=o?o.length:0,t&G){var u=a,d=o;a=o=B}var p=l?B:Ui(e),m=[e,t,n,a,o,u,d,s,i,r];if(p&&function(e,t){var n=e[1],a=t[1],o=n|a,s=o<131,i=a==J&&8==n||a==J&&n==Q&&e[7].length<=t[8]||384==a&&t[7].length<=t[8]&&8==n;if(!s&&!i)return e;1&a&&(e[2]=t[2],o|=1&n?0:4);var r=t[3];if(r){var l=e[3];e[3]=l?la(l,r,t[4]):r,e[4]=l?$(e[3],z):t[4]}r=t[5],r&&(l=e[5],e[5]=l?ca(l,r,t[6]):r,e[6]=l?$(e[5],z):t[6]),r=t[7],r&&(e[7]=r),a&J&&(e[8]=null==e[8]?t[8]:ii(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=o}(m,p),e=m[0],t=m[1],n=m[2],a=m[3],o=m[4],!(r=m[9]=m[9]===B?l?0:e.length:si(m[9]-c,0))&&24&t&&(t&=-25),t&&1!=t)f=8==t||t==q?ba(e,t,r):t!=W&&33!=t||o.length?ka.apply(B,m):La(e,t,n,a);else var f=function(e,t,n){var a=1&t,o=ya(e);return function t(){return(this&&this!==tn&&this instanceof t?o:e).apply(a?n:this,arguments)}}(e,t,n);return io((p?Pi:Bi)(f,m),e,t)}function xa(e,t,n,a){return e===B||Oo(e,Ls[n])&&!Ts.call(a,n)?t:e}function Ma(e,t,n,a,o,s){return Fo(e)&&Fo(t)&&(s.set(t,e),Ln(e,t,B,Ma,s),s.delete(t)),e}function $a(e){return Vo(e)?B:e}function Ua(e,t,n,a,o,s){var i=1&n,r=e.length,l=t.length;if(r!=l&&!(i&&l>r))return!1;var c=s.get(e),u=s.get(t);if(c&&u)return c==t&&u==e;var d=-1,m=!0,f=2&n?new gt:B;for(s.set(e,t),s.set(t,e);++d<r;){var h=e[d],g=t[d];if(a)var v=i?a(g,h,d,t,e,s):a(h,g,d,e,t,s);if(v!==B){if(v)continue;m=!1;break}if(f){if(!p(t,function(e,t){if(!A(f,t)&&(h===e||o(h,e,n,a,s)))return f.push(t)})){m=!1;break}}else if(h!==g&&!o(h,g,n,a,s)){m=!1;break}}return s.delete(e),s.delete(t),m}function Ha(e){return Yi(ao(e,B,go),e+"")}function Ea(e){return Yt(e,ts,Hi)}function Fa(e){return Yt(e,ns,Ei)}function Na(e){for(var t=e.name+"",n=yi[t],a=Ts.call(yi,t)?n.length:0;a--;){var o=n[a],s=o.func;if(null==s||s==e)return o.name}return t}function Ba(e){return(Ts.call(Ke,"placeholder")?Ke:e).placeholder}function Va(){var e=Ke.iteratee||us;return e=e===us?wn:e,arguments.length?e(arguments[0],arguments[1]):e}function Ya(e,t){var n=e.__data__;return function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}(t)?n["string"==typeof t?"string":"hash"]:n.map}function za(e){for(var t=ts(e),n=t.length;n--;){var a=t[n],o=e[a];t[n]=[a,o,to(o)]}return t}function qa(e,t){var n=function(e,t){return null==e?B:e[t]}(e,t);return bn(n)?n:B}function Wa(e,t,n){for(var a=-1,o=(t=na(t,e)).length,s=!1;++a<o;){var i=co(t[a]);if(!(s=null!=e&&n(e,i)))break;e=e[i]}return s||++a!=o?s:!!(o=null==e?0:e.length)&&Eo(o)&&Qa(i,o)&&(Tr(e)||jr(e))}function Ga(e){return"function"!=typeof e.constructor||eo(e)?{}:Li(Ns(e))}function Ja(e){return Tr(e)||jr(e)||!!(zs&&e&&e[zs])}function Qa(e,t){var n=typeof e;return!!(t=null==t?Z:t)&&("number"==n||"symbol"!=n&&it.test(e))&&e>-1&&e%1==0&&e<t}function Ka(e,t,n){if(!Fo(n))return!1;var a=typeof t;return!!("number"==a?xo(n)&&Qa(t,n.length):"string"==a&&t in n)&&Oo(n[t],e)}function Za(e,t){if(Tr(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!zo(e))||Be.test(e)||!Ne.test(e)||null!=t&&e in _s(t)}function Xa(e){var t=Na(e),n=Ke[t];if("function"!=typeof n||!(t in pt.prototype))return!1;if(e===n)return!0;var a=Ui(n);return!!a&&e===a[0]}function eo(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Ls)}function to(e){return e==e&&!Fo(e)}function no(e,t){return function(n){return null!=n&&n[e]===t&&(t!==B||e in _s(n))}}function ao(t,n,a){return n=si(n===B?t.length-1:n,0),function(){for(var o=arguments,s=-1,i=si(o.length-n,0),r=gs(i);++s<i;)r[s]=o[n+s];s=-1;for(var l=gs(n+1);++s<n;)l[s]=o[s];return l[n]=a(r),e(t,this,l)}}function oo(e,t){return t.length<2?e:Vt(e,Fn(t,0,-1))}function so(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function io(e,t,n){var a=t+"";return Yi(e,function(e,t){var n=t.length;if(!n)return e;var a=n-1;return t[a]=(n>1?"& ":"")+t[a],t=t.join(n>2?", ":" "),e.replace(Ge,"{\n/* [wrapped with "+t+"] */\n")}(a,po(function(e){var t=e.match(Je);return t?t[1].split(Qe):[]}(a),n)))}function ro(e){var t=0,n=0;return function(){var a=ri(),o=16-(a-n);if(n=a,o>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(B,arguments)}}function lo(e,t){var n=-1,a=e.length,o=a-1;for(t=t===B?a:t;++n<t;){var s=On(n,o),i=e[s];e[s]=e[n],e[n]=i}return e.length=t,e}function co(e){if("string"==typeof e||zo(e))return e;var t=e+"";return"0"==t&&1/e==-K?"-0":t}function uo(e){if(null!=e){try{return js.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function po(e,t){return n(ae,function(n){var a="_."+n[0];t&n[1]&&!i(e,a)&&e.push(a)}),e.sort()}function mo(e){if(e instanceof pt)return e.clone();var t=new dt(e.__wrapped__,e.__chain__);return t.__actions__=ua(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function fo(e,t,n){var a=null==e?0:e.length;if(!a)return-1;var o=null==n?0:Go(n);return o<0&&(o=si(a+o,0)),h(e,Va(t,3),o)}function ho(e,t,n){var a=null==e?0:e.length;if(!a)return-1;var o=a-1;return n!==B&&(o=Go(n),o=n<0?si(a+o,0):ii(o,a-1)),h(e,Va(t,3),o,!0)}function go(e){return null!=e&&e.length?Ut(e,1):[]}function vo(e){return e&&e.length?e[0]:B}function yo(e){var t=null==e?0:e.length;return t?e[t-1]:B}function bo(e,t){return e&&e.length&&t&&t.length?Rn(e,t):e}function wo(e){return null==e?e:ui.call(e)}function _o(e){if(!e||!e.length)return[];var t=0;return e=s(e,function(e){if(Mo(e))return t=si(e.length,t),!0}),C(t,function(t){return l(e,w(t))})}function ko(t,n){if(!t||!t.length)return[];var a=_o(t);return null==n?a:l(a,function(t){return e(n,B,t)})}function Io(e){var t=Ke(e);return t.__chain__=!0,t}function Co(e,t){return t(e)}function So(e,t){return(Tr(e)?n:Ai)(e,Va(t,3))}function Do(e,t){return(Tr(e)?a:ji)(e,Va(t,3))}function Lo(e,t){return(Tr(e)?l:Cn)(e,Va(t,3))}function Ao(e,t,n){return t=n?B:t,t=e&&null==t?e.length:t,Oa(e,J,B,B,B,B,t)}function jo(e,t){var n;if("function"!=typeof t)throw new Cs(V);return e=Go(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=B),n}}function To(e,t,n){function a(t){var n=l,a=c;return l=c=B,f=t,d=e.apply(a,n)}function o(e){var n=e-m;return m===B||n>=t||n<0||g&&e-f>=u}function s(){var e=yr();return o(e)?i(e):(p=Vi(s,function(e){var n=t-(e-m);return g?ii(n,u-(e-f)):n}(e)),B)}function i(e){return p=B,v&&l?a(e):(l=c=B,d)}function r(){var e=yr(),n=o(e);if(l=arguments,c=this,m=e,n){if(p===B)return function(e){return f=e,p=Vi(s,t),h?a(e):d}(m);if(g)return Mi(p),p=Vi(s,t),a(m)}return p===B&&(p=Vi(s,t)),d}var l,c,u,d,p,m,f=0,h=!1,g=!1,v=!0;if("function"!=typeof e)throw new Cs(V);return t=Qo(t)||0,Fo(n)&&(h=!!n.leading,u=(g="maxWait"in n)?si(Qo(n.maxWait)||0,t):u,v="trailing"in n?!!n.trailing:v),r.cancel=function(){p!==B&&Mi(p),f=0,l=m=c=p=B},r.flush=function(){return p===B?d:i(yr())},r}function Ro(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Cs(V);var n=function(){var a=arguments,o=t?t.apply(this,a):a[0],s=n.cache;if(s.has(o))return s.get(o);var i=e.apply(this,a);return n.cache=s.set(o,i)||s,i};return n.cache=new(Ro.Cache||ht),n}function Po(e){if("function"!=typeof e)throw new Cs(V);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Oo(e,t){return e===t||e!=e&&t!=t}function xo(e){return null!=e&&Eo(e.length)&&!Uo(e)}function Mo(e){return No(e)&&xo(e)}function $o(e){if(!No(e))return!1;var t=zt(e);return t==le||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!Vo(e)}function Uo(e){if(!Fo(e))return!1;var t=zt(e);return t==ce||t==ue||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Ho(e){return"number"==typeof e&&e==Go(e)}function Eo(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Z}function Fo(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function No(e){return null!=e&&"object"==typeof e}function Bo(e){return"number"==typeof e||No(e)&&zt(e)==pe}function Vo(e){if(!No(e)||zt(e)!=me)return!1;var t=Ns(e);if(null===t)return!0;var n=Ts.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&js.call(n)==xs}function Yo(e){return"string"==typeof e||!Tr(e)&&No(e)&&zt(e)==ve}function zo(e){return"symbol"==typeof e||No(e)&&zt(e)==ye}function qo(e){if(!e)return[];if(xo(e))return Yo(e)?E(e):ua(e);if(qs&&e[qs])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[qs]());var t=Fi(e);return(t==de?x:t==ge?U:os)(e)}function Wo(e){return e?(e=Qo(e))===K||e===-K?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function Go(e){var t=Wo(e),n=t%1;return t==t?n?t-n:t:0}function Jo(e){return e?jt(Go(e),0,ee):0}function Qo(e){if("number"==typeof e)return e;if(zo(e))return X;if(Fo(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Fo(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=S(e);var n=at.test(e);return n||st.test(e)?Zt(e.slice(2),n?2:8):nt.test(e)?X:+e}function Ko(e){return da(e,ns(e))}function Zo(e){return null==e?"":qn(e)}function Xo(e,t,n){var a=null==e?B:Vt(e,t);return a===B?n:a}function es(e,t){return null!=e&&Wa(e,t,en)}function ts(e){return xo(e)?yt(e):_n(e)}function ns(e){return xo(e)?yt(e,!0):kn(e)}function as(e,t){if(null==e)return{};var n=l(Fa(e),function(e){return[e]});return t=Va(t),Tn(e,n,function(e,n){return t(e,n[0])})}function os(e){return null==e?[]:L(e,ts(e))}function ss(e){return cl(Zo(e).toLowerCase())}function is(e){return(e=Zo(e))&&e.replace(rt,hn).replace(Nt,"")}function rs(e,t,n){return e=Zo(e),(t=n?B:t)===B?O(e)?N(e):m(e):e.match(t)||[]}function ls(e){return function(){return e}}function cs(e){return e}function us(e){return wn("function"==typeof e?e:Tt(e,1))}function ds(e,t,a){var o=ts(t),s=Bt(t,o);null!=a||Fo(t)&&(s.length||!o.length)||(a=t,t=e,e=this,s=Bt(t,ts(t)));var i=!(Fo(a)&&"chain"in a&&!a.chain),r=Uo(e);return n(s,function(n){var a=t[n];e[n]=a,r&&(e.prototype[n]=function(){var t=this.__chain__;if(i||t){var n=e(this.__wrapped__);return(n.__actions__=ua(this.__actions__)).push({func:a,args:arguments,thisArg:e}),n.__chain__=t,n}return a.apply(e,c([this.value()],arguments))})}),e}function ps(){}function ms(e){return Za(e)?w(co(e)):function(e){return function(t){return Vt(t,e)}}(e)}function fs(){return[]}function hs(){return!1}var gs=(We=null==We?tn:yn.defaults(tn.Object(),We,yn.pick(tn,qt))).Array,vs=We.Date,ys=We.Error,bs=We.Function,ws=We.Math,_s=We.Object,ks=We.RegExp,Is=We.String,Cs=We.TypeError,Ss=gs.prototype,Ds=bs.prototype,Ls=_s.prototype,As=We["__core-js_shared__"],js=Ds.toString,Ts=Ls.hasOwnProperty,Rs=0,Ps=function(){var e=/[^.]+$/.exec(As&&As.keys&&As.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Os=Ls.toString,xs=js.call(_s),Ms=tn._,$s=ks("^"+js.call(Ts).replace(Ye,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Us=on?We.Buffer:B,Hs=We.Symbol,Es=We.Uint8Array,Fs=Us?Us.allocUnsafe:B,Ns=M(_s.getPrototypeOf,_s),Bs=_s.create,Vs=Ls.propertyIsEnumerable,Ys=Ss.splice,zs=Hs?Hs.isConcatSpreadable:B,qs=Hs?Hs.iterator:B,Ws=Hs?Hs.toStringTag:B,Gs=function(){try{var e=qa(_s,"defineProperty");return e({},"",{}),e}catch(e){}}(),Js=We.clearTimeout!==tn.clearTimeout&&We.clearTimeout,Qs=vs&&vs.now!==tn.Date.now&&vs.now,Ks=We.setTimeout!==tn.setTimeout&&We.setTimeout,Zs=ws.ceil,Xs=ws.floor,ei=_s.getOwnPropertySymbols,ti=Us?Us.isBuffer:B,ni=We.isFinite,ai=Ss.join,oi=M(_s.keys,_s),si=ws.max,ii=ws.min,ri=vs.now,li=We.parseInt,ci=ws.random,ui=Ss.reverse,di=qa(We,"DataView"),pi=qa(We,"Map"),mi=qa(We,"Promise"),fi=qa(We,"Set"),hi=qa(We,"WeakMap"),gi=qa(_s,"create"),vi=hi&&new hi,yi={},bi=uo(di),wi=uo(pi),_i=uo(mi),ki=uo(fi),Ii=uo(hi),Ci=Hs?Hs.prototype:B,Si=Ci?Ci.valueOf:B,Di=Ci?Ci.toString:B,Li=function(){function e(){}return function(t){if(!Fo(t))return{};if(Bs)return Bs(t);e.prototype=t;var n=new e;return e.prototype=B,n}}();Ke.templateSettings={escape:He,evaluate:Ee,interpolate:Fe,variable:"",imports:{_:Ke}},Ke.prototype=ut.prototype,Ke.prototype.constructor=Ke,dt.prototype=Li(ut.prototype),dt.prototype.constructor=dt,pt.prototype=Li(ut.prototype),pt.prototype.constructor=pt,mt.prototype.clear=function(){this.__data__=gi?gi(null):{},this.size=0},mt.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},mt.prototype.get=function(e){var t=this.__data__;if(gi){var n=t[e];return n===Y?B:n}return Ts.call(t,e)?t[e]:B},mt.prototype.has=function(e){var t=this.__data__;return gi?t[e]!==B:Ts.call(t,e)},mt.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=gi&&t===B?Y:t,this},ft.prototype.clear=function(){this.__data__=[],this.size=0},ft.prototype.delete=function(e){var t=this.__data__,n=Ct(t,e);return!(n<0||(n==t.length-1?t.pop():Ys.call(t,n,1),--this.size,0))},ft.prototype.get=function(e){var t=this.__data__,n=Ct(t,e);return n<0?B:t[n][1]},ft.prototype.has=function(e){return Ct(this.__data__,e)>-1},ft.prototype.set=function(e,t){var n=this.__data__,a=Ct(n,e);return a<0?(++this.size,n.push([e,t])):n[a][1]=t,this},ht.prototype.clear=function(){this.size=0,this.__data__={hash:new mt,map:new(pi||ft),string:new mt}},ht.prototype.delete=function(e){var t=Ya(this,e).delete(e);return this.size-=t?1:0,t},ht.prototype.get=function(e){return Ya(this,e).get(e)},ht.prototype.has=function(e){return Ya(this,e).has(e)},ht.prototype.set=function(e,t){var n=Ya(this,e),a=n.size;return n.set(e,t),this.size+=n.size==a?0:1,this},gt.prototype.add=gt.prototype.push=function(e){return this.__data__.set(e,Y),this},gt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.clear=function(){this.__data__=new ft,this.size=0},vt.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},vt.prototype.get=function(e){return this.__data__.get(e)},vt.prototype.has=function(e){return this.__data__.has(e)},vt.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ft){var a=n.__data__;if(!pi||a.length<199)return a.push([e,t]),this.size=++n.size,this;n=this.__data__=new ht(a)}return n.set(e,t),this.size=n.size,this};var Ai=fa(Ht),ji=fa(Et,!0),Ti=ha(),Ri=ha(!0),Pi=vi?function(e,t){return vi.set(e,t),e}:cs,Oi=Gs?function(e,t){return Gs(e,"toString",{configurable:!0,enumerable:!1,value:ls(t),writable:!0})}:cs,xi=Mn,Mi=Js||function(e){return tn.clearTimeout(e)},$i=fi&&1/U(new fi([,-0]))[1]==K?function(e){return new fi(e)}:ps,Ui=vi?function(e){return vi.get(e)}:ps,Hi=ei?function(e){return null==e?[]:(e=_s(e),s(ei(e),function(t){return Vs.call(e,t)}))}:fs,Ei=ei?function(e){for(var t=[];e;)c(t,Hi(e)),e=Ns(e);return t}:fs,Fi=zt;(di&&Fi(new di(new ArrayBuffer(1)))!=_e||pi&&Fi(new pi)!=de||mi&&Fi(mi.resolve())!=fe||fi&&Fi(new fi)!=ge||hi&&Fi(new hi)!=be)&&(Fi=function(e){var t=zt(e),n=t==me?e.constructor:B,a=n?uo(n):"";if(a)switch(a){case bi:return _e;case wi:return de;case _i:return fe;case ki:return ge;case Ii:return be}return t});var Ni=As?Uo:hs,Bi=ro(Pi),Vi=Ks||function(e,t){return tn.setTimeout(e,t)},Yi=ro(Oi),zi=function(e){var t=Ro(e,function(e){return 500===n.size&&n.clear(),e}),n=t.cache;return t}(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ve,function(e,n,a,o){t.push(a?o.replace(Xe,"$1"):n||e)}),t}),qi=Mn(function(e,t){return Mo(e)?Ot(e,Ut(t,1,Mo,!0)):[]}),Wi=Mn(function(e,t){var n=yo(t);return Mo(n)&&(n=B),Mo(e)?Ot(e,Ut(t,1,Mo,!0),Va(n,2)):[]}),Gi=Mn(function(e,t){var n=yo(t);return Mo(n)&&(n=B),Mo(e)?Ot(e,Ut(t,1,Mo,!0),B,n):[]}),Ji=Mn(function(e){var t=l(e,ea);return t.length&&t[0]===e[0]?nn(t):[]}),Qi=Mn(function(e){var t=yo(e),n=l(e,ea);return t===yo(n)?t=B:n.pop(),n.length&&n[0]===e[0]?nn(n,Va(t,2)):[]}),Ki=Mn(function(e){var t=yo(e),n=l(e,ea);return(t="function"==typeof t?t:B)&&n.pop(),n.length&&n[0]===e[0]?nn(n,B,t):[]}),Zi=Mn(bo),Xi=Ha(function(e,t){var n=null==e?0:e.length,a=At(e,t);return Pn(e,l(t,function(e){return Qa(e,n)?+e:e}).sort(ra)),a}),er=Mn(function(e){return Wn(Ut(e,1,Mo,!0))}),tr=Mn(function(e){var t=yo(e);return Mo(t)&&(t=B),Wn(Ut(e,1,Mo,!0),Va(t,2))}),nr=Mn(function(e){var t=yo(e);return t="function"==typeof t?t:B,Wn(Ut(e,1,Mo,!0),B,t)}),ar=Mn(function(e,t){return Mo(e)?Ot(e,t):[]}),or=Mn(function(e){return Zn(s(e,Mo))}),sr=Mn(function(e){var t=yo(e);return Mo(t)&&(t=B),Zn(s(e,Mo),Va(t,2))}),ir=Mn(function(e){var t=yo(e);return t="function"==typeof t?t:B,Zn(s(e,Mo),B,t)}),rr=Mn(_o),lr=Mn(function(e){var t=e.length,n=t>1?e[t-1]:B;return n="function"==typeof n?(e.pop(),n):B,ko(e,n)}),cr=Ha(function(e){var t=e.length,n=t?e[0]:0,a=this.__wrapped__,o=function(t){return At(t,e)};return!(t>1||this.__actions__.length)&&a instanceof pt&&Qa(n)?((a=a.slice(n,+n+(t?1:0))).__actions__.push({func:Co,args:[o],thisArg:B}),new dt(a,this.__chain__).thru(function(e){return t&&!e.length&&e.push(B),e})):this.thru(o)}),ur=pa(function(e,t,n){Ts.call(e,n)?++e[n]:Lt(e,n,1)}),dr=wa(fo),pr=wa(ho),mr=pa(function(e,t,n){Ts.call(e,n)?e[n].push(t):Lt(e,n,[t])}),fr=Mn(function(t,n,a){var o=-1,s="function"==typeof n,i=xo(t)?gs(t.length):[];return Ai(t,function(t){i[++o]=s?e(n,t,a):an(t,n,a)}),i}),hr=pa(function(e,t,n){Lt(e,n,t)}),gr=pa(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]}),vr=Mn(function(e,t){if(null==e)return[];var n=t.length;return n>1&&Ka(e,t[0],t[1])?t=[]:n>2&&Ka(t[0],t[1],t[2])&&(t=[t[0]]),jn(e,Ut(t,1),[])}),yr=Qs||function(){return tn.Date.now()},br=Mn(function(e,t,n){var a=1;if(n.length){var o=$(n,Ba(br));a|=W}return Oa(e,a,t,n,o)}),wr=Mn(function(e,t,n){var a=3;if(n.length){var o=$(n,Ba(wr));a|=W}return Oa(t,a,e,n,o)}),_r=Mn(function(e,t){return Pt(e,1,t)}),kr=Mn(function(e,t,n){return Pt(e,Qo(t)||0,n)});Ro.Cache=ht;var Ir=xi(function(t,n){var a=(n=1==n.length&&Tr(n[0])?l(n[0],D(Va())):l(Ut(n,1),D(Va()))).length;return Mn(function(o){for(var s=-1,i=ii(o.length,a);++s<i;)o[s]=n[s].call(this,o[s]);return e(t,this,o)})}),Cr=Mn(function(e,t){return Oa(e,W,B,t,$(t,Ba(Cr)))}),Sr=Mn(function(e,t){return Oa(e,G,B,t,$(t,Ba(Sr)))}),Dr=Ha(function(e,t){return Oa(e,Q,B,B,B,t)}),Lr=ja(Qt),Ar=ja(function(e,t){return e>=t}),jr=sn(function(){return arguments}())?sn:function(e){return No(e)&&Ts.call(e,"callee")&&!Vs.call(e,"callee")},Tr=gs.isArray,Rr=ln?D(ln):function(e){return No(e)&&zt(e)==we},Pr=ti||hs,Or=cn?D(cn):function(e){return No(e)&&zt(e)==re},xr=un?D(un):function(e){return No(e)&&Fi(e)==de},Mr=dn?D(dn):function(e){return No(e)&&zt(e)==he},$r=pn?D(pn):function(e){return No(e)&&Fi(e)==ge},Ur=mn?D(mn):function(e){return No(e)&&Eo(e.length)&&!!Gt[zt(e)]},Hr=ja(In),Er=ja(function(e,t){return e<=t}),Fr=ma(function(e,t){if(eo(t)||xo(t))return da(t,ts(t),e),B;for(var n in t)Ts.call(t,n)&&It(e,n,t[n])}),Nr=ma(function(e,t){da(t,ns(t),e)}),Br=ma(function(e,t,n,a){da(t,ns(t),e,a)}),Vr=ma(function(e,t,n,a){da(t,ts(t),e,a)}),Yr=Ha(At),zr=Mn(function(e,t){e=_s(e);var n=-1,a=t.length,o=a>2?t[2]:B;for(o&&Ka(t[0],t[1],o)&&(a=1);++n<a;)for(var s=t[n],i=ns(s),r=-1,l=i.length;++r<l;){var c=i[r],u=e[c];(u===B||Oo(u,Ls[c])&&!Ts.call(e,c))&&(e[c]=s[c])}return e}),qr=Mn(function(t){return t.push(B,Ma),e(Kr,B,t)}),Wr=Ia(function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Os.call(t)),e[t]=n},ls(cs)),Gr=Ia(function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Os.call(t)),Ts.call(e,t)?e[t].push(n):e[t]=[n]},Va),Jr=Mn(an),Qr=ma(function(e,t,n){Ln(e,t,n)}),Kr=ma(function(e,t,n,a){Ln(e,t,n,a)}),Zr=Ha(function(e,t){var n={};if(null==e)return n;var a=!1;t=l(t,function(t){return t=na(t,e),a||(a=t.length>1),t}),da(e,Fa(e),n),a&&(n=Tt(n,7,$a));for(var o=t.length;o--;)Gn(n,t[o]);return n}),Xr=Ha(function(e,t){return null==e?{}:function(e,t){return Tn(e,t,function(t,n){return es(e,n)})}(e,t)}),el=Pa(ts),tl=Pa(ns),nl=va(function(e,t,n){return t=t.toLowerCase(),e+(n?ss(t):t)}),al=va(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),ol=va(function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}),sl=ga("toLowerCase"),il=va(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}),rl=va(function(e,t,n){return e+(n?" ":"")+cl(t)}),ll=va(function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}),cl=ga("toUpperCase"),ul=Mn(function(t,n){try{return e(t,B,n)}catch(e){return $o(e)?e:new ys(e)}}),dl=Ha(function(e,t){return n(t,function(t){t=co(t),Lt(e,t,br(e[t],e))}),e}),pl=_a(),ml=_a(!0),fl=Mn(function(e,t){return function(n){return an(n,e,t)}}),hl=Mn(function(e,t){return function(n){return an(e,n,t)}}),gl=Sa(l),vl=Sa(o),yl=Sa(p),bl=Aa(),wl=Aa(!0),_l=Ca(function(e,t){return e+t},0),kl=Ra("ceil"),Il=Ca(function(e,t){return e/t},1),Cl=Ra("floor"),Sl=Ca(function(e,t){return e*t},1),Dl=Ra("round"),Ll=Ca(function(e,t){return e-t},0);return Ke.after=function(e,t){if("function"!=typeof t)throw new Cs(V);return e=Go(e),function(){if(--e<1)return t.apply(this,arguments)}},Ke.ary=Ao,Ke.assign=Fr,Ke.assignIn=Nr,Ke.assignInWith=Br,Ke.assignWith=Vr,Ke.at=Yr,Ke.before=jo,Ke.bind=br,Ke.bindAll=dl,Ke.bindKey=wr,Ke.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Tr(e)?e:[e]},Ke.chain=Io,Ke.chunk=function(e,t,n){t=(n?Ka(e,t,n):t===B)?1:si(Go(t),0);var a=null==e?0:e.length;if(!a||t<1)return[];for(var o=0,s=0,i=gs(Zs(a/t));o<a;)i[s++]=Fn(e,o,o+=t);return i},Ke.compact=function(e){for(var t=-1,n=null==e?0:e.length,a=0,o=[];++t<n;){var s=e[t];s&&(o[a++]=s)}return o},Ke.concat=function(){var e=arguments.length;if(!e)return[];for(var t=gs(e-1),n=arguments[0],a=e;a--;)t[a-1]=arguments[a];return c(Tr(n)?ua(n):[n],Ut(t,1))},Ke.cond=function(t){var n=null==t?0:t.length,a=Va();return t=n?l(t,function(e){if("function"!=typeof e[1])throw new Cs(V);return[a(e[0]),e[1]]}):[],Mn(function(a){for(var o=-1;++o<n;){var s=t[o];if(e(s[0],this,a))return e(s[1],this,a)}})},Ke.conforms=function(e){return function(e){var t=ts(e);return function(n){return Rt(n,e,t)}}(Tt(e,1))},Ke.constant=ls,Ke.countBy=ur,Ke.create=function(e,t){var n=Li(e);return null==t?n:Dt(n,t)},Ke.curry=function e(t,n,a){var o=Oa(t,8,B,B,B,B,B,n=a?B:n);return o.placeholder=e.placeholder,o},Ke.curryRight=function e(t,n,a){var o=Oa(t,q,B,B,B,B,B,n=a?B:n);return o.placeholder=e.placeholder,o},Ke.debounce=To,Ke.defaults=zr,Ke.defaultsDeep=qr,Ke.defer=_r,Ke.delay=kr,Ke.difference=qi,Ke.differenceBy=Wi,Ke.differenceWith=Gi,Ke.drop=function(e,t,n){var a=null==e?0:e.length;return a?Fn(e,(t=n||t===B?1:Go(t))<0?0:t,a):[]},Ke.dropRight=function(e,t,n){var a=null==e?0:e.length;return a?Fn(e,0,(t=a-(t=n||t===B?1:Go(t)))<0?0:t):[]},Ke.dropRightWhile=function(e,t){return e&&e.length?Qn(e,Va(t,3),!0,!0):[]},Ke.dropWhile=function(e,t){return e&&e.length?Qn(e,Va(t,3),!0):[]},Ke.fill=function(e,t,n,a){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&Ka(e,t,n)&&(n=0,a=o),function(e,t,n,a){var o=e.length;for((n=Go(n))<0&&(n=-n>o?0:o+n),(a=a===B||a>o?o:Go(a))<0&&(a+=o),a=n>a?0:Jo(a);n<a;)e[n++]=t;return e}(e,t,n,a)):[]},Ke.filter=function(e,t){return(Tr(e)?s:$t)(e,Va(t,3))},Ke.flatMap=function(e,t){return Ut(Lo(e,t),1)},Ke.flatMapDeep=function(e,t){return Ut(Lo(e,t),K)},Ke.flatMapDepth=function(e,t,n){return n=n===B?1:Go(n),Ut(Lo(e,t),n)},Ke.flatten=go,Ke.flattenDeep=function(e){return null!=e&&e.length?Ut(e,K):[]},Ke.flattenDepth=function(e,t){return null!=e&&e.length?Ut(e,t=t===B?1:Go(t)):[]},Ke.flip=function(e){return Oa(e,512)},Ke.flow=pl,Ke.flowRight=ml,Ke.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,a={};++t<n;){var o=e[t];a[o[0]]=o[1]}return a},Ke.functions=function(e){return null==e?[]:Bt(e,ts(e))},Ke.functionsIn=function(e){return null==e?[]:Bt(e,ns(e))},Ke.groupBy=mr,Ke.initial=function(e){return null!=e&&e.length?Fn(e,0,-1):[]},Ke.intersection=Ji,Ke.intersectionBy=Qi,Ke.intersectionWith=Ki,Ke.invert=Wr,Ke.invertBy=Gr,Ke.invokeMap=fr,Ke.iteratee=us,Ke.keyBy=hr,Ke.keys=ts,Ke.keysIn=ns,Ke.map=Lo,Ke.mapKeys=function(e,t){var n={};return t=Va(t,3),Ht(e,function(e,a,o){Lt(n,t(e,a,o),e)}),n},Ke.mapValues=function(e,t){var n={};return t=Va(t,3),Ht(e,function(e,a,o){Lt(n,a,t(e,a,o))}),n},Ke.matches=function(e){return Sn(Tt(e,1))},Ke.matchesProperty=function(e,t){return Dn(e,Tt(t,1))},Ke.memoize=Ro,Ke.merge=Qr,Ke.mergeWith=Kr,Ke.method=fl,Ke.methodOf=hl,Ke.mixin=ds,Ke.negate=Po,Ke.nthArg=function(e){return e=Go(e),Mn(function(t){return An(t,e)})},Ke.omit=Zr,Ke.omitBy=function(e,t){return as(e,Po(Va(t)))},Ke.once=function(e){return jo(2,e)},Ke.orderBy=function(e,t,n,a){return null==e?[]:(Tr(t)||(t=null==t?[]:[t]),Tr(n=a?B:n)||(n=null==n?[]:[n]),jn(e,t,n))},Ke.over=gl,Ke.overArgs=Ir,Ke.overEvery=vl,Ke.overSome=yl,Ke.partial=Cr,Ke.partialRight=Sr,Ke.partition=gr,Ke.pick=Xr,Ke.pickBy=as,Ke.property=ms,Ke.propertyOf=function(e){return function(t){return null==e?B:Vt(e,t)}},Ke.pull=Zi,Ke.pullAll=bo,Ke.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Rn(e,t,Va(n,2)):e},Ke.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Rn(e,t,B,n):e},Ke.pullAt=Xi,Ke.range=bl,Ke.rangeRight=wl,Ke.rearg=Dr,Ke.reject=function(e,t){return(Tr(e)?s:$t)(e,Po(Va(t,3)))},Ke.remove=function(e,t){var n=[];if(!e||!e.length)return n;var a=-1,o=[],s=e.length;for(t=Va(t,3);++a<s;){var i=e[a];t(i,a,e)&&(n.push(i),o.push(a))}return Pn(e,o),n},Ke.rest=function(e,t){if("function"!=typeof e)throw new Cs(V);return Mn(e,t=t===B?t:Go(t))},Ke.reverse=wo,Ke.sampleSize=function(e,t,n){return t=(n?Ka(e,t,n):t===B)?1:Go(t),(Tr(e)?wt:Un)(e,t)},Ke.set=function(e,t,n){return null==e?e:Hn(e,t,n)},Ke.setWith=function(e,t,n,a){return a="function"==typeof a?a:B,null==e?e:Hn(e,t,n,a)},Ke.shuffle=function(e){return(Tr(e)?_t:En)(e)},Ke.slice=function(e,t,n){var a=null==e?0:e.length;return a?(n&&"number"!=typeof n&&Ka(e,t,n)?(t=0,n=a):(t=null==t?0:Go(t),n=n===B?a:Go(n)),Fn(e,t,n)):[]},Ke.sortBy=vr,Ke.sortedUniq=function(e){return e&&e.length?Yn(e):[]},Ke.sortedUniqBy=function(e,t){return e&&e.length?Yn(e,Va(t,2)):[]},Ke.split=function(e,t,n){return n&&"number"!=typeof n&&Ka(e,t,n)&&(t=n=B),(n=n===B?ee:n>>>0)?(e=Zo(e))&&("string"==typeof t||null!=t&&!Mr(t))&&(!(t=qn(t))&&P(e))?aa(E(e),0,n):e.split(t,n):[]},Ke.spread=function(t,n){if("function"!=typeof t)throw new Cs(V);return n=null==n?0:si(Go(n),0),Mn(function(a){var o=a[n],s=aa(a,0,n);return o&&c(s,o),e(t,this,s)})},Ke.tail=function(e){var t=null==e?0:e.length;return t?Fn(e,1,t):[]},Ke.take=function(e,t,n){return e&&e.length?Fn(e,0,(t=n||t===B?1:Go(t))<0?0:t):[]},Ke.takeRight=function(e,t,n){var a=null==e?0:e.length;return a?Fn(e,(t=a-(t=n||t===B?1:Go(t)))<0?0:t,a):[]},Ke.takeRightWhile=function(e,t){return e&&e.length?Qn(e,Va(t,3),!1,!0):[]},Ke.takeWhile=function(e,t){return e&&e.length?Qn(e,Va(t,3)):[]},Ke.tap=function(e,t){return t(e),e},Ke.throttle=function(e,t,n){var a=!0,o=!0;if("function"!=typeof e)throw new Cs(V);return Fo(n)&&(a="leading"in n?!!n.leading:a,o="trailing"in n?!!n.trailing:o),To(e,t,{leading:a,maxWait:t,trailing:o})},Ke.thru=Co,Ke.toArray=qo,Ke.toPairs=el,Ke.toPairsIn=tl,Ke.toPath=function(e){return Tr(e)?l(e,co):zo(e)?[e]:ua(zi(Zo(e)))},Ke.toPlainObject=Ko,Ke.transform=function(e,t,a){var o=Tr(e),s=o||Pr(e)||Ur(e);if(t=Va(t,4),null==a){var i=e&&e.constructor;a=s?o?new i:[]:Fo(e)&&Uo(i)?Li(Ns(e)):{}}return(s?n:Ht)(e,function(e,n,o){return t(a,e,n,o)}),a},Ke.unary=function(e){return Ao(e,1)},Ke.union=er,Ke.unionBy=tr,Ke.unionWith=nr,Ke.uniq=function(e){return e&&e.length?Wn(e):[]},Ke.uniqBy=function(e,t){return e&&e.length?Wn(e,Va(t,2)):[]},Ke.uniqWith=function(e,t){return t="function"==typeof t?t:B,e&&e.length?Wn(e,B,t):[]},Ke.unset=function(e,t){return null==e||Gn(e,t)},Ke.unzip=_o,Ke.unzipWith=ko,Ke.update=function(e,t,n){return null==e?e:Jn(e,t,ta(n))},Ke.updateWith=function(e,t,n,a){return a="function"==typeof a?a:B,null==e?e:Jn(e,t,ta(n),a)},Ke.values=os,Ke.valuesIn=function(e){return null==e?[]:L(e,ns(e))},Ke.without=ar,Ke.words=rs,Ke.wrap=function(e,t){return Cr(ta(t),e)},Ke.xor=or,Ke.xorBy=sr,Ke.xorWith=ir,Ke.zip=rr,Ke.zipObject=function(e,t){return Xn(e||[],t||[],It)},Ke.zipObjectDeep=function(e,t){return Xn(e||[],t||[],Hn)},Ke.zipWith=lr,Ke.entries=el,Ke.entriesIn=tl,Ke.extend=Nr,Ke.extendWith=Br,ds(Ke,Ke),Ke.add=_l,Ke.attempt=ul,Ke.camelCase=nl,Ke.capitalize=ss,Ke.ceil=kl,Ke.clamp=function(e,t,n){return n===B&&(n=t,t=B),n!==B&&(n=(n=Qo(n))==n?n:0),t!==B&&(t=(t=Qo(t))==t?t:0),jt(Qo(e),t,n)},Ke.clone=function(e){return Tt(e,4)},Ke.cloneDeep=function(e){return Tt(e,5)},Ke.cloneDeepWith=function(e,t){return Tt(e,5,t="function"==typeof t?t:B)},Ke.cloneWith=function(e,t){return Tt(e,4,t="function"==typeof t?t:B)},Ke.conformsTo=function(e,t){return null==t||Rt(e,t,ts(t))},Ke.deburr=is,Ke.defaultTo=function(e,t){return null==e||e!=e?t:e},Ke.divide=Il,Ke.endsWith=function(e,t,n){e=Zo(e),t=qn(t);var a=e.length,o=n=n===B?a:jt(Go(n),0,a);return(n-=t.length)>=0&&e.slice(n,o)==t},Ke.eq=Oo,Ke.escape=function(e){return(e=Zo(e))&&Ue.test(e)?e.replace(Me,gn):e},Ke.escapeRegExp=function(e){return(e=Zo(e))&&ze.test(e)?e.replace(Ye,"\\$&"):e},Ke.every=function(e,t,n){var a=Tr(e)?o:xt;return n&&Ka(e,t,n)&&(t=B),a(e,Va(t,3))},Ke.find=dr,Ke.findIndex=fo,Ke.findKey=function(e,t){return f(e,Va(t,3),Ht)},Ke.findLast=pr,Ke.findLastIndex=ho,Ke.findLastKey=function(e,t){return f(e,Va(t,3),Et)},Ke.floor=Cl,Ke.forEach=So,Ke.forEachRight=Do,Ke.forIn=function(e,t){return null==e?e:Ti(e,Va(t,3),ns)},Ke.forInRight=function(e,t){return null==e?e:Ri(e,Va(t,3),ns)},Ke.forOwn=function(e,t){return e&&Ht(e,Va(t,3))},Ke.forOwnRight=function(e,t){return e&&Et(e,Va(t,3))},Ke.get=Xo,Ke.gt=Lr,Ke.gte=Ar,Ke.has=function(e,t){return null!=e&&Wa(e,t,Xt)},Ke.hasIn=es,Ke.head=vo,Ke.identity=cs,Ke.includes=function(e,t,n,a){e=xo(e)?e:os(e),n=n&&!a?Go(n):0;var o=e.length;return n<0&&(n=si(o+n,0)),Yo(e)?n<=o&&e.indexOf(t,n)>-1:!!o&&g(e,t,n)>-1},Ke.indexOf=function(e,t,n){var a=null==e?0:e.length;if(!a)return-1;var o=null==n?0:Go(n);return o<0&&(o=si(a+o,0)),g(e,t,o)},Ke.inRange=function(e,t,n){return t=Wo(t),n===B?(n=t,t=0):n=Wo(n),function(e,t,n){return e>=ii(t,n)&&e<si(t,n)}(e=Qo(e),t,n)},Ke.invoke=Jr,Ke.isArguments=jr,Ke.isArray=Tr,Ke.isArrayBuffer=Rr,Ke.isArrayLike=xo,Ke.isArrayLikeObject=Mo,Ke.isBoolean=function(e){return!0===e||!1===e||No(e)&&zt(e)==ie},Ke.isBuffer=Pr,Ke.isDate=Or,Ke.isElement=function(e){return No(e)&&1===e.nodeType&&!Vo(e)},Ke.isEmpty=function(e){if(null==e)return!0;if(xo(e)&&(Tr(e)||"string"==typeof e||"function"==typeof e.splice||Pr(e)||Ur(e)||jr(e)))return!e.length;var t=Fi(e);if(t==de||t==ge)return!e.size;if(eo(e))return!_n(e).length;for(var n in e)if(Ts.call(e,n))return!1;return!0},Ke.isEqual=function(e,t){return rn(e,t)},Ke.isEqualWith=function(e,t,n){var a=(n="function"==typeof n?n:B)?n(e,t):B;return a===B?rn(e,t,B,n):!!a},Ke.isError=$o,Ke.isFinite=function(e){return"number"==typeof e&&ni(e)},Ke.isFunction=Uo,Ke.isInteger=Ho,Ke.isLength=Eo,Ke.isMap=xr,Ke.isMatch=function(e,t){return e===t||fn(e,t,za(t))},Ke.isMatchWith=function(e,t,n){return n="function"==typeof n?n:B,fn(e,t,za(t),n)},Ke.isNaN=function(e){return Bo(e)&&e!=+e},Ke.isNative=function(e){if(Ni(e))throw new ys("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return bn(e)},Ke.isNil=function(e){return null==e},Ke.isNull=function(e){return null===e},Ke.isNumber=Bo,Ke.isObject=Fo,Ke.isObjectLike=No,Ke.isPlainObject=Vo,Ke.isRegExp=Mr,Ke.isSafeInteger=function(e){return Ho(e)&&e>=-Z&&e<=Z},Ke.isSet=$r,Ke.isString=Yo,Ke.isSymbol=zo,Ke.isTypedArray=Ur,Ke.isUndefined=function(e){return e===B},Ke.isWeakMap=function(e){return No(e)&&Fi(e)==be},Ke.isWeakSet=function(e){return No(e)&&"[object WeakSet]"==zt(e)},Ke.join=function(e,t){return null==e?"":ai.call(e,t)},Ke.kebabCase=al,Ke.last=yo,Ke.lastIndexOf=function(e,t,n){var a=null==e?0:e.length;if(!a)return-1;var o=a;return n!==B&&(o=(o=Go(n))<0?si(a+o,0):ii(o,a-1)),t==t?function(e,t,n){for(var a=n+1;a--;)if(e[a]===t)return a;return a}(e,t,o):h(e,y,o,!0)},Ke.lowerCase=ol,Ke.lowerFirst=sl,Ke.lt=Hr,Ke.lte=Er,Ke.max=function(e){return e&&e.length?Mt(e,cs,Qt):B},Ke.maxBy=function(e,t){return e&&e.length?Mt(e,Va(t,2),Qt):B},Ke.mean=function(e){return b(e,cs)},Ke.meanBy=function(e,t){return b(e,Va(t,2))},Ke.min=function(e){return e&&e.length?Mt(e,cs,In):B},Ke.minBy=function(e,t){return e&&e.length?Mt(e,Va(t,2),In):B},Ke.stubArray=fs,Ke.stubFalse=hs,Ke.stubObject=function(){return{}},Ke.stubString=function(){return""},Ke.stubTrue=function(){return!0},Ke.multiply=Sl,Ke.nth=function(e,t){return e&&e.length?An(e,Go(t)):B},Ke.noConflict=function(){return tn._===this&&(tn._=Ms),this},Ke.noop=ps,Ke.now=yr,Ke.pad=function(e,t,n){e=Zo(e);var a=(t=Go(t))?H(e):0;if(!t||a>=t)return e;var o=(t-a)/2;return Da(Xs(o),n)+e+Da(Zs(o),n)},Ke.padEnd=function(e,t,n){e=Zo(e);var a=(t=Go(t))?H(e):0;return t&&a<t?e+Da(t-a,n):e},Ke.padStart=function(e,t,n){e=Zo(e);var a=(t=Go(t))?H(e):0;return t&&a<t?Da(t-a,n)+e:e},Ke.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),li(Zo(e).replace(qe,""),t||0)},Ke.random=function(e,t,n){if(n&&"boolean"!=typeof n&&Ka(e,t,n)&&(t=n=B),n===B&&("boolean"==typeof t?(n=t,t=B):"boolean"==typeof e&&(n=e,e=B)),e===B&&t===B?(e=0,t=1):(e=Wo(e),t===B?(t=e,e=0):t=Wo(t)),e>t){var a=e;e=t,t=a}if(n||e%1||t%1){var o=ci();return ii(e+o*(t-e+Kt("1e-"+((o+"").length-1))),t)}return On(e,t)},Ke.reduce=function(e,t,n){var a=Tr(e)?u:k,o=arguments.length<3;return a(e,Va(t,4),n,o,Ai)},Ke.reduceRight=function(e,t,n){var a=Tr(e)?d:k,o=arguments.length<3;return a(e,Va(t,4),n,o,ji)},Ke.repeat=function(e,t,n){return t=(n?Ka(e,t,n):t===B)?1:Go(t),xn(Zo(e),t)},Ke.replace=function(){var e=arguments,t=Zo(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Ke.result=function(e,t,n){var a=-1,o=(t=na(t,e)).length;for(o||(o=1,e=B);++a<o;){var s=null==e?B:e[co(t[a])];s===B&&(a=o,s=n),e=Uo(s)?s.call(e):s}return e},Ke.round=Dl,Ke.runInContext=_,Ke.sample=function(e){return(Tr(e)?bt:$n)(e)},Ke.size=function(e){if(null==e)return 0;if(xo(e))return Yo(e)?H(e):e.length;var t=Fi(e);return t==de||t==ge?e.size:_n(e).length},Ke.snakeCase=il,Ke.some=function(e,t,n){var a=Tr(e)?p:Nn;return n&&Ka(e,t,n)&&(t=B),a(e,Va(t,3))},Ke.sortedIndex=function(e,t){return Bn(e,t)},Ke.sortedIndexBy=function(e,t,n){return Vn(e,t,Va(n,2))},Ke.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var a=Bn(e,t);if(a<n&&Oo(e[a],t))return a}return-1},Ke.sortedLastIndex=function(e,t){return Bn(e,t,!0)},Ke.sortedLastIndexBy=function(e,t,n){return Vn(e,t,Va(n,2),!0)},Ke.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Bn(e,t,!0)-1;if(Oo(e[n],t))return n}return-1},Ke.startCase=rl,Ke.startsWith=function(e,t,n){return e=Zo(e),n=null==n?0:jt(Go(n),0,e.length),t=qn(t),e.slice(n,n+t.length)==t},Ke.subtract=Ll,Ke.sum=function(e){return e&&e.length?I(e,cs):0},Ke.sumBy=function(e,t){return e&&e.length?I(e,Va(t,2)):0},Ke.template=function(e,t,n){var a=Ke.templateSettings;n&&Ka(e,t,n)&&(t=B),e=Zo(e),t=Br({},t,a,xa);var o,s,i=Br({},t.imports,a.imports,xa),r=ts(i),l=L(i,r),c=0,u=t.interpolate||lt,d="__p += '",p=ks((t.escape||lt).source+"|"+u.source+"|"+(u===Fe?et:lt).source+"|"+(t.evaluate||lt).source+"|$","g"),m="//# sourceURL="+(Ts.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Wt+"]")+"\n";e.replace(p,function(t,n,a,i,r,l){return a||(a=i),d+=e.slice(c,l).replace(ct,R),n&&(o=!0,d+="' +\n__e("+n+") +\n'"),r&&(s=!0,d+="';\n"+r+";\n__p += '"),a&&(d+="' +\n((__t = ("+a+")) == null ? '' : __t) +\n'"),c=l+t.length,t}),d+="';\n";var f=Ts.call(t,"variable")&&t.variable;if(f){if(Ze.test(f))throw new ys("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(s?d.replace(Re,""):d).replace(Pe,"$1").replace(Oe,"$1;"),d="function("+(f||"obj")+") {\n"+(f?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(o?", __e = _.escape":"")+(s?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var h=ul(function(){return bs(r,m+"return "+d).apply(B,l)});if(h.source=d,$o(h))throw h;return h},Ke.times=function(e,t){if((e=Go(e))<1||e>Z)return[];var n=ee,a=ii(e,ee);t=Va(t),e-=ee;for(var o=C(a,t);++n<e;)t(n);return o},Ke.toFinite=Wo,Ke.toInteger=Go,Ke.toLength=Jo,Ke.toLower=function(e){return Zo(e).toLowerCase()},Ke.toNumber=Qo,Ke.toSafeInteger=function(e){return e?jt(Go(e),-Z,Z):0===e?e:0},Ke.toString=Zo,Ke.toUpper=function(e){return Zo(e).toUpperCase()},Ke.trim=function(e,t,n){if((e=Zo(e))&&(n||t===B))return S(e);if(!e||!(t=qn(t)))return e;var a=E(e),o=E(t);return aa(a,j(a,o),T(a,o)+1).join("")},Ke.trimEnd=function(e,t,n){if((e=Zo(e))&&(n||t===B))return e.slice(0,F(e)+1);if(!e||!(t=qn(t)))return e;var a=E(e);return aa(a,0,T(a,E(t))+1).join("")},Ke.trimStart=function(e,t,n){if((e=Zo(e))&&(n||t===B))return e.replace(qe,"");if(!e||!(t=qn(t)))return e;var a=E(e);return aa(a,j(a,E(t))).join("")},Ke.truncate=function(e,t){var n=30,a="...";if(Fo(t)){var o="separator"in t?t.separator:o;n="length"in t?Go(t.length):n,a="omission"in t?qn(t.omission):a}var s=(e=Zo(e)).length;if(P(e)){var i=E(e);s=i.length}if(n>=s)return e;var r=n-H(a);if(r<1)return a;var l=i?aa(i,0,r).join(""):e.slice(0,r);if(o===B)return l+a;if(i&&(r+=l.length-r),Mr(o)){if(e.slice(r).search(o)){var c,u=l;for(o.global||(o=ks(o.source,Zo(tt.exec(o))+"g")),o.lastIndex=0;c=o.exec(u);)var d=c.index;l=l.slice(0,d===B?r:d)}}else if(e.indexOf(qn(o),r)!=r){var p=l.lastIndexOf(o);p>-1&&(l=l.slice(0,p))}return l+a},Ke.unescape=function(e){return(e=Zo(e))&&$e.test(e)?e.replace(xe,vn):e},Ke.uniqueId=function(e){var t=++Rs;return Zo(e)+t},Ke.upperCase=ll,Ke.upperFirst=cl,Ke.each=So,Ke.eachRight=Do,Ke.first=vo,ds(Ke,function(){var e={};return Ht(Ke,function(t,n){Ts.call(Ke.prototype,n)||(e[n]=t)}),e}(),{chain:!1}),Ke.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){Ke[e].placeholder=Ke}),n(["drop","take"],function(e,t){pt.prototype[e]=function(n){n=n===B?1:si(Go(n),0);var a=this.__filtered__&&!t?new pt(this):this.clone();return a.__filtered__?a.__takeCount__=ii(n,a.__takeCount__):a.__views__.push({size:ii(n,ee),type:e+(a.__dir__<0?"Right":"")}),a},pt.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),n(["filter","map","takeWhile"],function(e,t){var n=t+1,a=1==n||3==n;pt.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Va(e,3),type:n}),t.__filtered__=t.__filtered__||a,t}}),n(["head","last"],function(e,t){var n="take"+(t?"Right":"");pt.prototype[e]=function(){return this[n](1).value()[0]}}),n(["initial","tail"],function(e,t){var n="drop"+(t?"":"Right");pt.prototype[e]=function(){return this.__filtered__?new pt(this):this[n](1)}}),pt.prototype.compact=function(){return this.filter(cs)},pt.prototype.find=function(e){return this.filter(e).head()},pt.prototype.findLast=function(e){return this.reverse().find(e)},pt.prototype.invokeMap=Mn(function(e,t){return"function"==typeof e?new pt(this):this.map(function(n){return an(n,e,t)})}),pt.prototype.reject=function(e){return this.filter(Po(Va(e)))},pt.prototype.slice=function(e,t){e=Go(e);var n=this;return n.__filtered__&&(e>0||t<0)?new pt(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==B&&(n=(t=Go(t))<0?n.dropRight(-t):n.take(t-e)),n)},pt.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},pt.prototype.toArray=function(){return this.take(ee)},Ht(pt.prototype,function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),a=/^(?:head|last)$/.test(t),o=Ke[a?"take"+("last"==t?"Right":""):t],s=a||/^find/.test(t);o&&(Ke.prototype[t]=function(){var t=this.__wrapped__,i=a?[1]:arguments,r=t instanceof pt,l=i[0],u=r||Tr(t),d=function(e){var t=o.apply(Ke,c([e],i));return a&&p?t[0]:t};u&&n&&"function"==typeof l&&1!=l.length&&(r=u=!1);var p=this.__chain__,m=!!this.__actions__.length,f=s&&!p,h=r&&!m;if(!s&&u){t=h?t:new pt(this);var g=e.apply(t,i);return g.__actions__.push({func:Co,args:[d],thisArg:B}),new dt(g,p)}return f&&h?e.apply(this,i):(g=this.thru(d),f?a?g.value()[0]:g.value():g)})}),n(["pop","push","shift","sort","splice","unshift"],function(e){var t=Ss[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",a=/^(?:pop|shift)$/.test(e);Ke.prototype[e]=function(){var e=arguments;if(a&&!this.__chain__){var o=this.value();return t.apply(Tr(o)?o:[],e)}return this[n](function(n){return t.apply(Tr(n)?n:[],e)})}}),Ht(pt.prototype,function(e,t){var n=Ke[t];if(n){var a=n.name+"";Ts.call(yi,a)||(yi[a]=[]),yi[a].push({name:t,func:n})}}),yi[ka(B,2).name]=[{name:"wrapper",func:B}],pt.prototype.clone=function(){var e=new pt(this.__wrapped__);return e.__actions__=ua(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=ua(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=ua(this.__views__),e},pt.prototype.reverse=function(){if(this.__filtered__){var e=new pt(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},pt.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Tr(e),a=t<0,o=n?e.length:0,s=function(e,t,n){for(var a=-1,o=n.length;++a<o;){var s=n[a],i=s.size;switch(s.type){case"drop":e+=i;break;case"dropRight":t-=i;break;case"take":t=ii(t,e+i);break;case"takeRight":e=si(e,t-i)}}return{start:e,end:t}}(0,o,this.__views__),i=s.start,r=s.end,l=r-i,c=a?r:i-1,u=this.__iteratees__,d=u.length,p=0,m=ii(l,this.__takeCount__);if(!n||!a&&o==l&&m==l)return Kn(e,this.__actions__);var f=[];e:for(;l--&&p<m;){for(var h=-1,g=e[c+=t];++h<d;){var v=u[h],y=v.iteratee,b=v.type,w=y(g);if(2==b)g=w;else if(!w){if(1==b)continue e;break e}}f[p++]=g}return f},Ke.prototype.at=cr,Ke.prototype.chain=function(){return Io(this)},Ke.prototype.commit=function(){return new dt(this.value(),this.__chain__)},Ke.prototype.next=function(){this.__values__===B&&(this.__values__=qo(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?B:this.__values__[this.__index__++]}},Ke.prototype.plant=function(e){for(var t,n=this;n instanceof ut;){var a=mo(n);a.__index__=0,a.__values__=B,t?o.__wrapped__=a:t=a;var o=a;n=n.__wrapped__}return o.__wrapped__=e,t},Ke.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof pt){var t=e;return this.__actions__.length&&(t=new pt(this)),(t=t.reverse()).__actions__.push({func:Co,args:[wo],thisArg:B}),new dt(t,this.__chain__)}return this.thru(wo)},Ke.prototype.toJSON=Ke.prototype.valueOf=Ke.prototype.value=function(){return Kn(this.__wrapped__,this.__actions__)},Ke.prototype.first=Ke.prototype.head,qs&&(Ke.prototype[qs]=function(){return this}),Ke}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(tn._=yn,define(function(){return yn})):an?((an.exports=yn)._=yn,nn._=yn):tn._=yn}).call(this),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null},hasLHSMenu:{type:Boolean,required:!1,default:!0}},template:'\n        <div :class="[header.isMenuOpen ? \'menuOpen\' : \'\']">\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid" :class="[!hasLHSMenu ? \'noLHSMenu\' : \'\']">\n                <yuno-header-v2 \n                    @userInfo="onUserInfo" \n                    @isMini="onMini" \n                    v-if="loginStatus && hasPageHeader"\n                >\n                </yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(e){this.$emit("onUserInfo",e)},onMini(e){this.isMiniSidebar=e}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(e,t){e!==t&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(e,t){"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(e){this.updateActiveOrg(e.id)},orgUpdated(e){const t=e?.response?.data;201===t?.code?(sessionStorage.clear(),window.location.reload(!0)):t?.message&&console.log(t.message)},updateActiveOrg(e){this.$buefy.loading.open();const t={apiURL:YUNOCommon.config.academy("activeOrg",!1),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:e},callback:!0,callbackFunc:e=>this.orgUpdated(e)};this.dispatchData("postData",t)},dispatchData(e,t){this.$store.dispatch(e,t)},init(){if("org-admin"===this.userInfo.data.role){const e=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=e}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e}}});const YUNOCommon=function(){const e={errorMsg:{common:"An error seems to have occurred. Please try again. ",notMapped:"The selected course doesn't mapped with any instructor. Please select other course.",enrollmentError:"It seems something went wrong with our servers. Our team has been notified. Please try again later.",sesstionExpired:"Your session has been expired. Please login again to resume your session. "},awsHost:function(){let e=window.location.hostname;return"localhost"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"www.yunolearning.com"===e?"https://api.yunolearning.com":"stage.yunolearning.com"===e||"dev.yunolearning.com"===e?"https://ewx6vb5jgg.execute-api.ap-south-1.amazonaws.com/dev":"webcache.googleusercontent.com"===e?"https://api.yunolearning.com":void 0},addVerion:function(e){let t="";return t=e?"?buildVersion=1":"&buildVersion=1",t},pickHost:function(){return"http://localhost"===this.host()||"http://*************"===this.host()?"https://dev.yunolearning.com":"https://www.yunolearning.com"===this.host()||"https://webcache.googleusercontent.com"===this.host()?"https://www.yunolearning.com":this.host()},laravelHost:function(){return"http://localhost"===this.host()||"https://dev.yunolearning.com"===this.host()?"https://dev-ext.yunolearning.com":"https://stage.yunolearning.com"===this.host()?"https://stage-ext.yunolearning.com":"https://www.yunolearning.com"===this.host()?"https://ai-laravel-yxdza.kinsta.app":void 0},host:function(){return window.location.protocol+"//"+window.location.hostname},footerAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/footer"},latesBlogAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/latest/blogs"},courseIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/short-detail"},allCoursesIELTSAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/courses/ielts/detail"},headerMenuAPI:function(e,t){let n="";void 0!==t&&(n="?category="+t);return this.pickHost()+"/wp-json/yuno/v1/menu/"+e+n},headerMenuAPIV2:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/menu/"+e+"/"+t},userRoleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/role"},userProfileAPI:function(e,t){let a="";t&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/users/"+e+"/profile"+a},studentResultsAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/results/"+e+"/"+t+"/"+n},faqAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/faq/"+e},courseAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/getdetail/"+e+"/"+t},scheduleAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/schedule"},instructorAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/getUserDetail"},updateInstructorDetailAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUserDetail"},instructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/instructor/"+e+"/"+t},instructorBatchAPI:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/instructor/"+t+"/"+n+"/"+a+"/"+o+"/"+s},instructorNonBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructorSingleProductDetail?instructorID="+e},categoriesAPi:function(e){void 0===e&&(e="");return this.pickHost()+"/wp-json/yuno/v1/category"+e},featuredCoursesAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredCourses"},featuredInstructorAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/featuredInstructor/"+e},batchAPi:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v2/batches/"+e+"/"+t+"/"+n+"/"+a},classAPi:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v1/classes/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s},signUpAPi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},signUpV2APi:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/"+t},isUserSignUpAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/signup/"+e},loginAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/loginWithGoogle"},createPaymentAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment"},updatePaymentAPi:function(e,t,n,a,o){return this.pickHost()+"/wp-json/yuno/v1/update/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/payment"},myLearnersAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/schedule/mylearners/mygroups/"+e},classTitleAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/class-schedule/titles/instructor/"+e},addClassTitleAPi:function(){return this.pickHost()+"/wp-json/yuno/v1/add/class/title"},createClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/create/class/"+e},updateClassAPi:function(e){return this.pickHost()+"/wp-json/yuno/v2/update/class/"+e},classesAPi:function(e,t,n,a,o){return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+"/"+t+"/"+n+"/"+a+"/"+o},classesByViewAPi:function(e,t,a,o,s,i,r,l,c,u){let d="",p="",m="";u&&(d="?ver="+n()),void 0!==t&&!1!==t&&(p="/"+t),void 0!==i&&!1!==i&&(m="/"+i);return this.pickHost()+"/wp-json/yuno/v2/classes/"+e+p+"/"+a+"/"+o+"/"+s+m+"/"+r+"/"+l+"/"+c+d},groupsAPi:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v1/get/groups/"+e+"/"+t+"/"+n+"/"+a},addLearnersToGroupAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateUsersInGroup"},updateGroupTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/updateGroup"},createGroupsAPi:function(e){return this.pickHost()+"/wp-json/yuno/v1/create/group"},crmContacts:function(){return this.awsHost()+"/getCRMContacts"},instructorLearnersAPI:function(e,t,n,a,o){let s="";!1!==o&&(s="?filter="+o);return this.pickHost()+"/wp-json/yuno/v1/mylearners/"+e+"/"+t+"/"+n+"/"+a+s},instructorBatchesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/batches/"+e+"/"+t},learnerCoursesAPI:function(e,t,a){let o="";a&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/courses/"+t+o},enHeroCardsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/courses/english-speaking/detail"},classDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/classDetail/"+e+"/"+t},classLearnerAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/classLearnerDetail/"+e+"/"+t+"/"+n},demoClassEnrollAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/enroll/demo/class"},editClassAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/class/detail/"+e+"/instructor/"+t},allCoursesAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/all/course/list"+t},allBatchesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/admin/batches/all"},enrollmentStatusAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/batch/"+e+"/"+t+"/"+n+"/status"},generatePaymentLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/payment/link"},reviewsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},paymentList:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v1/payments/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s},enrollmentList:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s},enrollmentsList:function(e,t,n,a,o,s,i,r,l,c,u,d,p,m){let f="";f=void 0!==p&&!1!==p?p:"v1";return this.pickHost()+"/wp-json/yuno/"+f+"/enrollments/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s+"/"+i+"/"+r+"/"+l+"/"+c+"/"+m+"/"+u+"/"+d},paymentsList:function(e,t,a,o,s,i,r,l,c,u){let d="";u&&(d="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/payment/"+e+"/"+t+"/"+a+"/"+o+"/"+s+"/"+i+"/"+r+"/"+l+"/"+c+d},updatePaymentLinkAPI:function(){return this.awsHost()+"/payments/updatelink"},updateLinkAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/updateLink/payment"},instructorListAPI:function(e,t){let n="",a="";void 0!==t&&!1!==t&&(a="/"+t),void 0!==e&&!1!==e&&(n="/"+e);return this.pickHost()+"/wp-json/yuno/v1/instructor/list"+n+a},reviewsByTypeAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/reviews/"+e+"/"+t},batchToggleAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/enrollment/"+e+"/"+t+"/"+n+"/status/toggle"},changeBatchAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/changeBatches"},blogListAPI:function(e,t){return this.pickHost()+"/wp-json/wp/v2/posts/?per_page="+e+"&offset="+t+"&_embed"},pageAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/pages/"+e+"?_embed"},blogAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/posts/"+e+"?_embed"},postCategoriesAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/?per_page="+e},courseBatchesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batches/all/"+e+"/0/upcomingOngoing"},blogsByCategoryAPI:function(e,t,n){return this.pickHost()+"/wp-json/wp/v2/posts?categories="+e+"&per_page="+t+"&offset="+n+"&_embed"},blogCategoryAPI:function(e){return this.pickHost()+"/wp-json/wp/v2/categories/"+e},settingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings/"+e},updateSettingsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/users/notificationsettings"},addressAPI:function(e,t,a){let o="";a&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+"/address/"+t+o},updateAddressAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/user/address"},listOfCounsellorsAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/counselor/list"},googleContactsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/getGoogleContacts/"+e},meetingAPI:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v1/meeting/"+e+"/"+t+"/"+n+"/"+a},participantsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/add/zoom/participants/"+e},batchesGrid:function(e,t,n,a,o,s,i,r,l){let c="";void 0!==i&&!1!==i&&(c="/"+i);return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s+c+"/"+r+"/"+l},mapCoursesAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/instructor/courses/"+e+"/"+t+"/"+n},updateInstructorCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/add/instructor/"+e+"/course/"+t},relatedCoursesAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/courses"},categoryListAPI:function(e){let t="";void 0!==e&&(t="?filter="+e);return this.pickHost()+"/wp-json/yuno/v1/all/category/signup"+t},categoryTaxonomyAPI:function(e){let t="";void 0!==e&&!1!==e&&(t="/"+e);return this.pickHost()+"/wp-json/yuno/v1/taxonomy/course_category"+t},createEBookAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/ebook/create"},eBookListAPI:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v1/ebook/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s},deleteResourceAttachmentAPI:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/"+t+"/attachment/delete/"+n},resourceEmailAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/send/email"},createDocAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/document/create"},docListAPI:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v1/document/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s},videoListAPI:function(e,t){let n="",a="";void 0!==e&&(n=e),void 0===a&&!1===a||(a=t);return this.pickHost()+"/wp-json/yuno/v1/videos/"+n},videoSearchAPI:function(e){let t="";void 0===t&&!1===t||(t=e);return this.pickHost()+"/wp-json/yuno/v1/videos/"+t},videoListByViewAPI:function(e,t,n,a,o){let s="";if(!1!==t)s=t;else{let t="";void 0!==n&&(t=n),s=e+"/"+t+"/"+a+"/"+o}return this.pickHost()+"/wp-json/yuno/v1/video/get/"+s},createVideoAPI:function(e){let t="";t=e?"update":"create";return this.pickHost()+"/wp-json/yuno/v1/video/"+t},userInfoAPI:function(e,t){let a="";t&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v3/user/info/"+e+a},vcSettingsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/admin/vc/settings/"+e},reviewAPI:function(e,t,a,o,s,i,r){let l="",c="",u="",d="",p="",m="";void 0!==e&&!1!==e&&(l="/"+e),void 0!==o&&!1!==o&&(d="/"+o),void 0!==s&&!1!==s&&(p="/"+s),void 0!==i&&!1!==i&&(m="/"+i),void 0!==a&&!1!==a&&(u="/"+a),r&&(c="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/review"+l+"/"+t+u+d+p+m+c},courseListAPI:function(e,t,a,o){let s="",i="",r="";o&&(s="?ver="+n()),void 0!==t&&!1!==t&&(i="/"+t),void 0!==a&&!1!==a&&(r="/"+a);return this.pickHost()+"/wp-json/yuno/v1/all/"+e+"/detail/list"+i+r+s},countriesListAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/countries"},stateListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/state/country/"+e},cityListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/city/state/"+e},languageListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/user/languages"},listOfMappedInstructorAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/instructor/course/batch"},batchCreateUpdateAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/batch"},batchDetailAPI:function(e,t){let a="";t&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/batches/"+e+a},learnerListAPI:function(e,t){let a="";t&&(a="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/list/"+e+a},instructorAvailabilityAPI:function(e,t,a){let o="",s="";a&&(o="?ver="+n()),void 0!==t&&!1!==t&&(s="/"+t);return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+s+o},createUpdateAvailabilityAPI:function(e,t){let a="";t&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+a},timeSlotsAPI:function(e){let t="";e&&(t="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/slots"+t},availabilityGridAPI:function(e,t){let a="";t&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/days/"+e+a},instructorsByCategoryAPI:function(e,t){let a="";t&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/category/"+e+a},capabilitiesAPI:function(e,t){let a="";t&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/forte/"+e+a},paymentLinkUpdateAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/update/payment/link"},getInviteURLAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/invitation/link/"+e},invitedByUserAPI:function(e,t){let a="";t&&(a="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/user/"+e+a},signInURLWithState(e){const t=["email","profile"],n=encodeURI(JSON.stringify(e));let a="";if(void 0!==yunoCognitoLoginURL){const e=new URL(yunoCognitoLoginURL);e.searchParams.set("state",n);a=e.toString()}else a="https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&state="+n+"&scope="+t.join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow";return a},updateUserCategoryAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/user/insert/category"},learnerHistoryAPI:function(e,t,a){let o="";a&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/event/history/"+e+"/"+t+o},eventDetailAPI:function(e,t,a,o,s){let i="";s&&(i="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/event/history/detail/"+e+"/"+t+"/"+a+"?uuid="+o+i},profileDetailAPI:function(e,t,a){let o="";a&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/profile/"+e+"/"+t+o},apiTokenExpiry:function(){return this.pickHost()+"/wp-json/yuno/v1/user/expire/time"},apiTokenRefresh:function(){return this.pickHost()+"/wp-json/yuno/v1/google/refresh/token"},staticPageAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/page/"+e},resourcesListingAPI:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s},resourcesDetailAPI:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v1/resources/"+e+"/"+t+"/"+n+"/"+a},videoTestimonialAPI:function(e,t){let a="";t&&(a="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+a},createExamResultAPI:function(e,t,n,a){let o="",s="";void 0===a&&!1===a&&(a="v1"),void 0!==t&&!1!==t&&(o="/"+t),void 0!==n&&!1!==n&&(s="/"+n);return this.pickHost()+"/wp-json/yuno/"+a+"/examresult/"+e+o+s},deleteExamResultAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/examresult/delete/"+e},manageVideotestimonialAPI:function(e,t,n){let a="",o="";void 0!==t&&(a="/"+t),void 0!==n&&(o="/"+n);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+a+o},videotestimonialListAPI:function(e,t,n,a){let o="",s="";void 0!==n&&(o="/"+n),void 0!==a&&(s="/"+a);return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/"+e+"/"+t+o+s},deleteVideotestimonialAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/videotestimonial/delete/"+e},manageArticleAPI:function(e,t,n,a,o,s,i){let r="",l="",c="",u="",d="",p="",m="";void 0!==e&&!1!==e&&(c="/"+e),void 0!==t&&!1!==t&&(m="/"+t),void 0!==n&&!1!==n&&(u="/"+n),void 0!==a&&!1!==a&&(d="/"+a),void 0!==o&&!1!==o&&(p="/"+o),void 0!==s&&!1!==s&&(r="/"+s),void 0!==i&&!1!==i&&(l="/"+i);return this.pickHost()+"/wp-json/yuno/v1/article"+c+m+u+d+p+r+l},webinarSingleAPI:function(e,t,a){let o="";a&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+o},webinarListingAPI:function(e,t,a,o,s){let i="";s&&(i="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+a+"/"+o+i},deleteWebinarAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/delete/class/"+e+"/"+t},webinarEnrollmentAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/webinar/enrollment"},webinarInsightsAPI:function(e,t,n,a,o,s,i){return this.pickHost()+"/wp-json/yuno/v1/webinar/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s+"/"+i},notificationListAPI:function(e,t){void 0!==t||(t="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+t+"/"+e},notificationUpdateAPI:function(e){void 0!==e||(e="channel");return this.pickHost()+"/wp-json/yuno/v1/notification/"+e+"/update"},manageNotificationAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/notification/"+e},searchResourceAPI:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v1/get-learning-content/all-categories/resources/"+e+"?search="+a},managelearningContentAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+n},learningContentAPI:function(e,t,n,a,o){let s="",i="",r="",l="";void 0!==t&&!1!==t&&(s="/"+t),void 0!==n&&!1!==n&&(i="/"+n),void 0!==a&&!1!==a&&(r="/"+a),void 0!==o&&!1!==o&&(l="/"+o);return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+s+i+r+l},learnerInsightsAPI:function(e,t,a,o,s,i,r,l,c){let u="";c&&(u="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/learner/"+e+"/"+t+"/"+a+"/"+o+"/"+s+"/"+i+"/"+r+"/"+l+u},learnerInsightsClassAPI:function(e,t,a,o){let s="",i="";i=void 0!==a&&!1!==a?"v2":"v1",o&&(s="?ver="+n());return this.pickHost()+"/wp-json/yuno/"+i+"/learner/class/"+e+"/"+t+s},signupFormAPI:function(e,t,a){let o="",s="";a&&(o="?ver="+n()),void 0!==t&&!1!==t&&(s="/?state="+t);return this.pickHost()+"/wp-json/yuno/v1/signup/form/"+e+s+o},resourceTitleAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/event/resources/?search="+e+"&item="+t},resourceDraftsAPI:function(e,t,a,o,s){let i="";s&&(i="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/drafts/"+e+"/"+t+"/"+a+"/"+o+i},resourceDraftsDeleteAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/drafts/delete/"+e},demoRequestAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/demo/class/enroll/request"},instructorProfileAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/users/"+e+"/instructor/profile"},subjectsListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/all/subjects/list"},campaignAudienceAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/campaign/audience"},createCampaignAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/create/campaign"},coursesFiltersAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/category/"+e+"/"+t},coursesResultsAPI:function(e,t,n,a){let o="";!1!==a&&(o="?filters="+encodeURI(JSON.stringify(a)));return this.pickHost()+"/wp-json/yuno/v2/courses/web/"+e+"/detail/"+t+"/"+n+"/"+o},resourcesResultsAPI:function(e,t,n,a,o,s,i){let r="";!1!==s&&(r="?filters="+encodeURI(JSON.stringify(s))),void 0!==i&&!1!==i||(i="web");return this.pickHost()+"/wp-json/yuno/v2/resources/"+i+"/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+r},instructorStatsAPI:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/getinstructorstats/"+e+"/attendance"},instructorCoursesV2API:function(e,t,a){let o="";a&&(o="?ver="+n());return this.pickHost()+"/wp-json/yuno/v2/instructor/courses/category/"+e+"/"+t+o},instructorInsightsAPI:function(e,t,a,o,s,i,r,l){let c="";l&&(c="?ver="+n());return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t+"/"+a+"/"+o+"/"+s+"/"+i+"/"+r+c},enableDisableInstructorAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/"+e+"/"+t},vcPermissionAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/instructor/vc/settings/"+e},instructorProfileInsightsAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/"+t+"/profile"},piiAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/"+e+"/users/pii/"+t},mappedCoursesAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/mapped/course/"+e+"/"+t},makeFeaturedAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/instructor/featured/"+e+"/"+t},dashboardLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/dashboard/user/enrollment?search="+e},manageDashboardAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/dashboard/"+e+"/report"+n},dashboardListAPI:function(e,t,n,a,o){let s="",i="",r="",l="";void 0!==n&&!1!==n&&(l="/"+n),void 0!==t&&!1!==t&&(r="/"+t),void 0!==a&&!1!==a&&(s="/"+a),void 0!==o&&!1!==o&&(i="/"+o);return this.pickHost()+"/wp-json/yuno/v1/dashboard/report/"+e+r+l+s+i},enrollmentDashboardAPI:function(e,t,n,a,o){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/"+e+"/"+t+"/"+n+"/"+a+"/"+o},usersListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/users/list/"+e},enrollmentClassDetailAPI:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/dashboard/enrollment/report/detail/"+e+"/"+t},vimeoVideoAPI:function(e){return"https://api.vimeo.com/videos/"+e},batchLearnersAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/learners"},courseBatchLearners:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/batch/"+e+"/"+t+"/learners"},blogCategoriesAPI:function(){return this.pickHost()+"/wp-json/yuno/v1/taxonomy/blog_category"},manageBlogAPI:function(e,t){let n="";void 0!==t&&!1!==t&&(n="/"+t);return this.pickHost()+"/wp-json/yuno/v1/blog/"+e+n},publishedBlogsAPI:function(e,t,n,a,o,s){let i="",r="",l="",c="",u="",d="";void 0!==e&&!1!==e&&(d="/"+e),void 0!==t&&!1!==t&&(l="/"+t),void 0!==n&&!1!==n&&(c="/"+n),void 0!==a&&!1!==a&&(u="/"+a),void 0!==o&&!1!==o&&(i="/"+o),void 0!==s&&!1!==s&&(r="/"+s);return this.pickHost()+"/wp-json/yuno/v1/blog"+d+l+c+u+i+r},categoriesListAPI:function(e){return this.pickHost()+"/wp-json/yuno/v1/category/"+e},vimeoUploadVideoAPI:function(){return"https://api.vimeo.com/me/videos"},vimeoVideoPrivacyAPI:function(e,t){return"https://api.vimeo.com/videos/"+e+"/privacy/domains/"+t},manageVideoClippingAPI:function(e,t,n,a,o,s){s=void 0!==s&&!1!==s?"clippings":"clipping",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",a=void 0!==a&&!1!==a?"/"+a:"",o=void 0!==o&&!1!==o?"/"+o:"";return this.pickHost()+"/wp-json/yuno/v1/"+s+"/"+e+t+n+a+o},instructorMyCourses:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/mycourses/instructor/"+e+"/"+t+"/"+n},instructorCourseBatches:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v1/mycourses/"+e+"/"+t+"/"+n+"/batches/"+a+"/"+o+"/"+s},manageBookmarkAPI:function(e,t,n,a,o,s){void 0!==t&&!1!==t||(t="v1"),n=void 0!==n&&!1!==n?"/"+n:"",a=void 0!==a&&!1!==a?"/"+a:"",o=void 0!==o&&!1!==o?"/"+o:"",s=void 0!==s&&!1!==s?"?filters="+encodeURI(JSON.stringify(s)):"";return this.pickHost()+"/wp-json/yuno/"+t+"/bookmark/"+e+n+a+o+s},availableCourses:function(e,t,n,a){t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",a=void 0!==a&&!1!==a?"?params="+encodeURI(JSON.stringify(a)):"";return this.pickHost()+"/wp-json/yuno/v2/available-courses/"+e+t+n+a},availableBatches:function(e,t,n,a){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"",n=void 0!==n&&!1!==n?"/"+n:"",a=void 0!==a&&!1!==a?"?params="+encodeURI(JSON.stringify(a)):"";return this.pickHost()+"/wp-json/yuno/v2/batches/upcomingOngoing"+e+t+n},courseEnrollmentStatus:function(e,t){e=void 0!==e&&!1!==e?"/"+e:"",t=void 0!==t&&!1!==t?"/"+t:"";return this.pickHost()+"/wp-json/yuno/v2/enrollment-status"+e+t},courseOneToOne:function(e,t,n,a,o,s,i){return this.pickHost()+"/wp-json/yuno/v1/instructor/availability/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s+"/"+i},cloudinaryImageUpload:function(e){return!!e&&{upload_URL:"https://api.cloudinary.com/v1_1/harman-singh/upload",upload_preset:"jg32bezo"}},imageUpload:function(){return this.pickHost()+"/wp-json/yuno/v1/image/upload/"},categorySearch:function(){return this.pickHost()+"/wp-json/yuno/v1/category/search"},categoryResources:function(e){e=void 0!==e&&!1!==e?"?ids="+encodeURI(JSON.stringify(e)):"";return this.pickHost()+"/wp-json/yuno/v2/resources/"+e},coursesList:function(){return this.pickHost()+"/wp-json/yuno/v2/courses/all"},upcomingOngoingBatchesList:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v2/batches/temp/upcomingOngoing/"+e+"/"+t+"/"+n+"/"+a},pastBatchesList:function(e,t,n,a,o,s){return this.pickHost()+"/wp-json/yuno/v2/batches/past/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s},checkout:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/checkout/"+e+"/"+t},instructorInsights:function(e,t,n,a,o,s,i,r,l,c,u,d,p){return this.pickHost()+"/wp-json/yuno/v2/instructor/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s+"/"+i+"/"+r+"/"+l+"/"+c+"/"+u+"/"+d+"/"+p},updateNativelanguage:function(e){return this.pickHost()+"/wp-json/yuno/v2/instructor/nativelanguage/"+e},endBatch:function(e){return this.pickHost()+"/wp-json/yuno/v1/endbatch/"+e},collections:function(e,t,n,a,o){return this.pickHost()+"/wp-json/yuno/v1/learning-content/"+e+"/"+t+"/"+n+"/"+a+"/"+o},instructorVideotestimonial:function(e,t,n,a,o){return this.pickHost()+"/wp-json/yuno/v1/instructorsvideotestimonials/videotestimonial/instructor"},courses:function(e,t,n,a,o,s,i){return this.pickHost()+"/wp-json/yuno/v1/courses/"+e+"/"+t+"/"+n+"/"+a+"/"+o+"/"+s+"/"+i},activityList:function(){return this.pickHost()+"/wp-json/yuno/v1/activity"},subCategoriyList:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/"+e+"/subcategories"},courseSchedule:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e},courseScheduleForm:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/schedule/"+e+"/"+t},createCSV:function(){return this.pickHost()+"/wp-json/yuno/v1/export/csv"},downloadCSV:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t},courseDetail:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/course/detail/"+e+"/"+t},reviewIssues:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/items/"+e+"/issue/"+t+"/"+n},reviewPost:function(e){return this.pickHost()+"/wp-json/yuno/v2/review/post"},formReview:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v2/review/get/"+e+"/"+t+"/"+n},classReviews:function(e,t,n,a,o){return this.pickHost()+"/wp-json/yuno/v2/review/average/"+e+"/"+t+"/"+n+"/"+a+"/"+o},classReviewsByInstructor:function(e,t,n,a,o){return this.pickHost()+"/wp-json/yuno/v2/review/classreviews/instructor/"+e+"/"+t+"/"+n+"/"+a+"/"+o},listOfUser:function(e,t,a){let o="";a&&(o="&ver="+n());return this.pickHost()+"/wp-json/yuno/v1/"+t+"/list/"+e+o},learnerActivity:function(e){return this.pickHost()+"/wp-json/yuno/v2/get-feedback/"+e},recentLearnerClass:function(e,t){return this.pickHost()+"/wp-json/yuno/v2/get-feedback-information/"+e+"/"+t},enrollmentListByType:function(e,t,n,a,o,s,i,r,l,c,u){return this.pickHost()+"/wp-json/yuno/v1/"+e+"/enrollments/"+t+"/"+n+"/"+a+"/"+o+"/"+s+"/"+i+"/"+r+"/"+l+"/"+c+"/"+u},courseEconomics:function(e){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e},courseEconomicsForm:function(e,t,n){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n},courseEconomicsSummary:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v1/course/economics/"+e+"/"+t+"/"+n+"/"+a},csvList:function(e,t,n,a,o){return this.pickHost()+"/wp-json/yuno/v1/csv/"+e+"/"+t+"/"+n+"/"+a+"/"+o},orgList:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/org/"+e},referrerDetails:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/details"},referrerID:function(e,t){return this.pickHost()+"/wp-json/yuno/v1/referrer/"+e+"/"+t},mappedInstructors:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/course/"+e},generateRefferralURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/get-referral-url?params="+encodeURI(JSON.stringify(e))},generateRefferralCode:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/code/generate"},referrerURL:function(e){return this.pickHost()+"/wp-json/yuno/v1/referrer/url"},referralDetail:function(e,t,n,a){void 0===a&&(a="v1");return this.pickHost()+"/wp-json/yuno/"+a+"/referrer/user/"+e+"/"+t+"/"+n},referralReports:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v1/referrer/report/"+e+"/"+t+"/"+n+"/"+a},orgToken:function(e,t){let n="";return n="POST"===t?this.pickHost()+"/wp-json/yuno/v1/create/token":"PUT"===t?this.pickHost()+"/wp-json/yuno/v1/update/token":this.pickHost()+"/wp-json/yuno/v1/token/"+e,n},webhooks:function(e,t,n,a,o,s){let i="";return"grid"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/"+t+"/"+n+"/"+a+"/"+o+"/"+s:"create"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/create":"events"===e?i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/events":"update"===e&&(i=this.pickHost()+"/wp-json/yuno/v1/org/webhook/update"),i},seo:function(e,t,n,a){let o="";return"status"===e?o=this.pickHost()+"/wp-json/yuno/v1/seo/status/"+t:"markNoIndex"===e?o=this.pickHost()+"/wp-json/yuno/v1/seo/mark-no-index":"pageSearch"===e&&(o=this.pickHost()+"/wp-json/yuno/v1/seo/search-get/"+n+"/?search="+a),o},quiz:function(e,t,n,a){let o="";return void 0!==a&&!1!==a||(a=""),"create"===e?o=this.pickHost()+"/wp-json/yuno/v1/quiz/":"update"===e?o=this.pickHost()+"/wp-json/yuno/v1/quiz":"edit"===e?o=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t+"/"+a:"quizgrid"===e?o=this.pickHost()+"/wp-json/yuno/v1/quizzes":"quizgridV2"===e?o=this.pickHost()+"/wp-json/yuno/v3/category/practice":"attempt"===e?o=this.pickHost()+"/wp-json/yuno/v1/attempt/":"review"===e?o=this.pickHost()+"/wp-json/yuno/v1/attempt/answers/"+t+"/"+n:"delete"===e?o=this.pickHost()+"/wp-json/yuno/v1/quiz/"+t:"quizReorder"===e&&(o=this.pickHost()+"/wp-json/yuno/v1/quiz/reorder"),o},question:function(e,t,n,a,o){let s="";return"questions"===e?s=this.pickHost()+"/wp-json/yuno/v1/questionbank/"+t+"/"+n:"create"===e?s=this.pickHost()+"/wp-json/yuno/v1/question":"single"===e||"delete"===e?s=this.pickHost()+"/wp-json/yuno/v1/question/"+a:"deleteQuestionSet"===e?s=this.pickHost()+"/wp-json/yuno/v1/questionset/"+o:"attempt"===e?s=this.pickHost()+"/wp-json/yuno/v1/question-attempt/"+a:"attemptQuestionSet"===e?s=this.pickHost()+"/wp-json/yuno/v1/questionset-attempt/"+o:"questionset"===e?s=this.pickHost()+"/wp-json/yuno/v1/questionset":"questionsetGET"===e?s=this.pickHost()+"/wp-json/yuno/v1/questionset/"+o:"questionsetQuestions"===e?s=this.pickHost()+"/wp-json/yuno/v1/questionset-questions":"questionsetQuestionsGET"===e?s=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/"+o:"questionsetQuestionsList"===e?s=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder/"+o:"questionsetQuestionsReorder"===e&&(s=this.pickHost()+"/wp-json/yuno/v1/questionset-questions/reorder"),s},enrollments:function(e,t,n,a){let o="";return"active"===e?o=this.pickHost()+"/wp-json/yuno/v2/batch/"+t+"/"+n+"/learners":"extendDate"===e&&(o=this.pickHost()+"/wp-json/yuno/v1/enrollment/update"),o},blog:function(e,t,n,a){let o="";return"recentSingle"===e?o=this.pickHost()+"/wp-json/yuno/v1/blog/recent/?is_list=false&category_id="+t:"recentList"===e?o=this.pickHost()+"/wp-json/yuno/v1/blog/recent/"+n+"/"+a+"?is_list=true&category_id="+t:"categoriesList"===e?o=this.pickHost()+"/wp-json/yuno/v1/blog/categories":"detail"===e&&(o=this.pickHost()+"/wp-json/yuno/v1/blog/"+t),o},writingTask:function(e,t,n,a,o,s,i,r){let l="";return"type"===e?l=this.pickHost()+"/wp-json/yuno/v1/writingtask/type/"+t+"/"+n:"create"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/create":"update"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/update":"singleRecord"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+a+"/"+o+"/"+s+"/"+i+"/"+r:"payload"===e?l=this.pickHost()+"/wp-json/yuno/v2/writingtask/"+a:"delete"===e&&(l=this.pickHost()+"/wp-json/yuno/v1/writingtask/delete/"+a),l},categoryLandingPage:function(e,t){let n="";return"category"===e?n=this.pickHost()+"/wp-json/yuno/v2/category/"+t:"practiceTests"===e&&(n=this.pickHost()+"/wp-json/yuno/v2/category/practice/"),n},examResults:function(e,t,n,a){return this.pickHost()+"/wp-json/yuno/v2/examresult/"+e+"/"+t+"/"+n+"/"+a},mainNav:function(e,t){return this.pickHost()+"/wp-json/yuno/v3/menu/"+e+"/"+t},org:function(e,t,n,a,o,s,i,r,l,c,u,d,p,m){return this.pickHost()+({info:`/wp-json/yuno/v2/org/${t}`,singleLearner:`/wp-json/yuno/v1/org/user/${t}/${n}`,industries:"/wp-json/yuno/v1/org/industries/details",detailsUpdate:"/wp-json/yuno/v1/org/update",create:"/wp-json/yuno/v1/org/create",settings:`/wp-json/yuno/v2/org/settings/${t}`,createCourseEconomics:`/wp-json/yuno/v3/org/course/economics/${a}`,courseEconomicsPersonalization:`/wp-json/yuno/v3/org/course/economics/${o}/${s}/${i}`,courseDetailForm:`/wp-json/yuno/v3/org/course/${o}`,courseDetailUpdate:"/wp-json/yuno/v3/org/course",orgAcademies:`/wp-json/yuno/v3/org/academies/${t}`,courseSchedule:`/wp-json/yuno/v3/org/course/schedule/${r}`,batchesUpcomingOngoing:"/wp-json/yuno/v3/org/batches/upcomingOngoing",createBatch:`/wp-json/yuno/v3/org/${l}/batch`,courses:`/wp-json/yuno/v3/org/course/${n}/${t}/${a}/${o}/${c}/${u}/${d}/${p}/${m}`,batchesPast:`/wp-json/yuno/v3/org/batches/past/${t}/${n}/${a}/${o}/${d}/${p}/${m}`,enrollments:"/wp-json/yuno/v3/org/academy/enrollments",academyDetails:`/wp-json/yuno/v1/academy/${t}`,academyInstructors:`/wp-json/yuno/v1/org/user/instructors/${t}`,createUpdateAcademy:"/wp-json/yuno/v1/academy",getAcademy:`/wp-json/yuno/v1/academy/${t}`}[e]||"")},leadForm:function(e,t,n){let a="";return"steps"===e?a=this.pickHost()+"/wp-json/yuno/v2/signup/form/"+t+"/"+n:"postStep"===e?a=this.pickHost()+"/wp-json/yuno/v2/signup/form/update/"+t+"/"+n:"updateMobile"===e&&(a=this.pickHost()+"/wp-json/yuno/v1/admin/user/phone/update "),a},availableCoursesV2:function(e){let t="";return"listing"===e&&(t=this.pickHost()+"/wp-json/yuno/v3/available-courses/"),t},activeCategory:function(e){let t="";return"set"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/category/"),t},learners:function(e,t,n,a,o,s){let i="";switch(e){case"insights":i=`/wp-json/yuno/v2/users/${t}/learner/${n}/${a}/${o}`;break;case"demoRequests":i=`/wp-json/yuno/v1/demo-requests/${t}/list/${a}/${o}`;break;case"demoRequestsOrg":i=`/wp-json/yuno/v1/demo-requests/org-admin/${t}/${n}/${a}/${o}`;break;case"learnerDetailOrg":i=`/wp-json/yuno/v1/demo-requests/${t}/${s}`;break;case"learnerDetail":i=`/wp-json/yuno/v1/demo-requests/${s}`;break;case"instructorLearnerDetail":i=`/wp-json/yuno/v2/instructor/mylearner/${s}`;break;case"orgAdminLearners":i=`/wp-json/yuno/v2/orgadmin/learner/${n}/${a}/${o}`}return this.pickHost()+i},deleteUser:function(e){let t="";return"requested"===e&&(t=this.pickHost()+"/wp-json/yuno/v1/user/add/delete/requests"),t},generic:function(e,t,n,a,o){let s="";switch(e){case"googleFonts":s=`https://www.googleapis.com/webfonts/v1/webfonts/?${t}`;break;case"courseSuggestions":s=`${this.pickHost()}/wp-json/yuno/v1/course/suggestions/${t}`;break;case"contentSearch":s=`${this.pickHost()}/wp-json/yuno/v1/resources/suggestions/${t}`;break;case"userSearch":s=`${this.pickHost()}/wp-json/yuno/v1/org/user/suggestions/${t}/${n}/${a}`;break;case"orgBatches":s=`${this.pickHost()}/wp-json/yuno/v3/org/academy/batch/${o}`;break;case"org":s=`${this.pickHost()}/wp-json/yuno/v2/org/${a}`;break;case"categories":s=`${this.pickHost()}/wp-json/yuno/v3/all/category/signup`}return s},course:function(e,t,n,a,o,s){let i="";switch(e){case"payload":i=`${this.pickHost()}/wp-json/yuno/v1/course/${t}`;break;case"updateCourse":i=`${this.pickHost()}/wp-json/yuno/v1/course`;break;case"mapInstructor":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/map`;break;case"invitedInstructors":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructors/invited/${t}/${n}/${a}/${o}/${s}`;break;case"createBatchOrg":i=`${this.pickHost()}/wp-json/yuno/v3/org/create/batch`;break;case"updateBatchOrg":i=`${this.pickHost()}/wp-json/yuno/v3/org/update/batch`;break;case"mapCourses":i=`${this.pickHost()}/wp-json/yuno/v1/course/instructor/map/bulk`}return i},learner:function(e,t,n,a,o){let s="";switch(e){case"enrolledCourses":s=`/wp-json/yuno/v3/learner/${t}/enrollments/${n}`;break;case"classes":s=`/wp-json/yuno/v4/classes/${n}/${a}/${t}?limit=${o.limit}&offset=${o.offset}&course=${o.course}&batch=${o.batch}&academy=${o.academy}`;break;case"filters":s=`/wp-json/yuno/v4/classes/filter/${a}/${t}`;break;case"getClassDetail":s=`/wp-json/yuno/v4/classes/${o.classID}`}return this.pickHost()+s},classes:function(e,t,n,a,o,s,i,r,l,c,u){let d="";if("allClasses"===e)d=`/wp-json/yuno/v3/classes/${t}/${n}/${a}/${o}/${s}/${i}/${r}/${l}/${c}/${u}`;return this.pickHost()+d},instructor:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v1/instructor/mylearners/${t.instructorID}/${t.limit}/${t.offset}`,learnerDetail:`/wp-json/yuno/v2/instructor/mylearner/${t.learnerID}`,learnersV2:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivate:"/wp-json/yuno/v4/class/schedule-private",freebusy:"/wp-json/yuno/v2/instructor/freebusy/batch",fetchMyLearners:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivateClass:"/wp-json/yuno/v4/classes/private",updatePrivateClass:`/wp-json/yuno/v4/classes/private/${t.classID}`,getClassDetail:`/wp-json/yuno/v4/classes/${t.classID}`,createAvailabilityV2:`/wp-json/yuno/v2/instructor/${t.id}/workinghours/create`,updateAvailabilityV2:`/wp-json/yuno/v2/instructor/${t.id}/workinghours/update`,getInstructorAvailability:`/wp-json/yuno/v4/working_hours/instructor/${t.id}`,learnersV2:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`,schedulePrivate:"/wp-json/yuno/v4/class/schedule-private",freebusy:"/wp-json/yuno/v2/instructor/freebusy/batch",learnersV2:`/wp-json/yuno/v2/${t.role}/mylearners/${t.instructorID}/${t.view}/${t.limit}/${t.offset}`}[e]||"")},attendance:function(e,t,n,a,o){return this.pickHost()+({learners:`/wp-json/yuno/v1/attendance/${t}/${n}/${a}/${o}`}[e]||"")},user:function(e,t){return this.pickHost()+({region:`/wp-json/yuno/v3/user/region/${t.loggedinUserID}`,languages:"/wp-json/yuno/v3/user/languages",countries:"/wp-json/yuno/v1/countries",timezones:"/wp-json/yuno/v3/user/timezones",currencies:"/wp-json/yuno/v3/user/currencies",virtualClassRoom:`/wp-json/yuno/v3/virtual-classroom/${t.loggedinUserID}`,virtualClassRoomV4:`/wp-json/yuno/v4/settings/virtual-classrooms/${t.loggedinUserID}`,vcDisconnect:"/wp-json/yuno/v3/virtual-classroom",classLaunchStatus:`/wp-json/yuno/v2/class/updateLaunchStatus/${t.classID}`,classSchedule:"/wp-json/yuno/v4/classes/demo",slots:"/wp-json/yuno/v2/instructor/freebusy/slots",slotsV4:`/wp-json/yuno/v4/availability/free_slots/${t.params}`}[e]||"")},classInsights:function(e,t,n,a,o){return this.pickHost()+({yunoAdminPast:`/wp-json/yuno/v3/classes/past/${t}/${n}/${a}/${o}`,yunoAdminOngoingUpcoming:`/wp-json/yuno/v3/classes/ongoingUpcoming/${t}/${n}/${a}/${o}`,yunoOrgPast:`/wp-json/yuno/v3/org/classes/past/${t}/${n}/${a}/${o}`,yunoOrgOngoingUpcoming:`/wp-json/yuno/v3/org/classes/ongoingUpcoming/${t}/${n}/${a}/${o}`}[e]||"")},resource:function(e,t){return this.pickHost()+({batches:`/wp-json/yuno/v2/batches/upcomingOngoing/${t.role}/${t.userID}/${t.limit}/${t.offset}`,batchLearners:`/wp-json/yuno/v1/batch/${t.batchID}/${t.courseID}/learners`,sendResource:"/wp-json/yuno/v1/resources/send/resource"}[e]||"")},academy:function(e,t){return this.pickHost()+({academies:"/wp-json/yuno/v3/org/academies",fetchAcademiesFilters:`/wp-json/yuno/v4/academies/filters${t.params}`,academiesV2:`/wp-json/yuno/v4/academies/${t.view_type}${t.params}`,activeOrg:"/wp-json/yuno/v3/user/state",getOrgInstructors:`/wp-json/yuno/v3/org/instructor/completed/${t.id}/${t.org_id}/${t.academy_id}/${t.days}/${t.status}/${t.vc_status}/${t.course_id}/${t.category_id}/${t.is_featured}/${t.native_language}/${t.avg_rating}/${t.view_type}/${t.limit}/${t.offset}`,addDemoInstructors:"/wp-json/yuno/v3/academy/demo-instructors/add",getDemoInstructors:`/wp-json/yuno/v3/academy/demo-instructors/${t.id}/${t.org_id}/${t.academy_id}`,updateDemoInstructors:"/wp-json/yuno/v3/academy/demo-instructors/edit",fetchAcademyDetails:`/wp-json/yuno/v4/academies/${t.id}`,organizations:`/wp-json/yuno/v4/organizations/${t.org_id}`,fetchAllPlaces:`/wp-json/yuno/v4/places/${t.view_type}?org_id=${t.org_id}&limit=${t.limit}&offset=${t.offset}`,fetchPlace:`/wp-json/yuno/v4/places/${t.placeID}`}[e]||"")},googleMapLocation:function(e,t){return"https://maps.googleapis.com/maps/api"+({geoLocation:`/geocode/json?latlng=${t.latitude},${t.longitude}&radius=100&strictbounds=true&location_type=ROOFTOP&key=${t.key}`}[e]||"")},google:function(e,t){return"https://www.google.com"+({openLocation:`/maps/search/?api=1&query=Google&query_place_id=${t.placeID}`}[e]||"")},Places:function(e,t){return this.pickHost()+({create:"/wp-json/yuno/v4/places",fetchAllPlaces:`/wp-json/yuno/v4/places/${t.view_type}`,fetchPlace:`/wp-json/yuno/v4/places/${t.placeID}`,fetchMapPlaceDetails:`/wp-json/yuno/v4/maps/places/details?place_id=${t.placeID}`}[e]||"")},classroom:function(e,t){return this.pickHost()+({createClassroom:"/wp-json/yuno/v4/classrooms"}[e]||"")},createCourse:function(e,t){return this.laravelHost()+({courseGPT:"/api/submit-chatgpt-request",schedules:"/api/generate-course-schedules",status:`/api/status/${t.loggedinUserID}/all/${t.jobID}`}[e]||"")},enrollmentsV4:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/enrollments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/enrollments/filters/${t.params}`,createLink:"/wp-json/yuno/v4/enrollments",changeBatch:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}`,enrollToggle:`/wp-json/yuno/v4/enrollments/${t.enrollmentID}/unenroll`}[e]||"")},header:function(e,t){return this.pickHost()+({menu:`/wp-json/yuno/v4/menus/${t.userID}/${t.orgID}`}[e]||"")},payment:function(e,t){return this.pickHost()+({list:`/wp-json/yuno/v4/payments/${t.view}/${t.params}`,filters:`/wp-json/yuno/v4/payments/filters/${t.params}`}[e]||"")},batch:function(e,t){return this.pickHost()+({learners:`/wp-json/yuno/v4/enrollments/active/batch/${t.batchID}`,create:"/wp-json/yuno/v4/batches",update:`/wp-json/yuno/v4/batches/${t.batchID}`}[e]||"")},courseV4:function(e,t){return this.pickHost()+({list:"/wp-json/yuno/v4/courses/search"}[e]||"")}},t=new Promise(function(e,t){try{if(navigator.userAgent.includes("Firefox")){var n=indexedDB.open("test");n.onerror=function(){e(!0)},n.onsuccess=function(){e(!1)}}else e(null)}catch(t){console.log(t),e(null)}}),n=function(){return performance.now()};return{config:e,findObjectByKey:(e,t,n)=>e.find(e=>e[t]===n)||null,heightOfEle:function(e,t){let n=e.offsetHeight;if(t){let t=getComputedStyle(e);return n+=parseInt(t.marginTop)+parseInt(t.marginBottom),n}return n},assignVValidationObj:function(e){const t=window.VeeValidate,n=window.VeeValidateRules,a=t.ValidationProvider,o=t.ValidationObserver;t.extend("minLength",{validate:(e,{length:t})=>e.length>=t,params:["length"],message:"At least {length} items must be selected"}),t.extend("maxLength",{validate:(e,{length:t})=>e.length<=t,params:["length"],message:"No more than {length} items must be selected"}),t.extend("isSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Custom title is not allowed"}),t.extend("isSelectedFromList",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the user from list"}),t.extend("isBatchSelected",{validate:(e,{length:t})=>0!==t,params:["length"],message:"Please select the batch from list"}),t.extend("notAllowed",{validate:(e,{number:t})=>!1===/^0[0-9].*$/.test(e),params:["number"],message:"Phone number can't start with {number}"}),t.extend("greaterThen",{validate:(e,{number:t})=>e>t,params:["number"],message:"Value should be greater then {number}"}),t.extend("isOverlapping",{validate:e=>!e,message:"Time overlap with another set of time"}),t.extend("isEndTime",{validate:e=>!e,message:"Choose an end time later than the start time."}),t.extend("selectLearner",{validate:(e,{number:t})=>0!==t,params:["number"],message:"Please add at least 1 learner from list"}),t.extend("isEmpty",{validate:(e,{getValue:t})=>""!==t,params:["getValue"],message:"Field should not be blank"}),t.extend("isNotBlank",{validate:(e,{getValue:t})=>null!==t,params:["getValue"],message:"Please select the learner from list"}),t.extend("url",{validate:(e,{getValue:t})=>!!/(http(s)?:\/\/.)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)/.test(e),params:["getValue"],message:"Please enter valid URL"}),t.extend("httpsURL",{validate:(e,{getValue:t})=>!!/^(https:\/\/)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/\S*)?$/.test(e),params:["getValue"],message:'Please make sure URL should start with "https" and should be valid'}),t.extend("email",{validate:e=>!!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),message:"Please enter a valid email address"}),t.extend("hasCurlyBrackets",{validate:e=>/\{.+?\}/.test(e),message:"String must have curly brackets with content inside"});for(let a in e.messages)t.extend(a,n[a]);t.localize("validationMsg",e),Vue.component("ValidationProvider",a),Vue.component("ValidationObserver",o)},removeObjInArr:function(e,t,n){let a=e.length;for(;a--;)e[a]&&e[a].hasOwnProperty(t)&&arguments.length>2&&e[a][t]===n&&e.splice(a,1);return e},formatDate:function(e){var t=new Date(e);if(isNaN(t.getTime()))return e;return day=t.getDate(),day<10&&(day="0"+day),["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]+" "+day+" "+t.getFullYear()},dateTimeToArray:function(e){new Array;return e.split(" ")},timeConvert:function(e){return(e=e.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/)||[e]).length>1&&((e=e.slice(1))[5]=+e[0]<12?"AM":"PM",e[0]=+e[0]%12||12),e.join("")},getQueryParameter:function(e){for(var t=window.location.search.substring(1).split("&"),n=0;n<t.length;n++){var a=t[n].split("=");if(a[0]==e)return a[1]}return!1},countriesData:function(){return[{name:"Afghanistan",code:"AF"},{name:"Åland Islands",code:"AX"},{name:"Albania",code:"AL"},{name:"Algeria",code:"DZ"},{name:"American Samoa",code:"AS"},{name:"AndorrA",code:"AD"},{name:"Angola",code:"AO"},{name:"Anguilla",code:"AI"},{name:"Antarctica",code:"AQ"},{name:"Antigua and Barbuda",code:"AG"},{name:"Argentina",code:"AR"},{name:"Armenia",code:"AM"},{name:"Aruba",code:"AW"},{name:"Australia",code:"AU"},{name:"Austria",code:"AT"},{name:"Azerbaijan",code:"AZ"},{name:"Bahamas",code:"BS"},{name:"Bahrain",code:"BH"},{name:"Bangladesh",code:"BD"},{name:"Barbados",code:"BB"},{name:"Belarus",code:"BY"},{name:"Belgium",code:"BE"},{name:"Belize",code:"BZ"},{name:"Benin",code:"BJ"},{name:"Bermuda",code:"BM"},{name:"Bhutan",code:"BT"},{name:"Bolivia",code:"BO"},{name:"Bosnia and Herzegovina",code:"BA"},{name:"Botswana",code:"BW"},{name:"Bouvet Island",code:"BV"},{name:"Brazil",code:"BR"},{name:"British Indian Ocean Territory",code:"IO"},{name:"Brunei Darussalam",code:"BN"},{name:"Bulgaria",code:"BG"},{name:"Burkina Faso",code:"BF"},{name:"Burundi",code:"BI"},{name:"Cambodia",code:"KH"},{name:"Cameroon",code:"CM"},{name:"Canada",code:"CA"},{name:"Cape Verde",code:"CV"},{name:"Cayman Islands",code:"KY"},{name:"Central African Republic",code:"CF"},{name:"Chad",code:"TD"},{name:"Chile",code:"CL"},{name:"China",code:"CN"},{name:"Christmas Island",code:"CX"},{name:"Cocos (Keeling) Islands",code:"CC"},{name:"Colombia",code:"CO"},{name:"Comoros",code:"KM"},{name:"Congo",code:"CG"},{name:"Congo, The Democratic Republic of the",code:"CD"},{name:"Cook Islands",code:"CK"},{name:"Costa Rica",code:"CR"},{name:"Cote D'Ivoire",code:"CI"},{name:"Croatia",code:"HR"},{name:"Cuba",code:"CU"},{name:"Cyprus",code:"CY"},{name:"Czech Republic",code:"CZ"},{name:"Denmark",code:"DK"},{name:"Djibouti",code:"DJ"},{name:"Dominica",code:"DM"},{name:"Dominican Republic",code:"DO"},{name:"Ecuador",code:"EC"},{name:"Egypt",code:"EG"},{name:"El Salvador",code:"SV"},{name:"Equatorial Guinea",code:"GQ"},{name:"Eritrea",code:"ER"},{name:"Estonia",code:"EE"},{name:"Ethiopia",code:"ET"},{name:"Falkland Islands (Malvinas)",code:"FK"},{name:"Faroe Islands",code:"FO"},{name:"Fiji",code:"FJ"},{name:"Finland",code:"FI"},{name:"France",code:"FR"},{name:"French Guiana",code:"GF"},{name:"French Polynesia",code:"PF"},{name:"French Southern Territories",code:"TF"},{name:"Gabon",code:"GA"},{name:"Gambia",code:"GM"},{name:"Georgia",code:"GE"},{name:"Germany",code:"DE"},{name:"Ghana",code:"GH"},{name:"Gibraltar",code:"GI"},{name:"Greece",code:"GR"},{name:"Greenland",code:"GL"},{name:"Grenada",code:"GD"},{name:"Guadeloupe",code:"GP"},{name:"Guam",code:"GU"},{name:"Guatemala",code:"GT"},{name:"Guernsey",code:"GG"},{name:"Guinea",code:"GN"},{name:"Guinea-Bissau",code:"GW"},{name:"Guyana",code:"GY"},{name:"Haiti",code:"HT"},{name:"Heard Island and Mcdonald Islands",code:"HM"},{name:"Holy See (Vatican City State)",code:"VA"},{name:"Honduras",code:"HN"},{name:"Hong Kong",code:"HK"},{name:"Hungary",code:"HU"},{name:"Iceland",code:"IS"},{name:"India",code:"IN"},{name:"Indonesia",code:"ID"},{name:"Iran, Islamic Republic Of",code:"IR"},{name:"Iraq",code:"IQ"},{name:"Ireland",code:"IE"},{name:"Isle of Man",code:"IM"},{name:"Israel",code:"IL"},{name:"Italy",code:"IT"},{name:"Jamaica",code:"JM"},{name:"Japan",code:"JP"},{name:"Jersey",code:"JE"},{name:"Jordan",code:"JO"},{name:"Kazakhstan",code:"KZ"},{name:"Kenya",code:"KE"},{name:"Kiribati",code:"KI"},{name:"Korea, Democratic People'S Republic of",code:"KP"},{name:"Korea, Republic of",code:"KR"},{name:"Kuwait",code:"KW"},{name:"Kyrgyzstan",code:"KG"},{name:"Lao People'S Democratic Republic",code:"LA"},{name:"Latvia",code:"LV"},{name:"Lebanon",code:"LB"},{name:"Lesotho",code:"LS"},{name:"Liberia",code:"LR"},{name:"Libyan Arab Jamahiriya",code:"LY"},{name:"Liechtenstein",code:"LI"},{name:"Lithuania",code:"LT"},{name:"Luxembourg",code:"LU"},{name:"Macao",code:"MO"},{name:"Macedonia, The Former Yugoslav Republic of",code:"MK"},{name:"Madagascar",code:"MG"},{name:"Malawi",code:"MW"},{name:"Malaysia",code:"MY"},{name:"Maldives",code:"MV"},{name:"Mali",code:"ML"},{name:"Malta",code:"MT"},{name:"Marshall Islands",code:"MH"},{name:"Martinique",code:"MQ"},{name:"Mauritania",code:"MR"},{name:"Mauritius",code:"MU"},{name:"Mayotte",code:"YT"},{name:"Mexico",code:"MX"},{name:"Micronesia, Federated States of",code:"FM"},{name:"Moldova, Republic of",code:"MD"},{name:"Monaco",code:"MC"},{name:"Mongolia",code:"MN"},{name:"Montserrat",code:"MS"},{name:"Morocco",code:"MA"},{name:"Mozambique",code:"MZ"},{name:"Myanmar",code:"MM"},{name:"Namibia",code:"NA"},{name:"Nauru",code:"NR"},{name:"Nepal",code:"NP"},{name:"Netherlands",code:"NL"},{name:"Netherlands Antilles",code:"AN"},{name:"New Caledonia",code:"NC"},{name:"New Zealand",code:"NZ"},{name:"Nicaragua",code:"NI"},{name:"Niger",code:"NE"},{name:"Nigeria",code:"NG"},{name:"Niue",code:"NU"},{name:"Norfolk Island",code:"NF"},{name:"Northern Mariana Islands",code:"MP"},{name:"Norway",code:"NO"},{name:"Oman",code:"OM"},{name:"Pakistan",code:"PK"},{name:"Palau",code:"PW"},{name:"Palestinian Territory, Occupied",code:"PS"},{name:"Panama",code:"PA"},{name:"Papua New Guinea",code:"PG"},{name:"Paraguay",code:"PY"},{name:"Peru",code:"PE"},{name:"Philippines",code:"PH"},{name:"Pitcairn",code:"PN"},{name:"Poland",code:"PL"},{name:"Portugal",code:"PT"},{name:"Puerto Rico",code:"PR"},{name:"Qatar",code:"QA"},{name:"Reunion",code:"RE"},{name:"Romania",code:"RO"},{name:"Russian Federation",code:"RU"},{name:"RWANDA",code:"RW"},{name:"Saint Helena",code:"SH"},{name:"Saint Kitts and Nevis",code:"KN"},{name:"Saint Lucia",code:"LC"},{name:"Saint Pierre and Miquelon",code:"PM"},{name:"Saint Vincent and the Grenadines",code:"VC"},{name:"Samoa",code:"WS"},{name:"San Marino",code:"SM"},{name:"Sao Tome and Principe",code:"ST"},{name:"Saudi Arabia",code:"SA"},{name:"Senegal",code:"SN"},{name:"Serbia and Montenegro",code:"CS"},{name:"Seychelles",code:"SC"},{name:"Sierra Leone",code:"SL"},{name:"Singapore",code:"SG"},{name:"Slovakia",code:"SK"},{name:"Slovenia",code:"SI"},{name:"Solomon Islands",code:"SB"},{name:"Somalia",code:"SO"},{name:"South Africa",code:"ZA"},{name:"South Georgia and the South Sandwich Islands",code:"GS"},{name:"Spain",code:"ES"},{name:"Sri Lanka",code:"LK"},{name:"Sudan",code:"SD"},{name:"Suriname",code:"SR"},{name:"Svalbard and Jan Mayen",code:"SJ"},{name:"Swaziland",code:"SZ"},{name:"Sweden",code:"SE"},{name:"Switzerland",code:"CH"},{name:"Syrian Arab Republic",code:"SY"},{name:"Taiwan, Province of China",code:"TW"},{name:"Tajikistan",code:"TJ"},{name:"Tanzania, United Republic of",code:"TZ"},{name:"Thailand",code:"TH"},{name:"Timor-Leste",code:"TL"},{name:"Togo",code:"TG"},{name:"Tokelau",code:"TK"},{name:"Tonga",code:"TO"},{name:"Trinidad and Tobago",code:"TT"},{name:"Tunisia",code:"TN"},{name:"Turkey",code:"TR"},{name:"Turkmenistan",code:"TM"},{name:"Turks and Caicos Islands",code:"TC"},{name:"Tuvalu",code:"TV"},{name:"Uganda",code:"UG"},{name:"Ukraine",code:"UA"},{name:"United Arab Emirates",code:"AE"},{name:"United Kingdom",code:"GB"},{name:"United States",code:"US"},{name:"United States Minor Outlying Islands",code:"UM"},{name:"Uruguay",code:"UY"},{name:"Uzbekistan",code:"UZ"},{name:"Vanuatu",code:"VU"},{name:"Venezuela",code:"VE"},{name:"Viet Nam",code:"VN"},{name:"Virgin Islands, British",code:"VG"},{name:"Virgin Islands, U.S.",code:"VI"},{name:"Wallis and Futuna",code:"WF"},{name:"Western Sahara",code:"EH"},{name:"Yemen",code:"YE"},{name:"Zambia",code:"ZM"},{name:"Zimbabwe",code:"ZW"}]},isPrivateWindow:function(e){t.then(function(t){e(t)})},setCookie:function(e,t,n){let a=new Date;void 0===n&&(n=30),a.setTime(a.getTime()+24*n*60*60*1e3);let o="expires="+a.toGMTString();document.cookie=e+"="+t+";"+o+";path=/"},deleteCookie:function(e){document.cookie=e+"=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;"},getCookie:function(e){let t=("; "+document.cookie).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},timestamp:n,removeValInArr:function(e){let t,n,a=arguments,o=a.length;for(;o>1&&e.length;)for(t=a[--o];-1!==(n=e.indexOf(t));)e.splice(n,1);return e},hasInArray:function(e,t){return-1!=e.indexOf(t)},getFromString:function(e,t,n){let a=e.match(t);return null!=a&&(!0===n?a[1].replace(/\/$/,""):a[1])},encodeObj:function(e){return encodeURI(JSON.stringify(e))},detectQueryString:function(){const e=window.location.search;return e||!1},scrollToElement:function(e,t,n){let a=window.pageYOffset,o=(s=e,window.pageYOffset+document.querySelector(s).getBoundingClientRect().top);var s;targetY=document.body.scrollHeight-o<window.innerHeight?document.body.scrollHeight-window.innerHeight:o,customHeight=void 0!==n?n:74,diff=targetY-a-customHeight;let i="";diff&&window.requestAnimationFrame(function e(n){i||(i=n);let o=n-i,s=Math.min(o/t,1);var r;s=(r=s)<.5?4*r*r*r:(r-1)*(2*r-2)*(2*r-2)+1,window.scrollTo(0,a+diff*s),o<t&&window.requestAnimationFrame(e)})},removeTagsFromString:function(e){return e.replace(/(<([^>]+)>)/gi,"")},findInArray:function(e,t){return void 0!==e.find(e=>e===t)},queryParameterNonWindow:function(e,t){for(var n=e.substring(1).split("&"),a=0;a<n.length;a++){var o=n[a].split("=");if(o[0]==t)return o[1]}return!1},cleanTextAndTruncate:function(e,t){let n=e.replace(/<\/[^>]+>/gi," ").replace(/<[^>]+>/gi,"").trim();if(n=n.replace(/\s\s+/g," "),n.length>t){const e=n.lastIndexOf(" ",t-1);return n.substring(0,e)+"..."}return n}}}(jQuery),YUNOStore=function(){const e=function(e,t,n,a,o){if(a){if(void 0===t.addToModule||t.addToModule)if(void 0!==o&&o){for(let t=0;t<n.length;t++)e.data.push(n[t]);t.hasLoadmore&&(e.count=t.response.data.count,e.currentCount=e.data.length,e.offset=e.currentCount)}else e.data=n}else t.moduleTabs?(e.error=n,e.loading=!1):e.error=n;e.success=!0,e.loading=!1};return{init:function(){return new Vuex.Store({state:{pageLoader:!1,loader:{overlay:!1,isActive:!1},themeURL:themeURL,homeURL:homePage,config:{signInURL:"",yunoAPIToken:"undefined"!=typeof yunoAPIToken?yunoAPIToken:"",unauthorizedModal:!1,vimeoToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",vimeoVideoToken:"Bearer 878869c3fe96f7ec679b9455c539ee77",vimeoVideoEditToken:"Bearer 4c4b3e1ac1851a23047dd7a338f5caee",googleAPIKey:"AIzaSyCwXZXa4WMaqMxIrRXHcfb3uFNmhGpnyRs",googleMapAPIKey:"AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo",googleMapLocationAPIKey:"AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI"},user:{isLoggedin:!1,userID:isLoggedIn},userRole:{loading:!1,error:null,success:!1,data:[],response:[]},userProfile:{loading:!1,error:null,success:!1,data:[]},header:{loading:!1,error:null,errorData:[],success:!1,data:[],isMenuOpen:!1},footer:{loading:!1,error:null,errorData:[],success:!1,data:[]},blogList:{loading:!1,error:null,success:!1,data:[]},courseListIELTS:{loading:!1,error:null,errorData:[],success:!1,data:[]},allCourseListIELTS:{title:"All our courses are delivered by expert IELTS trainers",demoPageURL:"/demo-classes",loading:!1,error:null,errorData:[],success:!1,data:{single:[],multiple:[]}},resultsIELTS:{title:"Our students' results",isLoadMore:!1,count:"",currentCount:"",limit:8,offset:0,loading:!1,error:null,errorData:[],success:!1,data:[]},faqIELTS:{title:"FAQs",loading:!1,error:null,errorData:[],success:!1,data:[]},recordedClasses:{title:"Checkout actual classes that we recorded",videos:[{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/Subject Verb Agreement Class - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video1.jpg",caption:"Subject Verb Agreement Class - Learn IELTS - Yuno Learning"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/IELTS Writing Task 2 Tips.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video3.jpg",caption:"IELTS Writing Task 2 Tips"},{url:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/How to Paraphrase - Learn IELTS - Yuno Learning.mp4",poster:"https://www.yunolearning.com/wp-content/themes/yunolearning-child/inc/videos/video2.jpg",caption:"How to Paraphrase - Learn IELTS - Yuno Learning"}]},course:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},courseV2:{loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:20,offset:0},schedule:{loading:!1,error:null,errorData:[],success:!1,data:[]},courseBatches:{title:"",coursesCount:"",loading:!1,error:null,errorData:[],success:!1,data:[],count:"",currentCount:"",limit:4,offset:0},courseBatchesFilters:{currentCourse:"",tabs:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},courseTabs:[],instructor:{loading:!1,error:null,errorData:[],success:!1,data:[],tabs:[]},learnerCourses:{loading:!1,error:null,errorData:[],success:!1,tabs:[{title:"My Courses",tab:"Upcoming and Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You have not enrolled any course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0},{title:"My Courses",tab:"Past",url:"yunoPast",isActive:!1,hasData:!1,type:"batchCard",errorMsg:"You do not have any past course yet",isExploreCTA:!0,data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!0}]},instructorCourses:{loading:!1,error:null,errorData:[],success:!1,data:[]},instructorBasicDetails:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isUpdateDetail:!1,fluentInSelected:[],understandSelected:[],payload:{user_id:"",flat_house_number:"",street:"",landmark:"",pin_code:"",country:"",state:"",city:"",experience:"",fluent_in:[],understand:[],is_about:!1}},instructorAbout:{loading:!1,error:null,errorData:[],success:!1,isLoading:!1,isAbout:!1,payload:{user_id:"",is_about:!0,about:""}},instructorDemoClasses:{loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:2,offset:0,isLoadMore:!1,data:[]},instructorMyCourses:{title:"My Courses",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},homeCategories:{title:"Top Courses on Yuno",data:[],loading:!1,error:null,errorData:[],success:!1},homeCarouselList:{title:"Featured Courses",data:[],loading:!1,error:null,errorData:[],success:!1},instructorslList:{title:"Meet Our Expert Instructors",description:"",data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},whyLearn:{title:"Why Choose Yuno for Your Learning Journey",list:[{title:"Online classes with personalized attention",description:"All classes on Yuno are personalised i.e. you get complete attention from your instructor. This is the best way to learn anything",icon:"portrait",iconType:"material-icons-outlined"},{title:"Instructors who care for your success",description:"Each of our instructors goes through rigorous training. Then our quality assurance staff makes sure that each class is well delivered",icon:"emoji_events",iconType:"material-icons-outlined"},{title:"Best instructors but affordable pricing",description:"All our instructors receive 5-star feedback from their students that is published as reviews and ratings their profiles",icon:"account_balance_wallet",iconType:"material-icons-outlined"},{title:"Really smooth experience of technology",description:"We continue improving our software to ensure that you and your instructor get really smooth technology experience without any glitch",icon:"code",iconType:"material-icons-outlined"},{title:"Your counsellor is just a call away",description:"You can reach out to your counsellor whenever you have doubts, want to change your batch or need any other help",icon:"call",iconType:"material-icons"}]},homeHero:{list:[{title:"Coding classes don’t have to cost so much. Try Yuno’s affordable classes",subTitle:"",cta:"Learn More",ctaURL:"/coding-for-kids",category:"",img:themeURL+"/assets/images/homeHero-kids.jpg"},{title:"Fluency builds confidence among children. Try our English speaking classes",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-English-Speaking.jpg"},{title:"Communicate. Succeed. English speaking classes for working professionals",subTitle:"",cta:"Learn More",ctaURL:"/english-speaking",category:"",img:themeURL+"/assets/images/homeHero-professional.jpg"}]},chooseType:{title:"English Speaking Online Classes",subTitle:"Learn to speak English with confidence from the instructors who care for your success. Attend live, online classes that will help boost your confidence.",data:[],loading:!1,error:null,success:!1,list:[{title:"Working Professionals",type:"professionals",age:"22+",description:"We all know how one’s command over English helps one be successful at work. Let us help you succeed and grow in your career",price:[{monthly:"1800",perClass:"150",level:"Intermediate"},{monthly:"2900",perClass:"242",level:"Advanced"}]},{title:"Students",type:"students",age:"15 - 22",description:"Getting ready for college or for the job market? How well you can speak English will determine how ready you are for the corporate world. Let us help you be prepared",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Kids",type:"kids",age:"8 to 14",description:"There’s no better time than to be trained when one’s young. We have the right courses to keep your child immersed in learning in the comfort of your home",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]},{title:"Homemakers",type:"homemakers",age:"25 to 55",description:"You’ve been the pillar of your home. You, too, deserve to invest in yourself. We offer you an opportunity to build your confidence, stand shoulder to shoulder with the working professionals around you. Be confident conversing in English with anyone you meet.",price:[{monthly:"1800",perClass:"150",level:"Intermediate"}]}]},meetInstructor:{title:"Instructors who really care",description:"You don’t want to be in classes where there are tens of other students. You also don’t want to learn on your own from a software. You want personalized attention from your instructor. We understand that. So we have designed our classes and the curriculum in a way that you will not just find high quality but also that your instructor really cares about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else",data:[],loading:!1,error:null,errorData:[],success:!1},featuredTestimonials:{title:"So immersive that you’d want more classes",description:"We believe that there’s no better way to learn than from real instructors. But instructors alone cannot complete the job. So we have designed the curriculum that’s tested on thousands of students in India and abroad. Each instructor keeps her students highly engaged in online classes that the students want to come back for more and more. We have got consistent feedback about this from our students - of all age groups. Enroll in any of our courses and see for yourself. We guarantee 100% satisfaction",footer:{title:"Affordable pricing",description:"So far instructor-led learning has been for the few - the elites who could afford it. But here, at Yuno, we have figured out ways to bring the best instructors at a fraction of the cost. See the pricing for yourself. We strive each day to make it more and more affordable, without compromising on the quality."}},spokenEnglishContentBlock:{title:"You don’t only get to practise, but you acquire English language skills for life",description:"The way we have designed our program is that you get to practise English speaking with your instructor and fellow students. But we don’t stop just there. Because we know where most people make mistakes, we make sure that you don’t. Our program focuses on four different areas: pronunciation, grammar, fluency and clarity of speech. So once you graduate from this program, you will know what mistakes to avoid and so you will learn English speaking skills for life!",img:themeURL+"/assets/images/languages.svg"},kidsHero:{description:"I taught myself how to program computers when I was a kid, bought my first computer when I was 10, and sold my first commercial program when I was 12.",img:themeURL+"/assets/images/codingForKids.svg",author:{name:"Elon Musk",img:themeURL+"/assets/images/Elon-Musk.jpg",about:"Elon Musk, Founder of Tesla and SpaceX"}},kidsOfferBanner:{title:"Introducing programming foundation course for 7 - 16 years old",img:themeURL+"/assets/images/offerPrice.svg",productURL:"/course/learn-to-code-with-mit-scratch",list:["We make learning to code fun for your child","24 hours of live classes with homework assignments","3 classes per week","Rs. 349 per class","All classes by expert computer science instructors","Industry standard curriculum designed by MIT, USA","Certificate on successful completion"]},kidsTestimonials:{title:"What parents have to say"},kidsHighlights:{title:"Highlights",call:"Call us at <span>+91 62390 91798</span>",list:[{type:"liveClass",name:"Live Class",label:"21 hours of live classes"},{type:"oneToOne",name:"One to One",label:"one-to-one doubt clearing sessions"},{type:"price",name:"Price",label:"&#8377;349 per class"},{type:"game",name:"Game",label:"Game development by students"},{type:"programming",name:"Programming",label:"Programming fundamentals"},{type:"flexible",name:"Flexible",label:"Flexible timings with multiple batches"},{type:"certificate",name:"Certificate",label:"Certificate of completion"},{type:"demo",name:"Demo",label:"Free Demo Class"},{type:"cv",name:"CV",label:"Curriculum by MIT, USA"}]},gamesShowcase:{title:"Game Developed by Students",list:[{title:"Pop The Balloon Game",img:themeURL+"/assets/images/PopUPthebaloon.png",url:"https://scratch.mit.edu/projects/419275974/"},{title:"Apple Catcher",img:themeURL+"/assets/images/Applecatcher.png",url:"https://scratch.mit.edu/projects/423139061/"},{title:"Killing Zombies",img:themeURL+"/assets/images/Kill-Zombies.png",url:"https://scratch.mit.edu/projects/425774405/"},{title:"Pac-man",img:themeURL+"/assets/images/PACMAN.png",url:"https://scratch.mit.edu/projects/429660245/"}]},ieltsReviews:{title:"What our students say",description:"",data:[],loading:!1,error:null,success:!1},ourInstructors:{title:"Our Instructors",description:"You don’t want to be in classes with tens of other students. You also don’t want to learn on your own from a software. We understand your need for personalized attention and offer you the perfect solution. At Yuno Learning, all classes are live classes where you get ample attention from your instructor. We have designed our classes and the curriculum in a way that ensures you get the best curriculum delivered by instructors who really care about your success. We allow no more than 5 students in a class so everyone gets the attention that they deserve. You will find your instructor chasing you for your own success. We bet that you can’t find such instructors anywhere else.",data:[],loading:!1,error:null,errorData:[],success:!1},curriculum:{title:"Best-in-class curriculum by MIT, USA",description:"The curriculum has been designed by world’s top computer science researchers at MIT and Harvard. More than 57 million(5.7 crore!) students around the world have used Scratch to learn programming. It doesn’t need any pre-requisites. It teaches students from the ground level in a fun and engaging way.",link:{label:"See curriculum",url:"/course/learn-to-code-with-mit-scratch"},img:themeURL+"/assets/images/scratch.svg",author:{name:"Mitchel Resnik",about:"PhD, Computer Science from MIT Leader of Kindergarten Group at MIT Media Lab Creator of Scratch",img:themeURL+"/assets/images/MitchellResnickThumb.jpg",link:{label:"Watch Mitchel Resnik’s Video on TED",url:"https://www.youtube.com/watch?v=Ok6LbV6bqaE"}}},signUpForm:{data:[],loading:!1,error:null,errorData:[],success:!1},loginWithGoogle:{isLoading:!1,data:[],payload:{State:loginState}},isUserSignUp:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollment:{isLoading:!1,isCourseEnrolled:!1,data:[],error:null,errorData:[],success:{username:"",productTitle:"",amount:"",message:"You can expect a call from us with instructions on how to get started. You can also reach out to us via call or Whatsapp at +91 7841024877"},payload:{id:"",receipt:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"razorpay",payment_mode:"",amount:"",amount_due:"",total_instalments:0,instalment_amount:0,duration:"",status:"",description:"",self_notes:"",currency:"INR",counselor_id:0,short_url:"",zoho_product_id:""}},paymentDismiss:{data:[],loading:!1,error:null,errorData:[],success:!1},classSchedule:{isLoading:!1,modal:!1,successModal:!1,data:[],error:null,errorData:[],currentLearner:"",date:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",time:"undefined"!=typeof moment?new Date(moment(new Date).add(5,"minutes").utc()):"",learnerSelected:"",relatedCourses:"",payload:{ClassTitle:"",ClassDescription:"",ClassDate:"",ClassTime:"",ClassDuration:"",classSchedule:"",learner:[],RelatedCourses:"",BatchID:"",academy_id:""}},classEdit:{data:[],loading:!1,error:null,success:!1},classDelete:{data:[],loading:!1,error:null,success:!1},classTitle:{data:[],loading:!1,error:null,errorData:[],success:!1},addClassTitle:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{instructor_id:"",title:""}},myLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},learner:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,emptyStateCTA:{ctaLabel:"See past classes & recordings",tab:"yunoPast"},count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0,emptyStateCTA:!1}]},instructorHome:{loading:!1,error:null,success:!1,tabs:[{title:"My Schedule",tab:"Upcoming & Ongoing",url:"yunoUpcomingOngoing",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any upcoming and ongoing class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0},{title:"My Schedule",tab:"Past Classes & Recordings",url:"yunoPast",isActive:!1,hasData:!1,type:"classCard",createAction:"Schedule New Class",createURL:"/class-schedule",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don’t have any past class",isExploreCTA:!1,manageState:!0,emptyStateCTA:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0,hasLoadMore:!0}]},instructorLearners:{scheduleClass:{modal:!1,data:[]},updateTitle:{modal:!1,modalData:null,isLoading:!1,payload:{title:"",group_id:""}},addLearner:{modal:!1,modalData:null,isLoading:!1,selectedLearner:"",deleteUser:[],newAddedUser:[],payload:{add_user_ids:[],delete_user_ids:[],group_id:""}},newGroupModal:{modal:!1,learners:[],selectedLearner:[],isLoading:!1,data:[],payload:{title:"",owner_id:"",role:"",access:"rw",user_ids:[]}},loading:!1,error:null,success:!1,tabs:[{title:"My Learners",tab:"All",url:"yunoAllLearners",isActive:!1,hasData:!1,type:"tableGrid-2",defaultSort:"name",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any learner",defaultFilters:[{type:"viewBy",val:"all"}],appliedFilters:[],filters:[{selected:"By Learner Type",default:"By Learner Type",type:"viewBy",module:"dropdown",isActive:!1,items:[{label:"All",val:"all",default:"all"},{label:"My Contacts",val:"myContacts",default:"all"},{label:"My Referrals",val:"myReferrals",default:"all"}]}],tableOptions:{isFluid:!0,pageLoading:!1,apiPaginated:!0,totalResult:"",perPage:20,currentPage:1,limit:100,offset:0},manageState:!0},{title:"My Groups",tab:"Groups",url:"yunoGroups",isActive:!1,hasData:!1,type:"groupCard",data:[],loading:!1,error:null,errorData:[],success:!1,errorMsg:"You don't have any group",count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,hasLoadMore:!0,manageState:!0}]},allLearners:{data:[],loading:!1,error:null,errorData:[],success:!1},classDetail:{classType:"",data:[],tabs:[],loading:!1,error:null,errorMsg:"",errorData:[],success:!1},demoClassEnroll:{isLoading:!1,modal:!1,successModal:!1,error:null,errorData:[],data:[],payload:{class_id:"",instructor_id:"",user_id:"",start_date:"",end_date:"",class_title:"",class_description:""}},paymentLink:{isLoading:!1,data:[],successModal:!1,form:{amount:"",selectedUser:"",user:"",selectedBatch:"",batchID:"",batch:"",courseID:"",course:"",paymentType:"",noteForSelf:"",isInstallment:!1,installments:"",isNextSlide:!1,howManyInstallments:["2","3"]},payload:{customer_name:"",customer_email:"",customer_contact:"",type:"link",view_less:1,amount:"",currency:"INR",payment_description:"",receipt:"",partial_payment:0}},crmContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},allCourses:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},allBatches:{data:[],loading:!1,error:null,errorData:[],success:!1},enrollmentStatus:{data:[],loading:!1,error:null,errorData:[],success:!1},createPayment:{data:[],payload:{id:"",receipt:"",Order_id:"",customer_id:"",user_id:"",course_id:"",batch_id:"",batch_name:"",batch_end_date:"",payment_gateway:"generate_link",payment_mode:"online",amount:"",amount_due:"",total_instalments:"",instalment_amount:"",duration:"",status:"",description:"",self_notes:"",currency:"INR",entity:"invoice",counselor_id:"",short_url:"",org_id:"",org_user_id:"",org_user_phone:"",org_user_name:"",org_user_email:"",org_crm_id:"",org_cohort:"",org_programs:"",org_business_unit:"",org_parents:[]}},reviews:{data:[],loading:!1,error:null,errorData:[],success:!1},updatePaymentLink:{data:[],successModal:!1,payload:{id:""}},updateLink:{data:[],error:null,errorData:[],loading:!1,success:!1,successModal:!1,payload:{receipt_id:"",razerpay_invoice_id:"",short_url:""}},instructorList:{data:[],refinedData:[],loading:!1,error:null,errorData:[],success:!1},profileReviews:{title:"Reviews"},reviewsByType:{data:[],loading:!1,error:null,success:!1},demoClasses:{loading:!1,error:null,success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1,data:[],filters:[{title:"Any Time",type:"anytime",isActive:!0,data:[]},{title:"Morning",type:"morning",isActive:!1,data:[]},{title:"Afternoon",type:"afternoon",isActive:!1,data:[]},{title:"Evening",type:"evening",isActive:!1,data:[]},{title:"Night",type:"night",isActive:!1,data:[]}]},manageEnroll:{data:[],loading:!1,error:null,errorData:[],success:!1},changeBatch:{data:[],loading:!1,error:null,errorData:[],success:!1},blogs:{data:[],headers:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",limit:20,offset:0,isLoadMore:!1},blogDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},postCategories:{data:[],loading:!1,error:null,success:!1},anilLambaHero:{list:[{title:"Anil Lamba on finance",img:themeURL+"/assets/images/hero-anilLamba.png"}]},settings:{loading:!1,error:null,errorData:[],success:!1,tabs:[]},notifications:{data:[],loading:!1,error:null,errorData:[],success:!1},counsellorList:{data:[],refinedData:[],loading:!1,error:null,success:!1},pageDetail:{data:[],loading:!1,error:null,success:!1},googleContacts:{data:[],loading:!1,error:null,errorData:[],success:!1},participants:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:{login_email:"",first_name:"",last_name:"",user_id:""}},mapCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},relatedCourses:{data:[],loading:!1,error:null,errorData:[],success:!1},categoryList:{data:[],loading:!1,error:null,errorData:[],success:!1,selected:""},categoryTaxonomy:{data:[],loading:!1,error:null,errorData:[],success:!1},createEBook:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedEBooks:{data:[],loading:!1,error:null,errorData:[],success:!1},deleteEBookAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},eBookEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},createResource:{data:[],loading:!1,error:null,errorData:[],success:!1},publishedResources:{data:[],loading:!1,error:null,errorData:[],success:!1,manageState:!1,defaultFilters:[],appliedFilters:[],filters:[]},deleteResourceAttachment:{data:[],loading:!1,error:null,errorData:[],success:!1},resourceEmail:{data:[],loading:!1,error:null,errorData:[],success:!1},videoList:{data:[],loading:!1,error:null,errorData:[],success:!1},userInfo:{data:[],loading:!1,error:null,errorData:[],success:!1},paymentLinkList:{data:[],changeBatch:"",paymentDetail:"",createPayment:"",generateLink:"",loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorInsights:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},instructorStats:{data:[],loading:!1,error:null,errorData:[],success:!1},goalsAchieved:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerRating:{data:[],loading:!1,error:null,errorData:[],success:!1},reviewVariations:{data:[],loading:!1,error:null,errorData:[],success:!1},completedEnrollments:{data:[],loading:!1,error:null,errorData:[],success:!1},classDelivered:{data:[],loading:!1,error:null,errorData:[],success:!1},activeEnrollment:{data:[],loading:!1,error:null,errorData:[],success:!1},qtRating:{data:[],loading:!1,error:null,errorData:[],success:!1},topIssuesCited:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorReviews:{data:[],loading:!1,error:null,errorData:[],success:!1},ratingBreakdown:{data:[],loading:!1,error:null,errorData:[],success:!1},countries:{data:[],loading:!1,error:null,errorData:[],success:!1},states:{data:[],loading:!1,error:null,errorData:[],success:!1},cities:{data:[],loading:!1,error:null,errorData:[],success:!1},languages:{data:[],loading:!1,error:null,errorData:[],success:!1},mappedInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},batchCreateUpdate:{data:[],loading:!1,error:null,errorData:[],success:!1},batchDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},timeSlots:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailability:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorAvailabilityGrid:{data:[],loading:!1,error:null,errorData:[],success:!1},instructorsByCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},capabilities:{data:[],loading:!1,error:null,errorData:[],success:!1},cancelPaymentLink:{data:[],loading:!1,error:null,errorData:[],success:!1},inviteLink:{data:[],loading:!1,error:null,errorData:[],success:!1},invitedByUser:{data:[],loading:!1,error:null,errorData:[],success:!1},updateUserCategory:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenExpiryTime:{data:[],loading:!1,error:null,errorData:[],success:!1},apiTokenRefresh:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfileHeader:{data:[],loading:!1,error:null,errorData:[],success:!1},staticPage:{data:[],loading:!1,error:null,errorData:[],success:!1},learnerProfile:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resources:{data:[],loading:!1,error:null,errorData:[],success:!1,tabs:[]},resource:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:15,offset:0},learnerInsightsClass:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},videoTestimonials:{data:[],loading:!1,error:null,errorData:[],success:!1},ieltsResults:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:10,offset:0},deleteResource:{data:[],loading:!1,error:null,errorData:[],success:!1},eventDetail:{data:[],loading:!1,error:null,errorData:[],success:!1},moduleWithoutTab:{data:[],loading:!1,error:null,errorData:[],success:!1,limit:20,offset:0,count:"",currentCount:"",isLoadMore:!1},moduleWithLoadMore:{data:[],other:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:3,offset:0},signupLastStep:{data:[],loading:!1,error:null,errorData:[],success:!1},subjectsList:{data:[],loading:!1,error:null,errorData:[],success:!1},filters:{data:[],loading:!1,error:null,errorData:[],success:!1,filters:null,payload:[]},filterResult:{data:[],additional:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,currentPage:1,isSidebar:!1,limit:20,offset:0,payload:[],modal:{isActive:!1,data:[]},tabs:[],refreshTable:!1},enrollmentV2:{data:[],loading:!1,error:null,errorData:[],success:!1},allReviews:{data:[],loading:!1,error:null,errorData:[],success:!1,count:"",currentCount:"",isLoadMore:!1,limit:20,offset:0},enableDisableInstructor:{data:[],loading:!1,error:null,errorData:[],success:!1},module:{data:[],loading:!1,error:null,errorData:[],success:!1},drawer:{data:[],isActive:!1,loading:!1,error:null,errorData:[],success:!1},form:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],fields:[]},subform:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[],calendarLoading:!1},subform2:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},subform3:{data:[],loading:!1,error:null,errorData:[],success:!1,payload:null,isLoading:!1,additional:[]},orgAdmin:{data:[],loading:!1,error:null,errorData:[],success:!1},referralCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},generateCode:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},searchSuggestions:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1},chooseAccountTypeModal:{modal:!1,data:[],loading:!1,error:null,errorData:[],success:!1},tabs:{data:[],loading:!1,additional:[],error:null,errorData:[],success:!1}},mutations:{gotData(t,n){if(n.isError){let a=n.response.response,o=!(void 0===n.pushData||!n.pushData),s=t[n.store],i="";i=void 0!==a&&void 0!==a.data&&void 0!==a.data.message?a.data.message:YUNOCommon.config.errorMsg.common,console.log(i),console.log(n.store),n.tabs?(e(s.tabs[n.tabIndex],n,a,!1,o),s.tabs[n.tabIndex].error=!0,s.tabs[n.tabIndex].errorData=i,n.callback&&n.callbackFunc(s.tabs[n.tabIndex].errorData)):(e(s,n,a,!1,o),s.error=!0,s.errorData=i,n.callback&&n.callbackFunc(s.errorData))}else{let a=n.response.data.data,o=!(void 0===n.pushData||!n.pushData),s=n.response.data,i=t[n.store];n.tabs?(204===s.code&&(i.tabs[n.tabIndex].error=!0,i.tabs[n.tabIndex].errorData=s.message,console.log(s.message),console.log(n.store)),401===s.code&&(i.tabs[n.tabIndex].error=!0,i.tabs[n.tabIndex].errorData=s.message,console.log(s.message),console.log(n.store)),e(i.tabs[n.tabIndex],n,a,!0,o)):(204===s.code&&(i.error=!0,i.errorData=s.message,console.log(s.message),console.log(n.store)),401===s.code&&(i.error=!0,i.errorData=s.message,console.log(s.message),console.log(n.store)),e(i,n,a,!0,o)),n.callback&&n.callbackFunc(n)}},thirdParty(e,t){module=e[t.store],module.error?t.callbackFunc(module):t.callbackFunc(t)},mapCourses(t,n){if(n.isError){let a=n.response;(void 0===n.overrideData||n.overrideData)&&e(t[n.module],n,a,!1),n.callback&&n.callbackFunc(t[n.module].errorData)}else{let a=n.response.data.data;(void 0===n.overrideData||n.overrideData)&&e(t[n.module],n,a,!0),n.callback&&n.callbackFunc(n)}},reviewsByType(t,n){if(n.isError){let a=n.response;e(t[n.module],n,a,!1)}else{let a=n.response.data.data;n.isTabAdded||t.instructor.data.tabs.push({tab:"Reviews",url:"yunoFeaturedTestimonials"}),e(t[n.module],n,a,!0),n.isTabAdded=!0,setTimeout(()=>{n.componentInstance.$refs.testimonialWrapper.initSlider()},30)}},crmContacts(t,n){if(n.isError){let a=n.response;e(t[n.module],n,a,!1)}else{let o=n.response.data.data;for(var a=0;a<o.length;a++)o[a].username_email_phone=`${o[a].username_email} (${o[a].phone})`;e(t[n.module],n,o,!0)}},classDelete(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(e.loader.isActive=!1,e.loader.overlay=!1,void 0!==t.classID){let n=e.instructorHome.tabs[0].data;YUNOCommon.removeObjInArr(n,"id",t.classID),0===n.length&&(e.instructorHome.tabs[0].error=!0),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Class successfully deleted",position:"is-bottom"})}else t.componentInstance.$buefy.dialog.alert({title:"Delete",message:"Class successfully deleted",confirmText:"Ok",type:"is-danger",onConfirm:()=>window.location.href=YUNOCommon.config.host()+"/instructor"});e[t.module].data=t.response,e[t.module].success=!0,e[t.module].loading=!1}},demoClassEnroll(e,t){if(t.isError)e[t.module].isLoading=!1,void 0!==t.classIndex&&(e.loader.isActive=!1,e.loader.overlay=!1),t.componentInstance.$buefy.toast.open({duration:5e3,message:`${YUNOCommon.config.errorMsg.common}`,position:"is-bottom",type:"is-danger"});else{if(localStorage.removeItem("demoClassState"),e[t.module].isLoading=!1,e[t.module].data=t.response.data.data,void 0!==t.classIndex){let n=e[t.parentModule].data[t.classIndex];void 0!==n&&(n.isLoading=!1,n.is_enrolled=!0),e.loader.isActive=!1,e.loader.overlay=!1}else e[t.parentModule].data.is_enrolled=!0;t.componentInstance.$buefy.toast.open({duration:5e3,message:"You have successfully enrolled",position:"is-bottom"});const n=localStorage.getItem("userSignUp");null!==n&&"pending"===n&&(localStorage.setItem("oldUserState","/learner/"),localStorage.setItem("userState","/sign-up"),window.location.href=YUNOCommon.config.host()+"/sign-up")}},userRole(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].response=t.response.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.componentInstance&&t.componentInstance.getUserRole(t.response.data.data))},userProfile(e,t){t.isError?(e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1):(e[t.module].data=t.response.data.data,e[t.module].success=!0,e[t.module].loading=!1,""!==t.instance&&void 0!==t.instance.gotUserProfile&&t.instance.gotUserProfile(t.response.data.data))},classDetail(t,n){if(n.isError){let a=n.response;"enrolled"===n.nestedTab?(t[n.module].tabs[n.tabIndex].loading=!1,t[n.module].tabs[n.tabIndex].pageLoading=!1,t[n.module].tabs[n.tabIndex].error=a):e(t[n.module],n,a,!1)}else n.callback&&n.callbackFunc(n,n.response)},instructorMyCourses(e,t){if(t.isError){if(t.batches){e[t.module].data[t.courseIndex].tabs[t.tabIndex].error=t.response}else e[t.module].error=t.response;e[t.module].success=!0,e[t.module].loading=!1}else{let n=t.response.data.data;if(t.batches){e[t.module].data[t.courseIndex].isBatches=!0;for(let a=0;a<n.length;a++)e[t.module].data[t.courseIndex].tabs[t.tabIndex].data.push(n[a]);let a=e[t.module].data[t.courseIndex].tabs[t.tabIndex];a.count=t.response.data.count,a.currentCount=a.data.length,a.offset=a.currentCount,a.isLoadMore=!1}else{for(let e=0;e<n.length;e++)n[e].isBatches=!1,n[e].isLoading=!1,n[e].tabs=[{tab:"Upcoming & Ongoing",isActive:!0,type:"upcomingOngoing",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null},{tab:"Past",isActive:!1,type:"past",data:[],isLoadMore:!1,count:"",currentCount:"",limit:4,offset:0,error:null}];e[t.module].data=n}e[t.module].success=!0,e[t.module].loading=!1}},allLearners(e,t){if(t.isError)e[t.module].error=t.response,e[t.module].success=!0,e[t.module].loading=!1,void 0!==t.nested&&(e[t.nested].tabs[0].error=!0);else{let a=t.response.data.data;if(void 0!==t.nested){let o=a.columns,s=a.rows,i={field:"actions",label:"Actions",sortable:!1};"Instructor"===t.userRole&&o.push(i);for(var n=0;n<s.length;n++)s[n].scheduleClass={active:!0,url:"/class-schedule/?learnerID="+s[n].id};e[t.nested].tabs[0].data=a,e[t.nested].tabs[0].totalResult=t.response.data.count,e[t.nested].tabs[0].pageLoading=!1}e[t.module].data=a.rows,e[t.module].success=!0,e[t.module].loading=!1}},instructorHome(e,t){const n=function(n){n?(e[t.module].tabs[t.index].hasData=!0,e[t.module].tabs[t.index].data=t.response.data.data):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0),e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1};t.isError?n(!1):n(!0)},instructorLearners(e,t){const n=function(n){if(n&&void 0!==t.response.data){let n=t.response.data.data;if(void 0!==t.form){if(e[t.module][t.form].data=n,e[t.module][t.form].modal=!1,"newGroupModal"===t.form){let a={date:YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n.group_created_time)[0]),group_created_time:n.group_created_time,group_id:""+n.group_id,group_name:t.payload.title,total_users:0,user:t.learners,scheduleClassURL:`/class-schedule/?groupID=${n.group_id}`};e[t.module].tabs[t.index].data.unshift(a),t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group successfully created",position:"is-bottom"})}if("addLearner"===t.form){e[t.module].tabs[t.index].data.filter(function(e){return e.group_id===t.group.group_id})[0].user=t.group.user;let n=e.instructorLearners.addLearner;n.selectedLearner="",n.payload.group_id="",n.payload.owner_id="",n.payload.user_ids=[],n.deleteUser=[],n.newAddedUser=[],n.payload.add_user_ids=[],n.payload.delete_user_ids=[],t.componentInstance.learnerAdded=!0,t.componentInstance.group=null,t.componentInstance.learnersList=[],t.componentInstance.$buefy.toast.open({duration:5e3,message:"Learners added successfully",position:"is-bottom"})}"updateTitle"===t.form&&t.componentInstance.$buefy.toast.open({duration:5e3,message:"Group title updated successfully",position:"is-bottom"})}else{const o=e[t.module].tabs[t.index];o.hasData=!0,o.isLoadMore=!1;for(var a=0;a<n.length;a++)n[a].date=YUNOCommon.formatDate(YUNOCommon.dateTimeToArray(n[a].group_created_time)[0]),n[a].scheduleClassURL=`/class-schedule/?groupID=${n[a].group_id}`,o.data.push(n[a]);o.count=t.response.data.count,o.currentCount=o.data.length,o.offset=o.currentCount}}else void 0!==t.form?(e[t.module][t.form].modal=!1,t.componentInstance.$buefy.toast.open({duration:5e3,message:`${t.response.response.data.message}`,position:"is-bottom",type:"is-danger"})):(e[t.module].tabs[t.index].hasData=!1,e[t.module].tabs[t.index].data=t.response,e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response);void 0!==t.form?e[t.module][t.form].isLoading=!1:(e[t.module].success=!0,e[t.module].loading=!1,e[t.module].tabs[t.index].success=!0,e[t.module].tabs[t.index].loading=!1)};t.isError?n(!1):(n(!0),204===t.response.data.code&&(e[t.module].tabs[t.index].error=!0,e[t.module].tabs[t.index].errorData=t.response.data.message,console.log(t.response.data.message)))}},actions:{fetchThirdPartyData({commit:e,state:t},n){let a=t[n.store];a.loading=!0,axios.get(n.apiURL,{headers:void 0!==n.headers?n.headers:""}).then(t=>{a.loading=!1,n.response=t,a.error=null,e(n.module,n)}).catch(t=>{a.loading=!1,a.errorData=t,n.response=t,a.error=!0,e(n.module,n)})},postThirdPartyData({commit:e,state:t},n){let a=t[n.store];a.loading=!0,axios.defaults.timeout=void 0===n.timeout?0:n.timeout,"post"===n.method?axios.post(n.apiURL,n.payload,{headers:n.headers}).then(t=>{a.loading=!1,n.response=t,a.error=null,e(n.module,n)}).catch(t=>{a.loading=!1,a.errorData=t,n.response=t,a.error=!0,e(n.module,n)}):"patch"===n.method?axios.patch(n.apiURL,n.payload,{headers:n.headers}).then(t=>{a.loading=!1,n.response=t,a.error=null,e(n.module,n)}).catch(t=>{a.loading=!1,a.errorData=t,n.response=t,a.error=!0,e(n.module,n)}):"head"===n.method?axios.head(n.apiURL,{headers:n.headers}).then(t=>{a.loading=!1,n.response=t,a.error=null,e(n.module,n)}).catch(t=>{a.loading=!1,a.errorData=t,n.response=t,a.error=!0,e(n.module,n)}):"put"===n.method?axios.put(n.apiURL,n.payload,{headers:n.headers}).then(t=>{a.loading=!1,n.response=t,a.error=null,e(n.module,n)}).catch(t=>{a.loading=!1,a.errorData=t,n.response=t,a.error=!0,e(n.module,n)}):console.log("not defined")},fetchData({commit:e,state:t},n){let a="",o="";o="0"!==isLoggedIn?{authorization:t.config.yunoAPIToken}:{authorization:""},a=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0===n.moduleLoading||n.moduleLoading?a.loading=!0:a.loading=!1,n.moduleTabs&&n.isTabLoader&&(a.loading=!0),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.get(n.apiURL,{headers:o}).then(a=>{n.response=a,n.isError=!1,e(n.module,n),403===(n.response?.data?.data?.status??"")&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0))}).catch(a=>{console.log(n.store),console.log(a),t[n.store].error=!0,t[n.store].loading=!1,t[n.store].success=!0;let o="";o=void 0!==n.store?n.tabs?t[n.store].tabs[n.tabIndex]:t[n.store]:t[n.module],void 0!==o.errorData&&void 0!==a.response&&(o.errorData=a.response),403===a.response.data.data.status&&(t.config.unauthorizedModal||("userInfo"===n.store&&(t.header.success=!0,t.footer.success=!0,t.capabilities.success=!0),t.config.unauthorizedModal=!0)),n.response=a,n.isError=!0,e(n.module,n)})},putData({commit:e,state:t},n){let a="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",a=n.headers):(a={"content-type":"text/json"},"0"!==isLoggedIn?a.authorization=t.config.yunoAPIToken:a.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,n.payload,{headers:a}).then(t=>{n.response=t,n.isError=!1,e(n.module,n)}).catch(a=>{console.log(n.store),console.log(a),t[n.store].error=!0;let o="";o=void 0!==n.store?t[n.store]:t[n.module],void 0!==o.errorData&&void 0!==a.response&&(o.errorData=a.response),n.response=a,n.isError=!0,e(n.module,n)})},awsPutData({commit:e,state:t},n){let a={accept:"application/json","content-type":"application/json",authorization:""};"0"!==isLoggedIn?a.authorization=t.config.yunoAPIToken:a.authorization="",axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.put(n.apiURL,JSON.stringify(n.payload),{headers:a}).then(t=>{n.response=t,n.isError=!1,e(n.module,n)}).catch(a=>{console.log(n.store),console.log(a),t[n.store].error=!0,n.response=a,n.isError=!0,e(n.module,n)})},postData({commit:e,state:t},n){let a="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",a=n.headers):(a={"content-type":"text/json"},"0"!==isLoggedIn?a.authorization=t.config.yunoAPIToken:a.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,n.payload,{headers:a}).then(t=>{n.response=t,n.isError=!1,e(n.module,n)}).catch(a=>{console.log(n.store),console.log(a),t[n.store].error=!0;let o="";o=void 0!==n.store?t[n.store]:t[n.module],void 0!==o.errorData&&void 0!==a.response&&(o.errorData=a.response),n.response=a,n.isError=!0,e(n.module,n)})},awsPostData({commit:e,state:t},n){let a="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",a=n.headers):(a={accept:"application/json","content-type":"application/json"},"0"!==isLoggedIn?a.authorization=t.config.yunoAPIToken:a.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.post(n.apiURL,JSON.stringify(n.payload),{headers:a}).then(t=>{n.response=t,n.isError=!1,e(n.module,n)}).catch(a=>{console.log(n.store),console.log(a),t[n.store].error=!0;let o="";o=void 0!==n.store?t[n.store]:t[n.module],void 0!==o.errorData&&void 0!==a.response&&(o.errorData=a.response),n.response=a,n.isError=!0,e(n.module,n)})},deleteData({commit:e,state:t},n){let a="";void 0!==n.headers?("0"!==isLoggedIn?n.headers.authorization=t.config.yunoAPIToken:n.headers.authorization="",a=n.headers):(a={"content-type":"text/json"},"0"!==isLoggedIn?a.authorization=t.config.yunoAPIToken:a.authorization=""),axios.defaults.timeout=void 0===n.timeout?6e4:n.timeout,axios.delete(n.apiURL,{headers:a,data:n.payload}).then(t=>{n.response=t,n.isError=!1,e(n.module,n)}).catch(a=>{console.log(n.store),console.log(a),t[n.store].error=!0,n.response=a,n.isError=!0,e(n.module,n)})}},getters:{getSignInURL:()=>"https://accounts.google.com/o/oauth2/auth/identifier?response_type=code&redirect_uri="+gRU+"&client_id="+gCID+"&scope="+["email","profile"].join("%20")+"&access_type=offline&approval_prompt=force&flowName=GeneralOAuthFlow",googleMeet(){const e=encodeURI(JSON.stringify({googleMeet:!0}));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/calendar.events","https://www.googleapis.com/auth/admin.reports.audit.readonly","https://www.googleapis.com/auth/drive.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},googleContacts(){const e=encodeURI(JSON.stringify("stateUpdate"));return"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state="+e+"&scope="+["email","profile","https://www.googleapis.com/auth/contacts.readonly","https://www.googleapis.com/auth/contacts.other.readonly"].join("%20")+"&approval_prompt=force&flowName=GeneralOAuthFlow"},getSwitchAccountURL:e=>"https://accounts.google.com/o/oauth2/auth?response_type=code&access_type=offline&client_id="+gCID+"&redirect_uri="+gRU+"&state=stateUpdate&scope=email%20profile&prompt=select_account&flowName=GeneralOAuthFlow"}})}}}(jQuery),YUNOPageLoader=(jQuery,{loader:function(){Vue.component("yuno-page-loader",{template:'\n                <div class="yunoPageLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}})}}),YUNOLoader=(jQuery,{loader:function(){Vue.component("yuno-loader",{template:'\n                <div \n                    :class="{\'withOverlay\': isOverlay, \'isActive\': loader.isActive}"\n                    class="yunoLoader">\n                    <div class="yunoSpinner"></div>\n                </div>\n            ',data:()=>({}),computed:{...Vuex.mapState(["loader"]),isOverlay:{get(){return this.loader.overlay?(document.querySelectorAll("body")[0].classList.add("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.add("yunoLoaderEnabled")):(document.querySelectorAll("body")[0].classList.remove("yunoLoaderEnabled"),document.querySelectorAll("html")[0].classList.remove("yunoLoaderEnabled")),this.loader.overlay}}},async created(){},mounted(){},methods:{}})}}),YUNOEmptyStates=(jQuery,{emptyStates:function(){Vue.component("yuno-empty-states",{props:["data","options","login"],template:'\n                <article :class="[\'emptyStates state\' + options.state, options.maxheight !== undefined && !options.maxheight ? \'noMaxHeight\' : \'\']">\n                    <figure>\n                        <img v-if="options.state !== \'notSelected\'" :src="wpThemeURL + \'/assets/images/\' + options.state + \'.png\'" :alt="options.state">\n                        <figcaption>\n                            <template v-if="options.state === \'404\'">\n                                <h1 class="stateTitle">Error (404)</h1>\n                                <p class="stateDescription">We can\'t find the page you\'re looking for.</p>\n                            </template>\n                            <template v-if="options.state === \'accountNotCreated\'">\n                                <h1 class="stateTitle">{{options.title}}</h1>\n                                <p class="stateDescription" v-html="options.description"></p>\n                            </template>\n                            <template v-if="options.state === \'notAuthorized\'">\n                                <template v-if="user.isLoggedin && options.type !== undefined && options.type === \'class\'">\n                                    <h1 class="stateTitle">You are not authorized to attend this class</h1>\n                                    <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag rounded v-if="false" type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                </template>\n                                <template v-else>\n                                    <template v-if="user.isLoggedin">\n                                        <h1 class="stateTitle">You are not authorized to view this page</h1>\n                                        <template v-if="data !== undefined">\n                                            <p class="stateDescription">{{data.errorMsg}}</p>\n                                        </template>\n                                        <template v-else>\n                                            <p class="stateDescription">You are logged in with <span class="darkColor">"{{userProfile.data.email}}"</span> <b-tag v-if="false" rounded type="is-info" @click="chooseAccountState()"><a :href="getSignInURL">Switch account</a></b-tag></p>\n                                        </template>\n                                    </template>\n                                    <template v-else>\n                                        <h1 class="stateTitle">You need to log in to access</h1>\n                                        <b-button class="googleSignIn" @click="initGoogleSignIn($event)">\n                                            <span class="icnGoogle"></span> Sign in with Google \n                                        </b-button>\n                                    </template>\n                                </template>\n                            </template>\n                            <template v-if="options.state === \'dataNotFound\'">\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle" v-else>Data not found</h1>\n                                <p class="stateDescription">\n                                    <template v-if="data !== undefined">\n                                        {{data.errorMsg}}\n                                    </template>\n                                    <template v-else>\n                                        <template v-if="options.description !== undefined">\n                                            {{options.description}}\n                                        </template>\n                                        <template v-else>\n                                            The data you requested has not been found in the server.    \n                                        </template>\n                                    </template>\n                                </p>\n                            </template>\n                            <template v-if="options.state === \'notEnrolled\'">\n                                <h1 class="stateTitle marginBtm30" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <h1 class="stateTitle marginBtm30" v-else>You have not enrolled any course yet</h1>\n                                <b-button\n                                    v-if="options.isCTA === undefined || options.isCTA" \n                                    tag="a"\n                                    href="/learner-classes/"\n                                    class="yunoPrimaryCTA exploreCTA">\n                                    Explore Our Courses\n                                </b-button>\n                                <b-button\n                                    v-if="options.emptyStateCTA !== false" \n                                    @click="emptyStateCTA(options.emptyStateCTA, $event)"\n                                    class="yunoPrimaryCTA viewPast">\n                                    {{options.emptyStateCTA.emptyStateCTA.ctaLabel}}\n                                </b-button>\n                            </template>\n                            <template v-if="options.state === \'notSelected\'">\n                                <span v-if="options.iconType === \'material-icons\'" class="material-icons">{{options.icon}}</span>\n                                <h1 class="stateTitle" v-if="options.title !== undefined">{{options.title}}</h1>\n                                <p class="stateDescription" v-if="options.description">{{options.description}}</p>\n                            </template>\n                        </figcaption>\n                    </figure>\n                </article>\n            ',data:()=>({signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""}}}),computed:{...Vuex.mapState(["user","userProfile","userRole"]),wpThemeURL(){return this.$store.state.themeURL},getSignInURL(){return this.$store.getters.getSwitchAccountURL}},mounted(){},methods:{emptyStateCTA(e,t){Event.$emit("emptyStateCTA",e,t)},setPayload(){let e=this.signIn,t="";t="undefined"!=typeof yunoCategory&&""!==yunoCategory?yunoCategory:"general",e.categoryURL=`/${t}`,e.landing_page.url=window.location.origin+window.location.pathname,e.landing_page.title=document.title,e.productCode="",e.leadStatus="",e.utmSource=YUNOCommon.getQueryParameter("utm_source"),e.utmCampaign=YUNOCommon.getQueryParameter("utm_campaign"),e.utmMedium=YUNOCommon.getQueryParameter("utm_medium"),e.adGroupID=YUNOCommon.getQueryParameter("adgroupid"),e.adContent=YUNOCommon.getQueryParameter("ad_content"),e.utmTerm=YUNOCommon.getQueryParameter("utm_term"),e.gclid=YUNOCommon.getQueryParameter("gclid"),e.content.type="",e.content.id=""},initGoogleSignIn(e){void 0===this.$props.login?(this.setPayload(),localStorage.setItem("userState",window.location.pathname+window.location.search),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)):"modal"===this.$props.login&&Event.$emit("initLoginModal",e)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)}}})}}),YUNOFooter=(jQuery,{footer:function(){Vue.component("yuno-footer",{props:["isnav","isLogo","whatsapp"],template:'\n                <footer id="yunoFooter" class="yunoFooter" :class="{\'noNav\': isnav === false, \'noBG\': isLogo !== undefined && !isLogo}">\n                    <div class="container noOverflow">\n                        <div class="row" v-if="isnav !== false">\n                            <template v-for="(primary, i) in footerData.primary">\n                                <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                    <template v-if="primary.type === \'withIcon\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :class="item.icon"\n                                                    :key="j">\n                                                    <a rel="nofollow" :href="item.url">\n                                                        <span>{{item.label}}</span>\n                                                    </a>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'basicNoLinks\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j"\n                                                    :class="[item.helper !== undefined && item.helper !== \'\' ? \'d-flex align-items-center\' : \'\']"\n                                                >\n                                                    <span>{{item.label}}</span>\n                                                    <small class="ml-2 helper" v-if="item.helper !== undefined && item.helper !== \'\'">{{item.helper}}</small>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'blocks\'">\n                                        <ul class="linkList" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j"\n                                                    :class="[item.helper !== undefined ? \'d-flex align-items-center\' : \'\']"\n                                                >\n                                                    <h4>{{item.label}}</h4>\n                                                    <p>{{item.description}}</p>\n                                                    <a :href="item.cta.url">{{item.cta.label}}</a>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="primary.type === \'withOrderList\'">\n                                        <ul class="linkList checkList marginBtm30" :class="primary.type">\n                                            <li v-if="primary.title !== undefined" class="listTitle">\n                                                <h3>{{ primary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in primary.items">\n                                                <li \n                                                    :key="j">\n                                                    {{item.label}}\n                                                </li>\n                                            </template>\n                                        </ul>\n                                        <b-button tag="a"\n                                            :href="primary.cta.url"\n                                            target="_blank"\n                                            rel="nofollow noopener"\n                                            class="yunoSecondaryCTA">\n                                            {{ primary.cta.label }}\n                                        </b-button>\n                                    </template>\n                                    <template v-if="primary.type === \'stackBlock\'">\n                                        <template v-for="(item, j) in primary.items" v-if="item.type === \'appCTA\'">\n                                            <ul class="linkList marginBtm30" :class="primary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <template v-for="(subitem, k) in item.items">\n                                                    <li \n                                                        :class="subitem.icon"\n                                                        :key="k">\n                                                        <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                            <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                        </a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>            \n                                        <template v-for="(item, j) in primary.items" v-if="item.type === \'iconOnly\'">\n                                            <ul class="linkList" :class="primary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <li class="iconsBlock">\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <div \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                        </div>\n                                                    </template>\n                                                </li>\n                                            </ul>\n                                        </template> \n                                    </template>\n                                </div>    \n                            </template>\n                        </div>\n                        <div class="row" v-if="isnav !== false">\n                            <div class="col-12">\n                                <div class="spacer"></div>\n                            </div>\n                        </div>\n                        <div class="row" v-if="isnav !== false">\n                            <template v-for="(secondary, i) in footerData.secondary">\n                                <div class="col-12 col-md-3 col-lg-3" :key="i">\n                                    <template v-if="secondary.type === \'basic\'">\n                                        <ul class="linkList" :class="secondary.type">\n                                            <li v-if="secondary.title !== undefined" class="listTitle">\n                                                <h3>{{ secondary.title }}</h3>\n                                            </li>\n                                            <template v-for="(item, j) in secondary.items">\n                                                <li \n                                                    :key="j"\n                                                >\n                                                    <template v-if="isContainsObject(item.url)">\n                                                        <a :href="getEnvironmentUrl(item.url)">{{item.label}}</a>\n                                                    </template>\n                                                    <template v-else>\n                                                        <a :href="item.url">{{item.label}}</a>\n                                                    </template>\n                                                </li>\n                                            </template>\n                                        </ul>\n                                    </template>\n                                    <template v-if="secondary.type === \'stackBlock\'">\n                                        <template v-for="(item, j) in secondary.items" v-if="item.type === \'appCTA\'">\n                                            <ul class="linkList marginBtm30" :class="secondary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <template v-for="(subitem, k) in item.items">\n                                                    <li \n                                                        :class="subitem.icon"\n                                                        :key="k">\n                                                        <img class="appQR" alt="Yuno Android APP QR Code" width="96" height="96" :src="wpThemeURL + \'/assets/images/appQR.webp\'">\n                                                        <a v-if="subitem.icon === \'android\'" rel="noopener" target="_blank" :href="subitem.url">\n                                                            <img alt="Yuno Learning Android APP" width="150" height="45" :src="wpThemeURL + \'/assets/images/android_badge.png\'">\n                                                        </a>\n                                                    </li>\n                                                </template>\n                                            </ul>\n                                        </template>            \n                                        <template v-for="(item, j) in secondary.items" v-if="item.type === \'iconOnly\'">\n                                            <ul class="linkList" :class="secondary.type">\n                                                <li v-if="item.title !== undefined" class="listTitle">\n                                                    <h3>{{ item.title }}</h3>\n                                                </li>\n                                                <li class="iconsBlock">\n                                                    <template v-for="(subitem, k) in item.items">\n                                                        <div \n                                                            :class="subitem.icon"\n                                                            :key="k">\n                                                            <a target="_blank" rel="noopener" :href="subitem.url">{{subitem.label}}</a>\n                                                        </div>\n                                                    </template>\n                                                </li>\n                                            </ul>\n                                        </template> \n                                        <a :href="secondary.cta.url" target="_blank" class="link">{{ secondary.cta.label }}</a>\n                                    </template>\n                                </div>    \n                            </template>\n                        </div>\n                        <div class="row" v-if="!isnav">\n                            <div class="col-12">\n                                <ul class="footerBottom d-flex align-items-center justify-content-center">\n                                    <li class="copy">© Yunolearnung. {{currentYear}}</li>\n                                    <li class="copy">\n                                        <a href="/privacy-policy/" target="_blank">Privacy Policy</a>\n                                    </li>\n                                    <li class="copy">\n                                        <a href="/terms-of-use/" target="_blank">Terms of use</a>\n                                    </li>\n                                    <li v-if="false">\n                                        <figure class="logo">\n                                            <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                                        </figure>\n                                    </li>\n                                </ul>\n                            </div>\n                        </div>    \n                    </div>\n                    <div class="whatsappSticky" v-if="whatsapp === undefined || whatsapp ? true : false"><a :href="\'https://api.whatsapp.com/send?phone=\' + mobile" target="_blank"><span>Chat with us</span></a></div>\n                </footer>\n            ',data:()=>({currentYear:(new Date).getFullYear(),mobile:"************",footerData:{primary:[{title:!1,type:"withIcon",items:[{label:"Check internet speed",url:"http://yunolearning.speedtestcustom.com",icon:"wifiSpeed"},{label:"Zoom test",url:"https://zoom.us/test",icon:"zoomTest"}]},{title:"Teach Online",type:"blocks",items:[{label:"Instructor",description:"Create an instructor account and start teaching",cta:{label:"Sign up for instructor",url:"/become-an-instructor/"}},{label:"Academy",description:"Create your academy, publish courses and get new learners",cta:{label:"Create academy",url:"/create-academy/"}}]},{title:"Coming Soon",type:"basicNoLinks",items:[{label:"Developer Guide",helper:"",url:"#"},{label:"API Documentation",url:"#"},{label:"Postman Collection",url:"#"}]},{title:"Yuno Business",type:"basicNoLinks",items:[{label:"Yuno for Study Abroad Consultants",helper:"",url:"/for-study-abroad-and-immigration-companies/"},{label:"Yuno for Career Conselors",helper:"",url:"#"},{label:"Yuno for Skilling Companies",helper:"",url:"#"},{label:"Yuno for Learning & Development",helper:"",url:"#"},{label:"Yuno for IT Service Companies",helper:"",url:"#"},{label:"Yuno for Airlines & Hospitality",helper:"",url:"#"}]}],secondary:[{title:"IELTS",type:"basic",items:[{label:"IELTS Online Classes",url:"/ielts"},{label:"IELTS Free Study Material",url:"/ielts/collection/ielts-study-material-and-practice-tests/"},{label:"IELTS Reading",url:"/blog/how-to-prepare-for-ielts-reading-test"},{label:"IELTS Writing Task 1",url:"/blog/how-to-prepare-for-ielts-writing-task-1"},{label:"IELTS Writing Task 2",url:"/blog/how-to-prepare-for-ielts-writing-task-2"},{label:"IELTS Listening",url:"/blog/how-to-prepare-for-ielts-listening-test"},{label:"IELTS Speaking",url:"/blog/how-to-prepare-for-ielts-speaking-test"},{label:"IELTS Practice Tests",url:"/ielts/practice-tests"},{label:"Free IELTS Resources",url:"/ielts"}]},{title:"English Speaking",type:"basic",items:[{label:"Spoken English Classes",url:{dev:'/search/?state=%7B"limit":20,"offset":0,"personalization":"all","category":%5B3084%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"price_per_hour":10000,"total_duration":24%7D',stage:'/search/?state=%7B"limit":20,"offset":0,"personalization":"all","category":%5B3084%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"price_per_hour":10000,"total_duration":24%7D',prod:'/search/?state=%7B"category":%5B2811%5D,"category_level_1":%5B%5D,"category_level_2":%5B%5D,"class_days_time":%5B%7B"selected":%5B%5D,"slug":"class_days"%7D,%7B"selected":%5B%5D,"slug":"class_time"%7D%5D,"instructor_id":0,"limit":20,"offset":0,"personalization":"all","price_per_hour":10000,"total_duration":24%7D'}},{label:"English Speaking for Working Professionals",url:"/english-speaking/professionals/"},{label:"English Speaking for Students",url:"/english-speaking/students/"},{label:"Spoken English for Kids",url:"/english-speaking/kids/"},{label:"English Speaking for Teenagers",url:"#"},{label:"English Speaking for Home makers",url:"/english-speaking/homemakers/"},{label:"English Speaking for Interview",url:"/english-speaking/interview/"}]},{title:"More Resources",type:"basic",items:[{label:"Microsoft Excel Video Tutorials",url:"/microsoft-excel/resources/"},{label:"Python Programming Videos",url:"/python-programming/resources/"},{label:"Vedic Maths Tricks",url:"/vedic-maths/resources/"},{label:"Learn Vedic Maths",url:"/vedic-maths/"},{label:"Coding Classes for kids",url:"/coding-for-kids/"}]},{type:"stackBlock",items:[{title:"Our Mobile App",type:"appCTA",items:[{label:"Get the Android App",url:"https://play.google.com/store/apps/details?id=com.yunolearning.learn",icon:"android"}]},{title:"Social Media Channels",type:"iconOnly",items:[{label:"Facebook",url:"https://www.facebook.com/yunolearning",icon:"facebook"},{label:"Twitter",url:"https://twitter.com/YunoLearning",icon:"twitter"},{label:"Linkedin",url:"https://www.linkedin.com/company/yunolearning",icon:"linkedin"},{label:"Instagram",url:"https://www.instagram.com/yuno.learning/",icon:"instagram"},{label:"Youtube",url:"https://www.youtube.com/channel/UCTVIUtnoO5c103cSWXIBelQ",icon:"youtube"}]}],cta:{label:"Check out our blog",url:"/blog/"}}]}}),computed:{...Vuex.mapState(["user","userRole","footer"]),wpThemeURL(){return this.$store.state.themeURL},host:()=>YUNOCommon.config.pickHost(),getEnvironmentUrl(){return e=>{if(!this.isContainsObject(e))return e;const t={"https://dev.yunolearning.com":"dev","https://www.yunolearning.com":"prod",default:"stage"},n=t[this.host]||t.default;return this.host+e[n]}}},async created(){},methods:{isContainsObject:e=>"object"==typeof e&&null!==e}})}});Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()})},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const e=sessionStorage.getItem("isLHSMenu");null===e||this.isMobile?this.reduce=!1:(this.reduce="true"===e,this.sidebarToggle(!0))},sidebarToggle(e){e||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout(()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)},n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},extractSlugFromURL(e){const t=e.replace(/\/$/,"").split("/");""===t[t.length-1]&&t.pop();return t[t.length-1]},manageCurrentPage(e){const t=e=>e.replace(/\/$/,""),n=t(window.location.origin+window.location.pathname);e.forEach(e=>{e.items.forEach(e=>{e.is_active=n===t(e.url);let a=!1;e.sub_items.forEach(o=>{o.is_active=n===t(o.url),o.is_active&&o.parent_id===e.id&&(a=!0)}),e.is_expended=!!a})})},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},gotPostLoginMenu(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";e?t=e.response.data.data:(t=this.header.data,this.header.success=!0),this.manageCurrentPage(t),this.header.data=t,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(e){const t={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,a={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPostLoginMenu(e)}};this.$store.dispatch("fetchData",a)},manageOrgAdmin(e){const{host:t}=YUNOCommon.config,{has_org:n,org_id:a}=e;null===sessionStorage.getItem("activeOrg")&&(n?a.length>1?(window.location.href=`${t()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(a[0].id)):window.location.href=`${t()}/create-organization-account`)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";if(e?t=e.response.data.data:(t=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(t.role),this.userRole.data=t.role,this.userProfile.data=t,this.userProfile.success=!0,t.role,"Learner"===t.role&&this.fetchReferralCode(),"Learner"===t.role&&"pending"===t.is_signup_completed){const e=localStorage.getItem("userState");window.location.pathname+window.location.search!==e&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout(()=>{localStorage.removeItem("skipSignUp")},10))}t.role,this.$emit("userInfo",t)}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},getStorage(){const e=this.storage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.header.data=e.menu}this.loginStatus()},setStorage(){const e=this.storage,t={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(e.name+"V"+e.version,JSON.stringify(t))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const e=this.storage;sessionStorage.removeItem(e.name+"V"+e.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(e){if(this.generateCode.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;Event.$emit("fetchReferralCode")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("postData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(e,t){0!==t.sub_items.length&&e.preventDefault(),"generate-code"===t.slug&&e.preventDefault(),"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.referralCode.data=t}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},gotCode(e){if(this.moduleWithoutTab.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;this.fetchReferralCode()}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotCode(t)}};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Referral":"Referral Earnings",isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)}}}),Vue.component("yuno-autocomplete-search",{props:{data:{type:Object,required:!0},defaultFilters:{type:Object,required:!0},payload:{type:Object,required:!0}},template:'\n        <div class="yunoAutocompleteSearch" :class="[data.filter]">\n            <b-field>\n                <b-autocomplete\n                    v-model="data.current"\n                    :clearable="true"\n                    :placeholder="data.placeholder"\n                    :data="data.items"\n                    :loading="data.loading"\n                    :field="data.search_field"\n                    autocomplete="search"\n                    @typing="onSearchType($event, data)"\n                    @select="onSearchItemSelect($event, data)"\n                >\n                    <template slot="empty">No results for {{selectedOption}}</template>\n                    <template slot-scope="props">\n                        {{props.option[data.search_field]}}\n                    </template>\n                </b-autocomplete>  \n            </b-field>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{onSearchType:_.debounce(function(e,t){e.length>2?(t.loading=!0,t.items=[],this.$emit("onQuerySearch",e,t,!1)):t.items=[]},500),onSearchItemSelect(e,t){t.selected=null!==e?e:null,this.$emit("onQuerySearch",name,t,!0)},searchFocusToggle(e,t){}}}),Vue.component("yuno-empty-state-v2",{props:{options:{type:Object,required:!0},cta:{type:Array,default:()=>[]},image:{type:String,default:"/assets/images/noDataFound.svg"}},template:'\n        <section class="emptyStateV2 " :class="[options.type]">\n            <figure>\n                <img \n                    width="80" \n                    height="59" \n                    :src="wpThemeURL + image" \n                    alt="Yuno Learning"\n                >\n                <figcaption>\n                    {{ options.message }}\n                </figcaption>\n                <div class="ctaWrapper" v-if="cta.length">\n                    <b-button \n                        @click="ctaTrigger()"\n                        :tag="ctaItem.type === \'link\' ? \'a\' : \'button\'"\n                        :href="ctaItem.url"\n                        :target="ctaItem.target"\n                        class="yunoPrimaryCTA wired"\n                        v-for="(ctaItem, index) in cta"\n                    >\n                        {{ ctaItem.label }}\n                    </b-button>\n                </div>\n            </figure>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},mounted(){},methods:{ctaTrigger(){Event.$emit("ctaTrigger")}}}),Vue.component("yuno-table-grid-filters",{props:["data","options","defaultFilters"],template:'\n        <section class="filtersWrapper" id="filters" :class="[options.isFilterMobile ? \'mobileView\' : \'\']">\n            <template v-if="filterResult.loading">\n                <div class="filters">\n                    <div class="yunoDropdown" v-for="i in 4" :key="i">\n                        <b-skeleton height="43px" width="150px"></b-skeleton>\n                    </div>\n                </div>\n            </template>\n            <template v-if="filterResult.success">\n                <div class="filterHeader">\n                    <h3 class="largerTitle">Filters</h3>\n                    <a class="filterTrigger" @click="manageFilters">\n                        <span class="material-icons">close</span>\n                    </a>\n                </div>\n                <div class="filters otherFilters" :class="[options.isFilterMobile ? \'mobileView\' : \'\']">\n                    <template v-for="(filter, i) in filters">\n                        <template v-if="filter.type === \'autocomplete_search\'">\n                            <yuno-autocomplete-search\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :defaultFilters="defaultFilters"\n                                :payload="data"\n                                @onQuerySearch="onQuerySearch"\n                            >\n                            </yuno-autocomplete-search>\n                        </template>\n                        <template v-if="filter.type === \'dropdown\'">\n                            <yuno-simple-dropdown\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :options="{\'payload\': data}"\n                                @onDropdownChange="onDropdownChange"\n                                @clearFilter="clearFilter"\n                                :defaultFilters="defaultFilters"\n                            >\n                            </yuno-simple-dropdown>\n                        </template>\n                        <template v-if="filter.type === \'dropdown_multi_select\'">\n                            <yuno-multi-select-dropdown\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :options="{\'payload\': data}"\n                                @onDropdownChange="onDropdownChange"\n                                @clearFilter="clearFilter"\n                                :defaultFilters="defaultFilters"\n                            >\n                            </yuno-multi-select-dropdown>\n                        </template>\n                        <template v-if="filter.type === \'search\'">\n                            <yuno-search\n                                :data="filter"\n                                :key="\'filter-\' + i"\n                                :defaultFilters="defaultFilters"\n                                :payload="data"\n                                @onSearchInput="onSearchInput"\n                            >\n                            </yuno-search>\n                        </template>\n                    </template>\n                    <a class="filterTrigger" @click="manageFilters">\n                        <span class="material-icons">filter_list</span>\n                    </a>\n                </div>\n            </template>\n        </section>\n    ',data:()=>({mobileView:!1}),computed:{...Vuex.mapState(["user","filterResult"]),filters(){return this.filterResult.data.filters}},async created(){},mounted(){},methods:{onSearchInput(e,t,n){this.$emit("onSearchInput",e,t,n)},onQuerySearch(e,t,n){this.$emit("onQuerySearch",e,t,n)},clearFilter(e,t){this.$emit("clearFilter",e,t)},manageFilters(){this.$emit("manageFilters")},onDropdownChange(e,t){this.$emit("onDropdownChange",e,t)}}}),Vue.component("yuno-multi-select-dropdown",{props:["data","options","defaultFilters"],template:'\n        <div class="yunoDropdown multiSelect" :class="[data.filter]">\n            <b-dropdown \n                v-model="selectedOption" \n                aria-role="list"\n                :mobile-modal="false"\n                :multiple="true"\n                @change="onChange($event, data)"\n            >\n                <template #trigger>\n                    <div class="labelWrapper">\n                        <span class="placeHolder" v-if="false">{{ data.placeholder }}</span>\n                        <span class="selectedItem hasGrid">\n                            <template v-if="data.selected.length === 0">\n                                {{ data.placeholder }}\n                            </template>\n                            <template v-else>\n                                <template v-for="(selected, i) in data.selected">\n                                    <div class="item" :key="i">\n                                        <span>\n                                            {{ selected.label }}\n                                        </span>\n                                        <a \n                                            href="#" \n                                            class="clearFilter" \n                                            v-if="manageClearFilterCTA(data)"\n                                            @click="clearFilter($event, selected, data)"\n                                        >\n                                            <span class="material-icons">cancel</span>\n                                        </a>\n                                    </div>\n                                </template>\n                                \n                            </template>\n                        </span>\n                        <span class="material-icons icon">expand_more</span>\n                    </div>\n                </template>\n                <template v-for="(option, i) in data.items">\n                    <b-dropdown-item \n                        :value="option" \n                        aria-role="listitem"\n                        :class="option.is_checked ? \'is-active\' : \'\'"\n                        @click="onItemSelect(option, data)"\n                    >\n                        {{ option.label }}\n                    </b-dropdown-item>\n                </template>\n            </b-dropdown>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{onItemSelect(e,t){e.is_checked?e.is_checked=!1:e.is_checked=!0},manageClearFilterCTA:e=>1!==e.selected.length||"all"!==e.selected[0].slug,clearFilter(e,t,n){e.preventDefault();const a=this.$props.options.payload;YUNOCommon.removeObjInArr(n.selected,"slug",t.slug),YUNOCommon.removeValInArr(a[n.filter],t.slug),0===a[n.filter].length&&(a[n.filter]=this.$props.defaultFilters[this.$props.data.filter]),this.$emit("onDropdownChange",e,n)},onChange(e,t){const n=this.$props.options.payload;"all"===e[0].slug?n[t.filter]=[e[0].slug]:(YUNOCommon.removeValInArr(n[t.filter],"all"),YUNOCommon.removeValInArr(t.selected,"all"),YUNOCommon.findInArray(n[t.filter],e[0].slug)?(YUNOCommon.removeValInArr(n[t.filter],e[0].slug),YUNOCommon.removeObjInArr(t.selected,"slug",e[0].slug)):(n[t.filter].push(e[0].slug),t.selected.push(e[0]))),this.$emit("onDropdownChange",e,t)}}}),Vue.component("yuno-search",{props:{data:{type:Object,required:!0,validator:function(e){return e.hasOwnProperty("filter")&&e.hasOwnProperty("placeholder")}},defaultFilters:{type:Object,required:!0},payload:{type:Object,required:!0}},template:'\n        <div class="yunoAutocompleteSearch" :class="[data.filter]">\n            <b-field>\n                <b-input \n                    v-model="data.selected"\n                    :placeholder="data.placeholder"\n                    @input="onSearchInput($event, data)"\n                >\n                </b-input>\n                \n            </b-field>\n        </div>\n    ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{onSearchInput:_.debounce(function(e,t){try{this.$set(t,"loading",!0),this.$set(t,"items",[]),this.$props.data&&this.$props.data.filter&&this.$set(this.$props.payload,this.$props.data.filter,e),this.$emit("onSearchInput",e,t,!1)}catch(e){console.error("Search error:",e),this.$emit("search-error",e)}finally{this.$set(t,"loading",!1)}},2e3)}}),Vue.component("yuno-simple-dropdown",{props:["data","options","defaultFilters"],template:'\n        <div class="yunoDropdown" :class="[data.filter]">\n            <b-dropdown \n                v-model="selectedOption" \n                aria-role="list"\n                :mobile-modal="false"\n                @change="onChange($event, data)"\n            >\n                <template #trigger>\n                    <div class="labelWrapper">\n                        <span class="placeHolder" v-if="false">{{ data.placeholder }}</span>\n                        <span class="selectedItem">{{ showSelected(data.selected, data) }}</span>\n                        <a \n                            href="#" \n                            class="clearFilter" \n                            v-if="manageClearFilterCTA(data)"\n                            @click="clearFilter($event, data.selected, data)"\n                        >\n                            <span class="material-icons">cancel</span>\n                        </a>\n                        <span class="material-icons icon">expand_more</span>\n                    </div>\n                </template>\n                <template v-for="(option, i) in data.items">\n                    <b-dropdown-item \n                        :value="option" \n                        aria-role="listitem"\n                    >\n                        {{ option.label }}\n                    </b-dropdown-item>\n                </template>\n            </b-dropdown>\n        </div>\n    ',data:()=>({selectedOption:""}),computed:{...Vuex.mapState(["user"])},async created(){},mounted(){},methods:{manageClearFilterCTA(e){return e.selected!==this.$props.defaultFilters[e.filter]},clearFilter(e,t,n){e.preventDefault(),this.$emit("clearFilter",t,n)},showSelected(e,t){let n="";return n=e===this.$props.defaultFilters[t.filter]?t.placeholder:YUNOCommon.findObjectByKey(t.items,"slug",e).label,n},onChange(e,t){this.$props.options.payload[e.filter]=e.slug,this.$emit("onDropdownChange",e,t)}}}),Vue.component("yuno-table-grid",{props:{apiURL:{type:Object,required:!0},apiMethodType:{type:String,required:!0},payload:{type:Object,required:!1},pageHeader:{type:Object,required:!0},sessionStorage:{type:Object,required:!0},emptyStateCTA:{type:Array,default:()=>[]},emptyStateImg:{type:String},recordFoundCaption:{type:String,required:!1},isTitleVisible:{type:Boolean,default:!0}},template:'\n        <div :class="[isFilterMobile ? \'filterActive\' : \'\']">\n            <div class="mainHeader">\n                <div class="block" v-if="isTitleVisible">\n                    <h1 class="pageTitle">{{ pageHeader.title }} \n                        <a \n                            class="refresh" \n                            @click.prevent="refreshTable()"\n                            href="#">\n                            <b-tooltip label="Refresh"\n                                type="is-dark"\n                                position="is-right">\n                                <span class="material-icons">refresh</span>\n                            </b-tooltip>\n                        </a>\n                    </h1>\n                    <div class="action" v-if="pageHeader.button.isActive">\n                        <template v-if="pageHeader.button.type === \'custom\'">\n                            <b-button \n                                @click="pageCTA()"\n                                class="yunoSecondaryCTA wired">\n                                {{pageHeader.button.label}}\n                            </b-button>\n                        </template>\n                        <template v-else>\n                            <b-button tag="a"\n                                :href="pageHeader.button.url"\n                                class="yunoSecondaryCTA wired">\n                                {{pageHeader.button.label}}\n                            </b-button>\n                        </template>\n                    </div> \n                </div>\n                <yuno-table-grid-filters\n                    @onDropdownChange="onDropdownChange"\n                    @manageFilters="onManageFilters"\n                    @clearFilter="onClearFilter"\n                    :data="payload"\n                    :defaultFilters="defaultFilters"\n                    :options="{\'isFilterMobile\': isFilterMobile}"\n                    @onQuerySearch="onQuerySearch"\n                    @onSearchInput="onSearchInput"\n                >\n                </yuno-table-grid-filters>\n                <slot name="stats"></slot>\n                <div class="gridInfo" v-if="filterResult.success && !filterResult.error">\n                    <p class="note">\n                        <span>{{ filterResult.count }}</span> {{ recordFoundCaption !== undefined ? recordFoundCaption : pageHeader.title + \' found\' }} \n                    </p>\n                    <ul class="actions">\n                        <template v-for="(action, i) in gridActions">\n                            <li v-if="action.type === \'dropdown\'" :key="i">\n                                <div class="yunoDropdown iconOnly" :class="[action.filter]">\n                                    <b-dropdown \n                                        v-model="action.selected" \n                                        aria-role="list"\n                                        :mobile-modal="false"\n                                        :multiple="true"\n                                        @change="onHideColChange($event, action)"\n                                    >\n                                        <template #trigger>\n                                            <div class="labelWrapper">\n                                                <span class="selectedItem"><span class="material-icons-outlined">{{ action.icon }}</span></span>\n                                                <span class="material-icons icon">expand_more</span>\n                                            </div>\n                                        </template>\n                                        <p class="listCaption">Hide Columns</p>\n                                        <template v-for="(option, j) in filterResult.data.grid_view.columns">\n                                            <b-dropdown-item \n                                                :value="option.field" \n                                                :kety="j"\n                                                aria-role="listitem"\n                                                @click="onHideColItemSelect(option, action)"\n                                            >\n                                                {{ option.label }}\n                                            </b-dropdown-item>\n                                        </template>\n                                    </b-dropdown>\n                                </div>\n                            </li>\n                        </template>\n                    </ul>\n                </div>\n            </div>\n            <yuno-csv-download-modal :data="filterResult"></yuno-csv-download-modal>\n            <section class="yunoTable">\n                <template v-if="filterResult.loading">\n                    <b-skeleton height="500px"></b-skeleton>\n                </template>\n                <template v-if="filterResult.success">\n                    <template v-if="filterResult.error">\n                        <yuno-empty-state-v2 \n                            :options="{\'type\': \'noDataFound\', \'message\': filterResult.errorData}"\n                            :cta="emptyStateCTA"\n                            :image="emptyStateImg"\n                        >\n                        </yuno-empty-state-v2>\n                    </template>    \n                    <template v-else>\n                        <b-table\n                            :data="filterResult.data.grid_view.rows"\n                            :loading="filterResult.isLoadMore"\n                            :paginated="true"\n                            :backend-pagination="true"\n                            :total="filterResult.count"\n                            :per-page="filterResult.limit"\n                            :current-page="filterResult.currentPage"\n                            :striped="true"\n                            backend-sorting\n                            @sort="sortPressed"\n                            :default-sort-direction="defaultSortOrder"\n                            :default-sort="defaultSort"\n                            @page-change="pageChange($event)"\n                            ref="table">\n                            <b-table-column\n                                v-for="(column, i) in filterResult.data.grid_view.columns" \n                                :key="i" \n                                :field="column.field" \n                                :visible="column.is_active"\n                                :label="column.label" \n                                v-slot="props"\n                                :sortable="column.sortable">\n                                <template v-if="column.field === \'time_of_study\'">\n                                    <div \n                                        class="grid"\n                                        :class="[column.field]">\n                                        <span class="item iconWithLabel">\n                                            <span class="material-icons">\n                                                <template v-if="props.row.time_of_study === \'morning\'">\n                                                    wb_twilight\n                                                </template>\n                                                <template v-else-if="props.row.time_of_study === \'afternoon\'">\n                                                    wb_sunny\n                                                </template>\n                                                <template v-else-if="props.row.time_of_study === \'evening\'">\n                                                    wb_twilight\n                                                </template>\n                                                <template v-else-if="props.row.time_of_study === \'night\'">\n                                                    dark_mode\n                                                </template>\n                                            </span>\n                                            <span class="itemLabel">\n                                                {{ props.row.time_of_study }}\n                                            </span>\n                                        </span>\n                                    </div>    \n                                </template>\n                                <template v-else-if="column.field === \'learner\'">\n                                    <figure \n                                        :class="[column.field]" class="userWithPhoto">\n                                        <img :src="props.row.learner.image" :alt="props.row.learner.name" width="20" height="20">\n                                        <figcaption>\n                                            {{ props.row.learner.name }}\n                                        </figcaption>\n                                    </figure>    \n                                </template>\n                                <template v-else-if="column.field === \'instructor_name\'">\n                                    <figure \n                                        :class="[column.field]" class="userWithPhoto">\n                                        <img :src="props.row.instructor.image" :alt="props.row.instructor.name" width="20" height="20">\n                                        <figcaption>\n                                            {{ props.row.instructor.name }}\n                                        </figcaption>\n                                    </figure>    \n                                </template>\n                                <template v-else-if="column.field === \'whatsapp\'">\n                                    <div \n                                        class="grid"\n                                        :class="[column.field]">\n                                        <b-tooltip :label="props.row[column.field].message"\n                                            type="is-dark"\n                                            position="is-left">\n                                            <span class="item iconWithLabel">\n                                                <span class="material-icons" :class="[props.row[column.field].value ? \'green\' : \'grey\']">\n                                                    <template v-if="props.row[column.field].value">\n                                                        check_circle\n                                                    </template>\n                                                    <template v-else>\n                                                        remove_circle\n                                                    </template>\n                                                </span>\n                                            </span>    \n                                        </b-tooltip>\n                                    </div>    \n                                </template>\n                                <template v-else-if="column.field === \'term_of_service\'">\n                                    <div \n                                        class="grid"\n                                        :class="[column.field]">\n                                        <b-tooltip :label="props.row[column.field].message"\n                                            type="is-dark"\n                                            position="is-left">\n                                            <span class="item iconWithLabel">\n                                                <span class="material-icons" :class="[props.row[column.field].value ? \'green\' : \'grey\']">\n                                                    <template v-if="props.row[column.field].value">\n                                                        check_circle\n                                                    </template>\n                                                    <template v-else>\n                                                        remove_circle\n                                                    </template>\n                                                </span>\n                                            </span>    \n                                        </b-tooltip>\n                                    </div>    \n                                </template>\n                                <template v-else-if="column.field === \'attendance_percentage_class_duration\' || column.field === \'attendance_percentage_class_count\' || column.field === \'attendance_percentage\'">\n                                    <div class="percentageBlock" :class="[column.field]">\n                                        <b-progress \n                                            :type="{\n                                                \'is-red\': props.row[column.field] <= 30,\n                                                \'is-orange\': props.row[column.field] > 30,\n                                                \'is-yellow\': props.row[column.field] > 50,\n                                                \'is-lightGreen\': props.row[column.field] > 70,\n                                                \'is-darkGreen\': props.row[column.field] > 80\n                                            }"  \n                                            format="percent"    \n                                            :value="Number(props.row[column.field])">\n                                            {{props.row[column.field]}}\n                                        </b-progress>\n                                        <div class="percentage">{{props.row[column.field]}}%</div>\n                                    </div>\n                                </template>\n                                <template v-else-if="column.field === \'last_class\'">\n                                    <div \n                                        class="grid"\n                                        :class="[column.field]">\n                                        <span>{{ props.row[column.field].date }}</span>\n                                        <span class="status" :class="[props.row[column.field].attended === \'Yes\' ? \'isActive\' : \'isNotActive\']"></span>\n                                    </div>    \n                                </template>\n                                <template v-else-if="column.field === \'recent_classes\'">\n                                    <div \n                                        class="grid recentClasses"\n                                        :class="[column.field]">\n                                        <span @click="getClassDetail(classAttendance)" v-for="(classAttendance, i) in props.row[column.field]" :key="classAttendance.class_id" :class="[classAttendance.attended === \'Yes\' ? \'isActive\' : \'\']" class="classDay2">\n                                            <b-tooltip \n                                                type="is-dark"\n                                                :triggers="[\'click\']"\n                                                position="is-right"\n                                                :auto-close="[\'outside\', \'escape\']">\n                                                <template v-slot:content>\n                                                    <div class="tooltipWrapper">\n                                                        <template v-if="learnerInsightsClass.loading">\n                                                            <b-skeleton size="is-large" :active="true" :count="4"></b-skeleton>\n                                                        </template>\n                                                        <template v-else-if="learnerInsightsClass.success && learnerInsightsClass.error === null">\n                                                            <p class="overline fontColorPrimary" v-if="attendance.is_absent">Did not attend</p>\n                                                            <p class="overline fontColorDarkVariant">{{ attendance.class_date }}</p>\n                                                            <h4 class="body2">{{ attendance.class_title }}</h4>\n                                                            <ul class="info">\n                                                                <li class="hasPipe">\n                                                                    <figure>\n                                                                        <div class="imgWrapper size24">\n                                                                            <img :src="attendance.instructor.image" :alt="attendance.instructor.name">\n                                                                        </div>\n                                                                        <figcaption>\n                                                                            <p class="overline fontColorDarkVariant">Instructor</p>\n                                                                            <span class="caption4">{{ attendance.instructor.name }}</span>\n                                                                        </figcaption>\n                                                                    </figure>\n                                                                </li>\n                                                                <li class="hasPipe hasRow">\n                                                                    <p class="overline fontColorDarkVariant">Start Time</p>\n                                                                    <span class="value caption3">\n                                                                        {{ attendance.class_start_time }}\n                                                                    </span>\n                                                                </li>\n                                                                <li class="hasPipe hasRow">\n                                                                    <p class="overline fontColorDarkVariant">Duration</p>\n                                                                    <span class="value caption3">\n                                                                        {{ attendance.class_duration }}\n                                                                    </span>\n                                                                </li>\n                                                            </ul>\n                                                            <div class="hasGrid">\n                                                                <div>\n                                                                    <p class="overline fontColorDarkVariant">Attendance Duration</p>\n                                                                </div>\n                                                                <div>\n                                                                    <p class="caption3">{{ attendance.attendence_duration.value }}</p>\n                                                                </div>\n                                                            </div>\n                                                            <b-progress \n                                                                :type="{\n                                                                    \'is-red\': attendance.attendence_duration.percentage <= 30,\n                                                                    \'is-orange\': attendance.attendence_duration.percentage > 30,\n                                                                    \'is-yellow\': attendance.attendence_duration.percentage > 50,\n                                                                    \'is-lightGreen\': attendance.attendence_duration.percentage > 70,\n                                                                    \'is-darkGreen\': attendance.attendence_duration.percentage > 80\n                                                                }"  \n                                                                format="percent"    \n                                                                size="is-small"\n                                                                :value="Number(attendance.attendence_duration.percentage)"\n                                                            >\n                                                            </b-progress>\n                                                        </template>\n                                                        <template v-else-if="learnerInsightsClass.success">\n                                                            {{learnerInsightsClass.errorData}}\n                                                        </template>\n                                                    </div>\n                                                </template>\n                                                <span class="classWrapper">{{ i + 1 }}</span>\n                                            </b-tooltip>\n                                        </span>\n                                        <span v-if="props.row.total_recent_classes_count !== 0">\n                                            {{ "(" + props.row.total_recent_classes_count + ")" }}\n                                        </span>\n                                    </div>    \n                                </template>\n                                <template v-else-if="column.field === \'enrollment_status\'">\n                                    <div :class="[column.field, \'hasActiveInactive\', props.row[column.field] === \'ACTIVE\' ? \'active\' : \'inactive\']">\n                                        <template v-if="props.row[column.field] === \'ACTIVE\'">\n                                            <span class="material-icons">how_to_reg</span>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons">person_off</span>\n                                        </template>\n                                        <span class="value">{{ props.row[column.field] }}</span>\n                                    </div>    \n                                </template>\n                                <template v-else-if="column.field === \'actions\'">\n                                    <ul :class="[column.field]">\n                                        <li \n                                            v-for="(item, k) in props.row[column.field]"\n                                            v-if="item.is_active"\n                                            :key="\'action-\' + k">\n                                            <b-tooltip :label="item.label"\n                                                type="is-dark"\n                                                position="is-left">\n                                                <a :href="item.url !== false ? item.url : \'#\'" :target="item.link_target" @click="initAction(props.row, item, $event)">\n                                                    <span class="itemLabel">{{ item.label }}</span>\n                                                    <span class="itemIcon" :class="[item.active_class]" @mouseover="manageMouse(item, \'over\')" @mouseout="manageMouse(item, \'out\')">\n                                                        {{ item.icon.value === undefined ?  item.icon.font : item.icon.value }}\n                                                    </span>\n                                                </a>\n                                            </b-tooltip>\n                                        </li>\n                                    </ul>    \n                                </template>\n                                <template v-else-if="column.field === \'recording_url\'">\n                                    <ul :class="[column.field]" class="actions">\n                                        <li \n                                            v-for="(item, k) in recordingActions(props.row, column.field)"\n                                            v-if="item.is_active"\n                                            :key="\'action-\' + k">\n                                            <b-tooltip :label="item.label"\n                                                type="is-dark"\n                                                position="is-left">\n                                                <a :href="item.url !== false ? item.url : \'#\'" :target="item.link_target" @click="initAction(props.row, item, $event)">\n                                                    <span class="itemLabel">{{ item.label }}</span>\n                                                    <span class="itemIcon" :class="[item.active_class]" @mouseover="manageMouse(item, \'over\')" @mouseout="manageMouse(item, \'out\')">\n                                                        {{ item.icon.value === undefined ?  item.icon.font : item.icon.value }}\n                                                    </span>\n                                                </a>\n                                            </b-tooltip>\n                                        </li>\n                                    </ul> \n                                </template>\n                                <template v-else-if="column.field === isDrawerCol(hasDrawerCols, column.field)">\n                                    <div :class="[column.field]">\n                                        <a href="#" @click.prevent="manageDrawer(props.row, column)">{{ props.row[column.field] }}</a>\n                                    </div>    \n                                </template>\n                                <template v-else>\n                                    <div :class="[column.field]">{{ props.row[column.field] }}</div>    \n                                </template>\n                            </b-table-column>\n                        </b-table>    \n                    </template>    \n                </template>\n            </section>\n        </div>\n    ',data:()=>({hasDrawerCols:["enrollment_count","attendance_count"],isFilterMobile:!1,defaultFilters:[],selectedOption:[],defaultSort:[],gridActions:[{message:"Request CSV download",icon:"summarize",filter:"request_CSV_download",is_active:!0,selected:"",type:"custom",placeholder:"Role",items:[]},{message:"Hide columns",icon:"settings",filter:"hideColumn",is_active:!0,selected:[],type:"dropdown",placeholder:"Role",items:[]}]}),computed:{...Vuex.mapState(["user","userInfo","filterResult","learnerInsightsClass"]),attendance(){return this.learnerInsightsClass.data}},watch:{"filterResult.refreshTable":{handler(e,t){e&&this.refreshTable()},deep:!0}},async created(){},destroyed(){},mounted(){this.manageDefaultFilters(),this.getStorage()},methods:{async copyToClipboard(e,t){try{await navigator.clipboard.writeText(t.url),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard",position:"is-bottom"})}catch(e){console.error("Failed to copy:",e),this.$buefy.toast.open({duration:1e3,message:"Failed to copy!",position:"is-bottom"})}},initAction(e,t,n){if("clickToCopy"===t.slug)n.preventDefault(),this.copyToClipboard(e,t);this.$emit("initAction",e,t,n)},recordingActions(e,t){const n={id:e.recording},a=encodeURI(JSON.stringify(n));return 0!==e.recording.length&&[{label:"Open in new tab",is_active:!0,slug:"openInNewTab",active_class:"material-icons-outlined",url:YUNOCommon.config.host()+"/recording/?videoID="+a+"&class_id="+e.id,link_target:"_blank",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"open_in_new"}},{label:"Click to copy",is_active:!0,slug:"clickToCopy",active_class:"material-icons-outlined",url:YUNOCommon.config.host()+"/recording/?videoID="+a+"&class_id="+e.id,link_target:"_blank",icon:{type:"mdl",class:"material-icons-outlined",hover:"material-icons",font:"file_copy"}}]},isDrawerCol(e,t){if(YUNOCommon.findInArray(e,t))return t},manageDrawer(e,t){this.$emit("manageDrawer",e,t)},getClassDetail(e){this.fetchClassDetails(e.class_id,e.user_id)},gotClassDetails(e){const{response:t}=e;if(200===t?.data?.code){const{data:e}=t;e?.is_absent&&(this.learnerInsightsClass.errorData=t.data.message)}},fetchClassDetails(e,t){Object.assign(this.learnerInsightsClass,{success:!1,error:null,data:[]});const n={apiURL:YUNOCommon.config.learnerInsightsClassAPI(e,t,!0),module:"gotData",store:"learnerInsightsClass",callback:!0,callbackFunc:e=>this.gotClassDetails(e)};this.$store.dispatch("fetchData",n)},manageAPIURL(){return"/wp-json/"+YUNOCommon.getFromString(YUNOCommon.config[this.$props.apiURL.configMethod](this.$props.apiURL.type,this.$props.apiURL.role,this.$props.apiURL.view,this.filterResult.limit,this.filterResult.offset),/wp-json\/(.*)/)},onHideColItemSelect(e,t){e.is_active?e.is_active=!1:e.is_active=!0,this.setStorage(!0)},onHideColChange(e,t){},manageMouse(e,t){e.active_class="over"===t?e.icon.hover:e.icon.class},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},getStorage(){const e=this.$props.sessionStorage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.filterResult.payload=e.payload,void 0!==this.filterResult.payload.org_id?this.filterResult.payload.org_id=this.activeOrg():void 0!==this.filterResult.payload.org&&(this.filterResult.payload.org=this.activeOrg()),this.gridActions=e.gridActions,this.filterResult.currentPage=e.currentPage,this.filterResult.offset=e.offset,this.fetchGrid(!0)}else this.fetchGrid(!0)},setStorage(e){const t=this.$props.sessionStorage,n={payload:this.filterResult.payload,gridActions:this.gridActions,currentPage:this.filterResult.currentPage,offset:this.filterResult.offset,currentTab:this.filterResult.tabs.activeTab};setTimeout(()=>{sessionStorage.setItem(t.name+"V"+t.version,JSON.stringify(n))},100)},manageDefaultFilters(){this.defaultFilters=JSON.parse(JSON.stringify(this.$props.payload)),this.defaultSortOrder=this.defaultFilters.sorting.type,this.defaultSort=this.defaultFilters.sorting.column},onClearFilter(e,t){this.$props.payload[t.filter]=this.defaultFilters[t.filter],this.resetTable(),this.fetchGrid(!0)},onDropdownChange(e,t){this.resetTable(),this.fetchGrid(!0)},onManageFilters(){this.isFilterMobile=!this.isFilterMobile},resetTable(){this.filterResult.currentPage=1,this.filterResult.error=null,this.filterResult.data=[],this.filterResult.success=!1,this.filterResult.offset=0},sortPressed(e,t,n){const a=this.$props.payload;a.sorting.column=e,a.sorting.type=t,this.filterResult.isLoadMore=!0,this.fetchGrid(!1)},refreshTable(){this.filterResult.isLoadMore=!0,this.fetchGrid(!1)},pageChange(e){this.filterResult.currentPage=e,this.filterResult.isLoadMore=!0,this.filterResult.offset=Math.floor(this.filterResult.limit*e-this.filterResult.limit),this.fetchGrid(!1)},manageColumnsVisibility(e){const t=YUNOCommon.findObjectByKey(this.gridActions,"filter","hideColumn");if(0!==t.selected.length)for(let n=0;n<t.selected.length;n++){const a=t.selected[n];YUNOCommon.findObjectByKey(e,"field",a).is_active=!1}},onQuerySearch(e,t,n){this.$emit("onQuerySearch",e,t,n),n&&(this.resetTable(),this.fetchGrid(!0))},onSearchInput(e,t){this.resetTable(),this.fetchGrid(!0)},refineFilters(e){for(let t=0;t<e.length;t++){const n=e[t];"dropdown"===n.type&&n.selected}this.$emit("onRefineFilters",e)},gotGrid(e){this.filterResult.isLoadMore=!1,this.filterResult.loading=!1,this.filterResult.refreshTable=!1;const{code:t,data:n}=e.response?.data||{};201===t||200===t?(this.refineFilters(n.filters),this.manageColumnsVisibility(n.grid_view.columns),this.$emit("onGotGrid",n.grid_view),this.filterResult.count=n.count,this.filterResult.data=n):(n.filters&&this.refineFilters(n.filters),this.filterResult.count=n.count,this.filterResult.data=n)},fetchGrid(e){this.setStorage(),this.isFilterMobile=!1,this.filterResult.loading=e;let t={apiURL:YUNOCommon.config[this.$props.apiURL.configMethod](this.$props.apiURL.type,this.$props.apiURL.role,this.$props.apiURL.view,this.filterResult.limit,this.filterResult.offset),module:"gotData",store:"filterResult",moduleLoading:e,callback:!0,addToModule:!1,callbackFunc:e=>this.gotGrid(e)};"POST"===this.$props.apiMethodType&&(t.payload=this.filterResult.payload,t.headers={accept:"application/json","content-type":"application/json"}),this.$store.dispatch("POST"===this.$props.apiMethodType?"postData":"fetchData",t)}}}),Vue.component("yuno-breadcrumbs",{props:{data:{type:Array,required:!0}},template:'\n        <ul class="breadcrumbs">\n            <li v-for="(item, index) in data" :key="index">\n                <template v-if="item.url === null">\n                    <span>{{ item.label }}</span>\n                </template>\n                <template v-else>\n                    <a :href="item.url" :class="[item.isActive ? \'active\' : \'\']">{{ item.label }}</a>\n                </template>\n            </li>\n        </ul>\n    ',data:()=>({}),computed:{},async created(){},destroyed(){},mounted(){},methods:{}}),Vue.component("yuno-page-title",{props:{data:{type:Object,required:!0},withoutTitle:{type:Boolean,default:!1},moduleObj:{type:Object,default:null}},template:'\n        <section class="pageTitle" :class="[withoutTitle ? \'noTitle\' : \'\']">\n            <template v-if="moduleObj !== null && moduleObj.loading">\n                <div class="inlineBlock">\n                    <h1 v-if="!withoutTitle">{{ data.title }}</h1>\n                </div>\n                <div class="inlineBlock">\n                    <b-skeleton active width="100px" height="40px"></b-skeleton>\n                </div>\n            </template>\n            <template v-else>\n                <div class="inlineBlock">\n                    <h1 v-if="!withoutTitle">{{ data.title }}</h1>\n                </div>\n                <div class="inlineBlock" :class="[data.cta.length > 1 ? \'hasGrid\' : \'\']">\n                    <template v-for="(cta, index) in data.cta">\n                        <b-button :class="[cta.class]" :tag="cta.tag" :href="cta.url" target="_blank" :key="index">{{ cta.label }}</b-button>\n                    </template>\n                </div>\n            </template>\n        </section>\n    ',data:()=>({}),computed:{},async created(){},destroyed(){},mounted(){},methods:{}}),Vue.component("yuno-batch-card-v2",{props:{data:{type:Object,required:!0}},template:'\n        <article class="batchCardV2">\n            <div class="cardHeader">\n                <div class="batchDate">\n                    <p class="month caption1">{{ manageDate(data.starts_on_iso, \'month\') }}</p>\n                    <p class="date h3">{{ manageDate(data.starts_on_iso, \'date\') }}</p>\n                    <p class="day caption2">{{ manageDate(data.starts_on_iso, \'day\') }}</p>\n                </div>\n                <div class="batchInfo">\n                    <div class="timeAmount">\n                        <p class="time h4">{{ data.batch_start_time }}</p>\n                        <p class="amount h4" v-if="data.price">₹{{ data.price }}</p>\n                    </div>\n                    <p class="caption2">Duration: {{ data.duration_weeks }}</p>\n                    <div class="hasArray">\n                        <span class="caption2" v-for="(day, i) in data.class_days" :class="{active: day.is_available}">\n                            {{day.label}}\n                        </span>\n                    </div>\n                    <ul class="colView">\n                        <li class="hasPipe">\n                            <div class="ctaLook caption2">\n                                <span class="material-icons-outlined">\n                                    {{ data.batch_personalisation === \'one_to_many\' ? \'groups\' : \'safety_divider\' }}\n                                </span>\n                                {{ data.batch_personalisation === \'one_to_many\' ? \'Group classes\' : \'1-to-1\' }}\n                            </div>\n                        </li>\n                        <li>\n                            <p class="caption2">Online</p>\n                        </li>\n                    </ul>\n                </div>\n            </div>\n            <div class="cardFooter">\n                <div>\n                    <p class="caption3">{{ data.instructor.name }}</p>\n                    <ul class="colView">\n                        <li class="hasPipe">\n                            <p class="caption2 fontColorDarkVariant">{{ data.instructor.rating }}</p>\n                            <span class="material-icons">star</span>\n                        </li>\n                        <li>\n                            <p class="caption2 fontColorDarkVariant">{{ data.successful_enrollment }} active students</p>\n                        </li>\n                    </ul>\n                </div>\n                <div>\n                    <img :src="data.instructor.image" :alt="data.instructor.name" class="avatar" width="48" height="48">\n                </div>\n            </div>\n        </article>\n    ',data:()=>({}),computed:{},async created(){},destroyed(){},mounted(){},methods:{manageDate(e,t){const n=new Date(e),a={month:()=>n.toLocaleString("default",{month:"short"}),date:()=>n.getDate(),day:()=>n.toLocaleString("default",{weekday:"short"})};return a[t]?.()||null}}}),Vue.component("yuno-instructor-card",{props:{data:{type:Object,required:!0}},template:'\n        <article class="instructorCard">\n            <div class="cardHeader">\n                <figure class="avatar">\n                    <div class="avatarWrapper">\n                        <img :src="data.profile_image" :alt="data.name" width="96" height="96">\n                    </div>\n                    <figcaption>\n                        <h3 class="h4">{{ data.name }}</h3>\n                        <div class="isFlexbox isCenter">\n                            <div class="isFlexbox isVCenter">\n                                <p class="h4">{{ data.rating === "" ? 0 : data.rating}}</p>\n                                <span class="material-icons">star</span>\n                            </div>\n                            <div>\n                                <p class="caption1 fontColorDarkVariant">({{ data.total_reviews === "" ? 0 : data.total_reviews }})</p>\n                            </div>\n                        </div>\n                    </figcaption>\n                </figure>\n            </div>\n        </article>\n    ',data:()=>({}),computed:{},async created(){},destroyed(){},mounted(){},methods:{}}),Vue.component("yuno-form",{props:["data","options"],template:'\n        <validation-observer \n            tag="div" \n            class="formWrapper"\n            ref="formObserver" \n            v-slot="{ handleSubmit, invalid }">\n            <form id="yunoForm" @submit.prevent="handleSubmit(initForm)">\n                <div class="fieldWrap">\n                    <h3 class="fieldLabelLarge">Diagnostic Test</h3>\n                    <p class="fieldDescription">We will check your current level of skills so that your instructor can help you make improvements in your weak areas</p>\n                    <b-field label="Is there a diagnostic test" class="radioField">\n                        <validation-provider \n                            tag="div"\n                            class="provider"\n                            :rules="{required: true}" \n                            v-slot="{ errors, classes }">\n                            <b-switch v-model="data.diagnostic_test.is_active"\n                                :true-value="true"\n                                :false-value="false"\n                                size="is-small"\n                                :class="classes">\n                            </b-switch>\n                            <p class="error">{{errors[0]}}</p>\n                        </validation-provider>\n                    </b-field>\n                    <b-field label="Duration of the test" v-if="data.diagnostic_test.is_active">\n                        <validation-provider \n                            tag="div"\n                            class="provider grid"\n                            :rules="{required: data.diagnostic_test.is_active ? true : false}" \n                            v-slot="{ errors, classes }">\n                            <b-select\n                                :class="classes"\n                                size="is-small"\n                                @input="optionSelect($event, \'diagnosticTestDuration\')"\n                                v-model="data.diagnostic_test.duration">\n                                <option value="">Select</option>\n                                <option\n                                    v-for="option in duration"\n                                    :value="option.slug"\n                                    :key="option.slug">\n                                    {{ option.label }}\n                                </option>\n                            </b-select>\n                            <span class="helper">minutes</span>\n                            <p class="error">{{errors[0]}}</p>\n                        </validation-provider>\n                    </b-field>\n                    <b-field label="Will the instructor give feedback on the diagnostic test?" class="radioField">\n                        <validation-provider \n                            tag="div"\n                            class="provider"\n                            :rules="{required: true}" \n                            v-slot="{ errors, classes }">\n                            <b-switch v-model="data.diagnostic_test.feedback"\n                                :true-value="true"\n                                :false-value="false"\n                                size="is-small"\n                                :class="classes">\n                            </b-switch>\n                            <p class="error">{{errors[0]}}</p>\n                        </validation-provider>\n                    </b-field>\n                    <div class="fieldBlock" v-if="data.diagnostic_test.feedback">\n                        <h4 class="fieldLabelSmall">Instructor effort</h4>\n                        <b-field label="Time to correct the test per student">\n                            <validation-provider \n                                tag="div"\n                                class="provider grid"\n                                :rules="{required: data.diagnostic_test.feedback ? true : false}" \n                                v-slot="{ errors, classes }">\n                                <b-select\n                                    :class="classes"\n                                    size="is-small"\n                                    @input="optionSelect($event, \'instructorEffort\')"\n                                    v-model="data.diagnostic_test.effort">\n                                    <option value="">Select</option>\n                                    <option\n                                        v-for="option in duration"\n                                        :value="option.slug"\n                                        :key="option.slug">\n                                        {{ option.label }}\n                                    </option>\n                                </b-select>\n                                <span class="helper">minutes per student</span>\n                                <p class="error">{{errors[0]}}</p>\n                            </validation-provider>\n                        </b-field>\n                    </div>\n                </div>\n                <div class="fieldWrap">\n                    <h3 class="fieldLabelLarge gap15">Live Classes</h3>\n                    <div class="fieldBlock noBg" v-if="data.personalization === \'1-Many\'">\n                        <h4 class="fieldLabelSmall">Group Classes</h4>\n                        <div class="fieldsGrid">\n                            <b-field label="Of 180 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.group_classes_duration[\'180_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 150 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-input \n                                        :class="classes" \n                                        v-model="data.group_classes_duration[\'150_min\']">\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 120 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-input \n                                        :class="classes" \n                                        v-model="data.group_classes_duration[\'120_min\']">\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 90 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-input \n                                        :class="classes" \n                                        v-model="data.group_classes_duration[\'90_min\']">\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 75 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-input \n                                        :class="classes" \n                                        v-model="data.group_classes_duration[\'75_min\']">\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 60 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-input \n                                        :class="classes" \n                                        v-model="data.group_classes_duration[\'60_min\']">\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 45 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}"  \n                                    v-slot="{ errors, classes }">\n                                    <b-input \n                                        :class="classes" \n                                        v-model="data.group_classes_duration[\'45_min\']">\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 30 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-input \n                                        :class="classes" \n                                        v-model="data.group_classes_duration[\'30_min\']">\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </div>\n                    </div>\n                    <div class="fieldBlock noBg">\n                        <h4 class="fieldLabelSmall">1-to-1 Classes</h4>\n                        <div class="fieldsGrid">\n                            <b-field label="Of 180 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.one_to_one_classes_duration[\'180_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 150 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.one_to_one_classes_duration[\'150_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 120 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.one_to_one_classes_duration[\'120_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 90 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}"  \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.one_to_one_classes_duration[\'90_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 75 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}" \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.one_to_one_classes_duration[\'75_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 60 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}"  \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.one_to_one_classes_duration[\'60_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 45 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}"  \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.one_to_one_classes_duration[\'45_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Of 30 minute duration">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false, numeric: true}"  \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.one_to_one_classes_duration[\'30_min\']"\n                                        min="0"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </div>\n                    </div>\n                </div>\n                <div class="fieldWrap">\n                    <h3 class="fieldLabelLarge gap15">Assignments</h3>\n                    <div class="fieldBlock noBg">\n                        <h4 class="fieldLabelSmall">Assignments requiring correction</h4>\n                        <div class="fieldsGrid">\n                            <b-field label="Number of assignments per student:">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false}" \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        v-model="data.assignments_require.total"\n                                        :min="0"\n                                        :max="20"\n                                        size="is-small"\n                                    >\n                                    </b-numberinput>\n                                    <span class="helper" v-if="false">{{ data.assignments_require.total }}</span>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Total time investment per student" v-if="data.assignments_require.total !== 0">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: data.assignments_require.total !== 0 ? true : false}" \n                                    v-slot="{ errors, classes }">\n                                    <b-select\n                                        :class="classes"\n                                        size="is-small"\n                                        @input="optionSelect($event, \'assignmentTimeInvestment\')"\n                                        v-model="data.assignments_require.time_investment">\n                                        <option value="">Select</option>\n                                        <option\n                                            v-for="option in hours"\n                                            :value="option.slug"\n                                            :key="option.slug">\n                                            {{ option.label }}\n                                        </option>\n                                    </b-select>\n                                    <span class="helper">minutes per student</span>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </div>\n                    </div>\n                    <div class="fieldBlock" v-if="data.assignments_require.total !== 0">\n                        <h4 class="fieldLabelSmall">Instructor effort</h4>\n                        <b-field label="Time to correct assignment per learner">\n                            <validation-provider \n                                tag="div"\n                                class="provider grid"\n                                :rules="{required: data.assignments_require.total !== 0 ? true : false}" \n                                v-slot="{ errors, classes }">\n                                <b-select\n                                    :class="classes"\n                                    size="is-small"\n                                    @input="optionSelect($event, \'instructorAssignmentEffort\')"\n                                    v-model="data.assignments_require.effort">\n                                    <option value="">Select</option>\n                                    <option\n                                        v-for="option in hours"\n                                        :value="option.slug"\n                                        :key="option.slug">\n                                        {{ option.label }}\n                                    </option>\n                                </b-select>\n                                <span class="helper">minutes per student</span>\n                                <p class="error">{{errors[0]}}</p>\n                            </validation-provider>\n                        </b-field>\n                    </div>\n                    <div class="fieldBlock noBg">\n                        <h4 class="fieldLabelSmall">Assignments not requiring correction</h4>\n                        <div class="fieldsGrid">\n                            <b-field label="Number of assignments per student:">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: false}" \n                                    v-slot="{ errors, classes }">\n                                    <b-numberinput \n                                        :class="classes"\n                                        :min="0"\n                                        :max="20"\n                                        size="is-small"\n                                        v-model="data.assignments_not_require.total">\n                                    </b-numberinput>\n                                    <span class="helper" v-if="false">{{ data.assignments_not_require.total }}</span>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                            <b-field label="Total time investment per student" v-if="data.assignments_not_require.total !== 0">\n                                <validation-provider \n                                    tag="div"\n                                    class="provider grid"\n                                    :rules="{required: data.assignments_not_require.total !== 0 ? true : false}" \n                                    v-slot="{ errors, classes }">\n                                    <b-select\n                                        :class="classes"\n                                        size="is-small"\n                                        @input="optionSelect($event, \'assignmentNotRequireTimeInvestment\')"\n                                        v-model="data.assignments_not_require.time_investment">\n                                        <option value="">Select</option>\n                                        <option\n                                            v-for="option in hours"\n                                            :value="option.slug"\n                                            :key="option.slug">\n                                            {{ option.label }}\n                                        </option>\n                                    </b-select>\n                                    <span class="helper">minutes per student</span>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </div>\n                    </div>\n                </div>\n                <div class="fieldWrap">\n                    <h3 class="fieldLabelLarge gap15">Post Test</h3>\n                    <p class="fieldDescription">The test that happens at the end of the course to judge the progress made by the learner</p>\n                    <b-field label="Is there a post test" class="radioField">\n                        <validation-provider \n                            tag="div"\n                            class="provider"\n                            :rules="{required: true}" \n                            v-slot="{ errors, classes }">\n                            <b-switch v-model="data.post_test.is_active"\n                                :true-value="true"\n                                :false-value="false"\n                                :class="classes"\n                                size="is-small">\n                            </b-switch>\n                            <p class="error">{{errors[0]}}</p>\n                        </validation-provider>\n                    </b-field>\n                    <b-field label="Time investment by each student" v-if="data.post_test.is_active">\n                        <validation-provider \n                            tag="div"\n                            class="provider grid"\n                            :rules="{required: data.post_test.is_active ? true : false}" \n                            v-slot="{ errors, classes }">\n                            <b-select\n                                :class="classes"\n                                size="is-small"\n                                @input="optionSelect($event, \'postTestTimeInvestment\')"\n                                v-model="data.post_test.time_investment">\n                                <option value="">Select</option>\n                                <option\n                                    v-for="option in timePerStudent"\n                                    :value="option.slug"\n                                    :key="option.slug">\n                                    {{ option.label }}\n                                </option>\n                            </b-select>\n                            <span class="helper">minutes</span>\n                            <p class="error">{{errors[0]}}</p>\n                        </validation-provider>\n                    </b-field>\n                    <b-field label="Will the instructor give feedback on the post test?" class="radioField">\n                        <validation-provider \n                            tag="div"\n                            class="provider"\n                            :rules="{required: true}" \n                            v-slot="{ errors, classes }">\n                            <b-switch v-model="data.post_test.feedback"\n                                :true-value="true"\n                                :false-value="false"\n                                :class="classes"\n                                size="is-small">\n                            </b-switch>\n                            <p class="error">{{errors[0]}}</p>\n                        </validation-provider>\n                    </b-field>\n                    <div class="fieldBlock" v-if="data.post_test.feedback">\n                        <h4 class="fieldLabelSmall">Instructor effort</h4>\n                        <b-field label="Post test correction:">\n                            <validation-provider \n                                tag="div"\n                                class="provider grid"\n                                :rules="{required: data.post_test.feedback ? true : false}" \n                                v-slot="{ errors, classes }">\n                                <b-select\n                                    :class="classes"\n                                    size="is-small"\n                                    @input="optionSelect($event, \'postTestEffort\')"\n                                    v-model="data.post_test.effort">\n                                    <option value="">Select</option>\n                                    <option\n                                        v-for="option in timePerStudent"\n                                        :value="option.slug"\n                                        :key="option.slug">\n                                        {{ option.label }}\n                                    </option>\n                                </b-select>\n                                <span class="helper">minutes per student</span>\n                                <p class="error">{{errors[0]}}</p>\n                            </validation-provider>\n                        </b-field>\n                    </div>\n                </div>\n                <div class="fieldWrap">\n                    <h3 class="fieldLabelLarge gap15">Mock Exam</h3>\n                    <p class="fieldDescription">This is a fully or partially simulated test, as it appears for the learner in the actual exam</p>\n                    <b-field label="Number of mock exams">\n                        <validation-provider \n                            tag="div"\n                            class="provider grid"\n                            :rules="{required: false}" \n                            v-slot="{ errors, classes }">\n                            <b-numberinput \n                                :class="classes"\n                                :min="0"\n                                :max="20"\n                                size="is-small"\n                                v-model="data.mock_exam.total">\n                            </b-numberinput>\n                            <span class="helper" v-if="false">{{ data.mock_exam.total }}</span>\n                            <p class="error">{{errors[0]}}</p>\n                        </validation-provider>\n                    </b-field>\n                    <div class="fieldBlock" v-if="data.mock_exam.total !== 0">\n                        <h4 class="fieldLabelSmall">Instructor effort</h4>\n                        <b-field label="Mock exam correction">\n                            <validation-provider \n                                tag="div"\n                                class="provider grid"\n                                :rules="{required: data.mock_exam.total !== 0 ? true : false}" \n                                v-slot="{ errors, classes }">\n                                <b-select\n                                    :class="classes"\n                                    size="is-small"\n                                    @input="optionSelect($event, \'mockExamEffort\')"\n                                    v-model="data.mock_exam.effort">\n                                    <option value="">Select</option>\n                                    <option\n                                        v-for="option in timePerStudent"\n                                        :value="option.slug"\n                                        :key="option.slug">\n                                        {{ option.label }}\n                                    </option>\n                                </b-select>\n                                <span class="helper">minutes per student</span>\n                                <p class="error">{{errors[0]}}</p>\n                            </validation-provider>\n                        </b-field>\n                    </div>\n                </div>                    \n                <div class="ctaWrapper">\n                    <b-button\n                        :loading="options.loading ? true : false"\n                        :disabled="options.loading ? true : false" \n                        native-type="submit"\n                        class="yunoSecondaryCTA">\n                        <template v-if="options.isEditMode">\n                            Update\n                        </template>\n                        <template v-else>\n                            Save\n                        </template>\n                    </b-button>    \n                </div>\n            </form>\n        </validation-observer>\n    ',data:()=>({validationMsg:{classes:"Value should not be 0"},hours:[{label:"15",slug:15},{label:"30",slug:30},{label:"60",slug:60},{label:"90",slug:90},{label:"120",slug:120},{label:"150",slug:150},{label:"180",slug:180},{label:"210",slug:210},{label:"240",slug:240},{label:"270",slug:270},{label:"300",slug:300},{label:"330",slug:330},{label:"360",slug:360},{label:"420",slug:420},{label:"480",slug:480},{label:"540",slug:540},{label:"600",slug:600},{label:"660",slug:660},{label:"720",slug:720},{label:"780",slug:780},{label:"840",slug:840},{label:"900",slug:900},{label:"960",slug:960},{label:"1020",slug:1020},{label:"1080",slug:1080},{label:"1140",slug:1140},{label:"1200",slug:1200},{label:"1260",slug:1260},{label:"1320",slug:1320},{label:"1380",slug:1380},{label:"1440",slug:1440},{label:"1500",slug:1500},{label:"1560",slug:1560},{label:"1620",slug:1620},{label:"1680",slug:1680},{label:"1740",slug:1740},{label:"1800",slug:1800},{label:"1860",slug:1860},{label:"1920",slug:1920},{label:"1980",slug:1980},{label:"2040",slug:2040},{label:"2100",slug:2100},{label:"2160",slug:2160}],assignments:[{label:"5",slug:5},{label:"10",slug:10},{label:"15",slug:15}],duration:[{label:"15",slug:15},{label:"30",slug:30},{label:"45",slug:45},{label:"60",slug:60},{label:"90",slug:90},{label:"120",slug:120},{label:"150",slug:150},{label:"180",slug:180}],timePerStudent:[{label:"15",slug:15},{label:"30",slug:30},{label:"45",slug:45},{label:"60",slug:60},{label:"75",slug:75},{label:"90",slug:90},{label:"120",slug:120},{label:150,slug:150},{label:180,slug:180},{label:210,slug:210},{label:240,slug:240},{label:270,slug:270},{label:300,slug:300},{label:330,slug:330},{label:360,slug:360},{label:390,slug:390},{label:420,slug:420},{label:450,slug:450}]}),computed:{...Vuex.mapState(["topIssuesCited","resource"])},async created(){},mounted(){},methods:{sliderChange(e){},optionSelect(e,t){},initForm(){this.$emit("initForm")}}}),Vue.component("yuno-form-skeleton",{props:["data","options"],template:'\n        <div class="formWrapper loading">\n            <div class="fieldWrap">\n                <h3 class="fieldLabelLarge noIcon">\n                    <b-skeleton active></b-skeleton>\n                </h3>\n                <p class="fieldDescription">\n                <b-skeleton height="80px"></b-skeleton>\n                </p>\n                <div class="field">\n                    <b-skeleton height="40px" width="100px"></b-skeleton>    \n                </div>\n                <div class="field">\n                    <b-skeleton height="40px" width="100px"></b-skeleton>    \n                </div>\n            </div>\n        </div>\n    ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}}),Vue.component("yuno-summary",{props:["data","options"],template:'\n        <section class="formWrapper withSticky">\n            <template v-if="options.isEditMode">\n                <template v-if="data.loading">\n                    <yuno-summary-skeleton></yuno-summary-skeleton>\n                </template>\n                <template v-if="data.success">\n                    <template v-if="data.error">\n                        <yuno-empty-state-v2 :options="{\'type\': \'noDataFound\', \'message\': data.errorData}"></yuno-empty-state-v2>\n                    </template> \n                    <template v-else>\n                        <div class="fieldWrap">\n                            <h3 class="fieldLabelLarge">What\'s included</h3>\n                            <ul class="checkList">\n                                <li :class="[data.data.what_included.is_diagnostic_test ? \'content_paste_search\' : \'hasCancel\']">Diagnostic test</li>\n                                <li class="schedule">\n                                    <span class="bold">{{ data.data.what_included.live_classes_duration }}</span> of live classes\n                                    <p class="otherInfo" v-if="data.data.what_included.group_classes_duration"><span class="bold">{{ data.data.what_included.group_classes_duration }}</span> of group classes</p>\n                                    <p class="otherInfo"><span class="bold">{{ data.data.what_included.one_to_one_classes_duration }}</span> of 1-to-1 classes</p>\n                                </li>\n                                <li :class="[data.data.what_included.total_assignments !== 0 ? \'assignment\' : \'hasCancel\']"><span class="bold">{{ data.data.what_included.total_assignments }}</span> Assignments</li>\n                                <li :class="[data.data.what_included.total_mock_exam !== \'0\' ? \'checklistPoint\' : \'hasCancel\']"><span class="bold">{{ data.data.what_included.total_mock_exam }}</span> Mock exams with feedback</li>\n                            </ul>\n                        </div>\n                        <div class="fieldWrap">\n                            <h3 class="fieldLabelLarge">Expected time investment by each student</h3>\n                            <ul class="checkList bullet">\n                                <li><span class="bold">{{ data.data.time_investment_student.total }}</span></li>\n                                <li><span class="bold">{{ data.data.time_investment_student.live_classes }}</span> of live classes</li>\n                                <li><span class="bold">{{ data.data.time_investment_student.assignments }}</span> of assignments</li>\n                                <li><span class="bold">{{ data.data.time_investment_student.tests }}</span> of tests</li>\n                            </ul>\n                        </div>\n                        <div class="fieldWrap">\n                            <h3 class="fieldLabelLarge">Max. time investment by instructor</h3>\n                            <b-field label="Number of students in the batch">\n                                <div class="provider">\n                                    <b-select\n                                        @input="optionSelect($event)"\n                                        v-model="options.studentsInBatch"\n                                        size="is-small">\n                                        <option value="">Select</option>\n                                        <option\n                                            v-for="option in students"\n                                            :value="option.slug"\n                                            :key="option.slug">\n                                            {{ option.label }}\n                                        </option>\n                                    </b-select>\n                                </div>\n                            </b-field>\n                            <ul class="checkList bullet">\n                                <li><span class="bold">{{ data.data.time_investment_instructor.total }}</span></li>\n                                <li><span class="bold">{{ data.data.time_investment_instructor.diagnostic_test_correction }}</span> of diagnostic test correction</li>\n                                <li><span class="bold">{{ data.data.time_investment_instructor.group_classes }}</span> of group live classes</li>\n                                <li><span class="bold">{{ data.data.time_investment_instructor.one_to_one_classes }}</span> of 1-to-1 live classes</li>\n                                <li><span class="bold">{{ data.data.time_investment_instructor.assignments_correction }}</span> of assignment correction</li>\n                                <li><span class="bold">{{ data.data.time_investment_instructor.post_test_correction }}</span> of assignment correction</li>\n                                <li><span class="bold">{{ data.data.time_investment_instructor.mock_exam_correction }}</span> of mock exam correction</li>\n                            </ul>\n                        </div>\n                    </template> \n                </template>\n            </template>\n            <template v-else>\n                <yuno-empty-state-v2 :options="{\'type\': \'noInput\', \'message\': \'Summary will display here once you save the form\'}"></yuno-empty-state-v2>\n            </template>\n        </section>\n    ',data:()=>({students:[{label:"1",slug:1},{label:"2",slug:2},{label:"3",slug:3},{label:"4",slug:4},{label:"5",slug:5},{label:"6",slug:6},{label:"7",slug:7},{label:"8",slug:8},{label:"9",slug:9},{label:"10",slug:10},{label:"11",slug:11},{label:"12",slug:12},{label:"13",slug:13},{label:"14",slug:14},{label:"15",slug:15},{label:"16",slug:16},{label:"17",slug:17},{label:"18",slug:18},{label:"19",slug:19},{label:"20",slug:20}]}),computed:{},async created(){},mounted(){},methods:{optionSelect(e){this.$emit("studentsInBatch",e)}}}),Vue.component("yuno-empty-state-v2",{props:["data","options"],template:'\n        <section class="emptyStateV2" :class="[options.type]">\n            <figure>\n                <img width="158" height="136" :src="wpThemeURL + \'/assets/images/noDataFound.svg\'" alt="Yuno Learning">\n                <figcaption>\n                    {{ options.message }}\n                </figcaption>\n                <b-button v-if="options.type === \'xxx\'" @click="noResult()" class="yunoSecondaryCTA">Refresh</b-button>\n            </figure>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},mounted(){},methods:{noResult(){this.$emit("fetchSummary")}}}),Vue.component("yuno-summary-skeleton",{props:["data","options"],template:'\n        <div>\n            <div class="field">\n                <b-skeleton active width="50%"></b-skeleton>\n            </div>\n            <div class="field">\n                <b-skeleton active width="50%"></b-skeleton>\n            </div>\n            <div class="field">\n                <b-skeleton active width="50%"></b-skeleton>\n            </div>\n        </div>\n    ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}}),Vue.component("yuno-schedule-form",{props:["data","options"],template:'\n        <validation-observer \n            tag="div" \n            class="formWrapper"\n            ref="formObserver" \n            v-slot="{ handleSubmit, invalid }">\n            <template v-if="data.course_schedule.length === 0">\n                <yuno-no-activity></yuno-no-activity>\n            </template>\n            <template v-else>\n                <form id="yunoForm" @submit.prevent="handleSubmit(initForm)">\n                    <draggable \n                        v-model="data.course_schedule" \n                        @change="onChange"\n                        @start="drag=true" \n                        @end="drag=false">\n                        <template v-for="(block, i) in data.course_schedule">\n                            <yuno-activity-block \n                                :data="block"\n                                :options="{\'duration\': options.duration}"\n                                @removeActivity="onRemoveActivity"\n                                :key="i">\n                            </yuno-activity-block>\n                        </template>    \n                    </draggable>\n                    <div class="ctaWrapper">\n                        <b-button\n                            :loading="options.loading ? true : false"\n                            :disabled="options.loading ? true : false" \n                            native-type="submit"\n                            class="yunoSecondaryCTA">\n                            Save\n                        </b-button>    \n                    </div>\n                </form>\n            </template>\n        </validation-observer>\n    ',data:()=>({drag:!1}),computed:{...Vuex.mapState(["schedule"])},async created(){},mounted(){},methods:{initForm(){this.$emit("initForm")},onRemoveActivity(e){this.$emit("onRemoveActivity",e)},onChange(e){let t=this.$props.data.course_schedule;for(let e=0;e<t.length;e++){const n=t[e];n.order=e,n.id=n.activity.slug+"-"+n.order}}}}),Vue.component("yuno-schedule-preview",{props:["data","options"],template:'\n        <div class="previewWrapper formWrapper">\n            <template v-if="data.course_schedule.length === 0">\n                <div class="alert alert-secondary" role="alert">\n                    <span class="material-icons-outlined">preview</span> Once you add a activity it\'ll preview here\n                </div>\n            </template>\n            <template v-else>\n                <template v-for="(activity, i) in data.course_schedule">\n                    <div class="courseCard noHover" :key="i">\n                        <div class="cardHeader">\n                            <small class="cardCategory">{{ activity.activity.label }}</small>\n                        </div>\n                        <h2 class="cardTitle" v-if="manageFields(activity.activity.slug, \'title\')">{{ activity.title }}</h2>\n                        <p class="cardDescription" v-if="manageFields(activity.activity.slug, \'description\')">{{ activity.description }}</p>\n                        <div class="cardBlock" :class="{\'noBorder\': !manageFields(activity.activity.slug, \'sub_cat\') && activity.duration === \'\'}">\n                            <ul class="cardStats">\n                                <li v-if="activity.duration !== \'\'"> \n                                    <span class="listLabel">Duration: <span class="dark">{{ duration(activity.duration) }}</span></span>\n                                </li>\n                                <template v-if="manageFields(activity.activity.slug, \'sub_cat\')" v-for="(category, k) in activity.sub_cat">\n                                    <li :key="k" v-if="category.selected !== \'\'"> \n                                        <span class="listLabel">{{ category.name }}: <span class="dark">{{ category.selected.name }}</span></span>\n                                    </li>\n                                </template>\n                            </ul>\n                        </div>\n                    </div>\n                </template>    \n            </template>\n        </div>\n    ',data(){return{preview:this.$props.data}},computed:{},async created(){},mounted(){},methods:{duration(e){return YUNOCommon.findObjectByKey(this.$props.options.duration,"slug",e).label},manageFields(e,t){switch(t){case"title":return"diagonstic_test"!==e;case"sub_cat":return!["diagonstic_test","assignment","mock_test"].includes(e);case"duration":return"live_class"===e||"mock_test"===e;case"description":return!0}}}}),Vue.component("yuno-no-activity",{props:["data","options"],template:'\n        <div class="alert alert-warning" role="alert">\n            <span class="material-icons-outlined">add_circle</span> Please add activity to create course schedule\n        </div>\n    ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}}),Vue.component("yuno-activity-block",{props:["data","options"],template:'\n        <div class="courseCard" :id="data.id">\n            <div class="cardHeader">\n                <small class="cardCategory">{{ data.activity.label }}</small>\n                <b-button \n                    @click="removeActivity(data)"\n                    class="yunoSecondaryCTA withIcon wired">\n                    <span class="material-icons-outlined">delete</span> Remove\n                </b-button>\n            </div>\n            <div class="fields">\n                <b-field label="Title" v-if="manageFields(data.activity.slug, \'title\')">\n                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                        <b-input \n                            placeholder="Title of the class"\n                            v-model="data.title"\n                            :class="classes">\n                        </b-input>\n                        <p class="error">{{errors[0]}}</p>\n                    </validation-provider>\n                </b-field>\n                <div class="grid" v-if="manageFields(data.activity.slug, \'sub_cat\')">\n                    <template v-for="(cat, j) in data.sub_cat">\n                        <b-field \n                            :label="cat.name" \n                            :key="\'subCat-\' + j"\n                            v-if="cat.sub_cat.length !== 0">\n                            <validation-provider :rules="{required:false}" v-slot="{ errors, classes }">\n                                <b-select \n                                    :class="classes"\n                                    v-model="cat.selected"\n                                    placeholder="Select">\n                                        <option value="">Select</option>\n                                        <option \n                                            v-for="(option, k) in cat.sub_cat" \n                                            :key="k"\n                                            :value="option">\n                                            {{option.name}}\n                                        </option>\n                                </b-select>    \n                                <p class="error">{{errors[0]}}</p>\n                            </validation-provider>\n                        </b-field>\n                    </template>  \n                </div>\n                <b-field label="Duration" v-if="manageFields(data.activity.slug, \'duration\')">\n                    <validation-provider :rules="{required:false}" v-slot="{ errors, classes }">\n                        <b-select \n                            :class="classes"\n                            v-model="data.duration"\n                            placeholder="Select">\n                            <option value="">Select</option>\n                            <option \n                                v-for="(item, j) in options.duration"\n                                :key="j"\n                                :value="item.slug">{{ item.label }}\n                            </option>\n                        </b-select>    \n                        <p class="error">{{errors[0]}}</p>\n                    </validation-provider>\n                </b-field>\n                <b-field label="Description" v-if="manageFields(data.activity.slug, \'description\')">\n                    <validation-provider \n                        :customMessages="{ required: \'Field should not be blank\' }"\n                        :rules="{required: data.activity.slug === \'diagonstic_test\' ? true : false}" v-slot="{ errors, classes }">\n                        <b-input \n                            :class="classes"\n                            v-model="data.description"\n                            placeholder="Describe in 150 words"\n                            maxlength="150"\n                            type="textarea">\n                        </b-input>\n                        <p class="error">{{errors[0]}}</p>\n                    </validation-provider>\n                </b-field>\n            </div>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["categoryList"])},async created(){},mounted(){},methods:{manageFields(e,t){switch(t){case"title":return"diagonstic_test"!==e;case"sub_cat":return"diagonstic_test"!==e&&"assignment"!==e&&"mock_test"!==e;case"duration":return"live_class"===e||"mock_test"===e||"sectional_test"===e;case"description":return!0;default:return!1}},removeActivity(e){this.$emit("removeActivity",e)}}}),Vue.component("yuno-activity",{props:["data","options"],template:'\n        <nav class="activities">\n            <template v-if="isScheduleLoading">\n                <p><span class="material-icons-outlined">add_circle_outline</span> Add:</p>\n                <ul class="loading">\n                    <li\n                        v-for="i in loadingList"\n                        :key="i">\n                        <b-skeleton width="80px" active></b-skeleton>\n                    </li>\n                </ul>\n            </template>\n            <template v-if="isScheduleReady">\n                <template v-if="data.error">\n                    <p>{{ data.errorData }}</p>\n                </template>\n                <template v-else>\n                    <p><span class="material-icons-outlined">add_circle_outline</span> Add:</p>\n                    <ul>\n                        <li\n                            v-for="(item, i) in data.data"\n                            @click="addActivity(item)"\n                            :key="i">\n                            {{ item.label }}\n                        </li>\n                    </ul>\n                </template>\n            </template>\n        </nav>\n    ',data:()=>({loadingList:3}),computed:{...Vuex.mapState(["categoryList","schedule","settings"]),isScheduleLoading:{get(){return this.categoryList.loading||this.settings.loading}},isScheduleReady:{get(){let e="";return e=this.categoryList.success&&this.settings.success,e}}},async created(){},mounted(){},methods:{addActivity(e){this.$emit("addActivity",e)}}}),Vue.component("yuno-about-course",{template:'\n        <section class="yunoAboutCourse">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n                <div class="inlineBlock">\n                    <template v-for="(cta, index) in pageTitleData.cta">\n                        <a href="#" class="trigger" @click="initEditMode($event, cta.module)">\n                            <span class="material-icons-outlined">edit</span>\n                        </a>\n                    </template>\n                </div>\n            </div>\n            <div class="wrapper">\n                <template v-if="course.loading">\n                    <b-skeleton width="20%" :animated="true"></b-skeleton>\n                    <b-skeleton width="40%" :animated="true"></b-skeleton>\n                    <b-skeleton width="80%" :animated="true"></b-skeleton>\n                    <b-skeleton :animated="true"></b-skeleton>\n                </template>\n                <template v-else-if="course.success && course.error === null">\n                    <div class="block">\n                        <h3 class="h4">Learning outcomes</h3>\n                        <ul class="checkList">\n                            <li \n                                :key="i"\n                                :class="[courseObj.learning_outcomes.length === 1 ? \'noCheck\' : \'\']"\n                                v-for="(item, i) in courseObj.learning_outcomes">\n                                {{ item }}\n                            </li>\n                        </ul>\n                    </div>\n                    <div class="block">\n                        <h3 class="h4">Short description</h3>\n                        <p>{{ courseObj.excerpt }}</p>\n                    </div>\n                    <div class="block">\n                        <h3 class="h4">Long description</h3>\n                        <p>{{ courseObj.description }}</p>\n                    </div>\n                    <div class="block">\n                        <h3 class="h4">Course {{ courseObj.media.type === \'Image\' ? \'image\' : \'video\' }}</h3>\n                        <div class="sectionMedia">\n                            <template v-if="courseObj.media.type === \'Image\'">\n                                <img \n                                    :src="courseObj.media.url"\n                                    width="302" \n                                    height="226"\n                                    :alt="courseObj.title">        \n                            </template>\n                            <template v-else>\n                                <iframe \n                                    class="yunoYoutube" \n                                    :src="courseObj.media.url" \n                                    frameborder="0" \n                                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" \n                                    allowfullscreen>\n                                </iframe>\n                            </template>\n                        </div>\n                    </div>\n                </template>\n                <template v-else-if="course.success">\n                    {{ course.errorData }}\n                </template>\n            </div>\n        </section>\n    ',data:()=>({pageTitleData:{title:"About the Course",cta:[{module:"aboutCourse"}]}}),computed:{...Vuex.mapState(["course"]),courseObj(){return this.course.data}},async created(){},destroyed(){},mounted(){},methods:{initEditMode(e,t){e.preventDefault(),this.$emit("initEditMode",t)}}}),Vue.component("yuno-table",{props:["data","options","tabindex"],template:'\n        <div class="yunoTable hasBorder" :class="{\'container-fluid\': options.isFluid, \'container\': options.isFluid === undefined || !options.isFluid}">\n            <template v-if="data.loading">\n                <div class="smallLoader"></div>\n            </template>\n            <template v-if="data.success">\n                <template v-if="data.error">\n                    <yuno-empty-states :options="{\'state\': \'dataNotFound\', \'description\': data.errorData}"></yuno-empty-states>\n                </template>\n                    <b-table\n                        :class="{\'scrollable\': options.scrollable, \'tableInvisible\': data.error}"\n                        :loading="options.pageLoading"\n                        :paginated="true"\n                        :detailed="options.hasDetailed !== undefined ? options.hasDetailed : false"\n                        :backend-pagination="options.apiPaginated !== undefined ? options.apiPaginated : false"\n                        :total="options.totalResult !== undefined ? options.totalResult : 0"\n                        :sticky-header="options.isStickyHeader !== undefined ? options.isStickyHeader : false"\n                        :height="options.height !== undefined ? options.height : \'\'"\n                        @page-change="onPageChange($event, tabindex)"\n                        :per-page="options.perPage"\n                        :current-page="options.currentPage !== undefined ? options.currentPage : 1"\n                        ref="table"\n                        :data="data.data.rows"\n                        :default-sort="options.defaultSort"\n                        :default-sort-direction="options.sortDirection !== undefined ? options.sortDirection : \'asc\'"\n                        :striped="options.hasStriped !== undefined ? options.hasStriped : true">\n                            <b-table-column \n                                v-for="(col, colIndex) in data.data.columns" \n                                :key="colIndex" \n                                :field="col.field" \n                                :visible="col.isActive === undefined ? true : col.isActive"\n                                :label="col.label"\n                                v-slot="props"\n                                :sortable="col.sortable">\n                                    <div :id="props.row.scrollID !== undefined ? props.row.scrollID : \'\'" class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                        <span class="tag" \n                                            :class="\n                                                [\n                                                    {\'is-success\': props.row[col.field] === \'PAID\'}, \n                                                    {\'is-danger\': props.row[col.field] === \'EXPIRED\'},\n                                                    {\'is-warning\': props.row[col.field] === \'FAILED\'},\n                                                    {\'is-info\': props.row[col.field] === \'ISSUED\'},\n                                                    {\'is-info noBG\': col.hasSlot === true},\n                                                    {\'is-success\': props.row[col.field] === \'ACTIVE\'},\n                                                    {\'is-success noText\': props.row[col.field] === true},\n                                                    {\'is-light noText\': props.row[col.field] === false},\n                                                    {\'is-warning\': props.row[col.field] === \'INACTIVE\'}\n                                                ]">\n                                            {{ props.row[col.field] }}\n                                        </span>\n                                    </div>\n                            </b-table-column>\n                    </b-table>\n                \n            </template>\n        </div>\n    ',data:()=>({}),computed:{},created(){},mounted(){},methods:{nestedTableModal(e,t,n,a){e.preventDefault(),Event.$emit("nestedTableModal",e,t,n,a)},toggleShowmore(e,t){e.preventDefault(),t.isExpand?t.isExpand=!1:t.isExpand=!0},filteredAutocomplete:(e,t)=>e.items.filter(n=>n[t].toString().toLowerCase().indexOf(e.selected.toLowerCase())>=0),scrollToEle(e){let t=YUNOCommon.heightOfEle(document.querySelectorAll(".yunoHeader")[0])+YUNOCommon.heightOfEle(document.querySelectorAll(".yunoTabNav")[0],!0)+10;jQuery([document.documentElement,document.body]).animate({scrollTop:jQuery("#"+e.url).offset().top-t},500)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard",position:"is-bottom"})},onCustomEvent(e,t,n){Event.$emit("onCustomEvent",e,t,n)},onPageChange(e,t){Event.$emit("onTablePageChange",e,t),this.$props.data.manageState&&this.manageState(this.$props.data,t,e)},onActionTrigger(e,t,n){Event.$emit("onActionTrigger",e,t,n)},onFilterChange(e,t){Event.$emit("onFilterChange",t,e)},manageState(e,t,n){let a=JSON.parse(JSON.stringify(e)),o=e.filters,s=YUNOCommon.findObjectByKey(o,"type","tableColumn");const i=function(e,t){for(let n=0;n<e.length;n++){const a=e[n];t.push(a)}},r={tab:a.tab,index:t,defaultFilters:[],appliedFilters:[],nestedTabs:"",hideColumns:[],page:!1!==n?n:1};if(null!==s&&i(s.selected,r.hideColumns),void 0!==e.nestedTabs){let t=YUNOCommon.findObjectByKey(e.nestedTabs,"isActive",!0);r.nestedTabs=t.value}i(a.defaultFilters,r.defaultFilters),i(a.appliedFilters,r.appliedFilters);const l=encodeURI(JSON.stringify(r));if(history.pushState){var c=window.location.protocol+"//"+window.location.host+window.location.pathname+"?state="+l;window.history.pushState({path:c},"",c)}},onFilterItemSelect(e,t,n,a,o){Event.$emit("onFilterItemSelect",e,t,n,a,o),this.$props.data.manageState&&this.manageState(this.$props.data,o,!1)},fetchQueryData:_.debounce(function(e,t){e.length>2?(t.isLoading=!0,Event.$emit("fetchQueryData",e,t)):t.data=[]},500),onAutocompleteSelect(e,t,n){Event.$emit("onAutocompleteSelect",e,t,n),this.$props.data.manageState&&this.manageState(this.$props.data,t,!1)},onFilterClear(e,t){Event.$emit("onFilterClear",e,t),this.$props.data.manageState&&this.manageState(this.$props.data,t,!1)},manageNestedTabs(e,t,n,a,o){event.preventDefault();let s=n.nestedTabs;for(let e=0;e<s.length;e++)s[e].isActive&&(s[e].isActive=!1);s[t].isActive=!0,Event.$emit("onNestedTabChange",n,a,o),this.$props.data.manageState&&this.manageState(n,o,!1)}}}),Vue.component("yuno-batches",{props:{courseID:{type:Number,required:!0}},template:'\n        <section class="yunoBatches">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n                <div class="inlineBlock">\n                    <template v-for="(cta, index) in pageTitleData.cta">\n                        <b-button :tag="cta.tag" @click="cta.tag !== \'a\' ? initEditMode(cta.module) : \'\'" :href="cta.url" :target="cta.target" :class="[cta.class]" :key="index">\n                            <span class="underline">{{ cta.label }}</span> <span class="material-icons-outlined" v-if="cta.icon">{{ cta.icon }}</span>\n                        </b-button>\n                    </template>\n                </div>\n            </div>\n            <b-tabs class="yunoTabsV2" v-model="tabs.activeTab" @input="tabChange" :animated="false" :destroy-on-hide="true">\n                <b-tab-item \n                    v-for="(tab, i) in tabs.items"\n                    :visible="tab.isVisble"\n                    :key="i"\n                    :label="tab.label">\n                    <template v-if="moduleWithLoadMore.loading">\n                        <div class="wrapper">\n                            <article class="batchCardV2" v-for="(item, index) in 3" :key="index">\n                                <div class="cardHeader">\n                                    <div class="batchDate">\n                                        <b-skeleton height="109px"></b-skeleton>\n                                    </div>\n                                    <div class="batchInfo">\n                                        <div class="timeAmount">\n                                            <p class="time h4"><b-skeleton active width="70px"></b-skeleton></p>\n                                            <p class="amount h4"><b-skeleton active width="70px"></b-skeleton></p>\n                                        </div>\n                                        <p class="caption2"><b-skeleton active width="70px"></b-skeleton></p>\n                                        <b-skeleton active></b-skeleton>\n                                    </div>\n                                </div>\n                                <div class="cardFooter">\n                                    <div>\n                                        <b-skeleton active width="100px"></b-skeleton>\n                                    </div>\n                                    <div>\n                                        <b-skeleton circle width="48px" height="48px"></b-skeleton>\n                                    </div>\n                                </div>\n                            </article>\n                        </div>\n                    </template>\n                    <template v-else-if="moduleWithLoadMore.success && moduleWithLoadMore.error === null">\n                        <b-carousel-list\n                            v-model="activeIndex"\n                            :data="moduleWithLoadMore.data"\n                            :arrow="moduleWithLoadMore.data.length > perList"\n                            :arrow-hover="arrowHover"\n                            :items-to-show="perList"\n                            :items-to-list="increment"\n                            :repeat="repeat"\n                            :has-drag="drag"\n                            :has-grayscale="gray"\n                            :has-opacity="opacity" \n                            :class="[\'activeBatchesWrapper\']"\n                            :breakpoints="breakpoints"\n                        >\n                            <template #item="list">\n                                <yuno-batch-card-v2\n                                    :data="list"\n                                >\n                                </yuno-batch-card-v2>\n                            </template>\n                        </b-carousel-list>\n                    </template>\n                    <template v-else-if="moduleWithLoadMore.success">\n                        <yuno-empty-state-v3\n                            :title="moduleWithLoadMore.errorData"\n                        >\n                        </yuno-empty-state-v3>\n                    </template>\n                </b-tab-item>\n            </b-tabs>\n        </section>\n    ',data(){return{pageTitleData:{title:"Batches",cta:[{label:"See all batches",class:"yunoSecondaryCTA withIcon",tag:"a",url:`/batch-insights/active/?courseid=${this.$props.courseID}`,target:"_blank"},{label:"Create New Batch",class:"yunoSecondaryCTA",module:"createBatch"}]},tabs:{activeTab:0,items:[{label:"Active Batches",slug:"active-batches",isActive:!1,isVisble:!0},{label:"Past Batches",slug:"past-batches",isActive:!1,isVisble:!0}]},activeBatchesPayload:{batch_days:["sun","mon","tue","wed","thu","fri","sat"],batch_time:["morning","afternoon","evening","night"],course_id:this.$props.courseID,personalisation:"all",view:"list-view"},loadMoreItems:[{title:"",slug:"dummy"},{title:"",slug:"dummy"},{title:"",slug:"dummy"}],breakpoints:{320:{itemsToShow:1},960:{itemsToShow:3}},arrow:!0,arrowHover:!0,drag:!0,gray:!1,opacity:!1,activeIndex:0,perList:3,increment:3,repeat:!1}},computed:{...Vuex.mapState(["moduleWithLoadMore"])},async created(){},destroyed(){},mounted(){this.init()},methods:{initEditMode(e){this.$emit("initEditMode",e)},tabChange(e){this.init()},gotActiveBatches(e){this.moduleWithLoadMore.loading=!1;const t=e?.response?.data;200===t?.code&&(this.moduleWithLoadMore.data=t.data)},fetchActiveBatches(){this.moduleWithLoadMore.loading=!0;const e={apiURL:YUNOCommon.config.availableBatches(this.activeBatchesPayload.course_id,this.moduleWithLoadMore.limit,this.moduleWithLoadMore.offset),module:"gotData",store:"moduleWithLoadMore",addToModule:!1,payload:this.activeBatchesPayload,callback:!0,callbackFunc:this.gotActiveBatches};this.$store.dispatch("postData",e)},gotPastBatches(e){const t=e?.response?.data;200===t?.code&&(this.moduleWithLoadMore.data=t.data.map(e=>({...e,starts_on_iso:new Date(e.starts_on).toISOString(),batch_start_time:e.class_start_time})))},fetchPastBatches(){const e={apiURL:YUNOCommon.config.pastBatchesList(this.activeBatchesPayload.course_id,this.activeBatchesPayload.personalisation,isLoggedIn,"list-view",100,0),module:"gotData",store:"moduleWithLoadMore",callback:!0,addToModule:!1,callbackFunc:this.gotPastBatches};this.$store.dispatch("fetchData",e)},resetModule(){this.moduleWithLoadMore.data=[],this.moduleWithLoadMore.error=null,this.moduleWithLoadMore.success=!1},init(){const e=this.tabs.items[this.tabs.activeTab];"active-batches"===e.slug?(this.resetModule(),this.fetchActiveBatches()):"past-batches"===e.slug&&(this.resetModule(),this.fetchPastBatches())}}}),Vue.component("yuno-choose-class-size",{props:{options:{type:Array,required:!0},title:{type:String,required:!0}},template:'\n        <section class="moduleEdit">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ title }}</h2>\n                </div>\n            </div>\n            <validation-observer tag="div" ref="classSizeObserver" v-slot="{ handleSubmit }">\n                <form id="selectCourseForm" @submit.prevent="handleSubmit(initClassSize)">\n                    <div class="modalBody">\n                        <div class="radioList groupElement">\n                            <small class="helper">Please choose one option</small>\n                            <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                <template v-for="(item, i) in options">\n                                    <b-field class="colorGrey">\n                                        <b-radio v-model="classSize.selected"\n                                            name="classSize"\n                                            :native-value="item.slug">\n                                            {{item.label}}\n                                        </b-radio>\n                                    </b-field>\n                                </template>\n                                <p class="error">{{errors[0]}}</p>\n                            </validation-provider>\n                        </div>\n                        <div class="ctaWrapper alignLeft">\n                            <b-button\n                                native-type="submit"\n                                class="yunoSecondaryCTA">\n                                Next\n                            </b-button>\n                        </div>        \n                    </div>\n                </form>\n            </validation-observer>\n        </section>\n    ',data:()=>({classSize:{selected:"",items:[{slug:"one_to_one",label:"1-to-1"},{slug:"one_to_many",label:"Group (10 max.)"}]}}),computed:{...Vuex.mapState(["userRole"])},async created(){},destroyed(){},mounted(){},methods:{initClassSize(){const e=this.classSize.selected,t=new URL(window.location.href);t.searchParams.set("classSize",e),window.history.pushState({path:t.href},"",t.href),this.$emit("initCreateNewBatch"),this.$emit("initNextStep")}}}),Vue.component("yuno-common-form",{props:{fields:{type:Array,required:!0},payload:{type:Object,required:!0},defaultTaxonomy:{type:Number,required:!1},industryData:{type:Object,required:!1},isFormData:{type:Boolean,required:!0}},template:'\n        <div class="yunoFormWrapper">\n            <validation-observer \n                tag="div" \n                class="observer"\n                ref="orgSettingsFormObserver" \n                v-slot="{ handleSubmit, invalid }"\n            >\n                <form @submit.prevent="handleSubmit(initForm)">\n                    <template v-for="(field, i) in fields">\n                        <template v-if="field.type === \'text\'">\n                            <b-field :label="field.label" :key="i">\n                                <validation-provider \n                                    tag="div" \n                                    class="fieldWrapper" \n                                    :rules="{\n                                        required: field.isRequired\n                                    }" \n                                    v-slot="{ errors, classes }"\n                                >\n                                    <b-input\n                                        :class="classes"\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                    >\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'textarea\'">\n                            <b-field :label="field.label" :key="i">\n                                <validation-provider \n                                    tag="div" \n                                    class="fieldWrapper" \n                                    :rules="{\n                                        required: field.isRequired\n                                    }" \n                                    v-slot="{ errors, classes }"\n                                >\n                                    <b-input\n                                        :class="classes"\n                                        type="textarea"\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                    >\n                                    </b-input>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'dropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        @input="dropdownChange($event)"\n                                    >\n                                        <option\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                            :value="option.value"\n                                        >\n                                            {{ option.label }}\n                                        </option>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'taginput\'">\n                            <b-field :label="field.label" :key="i">\n                                <validation-provider \n                                    tag="div" \n                                    class="fieldWrapper" \n                                    :rules="{\n                                        required: field.isRequired\n                                    }" \n                                    v-slot="{ errors, classes }"\n                                >\n                                    <b-taginput\n                                        :class="classes"\n                                        v-model="payload[field.name]"\n                                        ellipsis\n                                        :placeholder="field.placeholder"\n                                    >\n                                    </b-taginput>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'googleFontFamilydropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="payload[field.name]"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                    >\n                                        <option\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                            :value="option.family"\n                                        >\n                                            {{ option.family }}\n                                        </option>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'groupDropdown\'">\n                            <b-field :label="field.label" :key="i">\n                                <template v-if="field.loading">\n                                    <b-skeleton height="40px"></b-skeleton>\n                                </template>\n                                <template v-else>\n                                    <b-select\n                                        v-model="industry"\n                                        :placeholder="field.placeholder"\n                                        :disabled="field.disabled"\n                                        :loading="field.isLoading"\n                                        @input="groupDropdownChange($event, field)"\n                                    >\n                                        <optgroup \n                                            :label="option.label"\n                                            v-for="(option, j) in field.options"\n                                            :key="j"\n                                        >\n                                            <option\n                                                v-for="(subOption, k) in option.sub_industry"\n                                                :key="k"\n                                                :value="subOption"\n                                            >\n                                                {{ subOption.label }}\n                                            </option>\n                                        </optgroup>\n                                    </b-select>\n                                </template>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'upload\'">\n                            <b-field :label="field.label" :key="i" class="uploadField">\n                                <p class="helper">{{ field.placeholder }}</p>\n                                <validation-provider \n                                    tag="div" \n                                    class="fieldWrapper" \n                                    :rules="{\n                                        required: field.isRequired\n                                    }" \n                                    v-slot="{ errors, classes }"\n                                >\n                                    <b-upload\n                                        v-model="payload[field.name]"\n                                        :class="classes"\n                                        accept="image/*"\n                                        class="file-label"\n                                    >\n                                        <span class="file-cta">\n                                            <span class="material-icons-outlined">file_upload</span>\n                                            <span class="file-label">{{ field.cta }}</span>\n                                        </span>\n                                        <span class="file-name" v-if="payload[field.name]">\n                                            {{ payload[field.name].name }}\n                                        </span>\n                                    </b-upload>\n                                    <p class="error">{{errors[0]}}</p>\n                                </validation-provider>\n                            </b-field>\n                        </template>\n                        <template v-if="field.type === \'colorpicker\'">\n                            <b-field :label="field.label" :key="i">\n                                <b-colorpicker\n                                    :color-formatter="(color) => color.toString(\'hex\')"\n                                    representation="square"\n                                    @input="updateColor($event, field.name)"\n                                    :value="payload[field.name]"\n                                    :disabled="field.disabled"\n                                    :loading="field.isLoading"\n                                >\n                                </b-colorpicker>\n                            </b-field>\n                        </template>\n                    </template>\n                    <div class="ctaWrapper">\n                        <b-button\n                            native-type="reset"\n                            @click="clearForm"\n                            class="yunoPrimaryCTA wired fat">\n                            Cancel\n                        </b-button>    \n                        <b-button\n                            native-type="submit"\n                            :loading="form.isLoading"\n                            :disabled="form.isLoading"\n                            class="yunoSecondaryCTA fat">\n                            Save\n                        </b-button>    \n                    </div>\n                </form>\n            </validation-observer>\n        </div>\n    ',data:()=>({industry:null,defaultPayload:[]}),computed:{...Vuex.mapState(["user","categoryTaxonomy","form"])},watch:{industryData:{handler(e,t){e!==t&&(this.industry=e)},deep:!0}},async created(){},destroyed(){},mounted(){this.init()},methods:{updateColor(e,t){this.form.payload[t]=e.toString("hex")},init(){this.defaultPayload=JSON.parse(JSON.stringify(this.form.payload))},clearForm(){this.$refs.orgSettingsFormObserver.reset(),this.form.payload=JSON.parse(JSON.stringify(this.defaultPayload))},jsonToFormData(e){const t=new FormData;for(let n in e)t.append(n,e[n]);return t},initForm(){const e=JSON.parse(JSON.stringify(this.form.payload));void 0!==e.featured_img.name&&delete e.featured_img,this.$emit("submitForm",this.isFormData?this.jsonToFormData(e):this.form.payload)},dropdownChange(e){},groupDropdownChange({parent:e,slug:t},n){"sub_industry"===n.name&&(this.form.payload.industry=e,this.form.payload.sub_industry=t)}}}),Vue.component("yuno-course-economics",{template:'\n        <section class="courseEconomics">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n                <div class="inlineBlock">\n                    <template v-for="(cta, index) in pageTitleData.cta">\n                        <a href="#" class="trigger" @click="initEditMode($event, cta.module)">\n                            <span class="material-icons-outlined">edit</span>\n                        </a>\n                    </template>\n                </div>\n            </div>\n            <div class="wrapper">\n                <template v-if="course.loading">\n                    <b-skeleton width="20%" :animated="true"></b-skeleton>\n                    <b-skeleton width="40%" :animated="true"></b-skeleton>\n                    <b-skeleton width="80%" :animated="true"></b-skeleton>\n                    <b-skeleton :animated="true"></b-skeleton>\n                </template>\n                <template v-else-if="course.success && course.error === null">\n                    <b-tabs class="yunoTabsV2" v-model="defaultPersonalisation" :animated="false">\n                        <b-tab-item \n                            v-for="(tab, i) in courseObj.course_economics"\n                            :key="i"\n                            :label="tab.label">\n                            <template v-if="tab.slug === \'group\'">\n                                <h3 class="titleLargest">\n                                    <template \n                                        v-if="tab.price[0].price !== \'Free\' && tab.price[0].price !== 0">\n                                        &#8377;{{ tab.price[0].price }} <small>Inclusive of GST</small>\n                                    </template>\n                                    <template v-else>\n                                        Free\n                                    </template> \n                                </h3>\n                                <yuno-tab-course-economics :data="tab" :options="{\'data\': courseObj}"></yuno-tab-course-economics>\n                            </template>\n                            <template v-if="tab.slug === \'one-to-one\'">\n                                <h3 class="titleLargest">\n                                    <template \n                                        v-if="tab.price[0].price !== \'Free\' && tab.price[0].price !== 0">\n                                        &#8377;{{ tab.price[0].price }} <small>Inclusive of GST</small>\n                                    </template>\n                                    <template v-else>\n                                        Free\n                                    </template> \n                                </h3>\n                                <yuno-tab-course-economics :data="tab" :options="{\'data\': courseObj}"></yuno-tab-course-economics>\n                            </template>\n                        </b-tab-item>\n                    </b-tabs>\n                </template>\n                <template v-else-if="course.success">\n                    {{ course.errorData }}\n                </template>\n            </div>\n        </section>\n    ',data:()=>({defaultPersonalisation:0,pageTitleData:{title:"Course Economics",cta:[{module:"courseEconomics"}]}}),computed:{...Vuex.mapState(["course"]),courseObj(){return this.course.data}},async created(){},destroyed(){},mounted(){},methods:{initEditMode(e,t){e.preventDefault(),this.$emit("initEditMode",t)},findActiveTab(e){return this.defaultPersonalisation=e.findIndex(e=>!0===e.is_active),this.defaultPersonalisation}}}),Vue.component("yuno-course-schedule",{template:'\n        <section class="yunoCourseSchedule">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n                <div class="inlineBlock">\n                    <template v-for="(cta, index) in pageTitleData.cta">\n                        <a href="#" class="trigger" @click="initEditMode($event, cta.module)">\n                            <span class="material-icons-outlined">edit</span>\n                        </a>\n                    </template>\n                </div>\n            </div>\n            <div class="wrapper">\n                <template v-if="schedule.loading">\n                    <b-collapse\n                        class="faqCard"\n                        animation="slide"\n                        v-for="i in loadingResult"\n                        :key="i"\n                        :open="isOpen == i">\n                        <div\n                            slot="trigger"\n                            slot-scope="props"\n                            class="card-header"\n                            :class="[props.open ? \'active\' : \'\']"\n                            role="button">\n                            <p class="card-header-title">\n                                <b-skeleton width="50%" active></b-skeleton>\n                            </p>\n                            <a class="card-header-icon">\n                                <b-icon\n                                    :icon="props.open ? \'menu-down\' : \'menu-up\'">\n                                </b-icon>\n                            </a>\n                        </div>\n                        <div class="card-content">\n                            <b-skeleton width="100%" height="80px" active></b-skeleton>\n                        </div>\n                    </b-collapse>\n                </template>\n                <template v-else-if="schedule.success && schedule.error === null">\n                    <b-collapse\n                        class="faqCard"\n                        :class="[\'item-\' + index]"\n                        animation="slide"\n                        v-for="(collapse, index) of scheduleObj.course_schedule"\n                        :key="index"\n                        :open="isOpen == index"\n                        @open="isOpen = index">\n                        <div\n                            slot="trigger"\n                            slot-scope="props"\n                            class="card-header"\n                            :class="[props.open ? \'active\' : \'\']"\n                            role="button">\n                            <p class="card-header-title">\n                                <template v-if="manageFields(collapse.activity.slug, \'title\')">{{ collapse.title }}</template>\n                                <template v-if="manageFields(collapse.activity.slug, \'title\') === false" >{{ collapse.activity.label }}</template>\n                            </p>\n                            <a class="card-header-icon">\n                                <b-icon\n                                    :icon="props.open ? \'menu-down\' : \'menu-up\'">\n                                </b-icon>\n                            </a>\n                        </div>\n                        <div class="card-content">\n                            <div class="content">\n                                <p class="cardDescription" v-if="manageFields(collapse.activity.slug, \'description\')">{{ collapse.description }}</p>\n                                <div class="cardBlock" v-if="manageFields(collapse.activity.slug, \'sub_cat\')">\n                                    <ul class="cardStats">\n                                        <li v-if="collapse.duration !== \'\'"> \n                                            <span class="listLabel">Duration: <span class="dark">{{ duration(collapse.duration) }}</span></span>\n                                        </li>\n                                        <template v-if="false" v-for="(category, k) in collapse.sub_cat">\n                                            <li :key="k" v-if="category.selected !== \'\'"> \n                                                <span class="listLabel">{{ category.name }}: <span class="dark">{{ category.sub_cat[0].name }}</span></span>\n                                            </li>\n                                        </template>\n                                    </ul>\n                                </div>\n                            </div>\n                        </div>\n                    </b-collapse> \n                </template>\n                <template v-else-if="schedule.success">\n                    {{ schedule.errorData }}\n                </template>\n            </div>\n        </section>\n    ',data:()=>({isOpen:0,loadingResult:2,pageTitleData:{title:"Course Schedule",cta:[{module:"courseSchedule"}]}}),computed:{...Vuex.mapState(["schedule"]),scheduleObj(){return this.schedule.data}},async created(){},destroyed(){},mounted(){},methods:{initEditMode(e,t){e.preventDefault(),this.$emit("initEditMode",t)},duration:e=>"15"===e?"15 Minutes":"30"===e?"30 Minutes":"45"===e?"45 Minutes":"60"===e?"1 Hour":"75"===e?"1 Hour 15 Minutes":"90"===e?"1 Hour 30 Minutes":"105"===e?"1 Hour 45 Minutes":"2 Hours",manageFields(e,t){switch(t){case"title":return"diagonstic_test"!==e;case"sub_cat":return"diagonstic_test"!==e&&"assignment"!==e&&"mock_test"!==e;case"duration":return"live_class"===e;case"description":return!0}}}}),Vue.component("yuno-create-course-economics",{template:'\n        <section class="moduleEdit">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n            </div>\n            <div class="row">\n                <div class="col-6">\n                    <template v-if="isEditMode">\n                        <template v-if="resource.loading">\n                            <yuno-form-skeleton></yuno-form-skeleton>\n                        </template>\n                        <template v-if="resource.success">\n                            <yuno-form\n                                :options="{\'loading\': isFormLoading, \'isEditMode\': isEditMode}"\n                                @initForm="onFormSubmit"\n                                :data="payload"\n                            >\n                            </yuno-form>        \n                        </template>\n                    </template>\n                    <template v-else>\n                        <yuno-form\n                            :options="{\'loading\': isFormLoading, \'isEditMode\': isEditMode}"\n                            @initForm="onFormSubmit"\n                            :data="payload"\n                        >\n                        </yuno-form>    \n                    </template>\n                </div>\n                <div class="col-6">\n                    <div class="mainHeader">\n                        <div class="block">\n                            <h2 class="pageTitle">Summary</h2>\n                        </div>\n                    </div>\n                    <yuno-summary \n                        @studentsInBatch="onStudentsInBatch"\n                        :data="publishedResources" \n                        :options="{\'isEditMode\': isEditMode, \'studentsInBatch\': studentsInBatch}">\n                    </yuno-summary>\n                </div>\n            </div>\n        </section>\n    ',data:()=>({pageTitleData:{title:"Course Economics"},isEditMode:!1,isFormLoading:!1,studentsInBatch:1,payload:{course_id:"",id:0,personalization:""+YUNOCommon.getQueryParameter("classSize"),diagnostic_test:{is_active:"",duration:"",feedback:"",effort:""},group_classes_duration:{"180_min":0,"150_min":0,"120_min":0,"90_min":0,"75_min":0,"60_min":0,"45_min":0,"30_min":0},one_to_one_classes_duration:{"180_min":0,"150_min":0,"120_min":0,"90_min":0,"75_min":0,"60_min":0,"45_min":0,"30_min":0},assignments_require:{total:0,time_investment:"",effort:""},assignments_not_require:{total:0,time_investment:""},post_test:{is_active:"",time_investment:"",feedback:"",effort:""},mock_exam:{total:0,effort:""}}}),computed:{...Vuex.mapState(["userInfo","user","course","resource","publishedResources"]),courseObj(){return this.course.data}},async created(){},destroyed(){},mounted(){this.initEditMode()},methods:{onStudentsInBatch(e){this.studentsInBatch=e,this.fetchSummary(!0)},gotSummary(e){if(200===e.response?.data?.code){e.response.data.data}},fetchSummary(e){e&&(this.publishedResources.data=[],this.publishedResources.success=!1,this.publishedResources.error=null);const t={apiURL:YUNOCommon.config.courseEconomicsSummary(this.payload.course_id,this.payload.id,this.studentsInBatch,YUNOCommon.getQueryParameter("classSize")),module:"gotData",store:"publishedResources",callback:!0,callbackFunc:this.gotSummary};this.$store.dispatch("fetchData",t)},gotForm(e){200===e.response?.data?.code&&(this.payload=e.response.data.data)},fetchForm(){this.resource.data=[],this.resource.success=!1,this.resource.error=null;const e={apiURL:this.getFormURL(),module:"gotData",store:"resource",callback:!0,callbackFunc:this.gotForm};this.$store.dispatch("fetchData",e)},getFormURL(){const e=this.userInfo.data.role;return{"org-admin":YUNOCommon.config.org("courseEconomicsPersonalization",!1,!1,!1,this.payload.course_id,this.payload.id,YUNOCommon.getQueryParameter("classSize")),"yuno-admin":YUNOCommon.config.courseEconomicsForm(this.payload.course_id,this.payload.id,YUNOCommon.getQueryParameter("classSize"))}[e]||null},initEditMode(){this.payload.course_id=Number(this.courseObj.id),this.payload.id=Number(YUNOCommon.findObjectByKey(this.courseObj.course_economics_id,"type",this.payload.personalization).id),0===this.payload.id?this.isEditMode=!1:(this.isEditMode=!0,this.fetchForm(),this.fetchSummary(!0))},formPosted(e){if(this.isFormLoading=!1,e.response&&e.response.data){const{data:t}=e.response,n=t.message,a=201===t.code?"is-success":"is-danger";201===t.code&&(this.payload.id=t.data.id,this.isEditMode?this.fetchSummary(!0):this.initEditMode()),this.$buefy.toast.open({duration:5e3,message:n,position:"is-bottom",type:a})}},onFormSubmit(){this.setPayload(),this.isFormLoading=!0;const e={apiURL:this.getFormSubmitURL(),module:"gotData",store:"createResource",payload:this.payload,callback:!0,callbackFunc:this.formPosted};this.$store.dispatch("postData",e)},getFormSubmitURL(){const e=this.userInfo.data.role,t=0===this.payload.id;return{"org-admin":t?YUNOCommon.config.org("createCourseEconomics",!1,!1,"create"):YUNOCommon.config.org("createCourseEconomics",!1,!1,"edit"),"yuno-admin":t?YUNOCommon.config.courseEconomics("create"):YUNOCommon.config.courseEconomics("edit")}[e]||null},activeOrg(){const e=this.userInfo.data.current_state.org_id;return e||0},setPayload(){const e=(e,t,n)=>{e&&n.forEach(e=>{t[e]=""})};e(!this.payload.diagnostic_test.is_active,this.payload.diagnostic_test,["duration"]),e(!this.payload.diagnostic_test.feedback,this.payload.diagnostic_test,["effort"]),e(""===this.payload.assignments_require.total||"0"===this.payload.assignments_require.total,this.payload.assignments_require,["time_investment","effort"]),e(""===this.payload.assignments_not_require.total||"0"===this.payload.assignments_not_require.total,this.payload.assignments_not_require,["time_investment"]),e(0===this.payload.mock_exam.total,this.payload.mock_exam,["effort"]),e(!this.payload.post_test.is_active,this.payload.post_test,["time_investment"]),e(!this.payload.post_test.feedback,this.payload.post_test,["effort"]),"1-1"===this.payload.personalization&&delete this.payload.group_classes_duration;"org-admin"===this.userInfo.data.role&&(this.payload.org_id=this.activeOrg(),this.payload.user_id=isLoggedIn)}}}),Vue.component("yuno-create-course-schedule",{template:'\n        <section class="moduleEdit">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n            </div>\n            <yuno-activity :data="settings" @addActivity="onAddActivity"></yuno-activity>\n            <div class="row">\n                <template v-if="isEdit">\n                    <template v-if="publishedResources.loading">\n                        <div class="smallLoader"></div>\n                    </template>\n                    <template v-if="publishedResources.success">\n                        <div class="col-12 col-md-6">\n                            <yuno-schedule-form \n                                @onRemoveActivity="onRemoveActivity"\n                                @initForm="onFormSubmit"\n                                :options="{\'loading\': isFormLoading, \'isEdit\': isEdit, \'duration\': durationOpt}"\n                                :data="payload">\n                            </yuno-schedule-form>\n                        </div>\n                        <div class="col-12 col-md-6">\n                            <yuno-schedule-preview :data="payload" :options="{\'isEdit\': isEdit, \'duration\': durationOpt}"></yuno-schedule-preview>\n                        </div>\n                    </template>\n                </template>\n                <template v-else>\n                    <div class="col-12 col-md-6">\n                        <yuno-schedule-form \n                            v-if="isScheduleReady" \n                            @onRemoveActivity="onRemoveActivity"\n                            @initForm="onFormSubmit"\n                            :options="{\'loading\': isFormLoading, \'isEdit\': isEdit, \'duration\': durationOpt}"\n                            :data="payload">\n                        </yuno-schedule-form>\n                    </div>\n                    <div class="col-12 col-md-6">\n                        <yuno-schedule-preview :data="payload" :options="{\'isEdit\': isEdit, \'duration\': durationOpt}" v-if="isScheduleReady"></yuno-schedule-preview>\n                    </div>\n                </template>\n            </div>\n        </section>\n    ',data:()=>({isModuleLoading:!0,pageTitleData:{title:"Course Schedule"},isEdit:!1,isFormLoading:!1,section:{order:"",id:"",title:"",excerpt:"",description:"",duration:"",activity:"",sub_cat:[],is_active:!1,is_remove:!1},durationOpt:[{label:"15 Minutes",slug:"15"},{label:"30 Minutes",slug:"30"},{label:"45 Minutes",slug:"45"},{label:"1 Hour",slug:"60"},{label:"1 Hour 15 Minutes",slug:"75"},{label:"1 Hour 30 Minutes",slug:"90"},{label:"1 Hour 45 Minutes",slug:"105"},{label:"2 Hours",slug:"120"}],payload:{course_id:YUNOCommon.getQueryParameter("courseid"),id:YUNOCommon.getQueryParameter("scheduleid"),course_schedule:[]}}),computed:{...Vuex.mapState(["userInfo","user","course","categoryList","settings","createResource","publishedResources"]),courseObj(){return this.course.data},isScheduleReady:{get(){let e="";return e=this.categoryList.success&&this.settings.success,e}}},async created(){},destroyed(){},mounted(){this.initEditMode()},methods:{initEditMode(){this.isModuleLoading=!1,this.publishedResources.data=[],this.publishedResources.success=!1,this.publishedResources.error=null,this.fetchModules()},formPosted(e){if(this.isFormLoading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom"})}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},onFormSubmit(){this.isFormLoading=!0;const e=this,t={apiURL:this.getFormSubmitURL(),module:"gotData",store:"createResource",payload:this.refinePayload(),callback:!0,callbackFunc:function(t){return e.formPosted(t)}};this.$store.dispatch(this.isEdit?"putData":"postData",t)},getFormSubmitURL(){const e=this.userInfo.data.role,t=this.isEdit?"edit":"create";return{"org-admin":YUNOCommon.config.org("courseSchedule",!1,!1,!1,!1,!1,!1,t),"yuno-admin":YUNOCommon.config.courseSchedule(t)}[e]||null},refinePayload(){const e=JSON.parse(JSON.stringify(this.payload));for(const t of e.course_schedule)for(const e of t.sub_cat)e.selected?e.sub_cat=[e.selected]:e.sub_cat=[],delete e.selected;return e},onRemoveActivity(e){YUNOCommon.removeObjInArr(this.payload.course_schedule,"id",e.id);for(let e=0;e<this.payload.course_schedule.length;e++){const t=this.payload.course_schedule[e];t.order=e+1,t.id=t.activity.slug+"-"+t.order}},generateOrder(){let e="";return e=0===this.payload.course_schedule.length?0:this.payload.course_schedule.length,e},onAddActivity(e){let t=JSON.parse(JSON.stringify(this.section));t.order=this.generateOrder(),t.activity=e,t.id=e.slug+"-"+this.generateOrder(),t.sub_cat=JSON.parse(JSON.stringify(this.categoryList.data)),this.payload.course_schedule.push(t),setTimeout(()=>{YUNOCommon.scrollToElement("#"+t.id,500,200)},100)},gotActivities(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchActivities(){this.settings.data=[],this.settings.success=!1,this.settings.error=null;const e=this,t={apiURL:YUNOCommon.config.activityList(),module:"gotData",store:"settings",callback:!0,callbackFunc:function(t){return e.gotActivities(t)}};this.$store.dispatch("fetchData",t)},gotSubCategories(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;for(let e=0;e<t.length;e++){t[e].selected=""}this.categoryList.data=t,"0"!==YUNOCommon.getQueryParameter("scheduleid")&&this.fetchSchedule()}else"0"!==YUNOCommon.getQueryParameter("scheduleid")&&this.fetchSchedule()},fetchSubCategories(){this.categoryList.data=[],this.categoryList.success=!1,this.categoryList.error=null;const e=this,t={apiURL:YUNOCommon.config.subCategoriyList(YUNOCommon.getQueryParameter("courseid")),module:"gotData",store:"categoryList",addToModule:!1,callback:!0,callbackFunc:function(t){return e.gotSubCategories(t)}};this.$store.dispatch("fetchData",t)},prefillSubCats(e,t){const n=new Map(e.course_schedule.map(e=>[e.id,e]));t.course_schedule.forEach(e=>{const t=n.get(e.id);if(!t||!t.sub_cat)return void e.sub_cat.forEach(e=>{e.selected=""});const a=new Map(t.sub_cat.map(e=>[e.id,e]));e.sub_cat.forEach(e=>{const t=a.get(e.id);t&&t.sub_cat&&t.sub_cat.length>0?e.selected=t.sub_cat[0]:e.selected=""})})},prefillForm(e){let t=JSON.parse(JSON.stringify(e));courseSchedule=t.course_schedule;for(let e=0;e<courseSchedule.length;e++){const t=courseSchedule[e],n=JSON.parse(JSON.stringify(this.categoryList.data));t.sub_cat=[];for(let e=0;e<n.length;e++){const a=n[e];t.sub_cat.push(a)}}this.prefillSubCats(e,t),this.payload=t},gotSchedule(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.prefillForm(t)}},fetchSchedule(){this.publishedResources.data=[],this.publishedResources.success=!1,this.publishedResources.error=null;const e=this,t={apiURL:YUNOCommon.config.courseScheduleForm(YUNOCommon.getQueryParameter("courseid"),YUNOCommon.getQueryParameter("scheduleid")),module:"gotData",store:"publishedResources",callback:!0,callbackFunc:function(t){return e.gotSchedule(t)}};this.$store.dispatch("fetchData",t)},manageLoader(e){this.loader.isActive=e,this.loader.overlay=e},fetchModules(){this.fetchActivities(),this.fetchSubCategories(),"0"!==YUNOCommon.getQueryParameter("scheduleid")?this.isEdit=!0:this.isEdit=!1}}}),Vue.component("yuno-create-new-batch",{template:'\n        <section class="moduleEdit">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n            </div>\n            <validation-observer \n                tag="div" \n                ref="createBatchObserver" \n                v-slot="{ handleSubmit, invalid }">\n                <form id="createBatchForm" @submit.prevent="handleSubmit(initForm)">\n                    <div class="row isRelative">\n                        <div class="col-12 col-md-5 col-lg-5 noRelative">\n                            <div class="formWrapper">\n                                <b-field label="Batch Label">\n                                    <validation-provider \n                                        :rules="{required:true}" \n                                        v-slot="{ errors, classes }">\n                                        <b-input :class="classes" \n                                            placeholder="Add a batch title" \n                                            v-model="payload.batch_label">\n                                        </b-input>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Course" id="courseList" :class="[allCourses.loading ? \'loading\' : \'\']">\n                                    <template v-if="allCourses.loading">\n                                        <div class="smallLoader withField"></div>\n                                    </template>\n                                    <template v-if="allCourses.success">\n                                        <p class="caption2 fontColorDarkVariant">{{ courseObj.product_code }}</p>\n                                    </template>\n                                </b-field>\n                                <b-field label="Instructor" v-if="instructor.isActive" :class="[instructor.isLoading ? \'loading\' : \'\']">\n                                    <template v-if="instructor.isLoading">\n                                        <div class="smallLoader withField"></div>\n                                    </template>\n                                    <template v-if="mappedInstructor.success">\n                                        <validation-provider \n                                            tag="div"\n                                            v-if="instructor.isField"\n                                            :customMessages="{ isNotBlank: errorMsg.instructor }"\n                                            :rules="{required:true, isNotBlank:instructor.selected}" \n                                            v-slot="{ errors, classes }">\n                                            <b-autocomplete\n                                                :class="classes"\n                                                v-model="instructor.current"\n                                                :data="filteredInstructor"\n                                                placeholder="Add instructor"\n                                                :open-on-focus="true"\n                                                field="name"\n                                                @input="onInstructorChange"\n                                                @select="onInstructorSelect($event)"\n                                                :clearable="true">\n                                                <template slot="empty">No results for {{instructor.current}}</template>\n                                            </b-autocomplete>\n                                            <p class="error">{{errors[0]}}</p>\n                                        </validation-provider>\n                                    </template>\n                                </b-field>\n                                <b-field label="Start Date" v-if="courseDate">\n                                    <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-datepicker\n                                            @input="onDatepick"\n                                            :class="classes"\n                                            :date-formatter="formatDate"\n                                            v-model="payload.start_date"\n                                            placeholder="Pick date"\n                                            :mobile-native="false"\n                                            :min-date="startDate.minDate"\n                                            trap-focus>\n                                        </b-datepicker>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="End Date" v-if="courseDate">\n                                    <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-datepicker\n                                            :disabled="payload.enrollment_type === \'rolling\' ? false : true"\n                                            :class="classes"\n                                            :date-formatter="formatDate"\n                                            v-model="payload.end_date"\n                                            :mobile-native="false"\n                                            :min-date="endDate.minDate"\n                                            :max-date="endDate.maxDate"\n                                            trap-focus>\n                                        </b-datepicker>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Days Of The Week">\n                                    <validation-provider tag="div" class="makeItGrid" :rules="{required:true, minLength:1}" v-slot="{ errors, classes }">\n                                        <template v-for="(day, dayIndex) in daysOfWeek">\n                                            <div class="field" :key="dayIndex">\n                                                <b-checkbox\n                                                    :class="classes"\n                                                    :native-value="day.slug"\n                                                    v-model="payload.days_of_week">\n                                                    {{day.label}}\n                                                </b-checkbox>\n                                            </div>\n                                        </template>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Class Duration" v-if="userRole.data !== \'Instructor\'">\n                                    <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-select \n                                            :class="classes"\n                                            v-model="payload.duration"\n                                            placeholder="Select">\n                                            <option value="">Select</option>\n                                            <option value="15">15 Minutes</option>\n                                            <option value="30">30 Minutes</option>\n                                            <option value="45">45 Minutes</option>\n                                            <option value="60">1 Hour</option>\n                                            <option value="75"> 1 Hour 15 Minutes</option>\n                                            <option value="90">1 Hour 30 Minutes</option>\n                                            <option value="105">1 Hour 45 Minutes</option>\n                                            <option value="120">2 Hours</option>\n                                        </b-select>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Class Start Time">\n                                    <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                        <b-timepicker\n                                            :class="classes"\n                                            v-model="payload.class_time"\n                                            placeholder="Pick time"\n                                            hour-format="12"\n                                            :mobile-native="false"\n                                            icon="clock">\n                                        </b-timepicker>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field label="Max Seats">\n                                    <validation-provider tag="div" :rules="{required:false, numeric: true}" v-slot="{ errors, classes }">\n                                        <b-input \n                                            :disabled="true"\n                                            v-model="payload.vacancy"\n                                            :class="classes">\n                                        </b-input>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                                <b-field>\n                                    <validation-provider tag="div" :rules="{required:false}" v-slot="{ errors, classes }">\n                                        <b-checkbox\n                                            :class="classes"\n                                            :native-value="payload.is_locked"\n                                            v-model="payload.is_locked">\n                                            <b-tooltip \n                                                type="is-dark"\n                                                label="New enrollments cannot be made in a locked batch"\n                                                :multilined="true"\n                                                position="is-top">\n                                                Lock this batch    \n                                            </b-tooltip>\n                                        </b-checkbox>\n                                        <p class="error">{{errors[0]}}</p>\n                                    </validation-provider>\n                                </b-field>\n                            </div>\n                            <div class="ctaWrapper">\n                                <b-button\n                                    native-type="submit"\n                                    :loading="form.isLoading ? true : false"\n                                    :disabled="form.isLoading ? true : false"\n                                    class="yunoSecondaryCTA">\n                                    <template v-if="isEditBatch">\n                                        Update Batch\n                                    </template>\n                                    <template v-else>\n                                        Create Batch\n                                    </template>\n                                </b-button>    \n                            </div>\n                        </div>\n                        <div class="col-12 col-md-7 col-lg-7 availabilityWrapper">\n                            <h3 class="availabilityTitle">\n                                <template v-if="userRole.data === \'Instructor\'">\n                                    My availability\n                                </template>\n                                <template v-else>\n                                    Instructor availability <template v-if="payload.instructor_id !== \'\'">of <span class="highlight">{{instructor.current}}</span></template>    \n                                </template>\n                            </h3>\n                            <template v-if="payload.instructor_id === \'\'">\n                                <div class="noSelection">\n                                    <p><i class="fa fa-info-circle" aria-hidden="true"></i> To see the instructor availability, please add the course then pick the instructor.</p>\n                                </div>\n                            </template>\n                            <template v-else>\n                                <yuno-table :data="instructorAvailabilityGrid" :options="tableOptions"></yuno-table>\n                            </template>\n                        </div>\n                    </div>\n                </form>\n            </validation-observer>\n        </section>\n    ',data:()=>({pageTitleData:{title:"Create New Batch"},isEditBatch:!1,classSize:{isActive:!0,disabled:[],items:[{slug:"one_to_one",label:"1-to-1",isDisabled:!1},{slug:"one_to_many",label:"Group (10 max.)",isDisabled:!1}]},tableOptions:{isFluid:!1,pageLoading:!1,apiPaginated:!1,totalResult:"",perPage:50,limit:20,offset:0,hasStriped:!1,isStickyHeader:!0,height:"600"},startDate:{minDate:new Date},endDate:{minDate:new Date,maxDate:new Date},disabledEnrollment:"",courseDate:!1,courseSelection:{isActive:!1,filtered:[],selected:[]},daysOfWeek:[{label:"Monday",slug:"Mon"},{label:"Tuesday",slug:"Tue"},{label:"Wednesday",slug:"Wed"},{label:"Thursday",slug:"Thu"},{label:"Friday",slug:"Fri"},{label:"Saturday",slug:"Sat"},{label:"Sunday",slug:"Sun"}],instructor:{selected:null,current:"",isLoading:!1,isActive:!1,isField:!1},errorMsg:{instructor:"Please select the instructor from list"},form:{isLoading:!1},payload:{batch_label:"",user_id:isLoggedIn,enrollment_type:"",course_id:[],start_date:new Date,end_date:new Date(""),class_time:new Date,instructor_id:"",duration:"",days_of_week:[],group_type:"",vacancy:"",is_locked:!1}}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","footer","loader","allCourses","mappedInstructor","batchCreateUpdate","batchDetail","instructorAvailabilityGrid","capabilities","course"]),filteredInstructor:{get(){return this.mappedInstructor.data.filter(e=>e.name.toString().toLowerCase().indexOf(this.instructor.current.toLowerCase())>=0)}},courseObj(){return this.course.data}},async created(){},destroyed(){},mounted(){this.fetchCourses()},methods:{gotCapabilities(e,t){if(this.capabilities.loading=!t,t){this.capabilities.success=!0;const e={isEntranceWisdom:!0,isClassesWisdom:!0,isGangsWisdom:!0,isGovernClasses:!0,isGovernLearners:!0,isSysopWisdom:!0,isPagoWisdom:!0};"yuno-admin"===this.userRole.data?this.capabilities.data={...e,isGovernGangs:!0}:"Counselor"===this.userRole.data&&(this.capabilities.data=e),this.authorizedUser()}else 200===e.response?.data?.code&&(this.capabilities.data=e.response.data.data,this.authorizedUser())},fetchCapabilities(e){if(e)this.gotCapabilities(!1,e);else{const e={apiURL:YUNOCommon.config.capabilitiesAPI(isLoggedIn,!1),module:"gotData",store:"capabilities",addToModule:"false",callback:!0,callbackFunc:e=>this.gotCapabilities(e)};this.$store.dispatch("fetchData",e)}},gotInstructors(e,t){let n=this.mappedInstructor;this.instructor.isLoading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code?this.instructor.isField=!0:(n.data=[],n.error=null,n.errorData=[],this.instructor.isActive=!0,this.instructor.isField=!0,this.$buefy.snackbar.open({duration:5e3,message:YUNOCommon.config.errorMsg.notMapped,type:"is-warning",position:"is-top",actionText:"Ok"}))},fetchMappedInstructor(e,t){let n={course_id:e.map(e=>e)};const a={apiURL:YUNOCommon.config.listOfMappedInstructorAPI(),module:"gotData",store:"mappedInstructor",payload:JSON.stringify(n),headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:e=>this.gotInstructors(e,t)};this.$store.dispatch("postData",a)},generateClassEndDate(){let e=this.courseSelection.selected,t=[],n=this.payload.start_date;for(let n=0;n<e.length;n++){const a=e[n];t.push(a.duration_weeks)}let a=Math.max(...t),o=new Date(n);return o.setDate(o.getDate()+7*a),o},manageMaxSeats(e,t){0!==this.courseSelection.selected.length?this.payload.vacancy=Math.max(...this.courseSelection.selected.map(e=>e.max_seats)):this.payload.vacancy=""},onCourseSelect(e,t){const n=(e,t,n)=>{this.instructor.isActive=e,this.instructor.isField=t,this.instructor.isLoading=n},a=()=>{const e=new Date(this.generateClassEndDate());this.payload.end_date=e,this.endDate.maxDate=e},o=()=>{YUNOCommon.removeValInArr(this.payload.course_id,e.post_id),this.allCourses.data.push(e),0!==this.courseSelection.selected.length?a():(this.courseDate=!1,this.disabledEnrollment="",this.payload.enrollment_type="")};"add"===t?(()=>{const t=this.courseSelection.selected[0].enroll_type;this.disabledEnrollment="rolling"===t?"fixed":"rolling",this.payload.enrollment_type=t,e.enroll_type===this.disabledEnrollment?YUNOCommon.removeObjInArr(this.courseSelection.selected,"post_id",e.post_id):(this.payload.course_id.push(e.post_id),YUNOCommon.removeObjInArr(this.allCourses.data,"post_id",e.post_id),n(!0,!1,!0),this.fetchMappedInstructor(this.payload.course_id),this.courseDate=!0)})():o(),"Instructor"!==this.userRole.data&&(0===this.courseSelection.selected.length?(this.instructor.current="",this.instructor.selected=null,this.payload.instructor_id="",n(!1,!0,!1)):a()),this.manageMaxSeats(e,t)},onCourseKeypress(e){"ArrowDown"!==e.key&&"ArrowUp"!==e.key||this.manageItems(this.courseSelection.filtered)},manageItems(e){let t=document.querySelectorAll("#courseList .dropdown-menu .dropdown-item");for(let n=0;n<t.length;n++){const a=t[n],o=e[n];void 0!==o&&o.enroll_type===this.disabledEnrollment?a.classList.add("disabled"):a.classList.remove("disabled")}},getFilteredCourse(e){this.courseSelection.filtered=this.allCourses.data.filter(t=>t.product_code.toString().toLowerCase().indexOf(e.toLowerCase())>=0);const t=this;setTimeout(()=>{t.manageItems(t.course.filtered)},100)},differenceIndays:(e,t)=>(t.getTime()-e.getTime())/864e5,enrollmentType(e){this.disabledEnrollment="rolling"===e?"fixed":"rolling",this.courseSelection.selected=[],this.courseSelection.filtered=[],this.courseSelection.isActive=!0,this.payload.end_date=new Date(""),this.payload.start_date=new Date,this.payload.course_id=[]},formatDate:e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"2-digit"}),onDatepick(e){this.payload.end_date=new Date(this.generateClassEndDate()),this.endDate.maxDate=new Date(this.generateClassEndDate()),this.endDate.minDate=new Date(e);const t=new Date;this.startDate.minDate=new Date(t.setDate(t.getDate()-1))},onInstructorChange(e){""===e&&(this.payload.instructor_id="")},onInstructorSelect(e){null!==e?(this.instructor.selected=e,this.payload.instructor_id=e.instructor_id,this.fetchResources(!0,this.payload.instructor_id)):(this.instructor.selected=null,this.payload.instructor_id="")},resetForm(){let e=this.payload;this.$refs.createBatchObserver.reset(),this.courseSelection.filtered=[],this.courseSelection.selected=[],this.instructor.selected=null,this.instructor.current="",this.instructor.isLoading=!1,this.instructor.isActive=!1,this.instructor.isField=!1,this.courseSelection.isActive=!1,this.courseDate=!1,e.batch_label="",e.user_id=isLoggedIn,e.course_id=[],e.start_date=new Date,e.end_date=new Date(""),e.class_time=new Date,e.vacancy="",e.is_locked=!1,e.instructor_id="",e.duration="",e.days_of_week=[],e.enrollment_type="",e.batch_db_id="";"org-admin"===this.userInfo.data.role&&(e.org_id=this.activeOrg())},manageBatch(e){this.form.isLoading=!1;const t=YUNOCommon.getQueryParameter("isEdit"),n=!1!==t?"Update Batch":"Create Batch";if(201===e.response?.data?.code){const{message:n}=e.response.data;this.resetForm(),!1!==t&&this.fetchBatch(t),this.$buefy.toast.open({duration:5e3,message:n,position:"is-bottom"}),this.fetchCourses()}else this.$buefy.dialog.alert({title:n,message:this.batchCreateUpdate.errorData,confirmText:"Ok"})},initForm(){this.form.isLoading=!0;const e=YUNOCommon.getQueryParameter("isEdit"),t=YUNOCommon.getQueryParameter("classSize");let n={...this.payload,batch_db_id:!1!==e?e:void 0,group_type:t};"org-admin"===this.userInfo.data.role&&(n.org_id=this.activeOrg());const a={apiURL:this.getFormURL(),module:"gotData",store:"batchCreateUpdate",payload:JSON.stringify(n),headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:e=>this.manageBatch(e)};this.$store.dispatch(!1!==e?"putData":"postData",a)},getFormURL(){const e=this.userInfo.data.role,t=YUNOCommon.getQueryParameter("isEdit");return{"org-admin":YUNOCommon.config.course("createBatchOrg"),"yuno-admin":YUNOCommon.config.batchCreateUpdateAPI(!1!==t?"update":"create"),Instructor:YUNOCommon.config.batchCreateUpdateAPI(!1!==t?"update":"create")}[e]||null},activeOrg(){const e=this.userInfo.data.current_state.org_id;return e||0},getGroupType:e=>2===e.length?"both":1===e.length?YUNOCommon.findInArray(e,"1-1")?"one_to_one":"one_to_many":null,getGroupTypeFromElement:e=>e.one_to_one&&e.one_to_many?"both":e.one_to_many?"one_to_many":e.one_to_one?"one_to_one":null,gotCourses(e){if(200!==e.response?.data?.code)return;const t=e.response.data.data,n=Number(YUNOCommon.getQueryParameter("courseid")),a=this.userInfo.data.role,o=this.courseObj.max_students;t.forEach(e=>{"org-admin"===a?(e.enroll_type=e.enrollment_type,e.post_id=e.course_id,e.max_seats=Number(o),e.group_type=this.getGroupType(e.personalization)):e.group_type=this.getGroupTypeFromElement(e.group_type),n&&e.post_id===n&&this.courseSelection.selected.push(e)}),this.onCourseSelect(this.courseSelection.selected[0],"add"),"Instructor"===this.userRole.data&&this.fetchResources(!0,this.payload.instructor_id),this.allCourses.data=t},fetchCourses(){const e=this,t={apiURL:this.getCoursesURL(),module:"gotData",store:"allCourses",addToModule:!1,callback:!0,callbackFunc:function(t){return e.gotCourses(t)}};this.$store.dispatch("fetchData",t)},getCoursesURL(){const e=this.userInfo.data.role,t=YUNOCommon.getQueryParameter("classSize");return{"org-admin":YUNOCommon.config.org("courses",this.activeOrg(),isLoggedIn,0,"all",!1,!1,!1,!1,"all","all","list-view",100,0),"yuno-admin":YUNOCommon.config.allCoursesAPI(t),Instructor:YUNOCommon.config.mappedCoursesAPI(isLoggedIn,t)}[e]||null},authorizedUser(e){let t=this.capabilities.data;void 0!==t.isGovernGangs&&t.isGovernGangs&&this.fetchCourses()},additionalRow(e){for(let t=0;t<e.length;t++){const n=e[t];if(n.sun||n.mon||n.tue||n.wed||n.thu||n.fri||n.sat){n.scrollID="moveScrollTopHere";break}}},additionalCols(e){e.push({field:"slot",label:"",sortable:!0,hasSlot:!0,hasTag:!0});const t=["sun","mon","tue","wed","thu","fri","sat"];for(let n=0;n<e.length;n++){const a=e[n];t.includes(a.field)&&(a.hasTag=!0)}},scrollToActiveRow(){let e=document.querySelectorAll(".table-wrapper")[0],t=document.getElementById("moveScrollTopHere").parentElement.parentElement.offsetTop;e.scrollTop=t},gotResources(e){const t=this.instructorAvailabilityGrid;if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){let n=e.response.data.data,a=n.rows,o=n.columns,s=e.response.data.count;this.additionalCols(o),this.additionalRow(a),this.tableOptions.pageLoading=!1,this.tableOptions.totalResult=s,t.data=n,setTimeout(()=>{this.scrollToActiveRow()},500)}else t.data=[],this.tableOptions.totalResult=0},fetchResources(e,t){this.instructorAvailabilityGrid.data=[],this.instructorAvailabilityGrid.success=!1;const n=this,a={apiURL:YUNOCommon.config.availabilityGridAPI(t),module:"gotData",store:"instructorAvailabilityGrid",moduleLoading:e,addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotResources(e)}};this.$store.dispatch("fetchData",a)}}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required ",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-edit-course",{template:'\n        <yuno-page-grid\n            :authorizedRoles="authorizedRoles"\n            @onUserInfo="onUserInfo"\n            :hasSearchBar="false"\n        >\n            <template v-slot:main>\n                <div class="container-fluid">\n                    <transition :name="transitionName" mode="out-in">\n                        <yuno-view-mode \n                            v-if="activeMode === \'view\'" \n                            :courseID="courseID"\n                            @initEditMode="initEditMode"\n                        >\n                        </yuno-view-mode>\n                        <yuno-edit-mode \n                            v-if="activeMode === \'edit\'"\n                            :courseID="courseID"\n                            :whichModule="whichModule"\n                            @initViewMode="initViewMode"\n                        >\n                        </yuno-edit-mode>\n                    </transition>\n                </div>\n            </template>\n        </yuno-page-grid>\n    ',data:()=>({transitionName:"slide-left",isMiniSidebar:!1,authorizedRoles:["yuno-admin","org-admin"],activeMode:"view",whichModule:"",courseID:""}),computed:{...Vuex.mapState(["user","userInfo","header","userRole","loader","tabs","course","schedule"])},async created(){},destroyed(){},mounted(){this.removeQueryParams()},methods:{removeQueryParams(){if("view"===this.activeMode){const e=new URL(window.location.href);["classSize","scheduleid"].forEach(t=>{e.searchParams.has(t)&&e.searchParams.delete(t)}),window.history.pushState({path:e.href},"",e.href)}},initViewMode(){this.activeMode="view",this.whichModule="",this.viewMode(),this.fetchModules()},initEditMode(e){this.activeMode="edit",this.whichModule=e,this.editMode(),YUNOCommon.scrollToElement("#yunoMain",500)},editMode(){this.transitionName="slide-left"},viewMode(){this.transitionName="slide-right"},tabChange(e){this.tabs.data.items=this.tabs.data.items.map((t,n)=>({...t,isActive:n===e}))},manageTabs(){function e(e,t,n){return{label:e,slug:t,isActive:!1,isVisible:!0,class:n}}this.tabs.data={activeTab:0,items:[e("Share","share","yunoShare"),e("Enrollments","enrollments","yunoEnrollments"),e("Batches","batches","yunoBatches"),e("Instructors","instructors","yunoInstructors"),e("About","about","yunoAboutCourse"),e("Schedule","schedule","yunoCourseSchedule"),e("Price & Economics","price-economics","courseEconomics")]}},onUserInfo(e){YUNOCommon.findInArray(this.authorizedRoles,e.role)&&(this.courseID=Number(YUNOCommon.getQueryParameter("courseid")),this.manageTabs(),this.tabChange(this.tabs.data.activeTab),this.fetchModules())},onMini(e){this.isMiniSidebar=e},manageLoader(e){this.loader.isActive=e,this.loader.overlay=e},gotSchedule(e){const t=e?.response?.data;if(200===t?.code){t.data}},fetchSchedule(){const e={apiURL:YUNOCommon.config.courseScheduleForm(this.courseID,this.course.data.course_schedule_id),module:"gotData",store:"schedule",callback:!0,callbackFunc:this.gotSchedule};this.$store.dispatch("fetchData",e)},gotCoursesDetail(e){const t=e?.response?.data;if(200===t?.code){t.data;this.fetchSchedule()}},fetchCoursesDetail(){const e={apiURL:YUNOCommon.config.courseDetail(this.courseID,isLoggedIn),module:"gotData",store:"course",callback:!0,callbackFunc:this.gotCoursesDetail};this.$store.dispatch("fetchData",e)},fetchModules(){this.schedule.loading=!0,this.fetchCoursesDetail()}}}),Vue.component("yuno-edit-mode",{props:{whichModule:{type:String,required:!0},courseID:{type:Number,required:!0}},template:'\n        <section class="editMode">\n            <b-button @click="initView" class="withIcon backTrigger">\n                <span class="material-icons">arrow_back</span>\n                Back\n            </b-button>\n            <template v-if="whichModule === \'createBatch\'">\n                <yuno-choose-class-size\n                    v-if="!hasClassSize"\n                    :options="createBatchOptions"\n                    title="Create New Batch"\n                    @initCreateNewBatch="initCreateNewBatch"\n                >\n                </yuno-choose-class-size>\n                <yuno-create-new-batch\n                    v-else\n                >\n                </yuno-create-new-batch>\n            </template>\n            <template v-if="whichModule === \'courseSchedule\'">\n                <yuno-create-course-schedule>\n                </yuno-create-course-schedule>\n            </template>\n            <template v-if="whichModule === \'courseEconomics\'">\n                <yuno-choose-class-size\n                    v-if="!isFirstStep"\n                    :options="courseEconomicsOptions"\n                    title="Course Economics"\n                    @initNextStep="initNextStep"\n                >\n                </yuno-choose-class-size>\n                <yuno-create-course-economics\n                    v-else\n                >\n                </yuno-create-course-economics>\n            </template>\n            <template v-if="whichModule === \'aboutCourse\' || whichModule === \'share\'">\n                <yuno-update-about-course>\n                </yuno-update-about-course>\n            </template>\n            \n        </section>\n    ',data:()=>({hasClassSize:!1,isFirstStep:!1,courseEconomicsOptions:[{slug:"1-1",label:"1-to-1"},{slug:"1-Many",label:"Group"}],createBatchOptions:[{slug:"one_to_one",label:"1-to-1"},{slug:"one_to_many",label:"Group (10 max.)"}]}),computed:{},async created(){},destroyed(){},mounted(){},methods:{initNextStep(){this.isFirstStep=!0},initCreateNewBatch(){this.hasClassSize=!0},initView(){const e=new URL(window.location.href);["classSize","scheduleid"].forEach(t=>{e.searchParams.has(t)&&e.searchParams.delete(t)}),window.history.pushState({path:e.href},"",e.href),this.$emit("initViewMode")}}}),Vue.component("yuno-empty-state-v1",{props:["data","options"],template:'\n        <section class="emptyStateV2 " :class="[options.type]">\n            <figure>\n                <img width="158" height="136" :src="wpThemeURL + \'/assets/images/noDataFound.svg\'" alt="Yuno Learning">\n                <figcaption>\n                    {{ options.message }}\n                </figcaption>\n                <b-button v-if="options.type === \'course\'" @click="noResult()" class="yunoSecondaryCTA">Clear Filters</b-button>\n            </figure>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["user"]),wpThemeURL(){return this.$store.state.themeURL}},async created(){},mounted(){},methods:{noResult(){Event.$emit("defaultFilters")}}}),Vue.component("yuno-empty-state-v3",{props:{title:{type:String,required:!0},action:{type:Object,required:!1}},template:'\n        <div class="emptyStateV3">\n            <h3 class="h4 noBold fontColorDarkVariant">{{ title }}</h3>\n            <p class="caption2" v-if="action"><a :href="action.url" :target="action.target">{{ action.label }}</a></p>\n        </div>\n    ',data:()=>({}),computed:{},async created(){},destroyed(){},mounted(){},methods:{initEditMode(e){this.$emit("initEditMode",e)}}}),Vue.component("yuno-enrollments",{props:{courseID:{type:Number,required:!0}},template:'\n        <section class="yunoEnrollments">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n                <div class="inlineBlock">\n                    <template v-for="(cta, index) in pageTitleData.cta">\n                        <b-button :tag="cta.tag" :href="cta.url" :target="cta.target" :class="[cta.class]" :key="index">\n                            <span class="underline">{{ cta.label }}</span> <span class="material-icons-outlined" v-if="cta.icon">{{ cta.icon }}</span>\n                        </b-button>\n                    </template>\n                </div>\n            </div>\n            <div class="gridInfo">\n                <p class="note" \n                    v-if="filterResult.success">\n                    <span>{{ filterResult.count }}</span> Enrollments found\n                </p>\n            </div>\n            <yuno-enrollments-table></yuno-enrollments-table>\n        </section>\n    ',data(){return{pageTitleData:{title:"Enrollments",cta:[{label:"See all enrollment",class:"yunoSecondaryCTA withIcon",tag:"a",url:`/enrollments/all/?courseid=${this.$props.courseID}`,target:"_blank"}]},payload:{userID:isLoggedIn,search_instructor:0,orgAdmin:0,referral:"all",counsellors:0,status:"all",paymentStatus:"all",searchLearner:0,course_id:this.$props.courseID},orgPayload:{org_admin_id:"",org_id:"",course_id:"",academy_id:"",learner_id:"",enrollment_status:"",enrollment_period:"",payment_type:"",payment_status:"",view:"",limit:"",offset:"",count:""}}},computed:{...Vuex.mapState(["userInfo","userRole","filterResult","course"]),courseObj(){return this.course.data}},async created(){this.emitEvents(),this.fetchEnrollmentGrid(!1,!1)},destroyed(){},mounted(){},methods:{emitEvents(){Event.$on("onTablePageChange",(e,t)=>{this.filterResult.offset=Math.floor(this.filterResult.limit*e-this.filterResult.limit),this.fetchEnrollmentGrid(t,!1)})},isFilterAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},filterResultReset(e){this.filterResult.success=!1,this.filterResult.error=null,this.filterResult.data=[],this.filterResult.count=0,this.filterResult.additional=[]},manageColumns(e,t){YUNOCommon.removeObjInArr(e,"field","actions");const n={name:"learner",counselor:"counselor",instructor:"instructor",attendance:"percentage",enrollment_status:"activeInactive",payment_status:"highlight",org_admin:"orgAdmin",referrer:"referrer",course_title:"truncate"};e.forEach(e=>{e.isActive=!0,e.type=n[e.field]||""})},manageRows:(e,t)=>e.map(e=>({...e,attendance:Number(e.attendance),name:{name:e.name,image:e.image},hightlight:"Paid in full"===e.payment_status?"isGreen":"isRed"})),gotEnrollmentGrid(e){this.filterResult.loading=!1,Event.$emit("dataLoaded");const t=e?.response?.data;200!==t?.code&&201!==t?.code||(this.filterResult.count=t.count,this.manageColumns(t.data.columns,!1),t.data.rows=this.manageRows(t.data.rows,!1),this.filterResult.data=t.data)},fetchEnrollmentGrid(e,t){const n=this.userInfo.data.role;"org-admin"===n&&this.manageOrgPayload(),e||(this.filterResult.loading=!0,this.filterResultReset(t));let a={apiURL:this.getEnrollmentGridURL(),module:"gotData",store:"filterResult",moduleLoading:!e,addToModule:!1,callback:!0,callbackFunc:this.gotEnrollmentGrid};"org-admin"===n?(a.payload=this.orgPayload,a.headers={accept:"application/json","content-type":"application/json"},this.$store.dispatch("postData",a)):this.$store.dispatch("fetchData",a)},activeOrg(){const e=this.userInfo.data.current_state.org_id;return e||0},manageOrgPayload(){this.orgPayload={org_admin_id:isLoggedIn,org_id:this.activeOrg(),course_id:this.$props.courseID,academy_id:this.courseObj.academy_id,learner_id:0,enrollment_status:"all",enrollment_period:"all",payment_type:"all",payment_status:"all",view:"grid-view",limit:10,offset:0,count:0}},getEnrollmentGridURL(){const e=this.userInfo.data.role;this.payload.id;return{"org-admin":YUNOCommon.config.org("enrollments"),"yuno-admin":YUNOCommon.config.enrollmentsList(this.userRole.data,isLoggedIn,"grid-view",this.payload.search_instructor,this.payload.orgAdmin,this.payload.referral,this.payload.searchLearner,this.payload.status,this.payload.counsellors,this.payload.paymentStatus,10,this.filterResult.offset,"v2",this.payload.course_id)}[e]||null}}}),Vue.component("yuno-enrollments-table",{props:["data","options"],template:'\n        <section class="yunoTable ">\n            <template v-if="filterResult.loading">\n                <b-skeleton height="400px"></b-skeleton>\n            </template>\n            <template v-if="filterResult.success">\n                <template v-if="filterResult.error">\n                    <yuno-empty-state-v3\n                        :title="filterResult.errorData"\n                    >\n                    </yuno-empty-state-v3>\n                </template>    \n                <template v-else>\n                    <b-table\n                        :data="filterResult.data.rows"\n                        :loading="tableLoading"\n                        :paginated="true"\n                        :backend-pagination="true"\n                        :total="filterResult.count"\n                        :per-page="filterResult.limit"\n                        :current-page="filterResult.currentPage"\n                        :striped="true"\n                        @page-change="onPageChange($event)"\n                        ref="table"\n                    >\n                        <b-table-column\n                            v-for="(column, i) in filterResult.data.columns" \n                            :key="i" \n                            :field="column.field" \n                            :visible="column.isActive"\n                            :label="column.label" \n                            v-slot="props"\n                            :sortable="column.sortable">\n                            <template v-if="column.type === \'highlight\'">\n                                <div \n                                    class="grid"\n                                    :class="[column.field]">\n                                    <span :class="[props.row.hightlight]" class="hightlight item">\n                                        <template v-if="userRole.data !== \'Instructor\'">\n                                            <a href="#" @click.prevent="openDrawer(props.row, column)">{{ props.row[column.field] }}</a>\n                                        </template>\n                                        <template v-else>\n                                            <span>{{ props.row[column.field] }}</span>\n                                        </template>\n                                    </span>\n                                </div>    \n                            </template>\n                            <template v-else-if="column.type === \'days\'">\n                                <ul :class="[column.field]" class="classDays">\n                                    <li \n                                        v-for="(day, d) in props.row.class_days" \n                                        :key="d"\n                                        :class="[day.is_available ? \'\' : \'disabled\']">\n                                        {{day.label}}\n                                    </li>\n                                </ul>\n                            </template>\n                            <template v-else-if="column.type === \'instructor\'">\n                                <figure \n                                    :class="[column.field]" class="userWithPhoto">\n                                    <img :src="props.row.instructor.image" :alt="props.row.instructor.name" width="20" height="20">\n                                    <figcaption>\n                                        {{ props.row.instructor.name }}\n                                    </figcaption>\n                                </figure>    \n                            </template>\n                            <template v-else-if="column.type === \'orgAdmin\'">\n                                <template v-if="props.row.org_admin.id !== 0 && props.row.org_admin.id !== \'\'">\n                                    <figure \n                                        :class="[column.field]" class="userWithPhoto">\n                                        <img :src="props.row[column.field].image" :alt="props.row[column.field].name" width="20" height="20">\n                                        <figcaption>\n                                            {{ props.row[column.field].name }}\n                                        </figcaption>\n                                    </figure> \n                                </template>\n                                <template v-else>\n                                    <div :class="[column.field]">NA</div>\n                                </template>\n                            </template>\n                            <template v-else-if="column.type === \'learner\'">\n                                <figure \n                                    :class="[column.field]" class="userWithPhoto">\n                                    <img :src="props.row[column.field].image" :alt="props.row[column.field].name" width="20" height="20">\n                                    <figcaption>\n                                        {{ props.row[column.field].name }}\n                                    </figcaption>\n                                </figure>    \n                            </template>\n                            <template v-else-if="column.type === \'counselor\'">\n                                <template v-if="props.row[column.field].name !== \'NA\'">\n                                    <figure \n                                        :class="[column.field]" class="userWithPhoto">\n                                        <img :src="props.row[column.field].image" :alt="props.row[column.field].name" width="20" height="20">\n                                        <figcaption>\n                                            {{ props.row[column.field].name }}\n                                        </figcaption>\n                                    </figure>    \n                                </template>\n                                <template v-else>\n                                    <div :class="[column.field]">{{ props.row[column.field].name }}</div>    \n                                </template>\n                            </template>\n                            <template v-else-if="column.type === \'hasDrawer\'">\n                                <div :class="[column.field]"><a href="#" @click.prevent="openDrawer(props.row, column)">{{ props.row[column.field] }}</a></div>    \n                            </template>\n                            <template v-else-if="column.type === \'copyToClipboard\'">\n                                <template v-if="props.row[column.field].hasData !== undefined">\n                                    <ul class="actions copyToClipboard" :class="[column.field]">\n                                        <li>\n                                            <b-tooltip :label="props.row[column.field].label"\n                                                type="is-dark"\n                                                position="is-left">\n                                                <div class="wrapper" @click="copyToClipboard(props.row, \'tab-\' + options.tabIndex + \'-row-\' + props.row[column.field].slug + \'-\' + props.row.id)">\n                                                    <span class="itemLabel">\n                                                        <b-input :id="\'tab-\' + options.tabIndex + \'-row-\' + props.row[column.field].slug + \'-\' + props.row.id" :value="props.row[column.field].url" readonly></b-input>\n                                                    </span>\n                                                    <span class="itemIcon" :class="[props.row[column.field].activeClass]" @mouseover="manageMouse(props.row[column.field], \'over\')" @mouseout="manageMouse(props.row[column.field], \'out\')">\n                                                        {{ props.row[column.field].icon.font }}\n                                                    </span>\n                                                </div>\n                                            </b-tooltip>\n                                        </li>\n                                    </ul> \n                                </template>\n                                <template v-else>\n                                    <div :class="[column.field]">{{ props.row[column.field] }}</div>    \n                                </template>\n                            </template>\n                            <template v-else-if="column.type === \'visibility\'">\n                                <template v-if="props.row[column.field].hasData !== undefined">\n                                    <ul class="actions" :class="[column.field]">\n                                        <li>\n                                            <b-tooltip :label="props.row[column.field].label"\n                                                type="is-dark"\n                                                position="is-left">\n                                                <a :href="props.row[column.field].url !== false ? props.row[column.field].url : \'#\'" :target="props.row[column.field].linkTarget" @click="initAction(props.row, props.row[column.field], $event)">\n                                                    <span class="itemLabel">{{ props.row[column.field].label }}</span>\n                                                    <span class="itemIcon" :class="[props.row[column.field].activeClass]" @mouseover="manageMouse(props.row[column.field], \'over\')" @mouseout="manageMouse(props.row[column.field], \'out\')">\n                                                        {{ props.row[column.field].icon.font }}\n                                                    </span>\n                                                </a>\n                                            </b-tooltip>\n                                        </li>\n                                    </ul> \n                                </template>\n                                <template v-else>\n                                    <div :class="[column.field]">{{ props.row[column.field] }}</div>    \n                                </template>\n                            </template>\n                            <template v-else-if="column.type === \'action\'">\n                                <ul class="actions" :class="[column.field]">\n                                    <li \n                                        v-for="(item, k) in props.row[column.field]"\n                                        v-if="item.isActive"\n                                        :key="\'action-\' + k">\n                                        <b-tooltip :label="item.label"\n                                            type="is-dark"\n                                            position="is-left">\n                                            <a :href="item.url !== false ? item.url : \'#\'" :target="item.linkTarget" @click="initAction(props.row, item, $event)">\n                                                <span class="itemLabel">{{ item.label }}</span>\n                                                <span class="itemIcon" :class="[item.activeClass]" @mouseover="manageMouse(item, \'over\')" @mouseout="manageMouse(item, \'out\')">\n                                                    {{ item.icon.font }}\n                                                </span>\n                                            </a>\n                                        </b-tooltip>\n                                    </li>\n                                </ul>    \n                            </template>\n                            <template v-else-if="column.type === \'percentage\'">\n                                <div class="percentageBlock">\n                                    <b-progress \n                                        :type="{\n                                            \'is-red\': props.row[column.field] <= 30,\n                                            \'is-orange\': props.row[column.field] > 30,\n                                            \'is-yellow\': props.row[column.field] > 50,\n                                            \'is-lightGreen\': props.row[column.field] > 70,\n                                            \'is-darkGreen\': props.row[column.field] > 80\n                                        }"  \n                                        format="percent"    \n                                        :value="Number(props.row[column.field])">\n                                        {{props.row[column.field]}}\n                                    </b-progress>\n                                    <div class="percentage">{{props.row[column.field]}}% <a href="#" v-if="false" @click.prevent="openDrawer(props.row, column)"><span class="material-icons">open_in_new</span></a></div>\n                                </div>\n                            </template>\n                            <template v-else-if="column.type === \'hasDescription\'">\n                                <div :class="[column.field, \'hasDescription\']">\n                                    <span class="value">{{ props.row[column.field] }}</span>\n                                    <a href="#" @click.prevent="openDrawer(props.row, column)" v-if="props.row[column.field] !== \'\'"><span class="material-icons">open_in_new</span></a>\n                                </div>    \n                            </template>\n                            <template v-else-if="column.type === \'activeInactive\'">\n                                <div :class="[column.field, \'hasActiveInactive\', props.row[column.field] === \'ACTIVE\' ? \'active\' : \'inactive\']">\n                                    <template v-if="props.row[column.field] === \'ACTIVE\'">\n                                        <span class="material-icons">how_to_reg</span>\n                                    </template>\n                                    <template v-else>\n                                        <span class="material-icons">person_off</span>\n                                    </template>\n                                    <span class="value">{{ props.row[column.field] }}</span>\n                                </div>    \n                            </template>\n                            <template v-else-if="column.type === \'truncate\'">\n                                <template v-if="props.row[column.field] !== false">\n                                    <div class="truncateWrapper" :class="[column.field]">\n                                        <b-tooltip :label="props.row[column.field]"\n                                            type="is-dark"\n                                            position="is-top">\n                                            <div class="truncate">{{ props.row[column.field] }}</div>\n                                        </b-tooltip>\n                                    </div> \n                                </template>\n                                <template v-else>\n                                    <div :class="[column.field]">NA</div>    \n                                </template>\n                            </template>\n                            <template v-else-if="column.type === \'referrer\'">\n                                <template v-if="props.row[column.field].id !== 0">\n                                    <figure \n                                        :class="[column.field]" class="userWithPhoto withLink">\n                                        <img :src="props.row[column.field].image" :alt="props.row[column.field].name" width="20" height="20">\n                                        <figcaption>\n                                            {{ props.row[column.field].name }} <a href="#" v-if="false" @click.prevent="openDrawer(props.row, column)"><span class="material-icons">open_in_new</span></a>\n                                        </figcaption>\n                                    </figure>    \n                                </template>\n                                <template v-else>\n                                    <div :class="[column.field]">NA</div>    \n                                </template>\n                            </template>\n                            <template v-else>\n                                <div :class="[column.field]">{{ props.row[column.field] }}</div>    \n                            </template>\n                        </b-table-column>\n                    </b-table>    \n                </template>    \n            </template>\n        </section>\n    ',data:()=>({tableLoading:!1}),computed:{...Vuex.mapState(["filterResult","userRole"])},async created(){this.emitEvents()},destroyed(){},mounted(){},methods:{copyToClipboard(e,t){console.log(t);let n=document.getElementById(t);n.select(),n.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard",position:"is-bottom"})},manageMouse(e,t){e.activeClass="over"===t?e.icon.hover:e.icon.class},initAction(e,t,n){!1===t.url&&n.preventDefault(),Event.$emit("initAction",e,t,n)},emitEvents(){Event.$on("dataLoaded",()=>{this.tableLoading=!1})},openDrawer(e,t){Event.$emit("openDrawer",e,t)},onPageChange(e,t){this.filterResult.currentPage=e,this.tableLoading=!0,Event.$emit("onTablePageChange",e,this.tableLoading)}}}),Vue.component("yuno-instructors",{props:{courseID:{type:Number,required:!0}},template:'\n        <section class="yunoInstructors">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n                <div class="inlineBlock">\n                    <template v-for="(cta, index) in pageTitleData.cta">\n                        <b-button :tag="cta.tag" @click="cta.tag !== \'a\' ? initEditMode(cta.module) : \'\'" :href="cta.url" :target="cta.target" :class="[cta.class]" :key="index">\n                            <span class="underline">{{ cta.label }}</span> <span class="material-icons-outlined" v-if="cta.icon">{{ cta.icon }}</span>\n                        </b-button>\n                    </template>\n                </div>\n            </div>\n            <template v-if="instructorslList.loading">\n                <div class="wrapper">\n                    <article class="instructorCard" v-for="(item, index) in 4" :key="index">\n                        <div class="cardHeader">\n                            <figure class="avatar">\n                                <div class="avatarWrapper">\n                                    <b-skeleton circle width="96px" height="96px"></b-skeleton>\n                                </div>\n                                <figcaption>\n                                    <h3 class="h4"><b-skeleton active width="100px"></b-skeleton></h3>\n                                    <div class="isFlexbox isCenter">\n                                        <b-skeleton active width="100px"></b-skeleton>\n                                    </div>\n                                </figcaption>\n                            </figure>\n                        </div>\n                    </article>\n                </div>\n            </template>\n            <template v-else-if="instructorslList.success && instructorslList.error === null">\n                <b-carousel-list\n                    v-model="activeIndex"\n                    :data="instructorslList.data"\n                    :arrow="instructorslList.data.length > perList"\n                    :arrow-hover="arrowHover"\n                    :items-to-show="perList"\n                    :items-to-list="increment"\n                    :repeat="repeat"\n                    :has-drag="drag"\n                    :has-grayscale="gray"\n                    :has-opacity="opacity" \n                    :breakpoints="breakpoints"\n                >\n                    <template #item="list">\n                        <yuno-instructor-card\n                            :data="list"\n                        >\n                        </yuno-instructor-card>\n                    </template>\n                </b-carousel-list>\n            </template>\n            <template v-else-if="instructorslList.success">\n                <yuno-empty-state-v3\n                    :title="instructorslList.errorData"\n                >\n                </yuno-empty-state-v3>\n            </template>\n        </section>\n    ',data:()=>({pageTitleData:{title:"Instructors",cta:[{label:"Invite Instructors",class:"yunoSecondaryCTA",module:"inviteInstructors"}]},breakpoints:{320:{itemsToShow:1},960:{itemsToShow:4}},arrow:!0,arrowHover:!0,drag:!0,gray:!1,opacity:!1,activeIndex:0,perList:4,increment:4,repeat:!1,payload:{userID:isLoggedIn,days:0,status:"all",vcStatus:"all",course_search:0,category:0,featured:0,nativeLanguage:"all",avgRating:0}}),computed:{...Vuex.mapState(["instructorslList","course"]),courseObj(){return this.course.data}},async created(){},destroyed(){},mounted(){this.fetchInstructor()},methods:{showToastMessage(e){this.$buefy.toast.open({duration:3e3,message:`${e}`,position:"is-top"})},async manageCopyToClipboard(e){const t=e;if(t)try{await navigator.clipboard.writeText(t),this.showToastMessage("Link copied to clipboard!")}catch(e){console.error("Failed to copy:",e);const n=document.createElement("textarea");n.value=t,document.body.appendChild(n),n.select(),document.execCommand("copy"),document.body.removeChild(n),this.showToastMessage("Link copied to clipboard!")}else console.error("No URL found to copy")},initEditMode(e){"inviteInstructors"===e&&this.$buefy.dialog.alert({title:"Invitation URL",message:this.courseObj.invite_instructor_link,confirmText:"Copy link",canCancel:["escape","outside"],onConfirm:()=>this.manageCopyToClipboard(this.courseObj.invite_instructor_link)})},gotInstructor(e){const t=e?.response?.data;200===t?.code&&(this.instructorslList.data=t.data)},fetchInstructor(){const e={apiURL:YUNOCommon.config.course("invitedInstructors",this.courseID,this.payload.userID,"list-view",this.instructorslList.limit,this.instructorslList.offset),module:"gotData",store:"instructorslList",addToModule:!1,callback:!0,callbackFunc:this.gotInstructor};this.$store.dispatch("fetchData",e)}}}),Vue.component("yuno-share",{template:'\n        <section class="yunoShare">\n            <template v-if="course.loading">\n                <h2 class="hasFlexbox">\n                    <span class="h2"><b-skeleton active width="200px"></b-skeleton></span>\n                </h2>\n                <small class="caption1 fontColorDarkVariant"><b-skeleton active width="100px"></b-skeleton></small>\n                <h3 class="h4"><b-skeleton active width="50px"></b-skeleton></h3>\n            </template>\n            <template v-else-if="course.success && course.error === null">\n                <h2 class="hasFlexbox">\n                    <span class="h2">{{ courseObj.title }}</span>\n                    <a href="#" class="trigger" @click="initEditMode($event, \'share\')">\n                        <span class="material-icons-outlined">edit</span>\n                    </a>\n                </h2>\n                <small class="caption1 fontColorDarkVariant">Course created on {{ courseObj.published_at }}</small>\n                <h3 class="h4">Share</h3>\n                <ul class="hasCol">\n                    <li class="hasCol">\n                        <span class="caption2 fontColorDarkVariant">{{ courseObj.url }}</span>\n                        <a href="#" class="caption2" :data-url="courseObj.url" @click.prevent="copyToClipboard($event)">Copy link</a>\n                    </li>\n                    <li>\n\n                    </li>\n                </ul>\n            </template>\n            <template v-else-if="course.success">\n                {{ course.errorData }}\n            </template>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["course"]),courseObj(){return this.course.data}},async created(){},destroyed(){},mounted(){},methods:{showToastMessage(e){this.$buefy.toast.open({duration:5e3,message:`${e}`,position:"is-bottom"})},async copyToClipboard(e){const t=e.target.getAttribute("data-url");if(t)try{await navigator.clipboard.writeText(t),this.showToastMessage("Link copied to clipboard!")}catch(e){console.error("Failed to copy:",e);const n=document.createElement("textarea");n.value=t,document.body.appendChild(n),n.select(),document.execCommand("copy"),document.body.removeChild(n),this.showToastMessage("Link copied to clipboard!")}else console.error("No URL found to copy")},initEditMode(e,t){e.preventDefault(),this.$emit("initEditMode",t)}}}),Vue.component("yuno-tab-course-economics",{props:["data","options"],template:'\n        <div>\n            <div class="listWithIcon"> \n                <div class="otherInfo">\n                    <h5 class="titleSmall">Total duration: {{ options.data.duration_in_weeks }}</h5>\n                    <h5 class="titleSmall">{{ data.live_classes }}</h5>\n                </div>\n                <ul>\n                    <template v-for="(item, i) in data.what_you_get" v-if="item !== null">\n                        <li \n                            v-if="item.slug === \'diagnostic_test\' && item.value"\n                            :key="i"\n                        >\n                            <span class="material-icons-outlined">\n                                content_paste\n                            </span>\n                            <span class="caption hasTooltip">\n                                {{ item.label }}\n                                <b-tooltip v-if="item.message !== \'\'" :label="item.message"\n                                    type="is-dark"\n                                    multilined\n                                    position="is-bottom"\n                                >\n                                    <span class="material-icons-outlined">info</span>\n                                </b-tooltip>\n                            </span>\n                        </li>\n                        <li \n                            v-if="item.slug === \'live_classes\'"\n                            :key="i"\n                        >\n                            <span class="material-icons-outlined">\n                                videocam\n                            </span>\n                            <span class="caption">\n                                <template v-if="item.value !== true"><span>{{ item.value }}</span></template> {{ item.label }}\n                                <span class="subtitle" v-if="item.subtitle !== \'\'">{{ item.subtitle }}</span>\n                            </span>\n                            <ul class="subItems">\n                                <template v-for="(subItem, j) in item.items">\n                                    <li :key="\'subitem-\' + j">\n                                        <span class="material-icons-outlined">\n                                            <template v-if="subItem.slug === \'group_classes\'">\n                                                groups\n                                            </template>\n                                            <template v-if="subItem.slug === \'one_to_one_classes\'">\n                                                supervisor_account\n                                            </template>\n                                        </span>\n                                        <span class="caption">\n                                            <span>{{ subItem.value }}</span> {{ subItem.label }}\n                                            <b-tooltip v-if="subItem.message !== \'\'" :label="subItem.message"\n                                                type="is-dark"\n                                                multilined\n                                                position="is-left"\n                                            >\n                                                <span class="material-icons-outlined">info</span>\n                                            </b-tooltip>\n                                            <span class="subtitle" v-if="subItem.subtitle !== \'\'">{{ subItem.subtitle }}</span>\n                                        </span>\n                                    </li>\n                                </template>\n                            </ul>\n                        </li>\n                        <li \n                            v-if="item.slug !== \'diagnostic_test\' && item.slug !== \'live_classes\'"\n                            :key="i"\n                        >\n                            <span class="material-icons-outlined">\n                                <template v-if="item.slug === \'duration\'">\n                                    schedule\n                                </template>\n                                <template v-if="item.slug === \'assignments\'">\n                                    library_books\n                                </template>\n                                <template v-if="item.slug === \'mock_tests\'">\n                                    checklist\n                                </template>\n                                <template v-if="item.slug === \'post_test\'">\n                                    inventory\n                                </template>\n                                <template v-if="item.slug === \'self_study_resources\'">\n                                    edit_note\n                                </template>\n                            </span>\n                            <span class="caption" :class="[item.message !== \'\' ? \'hasTooltip\' : \'\']">\n                                <template v-if="item.value !== true"><span>{{ item.value }} </span></template>{{ \'&nbsp\' + item.label }}\n                                <b-tooltip v-if="item.message !== \'\'" :label="item.message"\n                                    type="is-dark"\n                                    multilined\n                                    position="is-bottom"\n                                >\n                                    <span class="material-icons-outlined">info</span>\n                                </b-tooltip>\n                            </span>\n                        </li>\n                    </template>\n                </ul>\n            </div>\n            <div class="cardMetaWrapper">\n                <h4 class="titleLarge withGrid">\n                    {{ data.expected_time_investment.title }}\n                    <b-tooltip v-if="data.expected_time_investment.message !== \'\'" :label="data.expected_time_investment.message"\n                        type="is-dark"\n                        multilined\n                        position="is-bottom">\n                        <span class="material-icons-outlined">info</span>\n                    </b-tooltip>\n                    <small>{{ data.expected_time_investment.subtitle }}</small>\n                </h4>\n                <ul class="cardMeta cols1">\n                    <li \n                        v-for="(item, i) in data.expected_time_investment.items"\n                        :key="\'investment-\' + i"\n                        class="meta">\n                        <span class="value">\n                            {{ item.hours }} <span>{{ item.label }}</span>\n                        </span>\n                        <ul class="subItems noIcon" v-if="item.items !== undefined">\n                            <template v-for="(subItem, j) in item.items">\n                                <li :key="\'subitem-\' + j">\n                                    <span class="caption">\n                                        <span>{{ subItem.value }}</span> {{ subItem.label }}\n                                    </span>\n                                </li>\n                            </template>\n                        </ul>\n                    </li>\n                </ul>\n            </div>\n        </div>\n    ',data:()=>({}),computed:{},async created(){},mounted(){},methods:{}}),Vue.component("yuno-tabs",{template:'\n        <section class="yunoTabsWrapper">\n            <b-tabs class="yunoTabsV2 noContent" v-model="tabs.data.activeTab" @input="tabChange" :animated="false">\n                <b-tab-item \n                    v-for="(tab, i) in tabs.data.items"\n                    :visible="tab.isVisible"\n                    :key="i"\n                    :label="tab.label">\n                </b-tab-item>\n            </b-tabs>\n        </section>\n    ',data:()=>({}),computed:{...Vuex.mapState(["tabs"])},async created(){},destroyed(){},mounted(){},methods:{tabChange(e){const t=this.tabs.data.items[e];YUNOCommon.scrollToElement(`.${t.class}`,500,40),this.$emit("tabChange",e)}}}),Vue.component("yuno-update-about-course",{template:'\n        <section class="moduleEdit">\n            <div class="sectionTitle">\n                <div class="inlineBlock">\n                    <h2 class="h3">{{ pageTitleData.title }}</h2>\n                </div>\n            </div>\n            <div class="row">\n                <div class="col-12 col-md-6">\n                    <template v-if="publishedResources.loading">\n                        <b-skeleton size="is-large" :active="true" :count="4"></b-skeleton>\n                    </template>\n                    <template v-else-if="publishedResources.success && publishedResources.error === null">\n                        <yuno-common-form\n                            :fields="form.fields"\n                            :payload="form.payload"\n                            @submitForm="submitForm"\n                            :isFormData="true"\n                        >\n                        </yuno-common-form>\n                    </template>\n                    <template v-else-if="publishedResources.success">\n                        {{ publishedResources.errorData }}\n                    </template>\n                </div>\n            </div>\n        </section>\n    ',data:()=>({pageTitleData:{title:"About Course"}}),computed:{...Vuex.mapState(["userInfo","user","course","form","publishedResources"]),courseObj(){return this.course.data}},async created(){this.setupForm(),this.fetchCoursePaload()},destroyed(){},mounted(){},methods:{activeOrg(){const e=this.userInfo.data.current_state.org_id;return e||0},gotAcademies(e,t){t.loading=!1,200===e.response.status&&e.response.data&&(t.options=e.response.data.data)},dispatchData(e,t){this.$store.dispatch(e,t)},fetchData(e,t,n){const a=YUNOCommon.findObjectByKey(this.form.fields,"name",e);a.loading=!0;const o={apiURL:t,module:"gotData",store:"subform2",callback:!0,addToModule:!1,callbackFunc:e=>n(e,a)};this.dispatchData("fetchData",o)},fetchAcademies(e){this.fetchData(e,YUNOCommon.config.org("orgAcademies",this.activeOrg()),this.gotAcademies)},fetchInlineDataData(e){if("academy_id"===e)this.fetchAcademies(e)},inlineData(e){e.forEach(e=>{this.fetchInlineDataData(e)})},gotCoursePayload({response:e}){if(200===e?.data?.code){const t=e.data.data,n={name:t.featured_img};t.featured_img=""!==t.featured_img?n:null,this.form.payload=t;"org-admin"===this.userInfo.data.role&&(this.form.payload.org_id=this.activeOrg(),this.form.payload.academy_id=this.courseObj.academy_id,this.inlineData(["academy_id"]))}},fetchCoursePaload(e){e&&(this.publishedResources.data=[],this.publishedResources.success=!1,this.publishedResources.error=null);const t={apiURL:this.getCoursePaloadURL(),module:"gotData",store:"publishedResources",addToModule:!1,callback:!0,callbackFunc:this.gotCoursePayload};this.$store.dispatch("fetchData",t)},getCoursePaloadURL(){const e=this.userInfo.data.role;return{"org-admin":YUNOCommon.config.org("courseDetailForm",!1,!1,!1,this.courseObj.id),"yuno-admin":YUNOCommon.config.course("payload",this.courseObj.id)}[e]||null},showToastMessage(e){this.$buefy.toast.open({duration:5e3,message:`${e}`,position:"is-bottom"})},formPosted(e){this.form.isLoading=!1;const t=e?.response?.data;(201===t?.code||t?.message)&&this.showToastMessage(t.message)},submitForm(e){this.form.isLoading=!0;const t={apiURL:this.getFormSubmitURL(),module:"gotData",store:"form",payload:e,callback:!0,callbackFunc:e=>this.formPosted(e)};this.dispatchData("postData",t)},getFormSubmitURL(){const e=this.userInfo.data.role;return{"org-admin":YUNOCommon.config.org("courseDetailUpdate"),"yuno-admin":YUNOCommon.config.course("updateCourse")}[e]||null},dispatchData(e,t){this.$store.dispatch(e,t)},setupForm(e){const t={isRequired:!0,isDisabled:!1,isLoading:!1},n=[{label:"Title",placeholder:"Enter course title",type:"text",name:"title",...t},{label:"Descripition",placeholder:"Enter course descripition here...",type:"textarea",name:"description",...t},{label:"Excerpt",placeholder:"Enter course excerpt here...",type:"textarea",name:"excerpt",...t},{label:"Video Link",placeholder:"Add YouTube embed link here...",type:"text",name:"video_link",...t},{label:"Upload Feature Image",placeholder:"File format: JPG, PNG or WEBP. Dimension: 16x9 ratio",type:"upload",name:"featured_img",cta:"Click to upload",...t},{label:"Policy For Instructors",placeholder:"Enter policy here...",type:"textarea",name:"policy_for_instructors",...t}];if("org-admin"===this.userInfo.data.role){const e={label:"Academies",placeholder:"Select academie",type:"dropdown",name:"academy_id",options:[],loading:!1,...t};n.push(e)}this.form.fields=n}}}),Vue.component("yuno-view-mode",{props:{courseID:{type:Number,required:!0}},template:'\n        <div class="viewMode">\n            <yuno-breadcrumbs\n                :data="[\n                    {\n                        label: \'Courses\',\n                        url: \'/courses\',\n                        isActive: false\n                    },\n                    {\n                        label: \'Edit\',\n                        url: null,\n                        isActive: true\n                    }\n                ]"\n            >\n            </yuno-breadcrumbs>\n            <yuno-page-title\n                :data="pageTitleData"\n                :moduleObj="course"\n            >\n            </yuno-page-title>\n            <yuno-tabs\n                @tabChange="tabChange"\n            >\n            </yuno-tabs>\n            <yuno-share \n                @initEditMode="initEditMode"\n            >\n            </yuno-share>\n            <yuno-enrollments\n                :courseID="courseID"\n            >\n            </yuno-enrollments>\n            <yuno-batches\n                :courseID="courseID"\n                @initEditMode="initEditMode"\n            >\n            </yuno-batches>\n            <yuno-instructors\n                :courseID="courseID"\n            >\n            </yuno-instructors>\n            <yuno-about-course\n                @initEditMode="initEditMode"\n            >\n            </yuno-about-course>\n            <yuno-course-schedule\n                @initEditMode="initEditMode"\n            >\n            </yuno-course-schedule>\n            <yuno-course-economics\n                @initEditMode="initEditMode"\n            >\n            </yuno-course-economics>\n            <yuno-page-title\n                v-if="false"\n                :data="pageTitleData"\n                :withoutTitle="true"\n            >\n            </yuno-page-title>\n        </div>\n    ',data:()=>({pageTitleData:{title:"Edit Course",cta:[{label:"View Course",class:"hideCTALook",url:"",tag:"a"}]}}),watch:{"course.data":{handler(e,t){e!==t&&this.manageCTA()},deep:!0}},computed:{...Vuex.mapState(["tabs","course"]),courseObj(){return this.course.data}},async created(){},destroyed(){},mounted(){this.tabChange(this.tabs.data.activeTab)},methods:{manageCTA(){YUNOCommon.findObjectByKey(this.pageTitleData.cta,"label","View Course").url=this.courseObj.url},manageCourseSchedule(){const e={courseid:this.$props.courseID,scheduleid:this.courseObj.course_schedule_id},t=new URL(window.location.href);Object.entries(e).forEach(([e,n])=>t.searchParams.set(e,n)),window.history.pushState({path:t.href},"",t.href)},initEditMode(e){"courseSchedule"===e&&this.manageCourseSchedule(),this.$emit("initEditMode",e)},tabChange(e){this.tabs.data.items=this.tabs.data.items.map((t,n)=>({...t,isActive:n===e}))}}});