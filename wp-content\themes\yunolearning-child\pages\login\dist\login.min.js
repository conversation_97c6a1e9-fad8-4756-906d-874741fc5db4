Vue.component("yuno-powered-by",{props:["isOrg"],template:'\n        <footer class="leadFormFooter">\n            <div class="container">\n                <div class="columns is-mobile is-centered">\n                    <div class="poweredBy">\n                        <p>Powered By</p>\n                        <img\n                            :src="logo"\n                            alt="Yuno Learning"\n                            width="51"\n                            height="24"\n                        >\n                    </div>\n                </div>\n            </div>\n        </footer>\n    ',data(){return{logo:this.$store.state.themeURL+"/assets/images/yuno-logo-grey.svg"}},computed:{},async created(){},mounted(){},methods:{}}),Vue.component("yuno-org-theme",{props:["data","options","resourceloaded"],template:"\n        <span></span>\n    ",data:()=>({}),watch:{resourceloaded(e){e&&this.resourceFetched()}},computed:{...Vuex.mapState(["orgAdmin"]),isNotYunoLearning:()=>!["yunolearning.com"].includes(window.location.hostname)},async created(){this.fetchOrgInfo(this.$props.options.orgID)},mounted(){},methods:{loadGoogleFont(e){var t=document.createElement("link");t.rel="stylesheet",t.href="https://fonts.googleapis.com/css2?family="+encodeURIComponent(e)+"&display=swap",document.head.appendChild(t)},resourceFetched(){const e=this.orgAdmin.data.theme;this.loadGoogleFont(e.font_family),document.documentElement.style.setProperty("--primary-color",e.primary_color),document.documentElement.style.setProperty("--body-bg-color",e.background_color),document.documentElement.style.setProperty("--font-family",e.font_family)},gotOrgInfo(e){const{code:t,data:o}=e.response?.data||{};200===t&&(void 0===this.$props.resourceloaded&&this.resourceFetched(),this.$emit("orgFetched"))},fetchOrgInfo(e){const t={apiURL:YUNOCommon.config.org("info",e),module:"gotData",store:"orgAdmin",callback:!0,callbackFunc:e=>this.gotOrgInfo(e)};console.log(this.isNotYunoLearning),this.isNotYunoLearning&&this.$store.dispatch("fetchData",t)}}}),window.Event=new Vue;const validationMsg={messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 10 numbers required",is:"Required",is_not:"New batch shouldn't be same as current batch"}};YUNOCommon.assignVValidationObj(validationMsg),Vue.component("yuno-login",{template:'\n        <div class="componentWrapper" :class="[isiOS ? \'isiOS\' : \'\']">\n            <yuno-org-theme v-if="isOrg" :options="{\n                    \'orgID\': orgID\n                }" \n                @orgFetched="onOrgFetched"\n            >\n            </yuno-org-theme>\n            <template v-if="user.isLoggedin"></template>\n            <template v-else>\n                <template v-if="isOrg">\n                    <template v-if="orgAdmin.loading">\n                        <section class="loginForm">\n                            <div class="b-tabs yunoTabsV2 noTopGap">\n                                <nav class="tabs">\n                                    <ul>\n                                        <li><b-skeleton width="80%" height="52px"></b-skeleton></li>\n                                        <li><b-skeleton width="80%" height="52px"></b-skeleton></li>\n                                    </ul>\n                                </nav>\n                                <section class="tab-content">\n                                    <div class="tab-item">\n                                        <div class="wrapper">\n                                            <figure class="logo">\n                                                <b-skeleton width="200px" height="70px"></b-skeleton>\n                                            </figure>\n                                            <div class="wired">\n                                                <div class="ctaWrapper"><b-skeleton width="100px" height="40px"></b-skeleton></div>\n                                            </div>\n                                        </div>\n                                    </div>\n                                </section>\n                            </div>\n                        </section>\n                    </template>\n                    <template v-if="orgAdmin.success">\n                        <template v-if="orgAdmin.error">\n                            {{ orgAdmin.errorData }}\n                        </template>\n                        <template v-else>\n                            <yuno-login-form \n                                :tabs="tabs"\n                                :options="{\n                                    isOrg: isOrg,\n                                    activeTab: activeTab,\n                                    cardFooter: cardFooter\n                                }"\n                            >\n                            </yuno-login-form> \n                        </template>\n                    </template>\n                </template>\n                <template v-else>\n                    <yuno-login-form \n                        :tabs="tabs"\n                        :isiOS="isiOS"\n                        :options="{\n                            isOrg: isOrg,\n                            activeTab: activeTab,\n                            cardFooter: cardFooter\n                        }"\n                    ></yuno-login-form> \n                </template>\n            </template>\n        </div>\n    ',data:()=>({isOrg:!1,activeTab:null,cardFooter:!0,orgID:"",isiOS:!1,tabs:[{label:"Login",slug:"login",title:"Login to Yuno Learning",footer:{title:"Don't have an account?",cta:{label:"Sign up here"}},poweredBy:{label:"POWERED BY",image:"https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg"}},{label:"Sign up",slug:"signup",title:"Create a free account on Yuno Learning",footer:{title:"Already a user?",cta:{label:"Login in here"}},poweredBy:{label:"POWERED BY",image:"https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg"}}]}),computed:{...Vuex.mapState(["user","userInfo","orgAdmin","form"]),isNotYunoLearning:()=>!["yunolearning.com","dev.yunolearning.com","stage.yunolearning.com"].includes(window.location.hostname)},async created(){this.isiOS=this.checkiOS(),this.loginStatus(),this.manageState()},mounted(){},methods:{checkiOS:()=>/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,onOrgFetched(){const e=this.orgAdmin.data.organisation_name;this.tabs=this.tabs.map(((t,o)=>0===o?{...t,title:`Login to ${e}`}:1===o?{...t,title:`Create a free account on ${e}`}:t))},gotOrgInfo(e){const{code:t,data:o}=e.response?.data||{}},fetchOrgInfo(e){const t={apiURL:YUNOCommon.config.org("info",e),module:"gotData",store:"form",callback:!0,callbackFunc:e=>this.gotOrgInfo(e)};this.$store.dispatch("fetchData",t)},manageState(){const e=YUNOCommon.getQueryParameter("org_id"),t=YUNOCommon.getQueryParameter("type"),o=YUNOCommon.getQueryParameter("footer");if(t){let e="";"login"===t?e=0:"signup"===t&&(e=1),this.activeTab=e}o&&(this.cardFooter="false"!==o),e&&(this.isOrg=!0,isLoggedIn=e,this.orgID=e)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},manageUserRedirection(){const e=new URLSearchParams(window.location.search),t=e.get("org_id"),o=e.get("org_url"),n=yunoAPIToken.replace("Bearer ","");window.location.href=t&&o||o?`${o}?user_id=${isLoggedIn}&yuno_token=${n}`:window.location.href=YUNOCommon.config.host()},loginStatus(){0!==Number(isLoggedIn)?(this.manageUserRedirection(),this.user.isLoggedin=!0,this.fetchUserInfo()):this.user.isLoggedin=!1}}}),Vue.component("yuno-login-form",{props:["data","options","tabs","isiOS"],template:'\n        <section class="loginForm">\n            <b-tabs class="yunoTabsV2 noTopGap" v-model="activeTab" @input="tabChange" :animated="false">\n                <b-tab-item \n                    v-for="(tab, i) in tabs"\n                    :key="i"\n                    :label="tab.label">\n                    <template v-if="tab.slug === \'login\'">\n                        <div class="wrapper">\n                            <figure class="logo">\n                                <img width="106" height="50" :src="options.isOrg ? orgAdmin.data.logo_image_url : logo" :alt="options.isOrg ? orgAdmin.data.title : \'Yuno Learning\'">\n                            </figure>\n                            <div class="wired">\n                                <h3 class="smallTitle">{{ tab.title }}</h3>\n                                <div class="ctaWrapper">\n                                    <button class="googleLogin width70" @click="setState">\n                                        <img :src="googleIcnURL" alt="google"></img> Login with Google\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n                        <p class="helperCaption">\n                            {{tab.footer.title}} <a @click="updateLoginState(tab)" href="#">{{tab.footer.cta.label}}</a>\n                        </p>\n                        <yuno-powered-by v-if="options.cardFooter"></yuno-powered-by>\n                    </template>\n                    <template v-if="tab.slug === \'signup\'">\n                        <div class="wrapper signup">\n                            <figure class="logo">\n                                <img width="106" height="50" :src="options.isOrg ? orgAdmin.data.logo_image_url : logo" :alt="options.isOrg ? orgAdmin.data.title : \'Yuno Learning\'">\n                            </figure>\n                            <div class="wired">\n                                <h3 class="smallTitle">{{ tab.title }}</h3>\n                                <validation-observer \n                                    tag="div" \n                                    class="observer"\n                                    ref="commonSignupObserver" \n                                    v-slot="{ handleSubmit, invalid }">\n                                    <form id="commonSignupForm" @submit.prevent="handleSubmit(initCommonSignup)">\n                                        <b-field label="Phone number" v-if="!isiOS">\n                                            <validation-provider :customMessages="{ required: \'Phone number is required\'}" tag="div" :rules="{required:true, numeric: true, min: 10, max: 10, notAllowed:0}" v-slot="{ errors, classes }">\n                                                <b-input placeholder="Enter your phone number" :class="classes" v-model="signIn.mobile"></b-input>\n                                                <p class="error">{{errors[0]}}</p>\n                                            </validation-provider>    \n                                        </b-field>\n                                        <div class="ctaWrapper noGap">\n                                            <button class="googleLogin" type="submit">\n                                                <img :src="googleIcnURL" alt="google"></img> Sign up with Google\n                                            </button>\n                                        </div>\n                                    </form>\n                                </validation-observer>\n                            </div>\n                        </div>\n                        <p class="helperCaption">\n                            {{tab.footer.title}} <a @click="updateLoginState(tab)" href="#">{{tab.footer.cta.label}}</a>\n                        </p>\n                        <yuno-powered-by v-if="options.cardFooter"></yuno-powered-by>\n                    </template>\n                </b-tab-item>\n            </b-tabs>\n        </section>\n    ',data(){return{activeTab:0,googleIcnURL:this.$store.state.themeURL+"/assets/images/google.svg",logo:"https://res.cloudinary.com/harman-singh/image/upload/v1702461079/production/yunoLogo_ckedzs.svg",signIn:{mobile:"",categoryURL:"",productCode:"",leadStatus:"",variant:"",utmSource:"",utmCampaign:"",utmMedium:"",adGroupID:"",adContent:"",utmTerm:"",gclid:"",content:{type:"",id:""},landing_page:{url:"",title:""},referral_url:"",redirect_url:"",course_to_be_map:"",org_details:{type:"login",org_id:"",org_url:"",phone:""},login_details:{role:""}}}},computed:{...Vuex.mapState(["orgAdmin"])},async created(){this.manageOrgPayload(),this.manageTab()},mounted(){},methods:{formatDateTime:e=>`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}:${e.getSeconds().toString().padStart(2,"0")}`,utf8ToBase64(e){const t=encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,(function(e,t){return String.fromCharCode("0x"+t)}));return btoa(t)},manageOrgPayload(){const e=YUNOCommon.getQueryParameter("org_url"),t=YUNOCommon.getQueryParameter("org_id"),o=YUNOCommon.getQueryParameter("phone"),n=YUNOCommon.getQueryParameter("deviceType");if(e){if(this.signIn.org_details.org_url=e,t){const e=t+"@@@"+this.formatDateTime(new Date);this.signIn.org_details.org_id=this.utf8ToBase64(e)}o&&(this.signIn.org_details.phone=o),n&&(this.signIn.org_details.type=n)}},manageTab(){null!==this.$props.options.activeTab&&(this.activeTab=this.$props.options.activeTab)},tabChange(){},updateLoginState(e){"login"===e.slug?this.activeTab=1:this.activeTab=0},initCommonSignup(){this.setState()},setSigninProps(){const e=JSON.parse(sessionStorage.getItem("landingPage"))||{};""!==e.redirect_url?localStorage.setItem("userState",e.redirect_url):localStorage.setItem("userState",window.location.pathname+window.location.search),void 0!==this.$props.postsignup&&localStorage.setItem("oldUserState",window.location.pathname+window.location.search)},setPayload(){const e=JSON.parse(sessionStorage.getItem("landingPage"))||{},t=e.urlParams||{},o=e.zohoMeta||{},n=YUNOCommon.getQueryParameter("org_url");let a="";a=n||(e.redirect_url||""),this.signIn={...this.signIn,categoryURL:e.category||"general",landing_page:{url:e.url||window.location.origin+window.location.pathname,title:e.pageTitle||document.title},utmSource:t.utm_source||"",utmCampaign:t.utm_campaign||"",utmMedium:t.utm_medium||"",adGroupID:t.adgroupid||"",adContent:t.ad_content||"",utmTerm:t.utm_term||"",gclid:t.gclid||"",content:{type:o.content_type||"",id:o.content_id||""},productCode:o.productcode||"",leadStatus:o.leadstatus||"",login_details:{role:e.role?e.role:""},redirect_url:a,course_to_be_map:e.courseToBeMap||""}},isFirefoxPrivate(e){console.log(e),null!==e&&e?setTimeout((()=>{this.setSigninProps(),this.setPayload(),window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),5e3):(this.setSigninProps(),this.setPayload(),setTimeout((()=>{window.location.href=YUNOCommon.config.signInURLWithState(this.signIn)}),50))},setState(){YUNOCommon.isPrivateWindow(this.isFirefoxPrivate)}}});