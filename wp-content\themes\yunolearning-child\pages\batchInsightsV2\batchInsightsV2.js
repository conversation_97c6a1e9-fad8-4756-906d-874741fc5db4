window.Event = new Vue();

const validationMsg = {
    "messages": {
        "required": "This field is required",
        "numeric": "Numbers only",
        "min": "Minimum 10 numbers required",
        "max": "Maximum 15 numbers required ",
        "is_not": "New batch shouldn't be same as current batch"
    }
};

YUNOCommon.assignVValidationObj(validationMsg);

Vue.component('yuno-batch-insights-v2', {
    template: `
        <yuno-page-grid
            :authorizedRoles="authorizedRoles"
            @onUserInfo="onUserInfo"
            :hasSearchBar="false"
        >
            <template v-slot:main>
                <div class="container-fluid">
                    <div class="mainHeader">
                        <div class="block">
                            <h1 class="pageTitle">{{ pageHeader.title }} 
                                <a 
                                    class="refresh" 
                                    @click.prevent="refreshTable()"
                                    href="#">
                                    <b-tooltip label="Refresh"
                                        type="is-dark"
                                        position="is-right">
                                        <span class="material-icons">refresh</span>
                                    </b-tooltip>
                                </a>
                            </h1>
                            <div class="action" v-if="pageHeader.button.isActive">
                                <template v-if="pageHeader.button.type === 'custom'">
                                    <b-button 
                                        @click="pageCTA()"
                                        class="yunoSecondaryCTA wired">
                                        {{pageHeader.button.label}}
                                    </b-button>
                                </template>
                                <template v-else>
                                    <b-button tag="a"
                                        :href="pageHeader.button.url"
                                        class="yunoSecondaryCTA wired">
                                        {{pageHeader.button.label}}
                                    </b-button>
                                </template>
                            </div> 
                        </div>
                    </div>
                </div>
                <yuno-tabs-v2
                    :destroyOnHide="true"
                    @tabChange="tabChange"
                >
                    <template v-slot:upcoming>
                        <yuno-upcoming
                            @tabChanged="onTabChanged"
                            ref="yunoUpcoming" 
                            :storage="storage"
                            :apiURL="apiURL"
                            :filterAPIURL="filterAPIURL"
                            @onPageChange="onPageChange"
                        >
                        </yuno-upcoming>
                    </template>
                    <template v-slot:past>
                        <yuno-past
                            @tabChanged="onTabChanged"
                            ref="yunoPast" 
                            :storage="storage"
                            :apiURL="apiURL"
                            :filterAPIURL="filterAPIURL"
                            @onPageChange="onPageChange"
                        >
                        </yuno-past>
                    </template>
                </yuno-tabs-v2>
                <b-modal 
                    :active.sync="classSize.modal" 
                    :width="500" 
                    :can-cancel="['escape', 'x']"
                    :on-cancel="classSizeModalClose"
                    class="yunoModal lightTheme">
                        <div class="modalHeader">
                            <h2 class="modalTitle">Choose Class Size</h2>
                        </div>
                        <validation-observer tag="div" ref="classSizeObserver" v-slot="{ handleSubmit }">
                            <form id="selectCourseForm" @submit.prevent="handleSubmit(initClassSize)">
                                <div class="modalBody">
                                    <div class="radioList groupElement">
                                        <small class="helper">Please choose one option</small>
                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                            <template v-for="(item, i) in classSize.items">
                                                <b-field class="colorGrey">
                                                    <b-radio v-model="classSize.selected"
                                                        name="classSize"
                                                        :native-value="item.slug">
                                                        {{item.label}}
                                                    </b-radio>
                                                </b-field>
                                            </template>
                                            <p class="error">{{errors[0]}}</p>
                                        </validation-provider>
                                    </div>
                                    <div class="ctaWrapper alignLeft">
                                        <b-button
                                            native-type="submit"
                                            class="yunoSecondaryCTA">
                                            Continue
                                        </b-button>
                                    </div>        
                                </div>
                            </form>
                        </validation-observer>
                </b-modal>
            </template>
        </yuno-page-grid>
    `,
    data() {
        return {
            apiURL: null,
            filterAPIURL: null,
            isMiniSidebar: false,
            storage: {
                name: "batchInsightsV2",
                version: 2
            },
            authorizedRoles: [
                "yuno-admin",
                "Counselor",
                "org-admin"
            ],
            pageHeader: {
                title: "Batches",
                button: {
                    label: "Create Batch",
                    type: "custom",
                    url: "/create-batch",
                    isActive: true
                }
            },
            classSize: {
                modal: false,
                selected: "",
                items: [
                    {
                        slug: "one_to_one",
                        label: "1-to-1"
                    },
                    {
                        slug: "one_to_many",
                        label: "Group (10 max.)"
                    }
                ]
            },
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'header',
            'userProfile',
            'userRole',
            'footer',
            'loader',
            'filterResult',
            'orgAdmin',
            'filters'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
    },
    async created() {
        
    },
    destroyed() {
        
    },
    mounted() {
        
    },
    methods: {
        pageCTA() {
            this.classSize.modal = true;
        },
        classSizeModalClose() {
            this.$refs.classSizeObserver.reset();
            this.classSize.selected = "";
        },
        initClassSize() {
            let url = this.pageHeader.button.url + "/?classSize=" + this.classSize.selected;
            window.location.href = YUNOCommon.config.host() + url;
        },
        refreshTable() {
            this.filterResult.refreshTable = true;
        },
        fetchModules(role) {
            this.setupTabs("tabsAvailable");
            this.setupTableGrid(this.userRole.data);
            this.setupFilters();
        },
        onUserInfo(data) {
            this.fetchModules(data.role);
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            } else {
                return 0;
            }
        },
        tabChange(e) {
            this.manageSessionStorage();
            this.resetModule();
            this.setupTableGrid(this.userRole.data, e);
            setTimeout(() => {
                const tabRefs = [this.$refs.yunoUpcoming, this.$refs.yunoPast];
                const tabRef = tabRefs[e];
                
                if (tabRef) {
                    tabRef.onTabChanged(e);
                }
            }, 50);
        },
        onTabChanged() {
            
        },
        resetModule() {
            this.filterResult.data = [];
            this.filterResult.error = null;
            this.filterResult.errorData = [];
            this.filterResult.success = false;
            this.filterResult.count = "";
            this.filterResult.currentCount = "";
            this.filterResult.offset = 0;
            this.filterResult.payload = [];
            this.filters.data = [];
            this.filters.error = null;
            this.filters.errorData = [];
            this.filters.success = false;
        },
        setupFilters() {
            const payload = JSON.parse(JSON.stringify(this.filterResult.payload));
            delete payload.limit;
            delete payload.offset;

            this.filterAPIURL = {
                configMethod: "batch",
                type: "filters",
                options: {
                    params: `?${this.objectToQueryString(payload)}`,
                }
            }
        },
        onPageChange() {
            this.setupTableGrid(this.userRole.data);
        },
        setupTableGrid(role, tabIndex) {
            const currentTab = this.filterResult.tabs.items[this.filterResult.tabs.activeTab];
            // Base payload with common properties
            const basePayload = {
                "user_id": isLoggedIn,
                "personalisation": "all",
                "course_id": 0,
                "only_enrollable": false,
                "batch_days": ["all"],
                "batch_time": ["all"],
                "org_id": this.activeOrg(),
                "academy_id": 0,
                "only_locked": false,
                "active_batch": "upcoming",
                "enrollment_request_date": "2025-07-16",
                "teaching_mode": "all",
                "limit": this.filterResult.limit,
                "offset": this.filterResult.offset  
            };

            if (currentTab.slug === 'past') {
                basePayload.active_batch = 'inactive';
            }

            // Role-specific payload configurations
            const rolePayloads = {
                "Counselor": {
                    ...basePayload,
                    "instructor_id": 0,
                },
                "org-admin": {
                    ...basePayload
                },
                "yuno-admin": {
                    ...basePayload,
                    "instructor_id": 0,
                }
            };

            if (currentTab.slug === "upcoming" && role === "org-admin") {
                rolePayloads["org-admin"].org_id = this.activeOrg();
            }

            if (currentTab.slug === "past" && role === "org-admin") {
                rolePayloads["org-admin"].org_id = this.activeOrg();
            }

            // Merge logic: keep only those keys from existingPayload that are not equal to the default value in newPayload
            const existingPayload = this.filterResult.payload || {};
            const defaultPayload = { ...basePayload, ...(rolePayloads[role] || {}) };
            let newPayload = {};
            for (const key in defaultPayload) {
                if (
                    existingPayload.hasOwnProperty(key) &&
                    existingPayload[key] !== defaultPayload[key]
                ) {
                    newPayload[key] = existingPayload[key];
                } else {
                    newPayload[key] = defaultPayload[key];
                }
            }
            // Always ensure offset is up-to-date
            newPayload.offset = this.filterResult.offset;

            this.filterResult.payload = newPayload;

            this.apiURL = {
                configMethod: "batch",
                type: "list",
                options: {
                    view: "grid",
                    params: this.filterResult.payload
                }
            };
        },
        objectToQueryString(obj) {
            return new URLSearchParams(obj).toString();
        },
        manageSessionStorage() {
            const storage = this.storage;
            const store = sessionStorage.getItem(storage.name + "V" + storage.version);

            if (store) {
                sessionStorage.removeItem(storage.name + "V" + storage.version);
            }
        },
        setupTabs(wrapperClass) {
            function createTab(label, slug, eleClass, isVisible) {
                return {
                    label,
                    slug,
                    isActive: false,
                    isVisible: isVisible,
                    class: eleClass
                };
            };

            const storage = this.storage;
            const store = sessionStorage.getItem(storage.name + "V" + storage.version);
            let activeTab = "";

            if (store) {
                activeTab = JSON.parse(store).currentTab;
            } else {
                activeTab = 0;
            }

            this.filterResult.tabs = {
                activeTab: activeTab,
                wrapperClass: wrapperClass,
                items: [
                    createTab("Upcoming Batches", "upcoming", "yunoUpcoming", true),
                    createTab("Past Batches", "past", "yunoPast", true),
                ],
            };
        },
    }
});