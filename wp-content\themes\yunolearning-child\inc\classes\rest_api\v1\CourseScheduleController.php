<?php
namespace V1;
use WP_REST_Controller;
use WP_REST_Response;
use WP_REST_Request;
use WP_Error;
use WP_REST_Server;
use UserElasticSearch;
use WP_User_Query;
use Utility;
use WP_Query;
use Exception;
use Countable;
/**
 * Course Schedule Controller
 */
class CourseScheduleController extends WP_REST_Controller
{

    public $namespace;
    public $courses;
    public $coursedetail;
    public $editcourse;
    public $createcourseschedule;
    public $editcourseSchedule;
    public $getactivity;
    public $courseaddkeys;
    public $getcourseSchedule;
    public $migratecourseschedule;
    public $getcoursesubcategories;
    public $migratecourseactiveenrollments;
    public $migratecourseactivebatches;
    public $updateoldschedule;
    public $orgcourses;

    public function __construct()
    {

        $this->namespace = 'yuno/v1';
        $this->courses = '/courses/(?P<role_id>\d+)/(?P<course_id>[a-zA-Z0-9-_-]+)/(?P<category_id>[a-zA-Z0-9-_-]+)/(?P<instructor_format>[a-zA-Z0-9-_-]+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->coursedetail = '/course/(?P<course_id>\d+)';
        $this->editcourse = '/course/edit/(?P<course_id>\d+)';
        $this->createcourseschedule = '/course/schedule/create';
        $this->editcourseSchedule = '/course/schedule/edit/';
        $this->getactivity = '/activity';
        $this->courseaddkeys = '/courese-new/add/newkeys';
        $this->getcourseSchedule = '/course/schedule/(?P<course_id>\d+)/(?P<course_schedule_id>\d+)';
        $this->migratecourseschedule = '/migrate/course/schedule';
        $this->getcoursesubcategories = '/course/(?P<course_id>\d+)/subcategories';
        $this->migratecourseactiveenrollments = '/migrate/course/active/enrollments';
        $this->migratecourseactivebatches = '/migrate/course/active/batches';
        $this->updateoldschedule = '/migrate/course/schedule/es/keys/(?P<limit>\d+)/(?P<offset>\d+)';
        $this->orgcourses = '/courses/(?P<role_id>\d+)/(?P<org_id>\d+)/(?P<academy_id>\d+)/(?P<course_id>[a-zA-Z0-9-_-]+)/(?P<category_id>[a-zA-Z0-9-_-]+)/(?P<instructor_format>[a-zA-Z0-9-_-]+)/(?P<view>[a-zA-Z0-9-]+)/(?P<limit>\d+)/(?P<offset>\d+)';
    }

    public function register_routes()
    {
        register_rest_route($this->namespace, $this->courses, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_list_courses'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->orgcourses, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_list_courses'),
                'permission_callback' => array($this, 'org_admin_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->coursedetail, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_course_details'),
                'permission_callback' => array($this, 'get_course_details_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->editcourse, array(
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'update_course_details'),
                'permission_callback' => array($this, 'update_course_details_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->createcourseschedule, array(
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array($this, 'course_schedule_create'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->editcourseSchedule, array(
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'course_schedule_edit'),
                'permission_callback' => array($this, 'check_access_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->getactivity, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_activity'),
                'permission_callback' => array($this, 'get_activity_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->courseaddkeys, array(
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array($this, 'create_new_keys_course_new'),
                'permission_callback' => array($this, 'create_new_keys_course_new_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->getcourseSchedule, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_course_schedules_es'),
                'permission_callback' => array($this, 'get_course_schedules_es_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->migratecourseschedule, array(
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'migrate_course_schedule_es'),
                'permission_callback' => array($this, 'migrate_course_schedule_es_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->getcoursesubcategories, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_courses_subcategories'),
                'permission_callback' => array($this, 'get_courses_subcategories_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->migratecourseactiveenrollments, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'migrate_course_active_enrollments'),
                'permission_callback' => array($this, 'migrate_course_active_enrollments_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->migratecourseactivebatches, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'migrate_course_active_batches'),
                'permission_callback' => array($this, 'migrate_course_active_batches_permissions_check'),
                'args' => array(),
            ),
        ));

        register_rest_route($this->namespace, $this->updateoldschedule, array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'update_old_schedule_db_es'),
                'permission_callback' => array($this, 'update_old_schedule_db_es_permissions_check'),
                'args' => array(),
            ),
        ));
    }

    public function org_admin_permissions_check()
    {
        return true;
    }
    /**
     * Token authorization check
     * This function common for all post login apis  
     */
    public function check_access_permissions_check(WP_REST_Request $request)
    {return true;
        $authToken = $request->get_header('authorization');
        if (empty($authToken)) {
            return false;
        }
        list($bearer, $token) = explode(" ", $authToken);
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman
            if ($return) {
                return true;
            } else {
                return false;
            }
        }
        $codes = error_code_setting();
        $authToken = $request->get_header('authorization');
        list($bearer, $token) = explode(" ", $authToken);
        $result = token_validation_check($token);
        $newData = [];
        if ($result === true) {
            return true;
        } elseif ($result != '' && strlen($result) > 10) {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => true,
                "token" => $result
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        } else {
            $newData = [
                "status" => $codes["TOKEN_FAIL"]["code"],
                "reValidate" => false
            ];
            return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
        }
    }
    /**
     * Get list of Courses
     */
    public function get_list_courses($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $course_id = $request['course_id'];
        $category_id = $request['category_id'];
        $instructor_format = $request['instructor_format'];
        $role_id = (int)$request['role_id'];
        $view = $request['view']; //list-view -- grid-view
        $limit = $request['limit'];
        $offset = $request['offset'];
        $userdata = get_userdata($role_id);
        $role = $userdata->roles[0];


        if ($role_id < 0)  {
            return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));
        }

        if (empty($userdata)) {
            return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
        }

        $must = []; // Initialize the $must array to accumulate query conditions

        if ($role == "um_org-admin") {
            $org_id = $request['org_id'];

            $academies = get_post_meta($org_id, 'academies', true);
            $academy = $request['academy_id'];
            if (!empty($academies)) {
                $must[] = [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["terms" => ["data.details.academies" => $academies]]
                                ]
                            ]
                        ]
                    ]
                ];
            } else {
                $must[] = [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ['match' => ['data.details.academies' => $academy]]
                                ]
                            ]
                        ]
                    ]
                ];
            }
        }

        $sort = [
            "data.details.product_order" => [
                "mode" => "avg",
                "order" => "desc",
                "nested" => [
                    "path" => "data.details"
                ]
            ]
        ];

        if ($course_id == "all" && $category_id == "all" && $instructor_format == "all") {
          
            $curlPost = [
                "query" => [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["terms" => ["data.details.academies" => $academies]]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        } else {
            if (!empty($course_id) && $course_id != "all") {
                $must[] = [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.record_id" => $course_id]]
                                ]
                            ]
                        ]
                    ]
                ];
            }

            if (!empty($category_id) && $category_id != "all") {
                $must[] = [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match" => ["data.details.taxonomy" => $category_id]]
                                ]
                            ]
                        ]
                    ]
                ];
            }

            if (!empty($instructor_format) && $instructor_format != "all") {
                if ($instructor_format == "one_to_one") {
                    $instructor_format = "1-1";
                }
                if ($instructor_format == "group") {
                    $instructor_format = "1-Many";
                }

                $must[] = [
                    "nested" => [
                        "path" => "data.details",
                        "query" => [
                            "bool" => [
                                "must" => [
                                    ["match_phrase" => ["data.details.group_type" => $instructor_format]]
                                ]
                            ]
                        ]
                    ]
                ];
            }

            $curlPost = [
                "query" => [
                    "bool" => [
                        "must" => $must
                    ]
                ],
                "sort" => [$sort]
            ];
        }
        $curlPost = [
            "query" => [
                "bool" => [
                    "must" => $must
                ]
            ],
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id",
                "data.details.user_id",
                "data.details.title",
                "data.details.url",
                "data.details.excerpt",
                "data.details.description",
                "data.details.product_description",
                "data.details.unit_price",
                "data.details.group_price",
                "data.details.one_to_one_price",
                "data.details.city_name",
                "data.details.country",
                "data.details.mapped_courses",
                "data.details.live_classes",
                "data.details.instructor_hours",
                "data.details.taxonomy",
                "data.details.mapped_instructors",
                "data.details.batch_details",
                "data.details.event_date",
                "data.details.course_schedule",
                "data.details.group_type",
                "data.details.duration_weeks",
                "data.details.active_enrollments",
                "data.details.course_schedule_id",
                "data.details.active_batches",
                "data.details.ongoing_batches",
                "data.details.course_economics",
                "data.details.course_economics_one_to_one"
            ]
        ];
      

        $elasticsearch_url = ELASTIC_SEARCH_END_URL . "/". YN_ES_PREFIX ."course/_search?from=" . $offset . "&size=" . $limit;
        $headers = [
            "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
            "cache-control: no-cache"
        ];
    
    $response = Utility::curl_request($elasticsearch_url, 'POST', $curlPost, $headers, ELASTIC_SEARCH_PORT);

        $res = json_decode($response['response']);
        $collections = [];
        foreach ($res->hits->hits as $result) {
            $record_id = $result->_source->data->details->record_id;
            $course_title = $result->_source->data->details->title;
            $course_url = $result->_source->data->details->url;
            $course_excerpt = $result->_source->data->details->excerpt;
            $product_description = $result->_source->data->details->product_description;
            $course_group_price = $result->_source->data->details->group_price;
            $course_1_to_1_Price = $result->_source->data->details->one_to_one_price;
            $live_classes = $result->_source->data->details->live_classes;
            $instructor_hours = $result->_source->data->details->instructor_hours;
            $event_date = $result->_source->data->details->event_date;
            $mapped_instructors = $result->_source->data->details->mapped_instructors;
            $group_type = $result->_source->data->details->group_type;
            $duration_weeks = $result->_source->data->details->duration_weeks;
            $active_enrollments = $result->_source->data->details->active_enrollments;
            $course_schedule_id = $result->_source->data->details->course_schedule_id;
            $course_economics_id = $result->_source->data->details->course_economics[0]->id;
            $course_economics_id_one_to_one = $result->_source->data->details->course_economics_one_to_one[0]->id;
            $active_batches = $result->_source->data->details->active_batches;
            $ongoing_batches = $result->_source->data->details->ongoing_batches;
            if (empty($course_economics_id) || $course_economics_id == 1) {
                $course_economics_id = 0;
            }
            if (empty($course_economics_id_one_to_one) || $course_economics_id_one_to_one == 1) {
                $course_economics_id_one_to_one = 0;
            }
            if (empty($active_batches)) {
                $active_batches = 0;
            }
            if (empty($ongoing_batches)) {
                $ongoing_batches = 0;
            }
            if (empty($active_enrollments)) {
                $active_enrollments = 0;
            }
            if (empty($course_schedule_id)) {
                $course_schedule_id = 0;
            }
            if (empty($course_economics_id)) {
                $course_economics_id = 0;
            }

            $schedule_meta = get_post_meta($course_schedule_id, "course_schedule_data", true);
            if (is_array($schedule_meta) || $schedule_meta instanceof Countable) {
                $schedule_count = count($schedule_meta);
            } else {
                // Handle the case when $schedule_meta is not countable.
                // You may set a default value or throw an exception, depending on your requirements.
            }

            if (empty($schedule_count)) {
                $schedule_count = 0;
            }

            $group_refine_price = course_price_distribution($course_group_price);
            $group_price = $group_refine_price['price_with_gst'];

            $oto_refine_price = course_price_distribution($course_1_to_1_Price);
            $oto_price = $oto_refine_price['price_with_gst'];
            $collections[] = [
                "course_id" => (int)$record_id,
                "course_title" => $course_title,
                "course_url" => $course_url,
                "course_excerpt" => $course_excerpt,
                "description" => $product_description,
                "duration_weeks" => $duration_weeks,
                "course_schedule_id" => $course_schedule_id,
                "course_economics_id" => $course_economics_id,
                "course_economics_id_one_to_one" => $course_economics_id_one_to_one,
                "group_price" => (int)$group_price,
                "one_to_one_price" => (int)$oto_price,
                "live_classes" => $live_classes,
                "instructor_hours" => $instructor_hours,
                "mapped_instructors" => $mapped_instructors,
                "ongoing_batches" => $ongoing_batches,
                "active_enrollments" => $active_enrollments,
                "personalization" => $group_type,
                "schedule_count" => $schedule_count,
                "time" => strtotime($event_date)
            ];
        }
        $columns = [
            [
                "field" => "course_title",
                "label" => "Course Title",
                "sortable" => true
            ],
            [
                "field" => "course_excerpt",
                "label" => "Excerpt",
                "sortable" => false
            ],
            [
                "field" => "description",
                "label" => "Description",
                "sortable" => false
            ],
            [
                "field" => "group_price",
                "label" => "Group Price",
                "sortable" => true
            ],
            [
                "field" => "one_to_one_price",
                "label" => "1-to-1 Price",
                "sortable" => true
            ],
            [
                "field" => "live_classes",
                "label" => "Live classes",
                "sortable" => true
            ],
            [
                "field" => "instructor_hours",
                "label" => "Instructor hours",
                "sortable" => true
            ],
            [
                "field" => "duration_weeks",
                "label" => "Duration(in weeks)",
                "sortable" => true
            ],
            [
                "field" => "ongoing_batches",
                "label" => "Ongoing &.. Batches",
                "sortable" => true
            ],
            [
                "field" => "active_enrollments",
                "label" => "Active enrollments",
                "sortable" => true
            ],
            [
                "field" => "personalization",
                "label" => "Personalization",
                "sortable" => false
            ],
            [
                "field" => "schedule_count",
                "label" => "Schedule",
                "sortable" => false
            ]
        ];
        // $totals = count($collections);
        $total_records = $res->hits->total->value;
        $insightsData = [
            "rows" => $collections,
            "columns" => $columns
        ];
        if (empty($collections)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Courses Not Found', array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            if ($view == "list-view") {
                $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Course insights", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $total_records, 'data' => $collections);
            } else {
                $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Course insights", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => $total_records, 'data' => $insightsData);
            }
            return new WP_REST_Response($result, 200);
        }
    }
    public function get_course_details($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $course_id = (int)$request['course_id'];
        $curlPost = [
            "query" => [
                "nested" => [
                    "path"  => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                "match" => [
                                    "data.details.record_id" => $course_id
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" .YN_ES_PREFIX. "course/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $res = json_decode($response);
        $collections = [];
        foreach ($res->hits->hits as $result) {
            $record_id = $result->_source->data->details->record_id;
            $course_title = $result->_source->data->details->title;
            $course_url = $result->_source->data->details->url;
            $course_excerpt = $result->_source->data->details->excerpt;
            $course_description = $result->_source->data->details->description;
            $course_price = $result->_source->data->details->unit_price;
            $live_classes = $result->_source->data->details->live_classes;
            $instructor_hours = $result->_source->data->details->instructor_hours;
            $event_date = $result->_source->data->details->event_date;
            $collections[] = [
                "course_id" => $record_id,
                "course_title" => $course_title,
                "course_url" => $course_url,
                "course_excerpt" => $course_excerpt,
                "description" => $course_description,
                "price" => $course_price,
                "live_classes" => $live_classes,
                "instructor_hours" => $instructor_hours,
                "time" => strtotime($event_date)
            ];
        }
        if (empty($collections)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Course Details Not Found', array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Course insights", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"],  'data' => $collections);
            return new WP_REST_Response($result, 200);
        }
    }
    public function get_course_details_permissions_check()
    {
        return true;
    }
    public function update_course_details($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $course_id = (int)$request['course_id'];
        $course_title = $request['course_title'];
        $course_excerpt = $request['course_excerpt'];
        $course_description = $request['description'];
        $price = (int)$request['price'];
        $live_classes = (int)$request['live_classes'];
        $instructor_hours = (int)$request['instructor_hours'];

        if (empty($course_id) || empty($course_title) || empty($course_excerpt)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Course ID/Title/Excerpt should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        if (strlen($course_excerpt) > 145) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Excerpt length upto 145 character', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        $post = array();
        $post['ID'] = $course_id;
        $post['post_status'] = 'publish';
        $post['post_type'] = 'course';
        $post['post_title'] = $course_title;
        $post['post_content'] = $course_description;
        $post['post_excerpt'] = $course_excerpt;
        try {
            // Update Post
            wp_update_post($post);
            update_post_meta($course_id, 'live_classes', $live_classes);
            update_post_meta($course_id, 'Instructor_Hours', $instructor_hours);
            update_post_meta($course_id, 'Unit_Price', $price);
        } catch (Exception $e) {
            error_log("Course Post is not updated");
        }

        $curlPost = [
            "data" => [
                "details" => [
                    "record_id" => $course_id,
                    "update_event_type" => "course",
                    "resource_type" => "course",
                    "title" => $course_title,
                    "excerpt" => $course_excerpt,
                    "description" => $course_description,
                    "unit_price" => $price,
                    "live_classes" => $live_classes,
                    "instructor_hours" => $instructor_hours,
                    "published_at" => get_the_time('M j, Y g:i A', $course_id)
                ],
                "@timestamp" => date("Y-m-d H:i:s")
            ]
        ];

        try {
            update_elastic_event($curlPost);
        } catch (Exception $e) {
            error_log("Elastic Search Post Entry is not updated");
        }
        $message = str_replace("[Module_Name]", "Course Details", $codes["PUT_UPDATE"]["message"]);
        $code = $codes["PUT_UPDATE"]["code"];
        if ($course_id > 0) {
            $result = array(
                'code' => $code,
                'message' => $message,
                'data' => array('status' => $code, 'resource_id' => $course_id)
            );
            return new WP_REST_Response($result, 200);
        } else {
            return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
        }
    }
    public function update_course_details_permissions_check()
    {
        return true;
    }
    public static function course_schedule_edit($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $data = json_decode($request->get_body(), true);
        $course_schedule_id = (int)$data['id'];
        $course_id = (int)$data['course_id'];
        $course_schedule_data = $data['course_schedule'];

        if (empty($course_id)) {
            return new WP_Error($codes["PUT_UPDATE_FAIL"]["code"], 'Course ID should not be blank', array('status' => $codes["PUT_UPDATE_FAIL"]["status"]));
        }

        $post = array();
        $post['ID'] = $course_schedule_id;
        $post['post_status'] = 'publish';
        $post['post_type'] = 'course_schedule';
        $post['post_title'] = get_the_title($course_schedule_id);

        try {
            // Update Post
            wp_update_post($post);
            update_post_meta($course_schedule_id, 'course_id', $course_id);
            update_post_meta($course_schedule_id, 'course_schedule_data', $course_schedule_data);
        } catch (Exception $e) {
            error_log("Course Schedule Post is not updated -- course_schedule_edit");
        }

        $args = array(
            'post_type'      => "course_schedule",
            'post_status'    => 'publish',
            'order'          => 'DESC',
            'orderby'        => 'ID',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => 'course_id',
                    'value' => $course_id,
                    'compare' => 'IN'
                )
            )
        );
        $the_query = new WP_Query($args);
        if ($the_query->have_posts()) {
            while ($the_query->have_posts()) {
                $the_query->the_post();
                $post_id = get_the_ID();
                $course_schedule_details[] = [
                    "id" => $post_id,
                    "course_id" => get_post_meta($post_id, 'course_id', true),
                    "title" => get_the_title($course_schedule_id),
                    "excerpt" => get_the_excerpt($post_id),
                    "description" => get_the_title($course_schedule_id),
                    "order" => get_post_meta($post_id, 'order', true),
                    "duration" => get_post_meta($post_id, 'duration', true),
                    "activity" => get_post_meta($post_id, 'activity', true),
                    "sub_cat" => json_encode(get_post_meta($post_id, 'sub_cat', true)),
                    "url" => get_permalink($post_id),
                    "published_at" => get_the_time('M j, Y g:i A', $post_id)
                ];
                $course_id = get_post_meta($post_id, 'course_id', true);
                $course_schedule_data = get_post_meta($post_id, 'course_schedule_data', true);

                // Create the document
                $document = [
                    'course_id' => $course_id,
                    'id' => $post_id,
                    'course_schedule' => $course_schedule_data
                ];
            }
        }
        $es_response = self::updateCourseScheduleInElasticsearch($course_id, $document);

        if ($es_response === true) {
            $message = str_replace("[Module_Name]", "Course Schedule", $codes["PUT_UPDATE"]["message"]);
            $code = $codes["PUT_UPDATE"]["code"];
            if ($course_schedule_id > 0) {
                $result = array(
                    'code' => $code,
                    'message' => $message,
                    'data' => array('status' => $code, 'course_schedule_id' => $course_schedule_id)
                );
                return new WP_REST_Response($result, 200);
            } else {
                return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
            }
        } else {
            return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
        }
    }
    public function get_activity($request)
    {
        $codes = error_code_setting();
        $activities = [
            [
                'label' => "Live Class",
                'slug' => "live_class"
            ],
            [
                'label' =>  "Diagnostic Test",
                'slug' => "diagonstic_test"
            ],
            [
                'label' =>  "Assignment",
                'slug' => "assignment"
            ],
            [
                'label' =>  "Sectional Test",
                'slug' => "sectional_test"
            ],
            [
                'label' =>  "Mock Test",
                'slug' => "mock_test"
            ]
        ];
        if (empty($activities)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'Activity Not Found', array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            $result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Activity", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"],  'data' => $activities);
            return new WP_REST_Response($result, 200);
        }
    }
    public function get_activity_permissions_check()
    {
        return true;
    }
    public function create_new_keys_course_new($request)
    {
        return true;
    }
    public function create_new_keys_course_new_permissions_check($request)
    {
        return true;
    }
    
    public function get_course_schedules_es_permissions_check()
    {
        return true;
    }
    /**
     * To migrate course schedule keys into ES document
     */
    public function migrate_course_schedule_es($request)
    {
        global $wpdb;
        $collections = [];
        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id"
            ]
        ];
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" .YN_ES_PREFIX. "course-new/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $results = json_decode($response);
        $sub_cat_data_l = [
            "id" => (int)1,
            "name" => 'name',
            "slug" => 'slug'
        ];
        $sub_cat_data = [
            "id" => (int)1,
            "name" => 'name',
            "slug" => 'slug',
            "sub_cat" => [$sub_cat_data_l]
        ];
        $activty = [
            "label" => 'label',
            "slug" => 'slug'
        ];
        foreach ($results->hits->hits as $result) {
            $course_schedule = [];
            $course_schedule[] = [
                'id' => (int)1,
                'title' => "title",
                "description" => "description",
                "order" => (int)1,
                "duration" => "duration",
                "sub_cat" => $sub_cat_data,
                "activity" => $activty,
                "is_active" => false,
                "is_remove" => false
            ];
        }
        if (!empty($course_schedule)) {
            $course_schedule_n = [
                'course_id' => (int)1,
                'id' => (int)1,
                'course_schedule' => $course_schedule
            ];
            $collections = array(
                'update_event_type' => "course-new",
                'record_id' => (int)8736,
                'course_schedule_detail' => $course_schedule_n
            );
            $curlData = [
                "data" => [
                    'details' => $collections,
                    '@timestamp' => date("Y-m-d H:i:s")
                ]
            ];
            try {
                update_elastic_event($curlData);
            } catch (Exception $e) {
                error_log(" catchhh update_course_schedule_object_while_creating_courses" . json_encode($e));
            }
        }
        return new WP_REST_Response($results, 200);
    }
    public function migrate_course_schedule_es_permissions_check()
    {
        return true;
    }
    public function get_courses_subcategories($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $course_id = $request['course_id'];
        if (empty($course_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Course ID should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }
        $course_categories = wp_get_post_terms($course_id, 'course_category');
        foreach ($course_categories as $k => $categories) {
            if ($categories->parent == 0) {
                $p_cat_id = $categories->term_id;
            }
        }
        if (!empty($p_cat_id)) {
            $terms = get_terms(array(
                'taxonomy' => 'course_category',
                'hide_empty' => 0,
                'parent' => $p_cat_id,
                'orderby' => 'term_id',
                'order' => 'ASC',
                'hierarchical' => 0
            ));
            foreach ($terms as $term) {
                $child_cat_id = $term->term_id;
                $s_terms = get_terms(array(
                    'taxonomy' => 'course_category',
                    'hide_empty' => 0,
                    'parent' => $child_cat_id,
                    'orderby' => 'term_id',
                    'order' => 'ASC',
                    'hierarchical' => 0
                ));
                $subcategory = [];
                foreach ($s_terms as $s_term) {
                    $s_child_cat_id = $s_term->term_id;
                    $subcategory[] = [
                        'id' => $s_child_cat_id,
                        'name' => $s_term->name,
                        'slug' => $s_term->slug
                    ];
                }
                $category[] = [
                    'id' => $child_cat_id,
                    'name' => $term->name,
                    'slug' => $term->slug,
                    'sub_cat' => $subcategory
                ];
            }
        }
        if (empty($category)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'No Data Found', array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            $final_result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Sub Categories", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'count' => count($category),  'data' => $category);

            return new WP_REST_Response($final_result, 200);
        }
    }
    public function get_courses_subcategories_permissions_check($request)
    {
        return true;
    }
    /**
     * To migrate course active enrollments into ES document
     * */
    public function migrate_course_active_enrollments($request)
    {
        global $wpdb;
        $collections = [];
        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id"
            ]
        ];
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" .YN_ES_PREFIX. "course/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $results = json_decode($response);
        foreach ($results->hits->hits as $result) {
            $record_id = $result->_source->data->details->record_id;
            $active_enrollments_count = $wpdb->get_results("SELECT * FROM `wp_enrollment` WHERE `product_db_id` = $record_id AND `enrollment_end_date` >= NOW() AND `enrollment_status` = 'ACTIVE'");

            $active_batches_count = $wpdb->get_row("SELECT count(t1.id) as active_batches FROM wp_batches as t1 left join wp_group_relationships as t2 on (t1.id = t2.batch_id) where t2.product_id = $record_id and t1.end_date > now() and t1.id IN (select batch_id from wp_enrollment where enrollment_status = 'ACTIVE')", ARRAY_A);




            $upcoming_batches_count = $wpdb->get_row("SELECT count(t1.id) as ongoing_batches FROM wp_batches as t1 left join wp_group_relationships as t2 on (t1.id = t2.batch_id) where t2.product_id = $record_id and t1.end_date > now()", ARRAY_A);

            error_log(" --- upcoming_batches_count -- " . "SELECT count(t1.id) as ongoing_batches FROM wp_batches as t1 left join wp_group_relationships as t2 on (t1.id = t2.batch_id) where t2.product_id = $record_id and t1.end_date > now()" . date("Y-m-d H:i:s") . json_encode($upcoming_batches_count) . "\n\n", 3, ABSPATH . "error-logs/migration_course.log");



            $collections = array(
                'update_event_type' => "course",
                'record_id' => (int)$record_id,
                'active_enrollments' => (int)count($active_enrollments_count),
                'active_batches' => $active_batches_count['active_batches'],
                'ongoing_batches' => $upcoming_batches_count['ongoing_batches']
            );
            error_log(" --- 93 collections -- " . date("Y-m-d H:i:s") . json_encode($collections) . "\n\n", 3, ABSPATH . "error-logs/migration_course.log");
            $curlData = [
                "data" => [
                    'details' => $collections,
                    '@timestamp' => date("Y-m-d H:i:s")
                ]
            ];
            try {
                update_elastic_event($curlData);
            } catch (Exception $e) {
                error_log(" catchhh migrate_course_active_enrollments" . json_encode($e));
            }
        }
        return new WP_REST_Response($results, 200);
    }
    public function migrate_course_active_enrollments_permissions_check()
    {
        return true;
    }
    public function migrate_course_active_batches($request)
    {
        global $wpdb;
        // $codes = error_code_setting();//
        $collections = [];
        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id"
            ]
        ];
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" .YN_ES_PREFIX. "course/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $results = json_decode($response);
        foreach ($results->hits->hits as $result) {
            $record_id = $result->_source->data->details->record_id;
            $args = array(
                'post_type'      => "course_schedule",
                'post_status'    => 'publish',
                'order'          => 'DESC',
                'orderby'        => 'ID',
                'posts_per_page' => 1,
                'meta_query' => array(
                    array(
                        'key' => 'course_id',
                        'value' => $record_id,
                        'compare' => '='
                    )
                )
            );

            $the_query = new WP_Query($args);
            $post_id = $the_query->posts[0]->ID;

            if (empty($post_id)) {
                $course_schedule_id = 0;
            } else {
                $course_schedule_id = $post_id;
            }
            $active_enrollments_count = $wpdb->get_results("SELECT * FROM `wp_enrollment` WHERE `product_db_id` = $record_id AND `enrollment_end_date` >= NOW() AND `enrollment_status` = 'ACTIVE'");
            $active_batches_count = $wpdb->get_results("SELECT count(t1.id) as active_batches FROM wp_batches as t1 left join wp_group_relationships as t2 on (t1.id = t2.batch_id) where t2.product_id = $record_id and t1.end_date > now() and t1.id IN (select batch_id from wp_enrollment where enrollment_status = 'ACTIVE')");
            $collections = array(
                'update_event_type' => "course",
                'record_id' => (int)$record_id,
                'active_enrollments' => (int)count($active_enrollments_count),
                'active_batches' => (int)$active_batches_count,
                'course_schedule_id' => (int)$course_schedule_id,
                'successful_students' => (int)1,
                'cancellation_policy' => "",
                'policy_for_instructors' => "",
                'is_enable' => false,
            );
            $curlData = [
                "data" => [
                    'details' => $collections,
                    '@timestamp' => date("Y-m-d H:i:s")
                ]
            ];
            try {
                update_elastic_event($curlData);
            } catch (Exception $e) {
                error_log(" catchhh migrate_course_active_batches" . json_encode($e));
            }
        }
        return new WP_REST_Response($results, 200);
    }
    public function migrate_course_active_batches_permissions_check()
    {
        return true;
    }
    public static function course_schedule_create($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $data = json_decode($request->get_body(), true);
        $course_id = (int)$data['course_id'];
        $course_schedule_data = $data['course_schedule'];

        if (empty($course_id)) {
            return new WP_Error($codes["POST_INSERT_FAIL"]["code"], 'Course ID should not be blank', array('status' => $codes["POST_INSERT_FAIL"]["status"]));
        }

        $post = array();
        $post['post_status'] = 'publish';
        $post['post_type'] = 'course_schedule';
        $post['post_title'] = 'course schedule';

        try {
            // Create Post


            $course_schedule_id = wp_insert_post($post);
            update_post_meta($course_schedule_id, 'course_id', $course_id);
            update_post_meta($course_schedule_id, 'course_schedule_data', $course_schedule_data);
            error_log(" --- 1510 -- " . date("Y-m-d H:i:s") . $course_schedule_id . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");
        } catch (Exception $e) {


            error_log(" --- 1518 notttttttttttt  inserttt  -- " . date("Y-m-d H:i:s") . $course_schedule_id . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");
        }


        // Create the document
        $document = [
            'course_id' => $course_id,
            'id' => $course_schedule_id,
            'course_schedule' => $course_schedule_data
        ];

        error_log(" --- 1537 -- " . date("Y-m-d H:i:s") . json_encode($document) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");

        $es_response =self::updateCourseScheduleInElasticsearch($course_id, $document);

        error_log(" --- 1543 -- " . date("Y-m-d H:i:s") . json_encode($es_response) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");

        if ($es_response === true) {

            error_log(" --- 1548 -- " . date("Y-m-d H:i:s") . json_encode($es_response) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");
            $message = str_replace("[Module_Name]", "Course Schedule", $codes["POST_INSERT"]["message"]);
            $code = $codes["POST_INSERT"]["code"];
            if ($course_schedule_id > 0) {
                $result = array(
                    'code' => $code,
                    'message' => $message,
                    'data' => array('status' => $code, 'course_schedule_id' => $course_schedule_id)
                );
                error_log(" --- 1557 -- " . date("Y-m-d H:i:s") . json_encode($result) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");
                return new WP_REST_Response($result, 200);
            } else {

                error_log(" --- 1561 not inserteddd  -- " . date("Y-m-d H:i:s") . $course_schedule_id . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");
                return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
            }
        } else {

            error_log(" --- 1567 essssss false -- " . date("Y-m-d H:i:s") . json_encode($es_response) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");

            return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
        }
    }
    public function course_schedule_create_new_permissions_check()
    {
        return true;
    }

    public static function updateCourseScheduleInElasticsearch($courseId, $scheduleData)
    {

        $event_type = 'course';  // Replace with your index name
        $documentType = 'course';  // Replace with your document type
        $course_schedule_id = $scheduleData['id'];

        $updateRequest = [
            'script' => [
                'source' => 'if (ctx._source.data == null) { ctx._source.data = [:]; } if (ctx._source.data.details == null) { ctx._source.data.details = [:]; } ctx._source.data.details.course_schedule = params.course_schedule; ctx._source.data.details.course_schedule_id = params.course_schedule_id; ctx._source.data.details.schedule_content = params.schedule_content;',
                'lang' => 'painless',
                'params' => [
                    'course_schedule' => $scheduleData['course_schedule'],
                    'course_schedule_id' => $course_schedule_id, // Replace this with the data you want to update for 'another_key'
                    'schedule_content' => 1 // 
                ]
            ]
        ];
        // return new WP_REST_Response($updateRequest, 200);
        // Send the update request to Elasticsearch
        $curl = curl_init();
        curl_setopt_array($curl, [
            //  CURLOPT_URL => "{$elasticsearchUrl}/{$index}/{$documentType}/{$recordId}/_update",
            CURLOPT_PORT           => ELASTIC_SEARCH_PORT,
            CURLOPT_URL            => ELASTIC_SEARCH_END_URL . "/" . YN_ES_PREFIX . $event_type . "/_doc/" . $documentType . "-" . $courseId . "/_update",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($updateRequest),
            CURLOPT_HTTPHEADER     => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            )
            /*CURLOPT_HTTPHEADER => [
            'Content-Type: application/json'
        ]*/
        ]);
        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);

        error_log(" --- 1633  es updateeeeeee course schedule  -- " . date("Y-m-d H:i:s") . json_encode($response) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");

        // Handle the response
        if ($error) {
            echo "Error updating course schedule: " . $error;
        } else {
            $responseData = json_decode($response, true);

            if (isset($responseData['result']) && $responseData['result'] === 'updated') {

                error_log(" --- 1646  es response  -- " . date("Y-m-d H:i:s") . json_encode($responseData) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");
                return true;
            } else {
                error_log(" --- 1649  es response  -- " . date("Y-m-d H:i:s") . json_encode($responseData) . "\n\n", 3, ABSPATH . "error-logs/course_schedule_test.log");
                return false;
            }
        }
    }

    public static function get_course_schedules_es($request)
    {
        global $wpdb;
        $course_id = (int)$request['course_id'];
        $course_schedule_id = (int)$request['course_schedule_id'];
        $codes = error_code_setting();
        $collections = [];

        // get active org
        // Get the current user
        $user_id = CURRENT_LOGGED_IN_USER_ID;
        $active_org = get_user_meta($user_id, 'active_org', true);
        $org_id = get_post_meta($course_id, 'organization',true);

        $current_userdata       = get_userdata($user_id);
        $current_role           = $current_userdata->roles[0]; //um_org-admin

        if (!empty($current_userdata->roles)) {
            if (in_array('um_org-admin', $current_userdata->roles)) {
                // Check if both are not equal
                if ($org_id !== $active_org) {
                    return new WP_Error($codes["GET_FAIL"]["code"], 'The data does not belong to your current organization. Switch to a different organization and try again.', array('status' => $codes["GET_FAIL"]["status"]));
                }
            }
        }


        if (!empty($course_id) && $course_id != "all") {
            $must[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.record_id" => $course_id
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        if (!empty($course_schedule_id) && $course_schedule_id != "all") {
            $must[] = [
                "nested" => [
                    "path" => "data.details",
                    "query" => [
                        "bool" => [
                            "must" => [
                                [
                                    "match" => [
                                        "data.details.course_schedule_id" => $course_schedule_id
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ];
        }

        $curlPost = [
            "query" => [
                "bool" => [
                    "must" => $must
                ]
            ],
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id",
                "data.details.course_schedule_id",
                "data.details.course_schedule"
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" .YN_ES_PREFIX. "course/_search?size=" . ELASTIC_RECORDS_COUNT,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $results = json_decode($response);
        error_log(" get_course_schedules_es  es response  -- " . date("Y-m-d H:i:s") . json_encode($results) . "\n\n", 3, ABSPATH . "error-logs/get_course_schedules_es.log");


        foreach ($results->hits->hits as $result) {
            $course_schedule_id = (int)$result->_source->data->details->course_schedule_id;
            $course_schedule = $result->_source->data->details->course_schedule;

            $collections = [
                "id" => $course_schedule_id,
                "course_id" => $course_id,
                "course_schedule" => $course_schedule
            ];

            if(empty($course_schedule)) {
                return new WP_Error($codes["GET_FAIL"]["code"], 'No Data Found', array('status' => $codes["GET_FAIL"]["status"]));
            }
        }

        error_log(" get_course_schedules_es collections  -- " . date("Y-m-d H:i:s") . json_encode($collections) . "\n\n", 3, ABSPATH . "error-logs/get_course_schedules_es.log");

        if (empty($collections)) {
            return new WP_Error($codes["GET_FAIL"]["code"], 'No Data Found', array('status' => $codes["GET_FAIL"]["status"]));
        } else {
            $final_result = array('code' => $codes["GET_SUCCESS"]["code"], 'message' => str_replace("[Module_Name]", "Course Schedule", $codes["GET_SUCCESS"]["message"]), 'status' => $codes["GET_SUCCESS"]["status"], 'data' => $collections);
        }
        return new WP_REST_Response($final_result, 200);
    }
    public function get_course_schedules_es_test_permissions_check()
    {
        return true;
    }
    public function update_old_schedule_db_es_permissions_check()
    {
        return true;
    }
    public function update_old_schedule_db_es($request)
    {
        global $wpdb;
        $codes = error_code_setting();
        $limit = (int) $request['limit'];
        $offset = (int) $request['offset'];
        $collections = [];
        $curlPost = [
            "_source" => [
                "inner_hits",
                "type",
                "data.details.record_id",
                "data.details.course_schedule_id",
                "data.details.course_schedule"
            ]
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_PORT => ELASTIC_SEARCH_PORT,
            CURLOPT_URL => ELASTIC_SEARCH_END_URL . "/" .YN_ES_PREFIX. "course/_search?from=" . $offset . "&size=" . $limit,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => json_encode($curlPost, JSON_UNESCAPED_SLASHES),
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . ELASTIC_SEARCH_BASIC_AUTHORIZATION,
                "cache-control: no-cache",
                "content-type: application/json"
            ),
        ));
        $response = curl_exec($curl);
        curl_close($curl);
        $results = json_decode($response);
        foreach ($results->hits->hits as $result) {
            $record_id = (int)$result->_source->data->details->record_id;



            $args = array(
                'post_type'      => "course_schedule",
                'post_status'    => 'publish',
                'order'          => 'DESC',
                'orderby'        => 'date',
                'posts_per_page' => 1,
                'meta_query' => array(
                    array(
                        'key' => 'course_id',
                        'value' => $record_id,
                        'compare' => 'IN'
                    )
                )
            );
            $the_query = new WP_Query($args);
            if ($the_query->have_posts()) {
                while ($the_query->have_posts()) {
                    $the_query->the_post();
                    $post_id = get_the_ID();
                    $course_id = get_post_meta($post_id, 'course_id', true);
                    $course_schedule_data = get_post_meta($post_id, 'course_schedule_data', true);
                    if (!empty($course_schedule_data)) {
                        // Create the document
                        $document = [
                            'course_id' => $course_id,
                            'id' => $post_id,
                            'course_schedule' => $course_schedule_data
                        ];

                        $es_response = $this->updateCourseScheduleInElasticsearch($course_id, $document);
                        if ($es_response === true) {
                            $collections[] = $course_id;
                        }
                    }
                }
            }
        }

        if (!empty($collections)) {
            return new WP_REST_Response($collections, 200);
        } else {
            return new WP_Error($codes["API_FAIL"]["code"], $codes["API_FAIL"]["message"], array('status' => $codes["API_FAIL"]["status"]));
        }
    }
}
