/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function e(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function t(e,t,n,r){for(var i=-1,a=null==e?0:e.length;++i<a;){var o=e[i];t(r,o,n(o),e)}return r}function n(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function r(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function i(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function a(e,t){for(var n=-1,r=null==e?0:e.length,i=0,a=[];++n<r;){var o=e[n];t(o,n,e)&&(a[i++]=o)}return a}function o(e,t){return!(null==e||!e.length)&&v(e,t,0)>-1}function s(e,t,n){for(var r=-1,i=null==e?0:e.length;++r<i;)if(n(t,e[r]))return!0;return!1}function l(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}function u(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}function c(e,t,n,r){var i=-1,a=null==e?0:e.length;for(r&&a&&(n=e[++i]);++i<a;)n=t(n,e[i],i,e);return n}function d(e,t,n,r){var i=null==e?0:e.length;for(r&&i&&(n=e[--i]);i--;)n=t(n,e[i],i,e);return n}function f(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}function p(e){return e.match(Qe)||[]}function h(e,t,n){var r;return n(e,function(e,n,i){if(t(e,n,i))return r=n,!1}),r}function m(e,t,n,r){for(var i=e.length,a=n+(r?1:-1);r?a--:++a<i;)if(t(e[a],a,e))return a;return-1}function v(e,t,n){return t==t?function(e,t,n){for(var r=n-1,i=e.length;++r<i;)if(e[r]===t)return r;return-1}(e,t,n):m(e,y,n)}function g(e,t,n,r){for(var i=n-1,a=e.length;++i<a;)if(r(e[i],t))return i;return-1}function y(e){return e!=e}function _(e,t){var n=null==e?0:e.length;return n?S(e,t)/n:X}function b(e){return function(t){return null==t?F:t[e]}}function w(e){return function(t){return null==e?F:e[t]}}function k(e,t,n,r,i){return i(e,function(e,i,a){n=r?(r=!1,e):t(n,e,i,a)}),n}function S(e,t){for(var n,r=-1,i=e.length;++r<i;){var a=t(e[r]);a!==F&&(n=n===F?a:n+a)}return n}function x(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function C(e){return e?e.slice(0,P(e)+1).replace(Ge,""):e}function D(e){return function(t){return e(t)}}function O(e,t){return l(t,function(t){return e[t]})}function A(e,t){return e.has(t)}function M(e,t){for(var n=-1,r=e.length;++n<r&&v(t,e[n],0)>-1;);return n}function I(e,t){for(var n=e.length;n--&&v(t,e[n],0)>-1;);return n}function T(e){return"\\"+Jt[e]}function R(e){return zt.test(e)}function $(e){return Ht.test(e)}function Y(e){var t=-1,n=Array(e.size);return e.forEach(function(e,r){n[++t]=[r,e]}),n}function N(e,t){return function(n){return e(t(n))}}function L(e,t){for(var n=-1,r=e.length,i=0,a=[];++n<r;){var o=e[n];o!==t&&o!==H||(e[n]=H,a[i++]=n)}return a}function E(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}function j(e){return R(e)?function(e){for(var t=Ft.lastIndex=0;Ft.test(e);)++t;return t}(e):hn(e)}function U(e){return R(e)?function(e){return e.match(Ft)||[]}(e):function(e){return e.split("")}(e)}function P(e){for(var t=e.length;t--&&Be.test(e.charAt(t)););return t}function W(e){return e.match(Vt)||[]}var F,V="Expected a function",z="__lodash_hash_undefined__",H="__lodash_placeholder__",G=16,B=32,Z=64,q=128,J=256,Q=1/0,K=9007199254740991,X=NaN,ee=4294967295,te=ee-1,ne=ee>>>1,re=[["ary",q],["bind",1],["bindKey",2],["curry",8],["curryRight",G],["flip",512],["partial",B],["partialRight",Z],["rearg",J]],ie="[object Arguments]",ae="[object Array]",oe="[object Boolean]",se="[object Date]",le="[object Error]",ue="[object Function]",ce="[object GeneratorFunction]",de="[object Map]",fe="[object Number]",pe="[object Object]",he="[object Promise]",me="[object RegExp]",ve="[object Set]",ge="[object String]",ye="[object Symbol]",_e="[object WeakMap]",be="[object ArrayBuffer]",we="[object DataView]",ke="[object Float32Array]",Se="[object Float64Array]",xe="[object Int8Array]",Ce="[object Int16Array]",De="[object Int32Array]",Oe="[object Uint8Array]",Ae="[object Uint8ClampedArray]",Me="[object Uint16Array]",Ie="[object Uint32Array]",Te=/\b__p \+= '';/g,Re=/\b(__p \+=) '' \+/g,$e=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Ye=/&(?:amp|lt|gt|quot|#39);/g,Ne=/[&<>"']/g,Le=RegExp(Ye.source),Ee=RegExp(Ne.source),je=/<%-([\s\S]+?)%>/g,Ue=/<%([\s\S]+?)%>/g,Pe=/<%=([\s\S]+?)%>/g,We=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Fe=/^\w*$/,Ve=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ze=/[\\^$.*+?()[\]{}|]/g,He=RegExp(ze.source),Ge=/^\s+/,Be=/\s/,Ze=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,qe=/\{\n\/\* \[wrapped with (.+)\] \*/,Je=/,? & /,Qe=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Ke=/[()=,{}\[\]\/\s]/,Xe=/\\(\\)?/g,et=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,tt=/\w*$/,nt=/^[-+]0x[0-9a-f]+$/i,rt=/^0b[01]+$/i,it=/^\[object .+?Constructor\]$/,at=/^0o[0-7]+$/i,ot=/^(?:0|[1-9]\d*)$/,st=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,lt=/($^)/,ut=/['\n\r\u2028\u2029\\]/g,ct="\\ud800-\\udfff",dt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",ft="\\u2700-\\u27bf",pt="a-z\\xdf-\\xf6\\xf8-\\xff",ht="A-Z\\xc0-\\xd6\\xd8-\\xde",mt="\\ufe0e\\ufe0f",vt="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",gt="['’]",yt="["+ct+"]",_t="["+vt+"]",bt="["+dt+"]",wt="\\d+",kt="["+ft+"]",St="["+pt+"]",xt="[^"+ct+vt+wt+ft+pt+ht+"]",Ct="\\ud83c[\\udffb-\\udfff]",Dt="[^"+ct+"]",Ot="(?:\\ud83c[\\udde6-\\uddff]){2}",At="[\\ud800-\\udbff][\\udc00-\\udfff]",Mt="["+ht+"]",It="\\u200d",Tt="(?:"+St+"|"+xt+")",Rt="(?:"+Mt+"|"+xt+")",$t="(?:['’](?:d|ll|m|re|s|t|ve))?",Yt="(?:['’](?:D|LL|M|RE|S|T|VE))?",Nt="(?:"+bt+"|"+Ct+")"+"?",Lt="["+mt+"]?",Et=Lt+Nt+("(?:"+It+"(?:"+[Dt,Ot,At].join("|")+")"+Lt+Nt+")*"),jt="(?:"+[kt,Ot,At].join("|")+")"+Et,Ut="(?:"+[Dt+bt+"?",bt,Ot,At,yt].join("|")+")",Pt=RegExp(gt,"g"),Wt=RegExp(bt,"g"),Ft=RegExp(Ct+"(?="+Ct+")|"+Ut+Et,"g"),Vt=RegExp([Mt+"?"+St+"+"+$t+"(?="+[_t,Mt,"$"].join("|")+")",Rt+"+"+Yt+"(?="+[_t,Mt+Tt,"$"].join("|")+")",Mt+"?"+Tt+"+"+$t,Mt+"+"+Yt,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",wt,jt].join("|"),"g"),zt=RegExp("["+It+ct+dt+mt+"]"),Ht=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Gt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Bt=-1,Zt={};Zt[ke]=Zt[Se]=Zt[xe]=Zt[Ce]=Zt[De]=Zt[Oe]=Zt[Ae]=Zt[Me]=Zt[Ie]=!0,Zt[ie]=Zt[ae]=Zt[be]=Zt[oe]=Zt[we]=Zt[se]=Zt[le]=Zt[ue]=Zt[de]=Zt[fe]=Zt[pe]=Zt[me]=Zt[ve]=Zt[ge]=Zt[_e]=!1;var qt={};qt[ie]=qt[ae]=qt[be]=qt[we]=qt[oe]=qt[se]=qt[ke]=qt[Se]=qt[xe]=qt[Ce]=qt[De]=qt[de]=qt[fe]=qt[pe]=qt[me]=qt[ve]=qt[ge]=qt[ye]=qt[Oe]=qt[Ae]=qt[Me]=qt[Ie]=!0,qt[le]=qt[ue]=qt[_e]=!1;var Jt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Qt=parseFloat,Kt=parseInt,Xt="object"==typeof global&&global&&global.Object===Object&&global,en="object"==typeof self&&self&&self.Object===Object&&self,tn=Xt||en||Function("return this")(),nn="object"==typeof exports&&exports&&!exports.nodeType&&exports,rn=nn&&"object"==typeof module&&module&&!module.nodeType&&module,an=rn&&rn.exports===nn,on=an&&Xt.process,sn=function(){try{var e=rn&&rn.require&&rn.require("util").types;return e||on&&on.binding&&on.binding("util")}catch(e){}}(),ln=sn&&sn.isArrayBuffer,un=sn&&sn.isDate,cn=sn&&sn.isMap,dn=sn&&sn.isRegExp,fn=sn&&sn.isSet,pn=sn&&sn.isTypedArray,hn=b("length"),mn=w({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),vn=w({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),gn=w({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),yn=function w(Be){function Qe(e){if(Pi(e)&&!Is(e)&&!(e instanceof ft)){if(e instanceof dt)return e;if(Ma.call(e,"__wrapped__"))return fi(e)}return new dt(e)}function ct(){}function dt(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=F}function ft(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ee,this.__views__=[]}function pt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function ht(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function mt(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function vt(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new mt;++t<n;)this.add(e[t])}function gt(e){this.size=(this.__data__=new ht(e)).size}function yt(e,t){var n=Is(e),r=!n&&Ms(e),i=!n&&!r&&Rs(e),a=!n&&!r&&!i&&Es(e),o=n||r||i||a,s=o?x(e.length,ka):[],l=s.length;for(var u in e)!t&&!Ma.call(e,u)||o&&("length"==u||i&&("offset"==u||"parent"==u)||a&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||Jr(u,l))||s.push(u);return s}function _t(e){var t=e.length;return t?e[$n(0,t-1)]:F}function bt(e,t){return li(cr(e),Mt(t,0,e.length))}function wt(e){return li(cr(e))}function kt(e,t,n){(n===F||Ri(e[t],n))&&(n!==F||t in e)||Ot(e,t,n)}function St(e,t,n){var r=e[t];Ma.call(e,t)&&Ri(r,n)&&(n!==F||t in e)||Ot(e,t,n)}function xt(e,t){for(var n=e.length;n--;)if(Ri(e[n][0],t))return n;return-1}function Ct(e,t,n,r){return Ao(e,function(e,i,a){t(r,e,n(e),a)}),r}function Dt(e,t){return e&&dr(t,ea(t),e)}function Ot(e,t,n){"__proto__"==t&&Ba?Ba(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function At(e,t){for(var n=-1,r=t.length,i=ma(r),a=null==e;++n<r;)i[n]=a?F:Ki(e,t[n]);return i}function Mt(e,t,n){return e==e&&(n!==F&&(e=e<=n?e:n),t!==F&&(e=e>=t?e:t)),e}function It(e,t,r,i,a,o){var s,l=1&t,u=2&t,c=4&t;if(r&&(s=a?r(e,i,a,o):r(e)),s!==F)return s;if(!Ui(e))return e;var d=Is(e);if(d){if(s=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Ma.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(e),!l)return cr(e,s)}else{var f=Po(e),p=f==ue||f==ce;if(Rs(e))return ir(e,l);if(f==pe||f==ie||p&&!a){if(s=u||p?{}:Zr(e),!l)return u?function(e,t){return dr(e,Uo(e),t)}(e,function(e,t){return e&&dr(t,ta(t),e)}(s,e)):function(e,t){return dr(e,jo(e),t)}(e,Dt(s,e))}else{if(!qt[f])return a?e:{};s=function(e,t,n){var r=e.constructor;switch(t){case be:return ar(e);case oe:case se:return new r(+e);case we:return function(e,t){return new e.constructor(t?ar(e.buffer):e.buffer,e.byteOffset,e.byteLength)}(e,n);case ke:case Se:case xe:case Ce:case De:case Oe:case Ae:case Me:case Ie:return or(e,n);case de:return new r;case fe:case ge:return new r(e);case me:return function(e){var t=new e.constructor(e.source,tt.exec(e));return t.lastIndex=e.lastIndex,t}(e);case ve:return new r;case ye:return function(e){return Co?ba(Co.call(e)):{}}(e)}}(e,f,l)}}o||(o=new gt);var h=o.get(e);if(h)return h;o.set(e,s),Ls(e)?e.forEach(function(n){s.add(It(n,t,r,n,e,o))}):Ys(e)&&e.forEach(function(n,i){s.set(i,It(n,t,r,i,e,o))});var m=d?F:(c?u?Pr:Ur:u?ta:ea)(e);return n(m||e,function(n,i){m&&(n=e[i=n]),St(s,i,It(n,t,r,i,e,o))}),s}function Tt(e,t,n){var r=n.length;if(null==e)return!r;for(e=ba(e);r--;){var i=n[r],a=t[i],o=e[i];if(o===F&&!(i in e)||!a(o))return!1}return!0}function Rt(e,t,n){if("function"!=typeof e)throw new Sa(V);return Vo(function(){e.apply(F,n)},t)}function $t(e,t,n,r){var i=-1,a=o,u=!0,c=e.length,d=[],f=t.length;if(!c)return d;n&&(t=l(t,D(n))),r?(a=s,u=!1):t.length>=200&&(a=A,u=!1,t=new vt(t));e:for(;++i<c;){var p=e[i],h=null==n?p:n(p);if(p=r||0!==p?p:0,u&&h==h){for(var m=f;m--;)if(t[m]===h)continue e;d.push(p)}else a(t,h,r)||d.push(p)}return d}function Yt(e,t){var n=!0;return Ao(e,function(e,r,i){return n=!!t(e,r,i)}),n}function Nt(e,t,n){for(var r=-1,i=e.length;++r<i;){var a=e[r],o=t(a);if(null!=o&&(s===F?o==o&&!zi(o):n(o,s)))var s=o,l=a}return l}function Lt(e,t){var n=[];return Ao(e,function(e,r,i){t(e,r,i)&&n.push(e)}),n}function Et(e,t,n,r,i){var a=-1,o=e.length;for(n||(n=qr),i||(i=[]);++a<o;){var s=e[a];t>0&&n(s)?t>1?Et(s,t-1,n,r,i):u(i,s):r||(i[i.length]=s)}return i}function jt(e,t){return e&&Io(e,t,ea)}function Ut(e,t){return e&&To(e,t,ea)}function Ft(e,t){return a(t,function(t){return Li(e[t])})}function Vt(e,t){for(var n=0,r=(t=nr(t,e)).length;null!=e&&n<r;)e=e[ui(t[n++])];return n&&n==r?e:F}function zt(e,t,n){var r=t(e);return Is(e)?r:u(r,n(e))}function Ht(e){return null==e?e===F?"[object Undefined]":"[object Null]":Ga&&Ga in ba(e)?function(e){var t=Ma.call(e,Ga),n=e[Ga];try{e[Ga]=F;var r=!0}catch(e){}var i=Ra.call(e);return r&&(t?e[Ga]=n:delete e[Ga]),i}(e):function(e){return Ra.call(e)}(e)}function Jt(e,t){return e>t}function Xt(e,t){return null!=e&&Ma.call(e,t)}function en(e,t){return null!=e&&t in ba(e)}function nn(e,t,n){for(var r=n?s:o,i=e[0].length,a=e.length,u=a,c=ma(a),d=1/0,f=[];u--;){var p=e[u];u&&t&&(p=l(p,D(t))),d=ao(p.length,d),c[u]=!n&&(t||i>=120&&p.length>=120)?new vt(u&&p):F}p=e[0];var h=-1,m=c[0];e:for(;++h<i&&f.length<d;){var v=p[h],g=t?t(v):v;if(v=n||0!==v?v:0,!(m?A(m,g):r(f,g,n))){for(u=a;--u;){var y=c[u];if(!(y?A(y,g):r(e[u],g,n)))continue e}m&&m.push(g),f.push(v)}}return f}function rn(t,n,r){var i=null==(t=ii(t,n=nr(n,t)))?t:t[ui(gi(n))];return null==i?F:e(i,t,r)}function on(e){return Pi(e)&&Ht(e)==ie}function sn(e,t,n,r,i){return e===t||(null==e||null==t||!Pi(e)&&!Pi(t)?e!=e&&t!=t:function(e,t,n,r,i,a){var o=Is(e),s=Is(t),l=o?ae:Po(e),u=s?ae:Po(t);l=l==ie?pe:l,u=u==ie?pe:u;var c=l==pe,d=u==pe,f=l==u;if(f&&Rs(e)){if(!Rs(t))return!1;o=!0,c=!1}if(f&&!c)return a||(a=new gt),o||Es(e)?Er(e,t,n,r,i,a):function(e,t,n,r,i,a,o){switch(n){case we:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case be:return!(e.byteLength!=t.byteLength||!a(new ja(e),new ja(t)));case oe:case se:case fe:return Ri(+e,+t);case le:return e.name==t.name&&e.message==t.message;case me:case ge:return e==t+"";case de:var s=Y;case ve:var l=1&r;if(s||(s=E),e.size!=t.size&&!l)return!1;var u=o.get(e);if(u)return u==t;r|=2,o.set(e,t);var c=Er(s(e),s(t),r,i,a,o);return o.delete(e),c;case ye:if(Co)return Co.call(e)==Co.call(t)}return!1}(e,t,l,n,r,i,a);if(!(1&n)){var p=c&&Ma.call(e,"__wrapped__"),h=d&&Ma.call(t,"__wrapped__");if(p||h){var m=p?e.value():e,v=h?t.value():t;return a||(a=new gt),i(m,v,n,r,a)}}return!!f&&(a||(a=new gt),function(e,t,n,r,i,a){var o=1&n,s=Ur(e),l=s.length;if(l!=Ur(t).length&&!o)return!1;for(var u=l;u--;){var c=s[u];if(!(o?c in t:Ma.call(t,c)))return!1}var d=a.get(e),f=a.get(t);if(d&&f)return d==t&&f==e;var p=!0;a.set(e,t),a.set(t,e);for(var h=o;++u<l;){var m=e[c=s[u]],v=t[c];if(r)var g=o?r(v,m,c,t,e,a):r(m,v,c,e,t,a);if(!(g===F?m===v||i(m,v,n,r,a):g)){p=!1;break}h||(h="constructor"==c)}if(p&&!h){var y=e.constructor,_=t.constructor;y!=_&&"constructor"in e&&"constructor"in t&&!("function"==typeof y&&y instanceof y&&"function"==typeof _&&_ instanceof _)&&(p=!1)}return a.delete(e),a.delete(t),p}(e,t,n,r,i,a))}(e,t,n,r,sn,i))}function hn(e,t,n,r){var i=n.length,a=i,o=!r;if(null==e)return!a;for(e=ba(e);i--;){var s=n[i];if(o&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<a;){var l=(s=n[i])[0],u=e[l],c=s[1];if(o&&s[2]){if(u===F&&!(l in e))return!1}else{var d=new gt;if(r)var f=r(u,c,l,e,t,d);if(!(f===F?sn(c,u,3,r,d):f))return!1}}return!0}function _n(e){return!(!Ui(e)||function(e){return!!Ta&&Ta in e}(e))&&(Li(e)?Na:it).test(ci(e))}function bn(e){return"function"==typeof e?e:null==e?la:"object"==typeof e?Is(e)?Dn(e[0],e[1]):Cn(e):fa(e)}function wn(e){if(!ei(e))return ro(e);var t=[];for(var n in ba(e))Ma.call(e,n)&&"constructor"!=n&&t.push(n);return t}function kn(e){if(!Ui(e))return function(e){var t=[];if(null!=e)for(var n in ba(e))t.push(n);return t}(e);var t=ei(e),n=[];for(var r in e)("constructor"!=r||!t&&Ma.call(e,r))&&n.push(r);return n}function Sn(e,t){return e<t}function xn(e,t){var n=-1,r=$i(e)?ma(e.length):[];return Ao(e,function(e,i,a){r[++n]=t(e,i,a)}),r}function Cn(e){var t=Hr(e);return 1==t.length&&t[0][2]?ni(t[0][0],t[0][1]):function(n){return n===e||hn(n,e,t)}}function Dn(e,t){return Kr(e)&&ti(t)?ni(ui(e),t):function(n){var r=Ki(n,e);return r===F&&r===t?Xi(n,e):sn(t,r,3)}}function On(e,t,n,r,i){e!==t&&Io(t,function(a,o){if(i||(i=new gt),Ui(a))!function(e,t,n,r,i,a,o){var s=ai(e,n),l=ai(t,n),u=o.get(l);if(u)return kt(e,n,u),F;var c=a?a(s,l,n+"",e,t,o):F,d=c===F;if(d){var f=Is(l),p=!f&&Rs(l),h=!f&&!p&&Es(l);c=l,f||p||h?Is(s)?c=s:Yi(s)?c=cr(s):p?(d=!1,c=ir(l,!0)):h?(d=!1,c=or(l,!0)):c=[]:Fi(l)||Ms(l)?(c=s,Ms(s)?c=Ji(s):Ui(s)&&!Li(s)||(c=Zr(l))):d=!1}d&&(o.set(l,c),i(c,l,r,a,o),o.delete(l)),kt(e,n,c)}(e,t,o,n,On,r,i);else{var s=r?r(ai(e,o),a,o+"",e,t,i):F;s===F&&(s=a),kt(e,o,s)}},ta)}function An(e,t){var n=e.length;if(n)return Jr(t+=t<0?n:0,n)?e[t]:F}function Mn(e,t,n){t=t.length?l(t,function(e){return Is(e)?function(t){return Vt(t,1===e.length?e[0]:e)}:e}):[la];var r=-1;return t=l(t,D(Vr())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(xn(e,function(e,n,i){return{criteria:l(t,function(t){return t(e)}),index:++r,value:e}}),function(e,t){return function(e,t,n){for(var r=-1,i=e.criteria,a=t.criteria,o=i.length,s=n.length;++r<o;){var l=sr(i[r],a[r]);if(l)return r>=s?l:l*("desc"==n[r]?-1:1)}return e.index-t.index}(e,t,n)})}function In(e,t,n){for(var r=-1,i=t.length,a={};++r<i;){var o=t[r],s=Vt(e,o);n(s,o)&&jn(a,nr(o,e),s)}return a}function Tn(e,t,n,r){var i=r?g:v,a=-1,o=t.length,s=e;for(e===t&&(t=cr(t)),n&&(s=l(e,D(n)));++a<o;)for(var u=0,c=t[a],d=n?n(c):c;(u=i(s,d,u,r))>-1;)s!==e&&Va.call(s,u,1),Va.call(e,u,1);return e}function Rn(e,t){for(var n=e?t.length:0,r=n-1;n--;){var i=t[n];if(n==r||i!==a){var a=i;Jr(i)?Va.call(e,i,1):Zn(e,i)}}return e}function $n(e,t){return e+Ka(lo()*(t-e+1))}function Yn(e,t){var n="";if(!e||t<1||t>K)return n;do{t%2&&(n+=e),(t=Ka(t/2))&&(e+=e)}while(t);return n}function Nn(e,t){return zo(ri(e,t,la),e+"")}function Ln(e){return _t(ra(e))}function En(e,t){var n=ra(e);return li(n,Mt(t,0,n.length))}function jn(e,t,n,r){if(!Ui(e))return e;for(var i=-1,a=(t=nr(t,e)).length,o=a-1,s=e;null!=s&&++i<a;){var l=ui(t[i]),u=n;if("__proto__"===l||"constructor"===l||"prototype"===l)return e;if(i!=o){var c=s[l];(u=r?r(c,l,s):F)===F&&(u=Ui(c)?c:Jr(t[i+1])?[]:{})}St(s,l,u),s=s[l]}return e}function Un(e){return li(ra(e))}function Pn(e,t,n){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var a=ma(i);++r<i;)a[r]=e[r+t];return a}function Wn(e,t){var n;return Ao(e,function(e,r,i){return!(n=t(e,r,i))}),!!n}function Fn(e,t,n){var r=0,i=null==e?r:e.length;if("number"==typeof t&&t==t&&i<=ne){for(;r<i;){var a=r+i>>>1,o=e[a];null!==o&&!zi(o)&&(n?o<=t:o<t)?r=a+1:i=a}return i}return Vn(e,t,la,n)}function Vn(e,t,n,r){var i=0,a=null==e?0:e.length;if(0===a)return 0;for(var o=(t=n(t))!=t,s=null===t,l=zi(t),u=t===F;i<a;){var c=Ka((i+a)/2),d=n(e[c]),f=d!==F,p=null===d,h=d==d,m=zi(d);if(o)var v=r||h;else v=u?h&&(r||f):s?h&&f&&(r||!p):l?h&&f&&!p&&(r||!m):!p&&!m&&(r?d<=t:d<t);v?i=c+1:a=c}return ao(a,te)}function zn(e,t){for(var n=-1,r=e.length,i=0,a=[];++n<r;){var o=e[n],s=t?t(o):o;if(!n||!Ri(s,l)){var l=s;a[i++]=0===o?0:o}}return a}function Hn(e){return"number"==typeof e?e:zi(e)?X:+e}function Gn(e){if("string"==typeof e)return e;if(Is(e))return l(e,Gn)+"";if(zi(e))return Do?Do.call(e):"";var t=e+"";return"0"==t&&1/e==-Q?"-0":t}function Bn(e,t,n){var r=-1,i=o,a=e.length,l=!0,u=[],c=u;if(n)l=!1,i=s;else if(a>=200){var d=t?null:Lo(e);if(d)return E(d);l=!1,i=A,c=new vt}else c=t?[]:u;e:for(;++r<a;){var f=e[r],p=t?t(f):f;if(f=n||0!==f?f:0,l&&p==p){for(var h=c.length;h--;)if(c[h]===p)continue e;t&&c.push(p),u.push(f)}else i(c,p,n)||(c!==u&&c.push(p),u.push(f))}return u}function Zn(e,t){return null==(e=ii(e,t=nr(t,e)))||delete e[ui(gi(t))]}function qn(e,t,n,r){return jn(e,t,n(Vt(e,t)),r)}function Jn(e,t,n,r){for(var i=e.length,a=r?i:-1;(r?a--:++a<i)&&t(e[a],a,e););return n?Pn(e,r?0:a,r?a+1:i):Pn(e,r?a+1:0,r?i:a)}function Qn(e,t){var n=e;return n instanceof ft&&(n=n.value()),c(t,function(e,t){return t.func.apply(t.thisArg,u([e],t.args))},n)}function Kn(e,t,n){var r=e.length;if(r<2)return r?Bn(e[0]):[];for(var i=-1,a=ma(r);++i<r;)for(var o=e[i],s=-1;++s<r;)s!=i&&(a[i]=$t(a[i]||o,e[s],t,n));return Bn(Et(a,1),t,n)}function Xn(e,t,n){for(var r=-1,i=e.length,a=t.length,o={};++r<i;)n(o,e[r],r<a?t[r]:F);return o}function er(e){return Yi(e)?e:[]}function tr(e){return"function"==typeof e?e:la}function nr(e,t){return Is(e)?e:Kr(e,t)?[e]:Ho(Qi(e))}function rr(e,t,n){var r=e.length;return n=n===F?r:n,!t&&n>=r?e:Pn(e,t,n)}function ir(e,t){if(t)return e.slice();var n=e.length,r=Ua?Ua(n):new e.constructor(n);return e.copy(r),r}function ar(e){var t=new e.constructor(e.byteLength);return new ja(t).set(new ja(e)),t}function or(e,t){return new e.constructor(t?ar(e.buffer):e.buffer,e.byteOffset,e.length)}function sr(e,t){if(e!==t){var n=e!==F,r=null===e,i=e==e,a=zi(e),o=t!==F,s=null===t,l=t==t,u=zi(t);if(!s&&!u&&!a&&e>t||a&&o&&l&&!s&&!u||r&&o&&l||!n&&l||!i)return 1;if(!r&&!a&&!u&&e<t||u&&n&&i&&!r&&!a||s&&n&&i||!o&&i||!l)return-1}return 0}function lr(e,t,n,r){for(var i=-1,a=e.length,o=n.length,s=-1,l=t.length,u=io(a-o,0),c=ma(l+u),d=!r;++s<l;)c[s]=t[s];for(;++i<o;)(d||i<a)&&(c[n[i]]=e[i]);for(;u--;)c[s++]=e[i++];return c}function ur(e,t,n,r){for(var i=-1,a=e.length,o=-1,s=n.length,l=-1,u=t.length,c=io(a-s,0),d=ma(c+u),f=!r;++i<c;)d[i]=e[i];for(var p=i;++l<u;)d[p+l]=t[l];for(;++o<s;)(f||i<a)&&(d[p+n[o]]=e[i++]);return d}function cr(e,t){var n=-1,r=e.length;for(t||(t=ma(r));++n<r;)t[n]=e[n];return t}function dr(e,t,n,r){var i=!n;n||(n={});for(var a=-1,o=t.length;++a<o;){var s=t[a],l=r?r(n[s],e[s],s,n,e):F;l===F&&(l=e[s]),i?Ot(n,s,l):St(n,s,l)}return n}function fr(e,n){return function(r,i){var a=Is(r)?t:Ct,o=n?n():{};return a(r,e,Vr(i,2),o)}}function pr(e){return Nn(function(t,n){var r=-1,i=n.length,a=i>1?n[i-1]:F,o=i>2?n[2]:F;for(a=e.length>3&&"function"==typeof a?(i--,a):F,o&&Qr(n[0],n[1],o)&&(a=i<3?F:a,i=1),t=ba(t);++r<i;){var s=n[r];s&&e(t,s,r,a)}return t})}function hr(e,t){return function(n,r){if(null==n)return n;if(!$i(n))return e(n,r);for(var i=n.length,a=t?i:-1,o=ba(n);(t?a--:++a<i)&&!1!==r(o[a],a,o););return n}}function mr(e){return function(t,n,r){for(var i=-1,a=ba(t),o=r(t),s=o.length;s--;){var l=o[e?s:++i];if(!1===n(a[l],l,a))break}return t}}function vr(e){return function(t){var n=R(t=Qi(t))?U(t):F,r=n?n[0]:t.charAt(0),i=n?rr(n,1).join(""):t.slice(1);return r[e]()+i}}function gr(e){return function(t){return c(oa(aa(t).replace(Pt,"")),e,"")}}function yr(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Oo(e.prototype),r=e.apply(n,t);return Ui(r)?r:n}}function _r(t,n,r){var i=yr(t);return function a(){for(var o=arguments.length,s=ma(o),l=o,u=Fr(a);l--;)s[l]=arguments[l];var c=o<3&&s[0]!==u&&s[o-1]!==u?[]:L(s,u);return(o-=c.length)<r?Ir(t,n,kr,a.placeholder,F,s,c,F,F,r-o):e(this&&this!==tn&&this instanceof a?i:t,this,s)}}function br(e){return function(t,n,r){var i=ba(t);if(!$i(t)){var a=Vr(n,3);t=ea(t),n=function(e){return a(i[e],e,i)}}var o=e(t,n,r);return o>-1?i[a?t[o]:o]:F}}function wr(e){return jr(function(t){var n=t.length,r=n,i=dt.prototype.thru;for(e&&t.reverse();r--;){var a=t[r];if("function"!=typeof a)throw new Sa(V);if(i&&!o&&"wrapper"==Wr(a))var o=new dt([],!0)}for(r=o?r:n;++r<n;){var s=Wr(a=t[r]),l="wrapper"==s?Eo(a):F;o=l&&Xr(l[0])&&424==l[1]&&!l[4].length&&1==l[9]?o[Wr(l[0])].apply(o,l[3]):1==a.length&&Xr(a)?o[s]():o.thru(a)}return function(){var e=arguments,r=e[0];if(o&&1==e.length&&Is(r))return o.plant(r).value();for(var i=0,a=n?t[i].apply(this,e):r;++i<n;)a=t[i].call(this,a);return a}})}function kr(e,t,n,r,i,a,o,s,l,u){var c=t&q,d=1&t,f=2&t,p=24&t,h=512&t,m=f?F:yr(e);return function v(){for(var g=arguments.length,y=ma(g),_=g;_--;)y[_]=arguments[_];if(p)var b=Fr(v),w=function(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}(y,b);if(r&&(y=lr(y,r,i,p)),a&&(y=ur(y,a,o,p)),g-=w,p&&g<u)return Ir(e,t,kr,v.placeholder,n,y,L(y,b),s,l,u-g);var k=d?n:this,S=f?k[e]:e;return g=y.length,s?y=function(e,t){for(var n=e.length,r=ao(t.length,n),i=cr(e);r--;){var a=t[r];e[r]=Jr(a,n)?i[a]:F}return e}(y,s):h&&g>1&&y.reverse(),c&&l<g&&(y.length=l),this&&this!==tn&&this instanceof v&&(S=m||yr(S)),S.apply(k,y)}}function Sr(e,t){return function(n,r){return function(e,t,n,r){return jt(e,function(e,i,a){t(r,n(e),i,a)}),r}(n,e,t(r),{})}}function xr(e,t){return function(n,r){var i;if(n===F&&r===F)return t;if(n!==F&&(i=n),r!==F){if(i===F)return r;"string"==typeof n||"string"==typeof r?(n=Gn(n),r=Gn(r)):(n=Hn(n),r=Hn(r)),i=e(n,r)}return i}}function Cr(t){return jr(function(n){return n=l(n,D(Vr())),Nn(function(r){var i=this;return t(n,function(t){return e(t,i,r)})})})}function Dr(e,t){var n=(t=t===F?" ":Gn(t)).length;if(n<2)return n?Yn(t,e):t;var r=Yn(t,Qa(e/j(t)));return R(t)?rr(U(r),0,e).join(""):r.slice(0,e)}function Or(t,n,r,i){var a=1&n,o=yr(t);return function n(){for(var s=-1,l=arguments.length,u=-1,c=i.length,d=ma(c+l),f=this&&this!==tn&&this instanceof n?o:t;++u<c;)d[u]=i[u];for(;l--;)d[u++]=arguments[++s];return e(f,a?r:this,d)}}function Ar(e){return function(t,n,r){return r&&"number"!=typeof r&&Qr(t,n,r)&&(n=r=F),t=Gi(t),n===F?(n=t,t=0):n=Gi(n),function(e,t,n,r){for(var i=-1,a=io(Qa((t-e)/(n||1)),0),o=ma(a);a--;)o[r?a:++i]=e,e+=n;return o}(t,n,r=r===F?t<n?1:-1:Gi(r),e)}}function Mr(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=qi(t),n=qi(n)),e(t,n)}}function Ir(e,t,n,r,i,a,o,s,l,u){var c=8&t;t|=c?B:Z,4&(t&=~(c?Z:B))||(t&=-4);var d=[e,t,i,c?a:F,c?o:F,c?F:a,c?F:o,s,l,u],f=n.apply(F,d);return Xr(e)&&Fo(f,d),f.placeholder=r,oi(f,e,t)}function Tr(e){var t=_a[e];return function(e,n){if(e=qi(e),(n=null==n?0:ao(Bi(n),292))&&to(e)){var r=(Qi(e)+"e").split("e");return+((r=(Qi(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}function Rr(e){return function(t){var n=Po(t);return n==de?Y(t):n==ve?function(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=[e,e]}),n}(t):function(e,t){return l(t,function(t){return[t,e[t]]})}(t,e(t))}}function $r(e,t,n,r,i,a,o,s){var l=2&t;if(!l&&"function"!=typeof e)throw new Sa(V);var u=r?r.length:0;if(u||(t&=-97,r=i=F),o=o===F?o:io(Bi(o),0),s=s===F?s:Bi(s),u-=i?i.length:0,t&Z){var c=r,d=i;r=i=F}var f=l?F:Eo(e),p=[e,t,n,r,i,c,d,a,o,s];if(f&&function(e,t){var n=e[1],r=t[1],i=n|r,a=i<131,o=r==q&&8==n||r==q&&n==J&&e[7].length<=t[8]||384==r&&t[7].length<=t[8]&&8==n;if(!a&&!o)return e;1&r&&(e[2]=t[2],i|=1&n?0:4);var s=t[3];if(s){var l=e[3];e[3]=l?lr(l,s,t[4]):s,e[4]=l?L(e[3],H):t[4]}s=t[5],s&&(l=e[5],e[5]=l?ur(l,s,t[6]):s,e[6]=l?L(e[5],H):t[6]),s=t[7],s&&(e[7]=s),r&q&&(e[8]=null==e[8]?t[8]:ao(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=i}(p,f),e=p[0],t=p[1],n=p[2],r=p[3],i=p[4],!(s=p[9]=p[9]===F?l?0:e.length:io(p[9]-u,0))&&24&t&&(t&=-25),t&&1!=t)h=8==t||t==G?_r(e,t,s):t!=B&&33!=t||i.length?kr.apply(F,p):Or(e,t,n,r);else var h=function(e,t,n){var r=1&t,i=yr(e);return function t(){return(this&&this!==tn&&this instanceof t?i:e).apply(r?n:this,arguments)}}(e,t,n);return oi((f?Ro:Fo)(h,p),e,t)}function Yr(e,t,n,r){return e===F||Ri(e,Da[n])&&!Ma.call(r,n)?t:e}function Nr(e,t,n,r,i,a){return Ui(e)&&Ui(t)&&(a.set(t,e),On(e,t,F,Nr,a),a.delete(t)),e}function Lr(e){return Fi(e)?F:e}function Er(e,t,n,r,i,a){var o=1&n,s=e.length,l=t.length;if(s!=l&&!(o&&l>s))return!1;var u=a.get(e),c=a.get(t);if(u&&c)return u==t&&c==e;var d=-1,p=!0,h=2&n?new vt:F;for(a.set(e,t),a.set(t,e);++d<s;){var m=e[d],v=t[d];if(r)var g=o?r(v,m,d,t,e,a):r(m,v,d,e,t,a);if(g!==F){if(g)continue;p=!1;break}if(h){if(!f(t,function(e,t){if(!A(h,t)&&(m===e||i(m,e,n,r,a)))return h.push(t)})){p=!1;break}}else if(m!==v&&!i(m,v,n,r,a)){p=!1;break}}return a.delete(e),a.delete(t),p}function jr(e){return zo(ri(e,F,mi),e+"")}function Ur(e){return zt(e,ea,jo)}function Pr(e){return zt(e,ta,Uo)}function Wr(e){for(var t=e.name+"",n=yo[t],r=Ma.call(yo,t)?n.length:0;r--;){var i=n[r],a=i.func;if(null==a||a==e)return i.name}return t}function Fr(e){return(Ma.call(Qe,"placeholder")?Qe:e).placeholder}function Vr(){var e=Qe.iteratee||ua;return e=e===ua?bn:e,arguments.length?e(arguments[0],arguments[1]):e}function zr(e,t){var n=e.__data__;return function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}(t)?n["string"==typeof t?"string":"hash"]:n.map}function Hr(e){for(var t=ea(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,ti(i)]}return t}function Gr(e,t){var n=function(e,t){return null==e?F:e[t]}(e,t);return _n(n)?n:F}function Br(e,t,n){for(var r=-1,i=(t=nr(t,e)).length,a=!1;++r<i;){var o=ui(t[r]);if(!(a=null!=e&&n(e,o)))break;e=e[o]}return a||++r!=i?a:!!(i=null==e?0:e.length)&&ji(i)&&Jr(o,i)&&(Is(e)||Ms(e))}function Zr(e){return"function"!=typeof e.constructor||ei(e)?{}:Oo(Pa(e))}function qr(e){return Is(e)||Ms(e)||!!(za&&e&&e[za])}function Jr(e,t){var n=typeof e;return!!(t=null==t?K:t)&&("number"==n||"symbol"!=n&&ot.test(e))&&e>-1&&e%1==0&&e<t}function Qr(e,t,n){if(!Ui(n))return!1;var r=typeof t;return!!("number"==r?$i(n)&&Jr(t,n.length):"string"==r&&t in n)&&Ri(n[t],e)}function Kr(e,t){if(Is(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!zi(e))||Fe.test(e)||!We.test(e)||null!=t&&e in ba(t)}function Xr(e){var t=Wr(e),n=Qe[t];if("function"!=typeof n||!(t in ft.prototype))return!1;if(e===n)return!0;var r=Eo(n);return!!r&&e===r[0]}function ei(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Da)}function ti(e){return e==e&&!Ui(e)}function ni(e,t){return function(n){return null!=n&&n[e]===t&&(t!==F||e in ba(n))}}function ri(t,n,r){return n=io(n===F?t.length-1:n,0),function(){for(var i=arguments,a=-1,o=io(i.length-n,0),s=ma(o);++a<o;)s[a]=i[n+a];a=-1;for(var l=ma(n+1);++a<n;)l[a]=i[a];return l[n]=r(s),e(t,this,l)}}function ii(e,t){return t.length<2?e:Vt(e,Pn(t,0,-1))}function ai(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}function oi(e,t,n){var r=t+"";return zo(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(Ze,"{\n/* [wrapped with "+t+"] */\n")}(r,di(function(e){var t=e.match(qe);return t?t[1].split(Je):[]}(r),n)))}function si(e){var t=0,n=0;return function(){var r=oo(),i=16-(r-n);if(n=r,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(F,arguments)}}function li(e,t){var n=-1,r=e.length,i=r-1;for(t=t===F?r:t;++n<t;){var a=$n(n,i),o=e[a];e[a]=e[n],e[n]=o}return e.length=t,e}function ui(e){if("string"==typeof e||zi(e))return e;var t=e+"";return"0"==t&&1/e==-Q?"-0":t}function ci(e){if(null!=e){try{return Aa.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function di(e,t){return n(re,function(n){var r="_."+n[0];t&n[1]&&!o(e,r)&&e.push(r)}),e.sort()}function fi(e){if(e instanceof ft)return e.clone();var t=new dt(e.__wrapped__,e.__chain__);return t.__actions__=cr(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function pi(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:Bi(n);return i<0&&(i=io(r+i,0)),m(e,Vr(t,3),i)}function hi(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=r-1;return n!==F&&(i=Bi(n),i=n<0?io(r+i,0):ao(i,r-1)),m(e,Vr(t,3),i,!0)}function mi(e){return null!=e&&e.length?Et(e,1):[]}function vi(e){return e&&e.length?e[0]:F}function gi(e){var t=null==e?0:e.length;return t?e[t-1]:F}function yi(e,t){return e&&e.length&&t&&t.length?Tn(e,t):e}function _i(e){return null==e?e:uo.call(e)}function bi(e){if(!e||!e.length)return[];var t=0;return e=a(e,function(e){if(Yi(e))return t=io(e.length,t),!0}),x(t,function(t){return l(e,b(t))})}function wi(t,n){if(!t||!t.length)return[];var r=bi(t);return null==n?r:l(r,function(t){return e(n,F,t)})}function ki(e){var t=Qe(e);return t.__chain__=!0,t}function Si(e,t){return t(e)}function xi(e,t){return(Is(e)?n:Ao)(e,Vr(t,3))}function Ci(e,t){return(Is(e)?r:Mo)(e,Vr(t,3))}function Di(e,t){return(Is(e)?l:xn)(e,Vr(t,3))}function Oi(e,t,n){return t=n?F:t,t=e&&null==t?e.length:t,$r(e,q,F,F,F,F,t)}function Ai(e,t){var n;if("function"!=typeof t)throw new Sa(V);return e=Bi(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=F),n}}function Mi(e,t,n){function r(t){var n=l,r=u;return l=u=F,h=t,d=e.apply(r,n)}function i(e){var n=e-p;return p===F||n>=t||n<0||v&&e-h>=c}function a(){var e=ys();return i(e)?o(e):(f=Vo(a,function(e){var n=t-(e-p);return v?ao(n,c-(e-h)):n}(e)),F)}function o(e){return f=F,g&&l?r(e):(l=u=F,d)}function s(){var e=ys(),n=i(e);if(l=arguments,u=this,p=e,n){if(f===F)return function(e){return h=e,f=Vo(a,t),m?r(e):d}(p);if(v)return No(f),f=Vo(a,t),r(p)}return f===F&&(f=Vo(a,t)),d}var l,u,c,d,f,p,h=0,m=!1,v=!1,g=!0;if("function"!=typeof e)throw new Sa(V);return t=qi(t)||0,Ui(n)&&(m=!!n.leading,c=(v="maxWait"in n)?io(qi(n.maxWait)||0,t):c,g="trailing"in n?!!n.trailing:g),s.cancel=function(){f!==F&&No(f),h=0,l=p=u=f=F},s.flush=function(){return f===F?d:o(ys())},s}function Ii(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Sa(V);var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var o=e.apply(this,r);return n.cache=a.set(i,o)||a,o};return n.cache=new(Ii.Cache||mt),n}function Ti(e){if("function"!=typeof e)throw new Sa(V);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Ri(e,t){return e===t||e!=e&&t!=t}function $i(e){return null!=e&&ji(e.length)&&!Li(e)}function Yi(e){return Pi(e)&&$i(e)}function Ni(e){if(!Pi(e))return!1;var t=Ht(e);return t==le||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!Fi(e)}function Li(e){if(!Ui(e))return!1;var t=Ht(e);return t==ue||t==ce||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Ei(e){return"number"==typeof e&&e==Bi(e)}function ji(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=K}function Ui(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Pi(e){return null!=e&&"object"==typeof e}function Wi(e){return"number"==typeof e||Pi(e)&&Ht(e)==fe}function Fi(e){if(!Pi(e)||Ht(e)!=pe)return!1;var t=Pa(e);if(null===t)return!0;var n=Ma.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Aa.call(n)==$a}function Vi(e){return"string"==typeof e||!Is(e)&&Pi(e)&&Ht(e)==ge}function zi(e){return"symbol"==typeof e||Pi(e)&&Ht(e)==ye}function Hi(e){if(!e)return[];if($i(e))return Vi(e)?U(e):cr(e);if(Ha&&e[Ha])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[Ha]());var t=Po(e);return(t==de?Y:t==ve?E:ra)(e)}function Gi(e){return e?(e=qi(e))===Q||e===-Q?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function Bi(e){var t=Gi(e),n=t%1;return t==t?n?t-n:t:0}function Zi(e){return e?Mt(Bi(e),0,ee):0}function qi(e){if("number"==typeof e)return e;if(zi(e))return X;if(Ui(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Ui(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=C(e);var n=rt.test(e);return n||at.test(e)?Kt(e.slice(2),n?2:8):nt.test(e)?X:+e}function Ji(e){return dr(e,ta(e))}function Qi(e){return null==e?"":Gn(e)}function Ki(e,t,n){var r=null==e?F:Vt(e,t);return r===F?n:r}function Xi(e,t){return null!=e&&Br(e,t,en)}function ea(e){return $i(e)?yt(e):wn(e)}function ta(e){return $i(e)?yt(e,!0):kn(e)}function na(e,t){if(null==e)return{};var n=l(Pr(e),function(e){return[e]});return t=Vr(t),In(e,n,function(e,n){return t(e,n[0])})}function ra(e){return null==e?[]:O(e,ea(e))}function ia(e){return ul(Qi(e).toLowerCase())}function aa(e){return(e=Qi(e))&&e.replace(st,mn).replace(Wt,"")}function oa(e,t,n){return e=Qi(e),(t=n?F:t)===F?$(e)?W(e):p(e):e.match(t)||[]}function sa(e){return function(){return e}}function la(e){return e}function ua(e){return bn("function"==typeof e?e:It(e,1))}function ca(e,t,r){var i=ea(t),a=Ft(t,i);null!=r||Ui(t)&&(a.length||!i.length)||(r=t,t=e,e=this,a=Ft(t,ea(t)));var o=!(Ui(r)&&"chain"in r&&!r.chain),s=Li(e);return n(a,function(n){var r=t[n];e[n]=r,s&&(e.prototype[n]=function(){var t=this.__chain__;if(o||t){var n=e(this.__wrapped__);return(n.__actions__=cr(this.__actions__)).push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,u([this.value()],arguments))})}),e}function da(){}function fa(e){return Kr(e)?b(ui(e)):function(e){return function(t){return Vt(t,e)}}(e)}function pa(){return[]}function ha(){return!1}var ma=(Be=null==Be?tn:yn.defaults(tn.Object(),Be,yn.pick(tn,Gt))).Array,va=Be.Date,ga=Be.Error,ya=Be.Function,_a=Be.Math,ba=Be.Object,wa=Be.RegExp,ka=Be.String,Sa=Be.TypeError,xa=ma.prototype,Ca=ya.prototype,Da=ba.prototype,Oa=Be["__core-js_shared__"],Aa=Ca.toString,Ma=Da.hasOwnProperty,Ia=0,Ta=function(){var e=/[^.]+$/.exec(Oa&&Oa.keys&&Oa.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Ra=Da.toString,$a=Aa.call(ba),Ya=tn._,Na=wa("^"+Aa.call(Ma).replace(ze,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),La=an?Be.Buffer:F,Ea=Be.Symbol,ja=Be.Uint8Array,Ua=La?La.allocUnsafe:F,Pa=N(ba.getPrototypeOf,ba),Wa=ba.create,Fa=Da.propertyIsEnumerable,Va=xa.splice,za=Ea?Ea.isConcatSpreadable:F,Ha=Ea?Ea.iterator:F,Ga=Ea?Ea.toStringTag:F,Ba=function(){try{var e=Gr(ba,"defineProperty");return e({},"",{}),e}catch(e){}}(),Za=Be.clearTimeout!==tn.clearTimeout&&Be.clearTimeout,qa=va&&va.now!==tn.Date.now&&va.now,Ja=Be.setTimeout!==tn.setTimeout&&Be.setTimeout,Qa=_a.ceil,Ka=_a.floor,Xa=ba.getOwnPropertySymbols,eo=La?La.isBuffer:F,to=Be.isFinite,no=xa.join,ro=N(ba.keys,ba),io=_a.max,ao=_a.min,oo=va.now,so=Be.parseInt,lo=_a.random,uo=xa.reverse,co=Gr(Be,"DataView"),fo=Gr(Be,"Map"),po=Gr(Be,"Promise"),ho=Gr(Be,"Set"),mo=Gr(Be,"WeakMap"),vo=Gr(ba,"create"),go=mo&&new mo,yo={},_o=ci(co),bo=ci(fo),wo=ci(po),ko=ci(ho),So=ci(mo),xo=Ea?Ea.prototype:F,Co=xo?xo.valueOf:F,Do=xo?xo.toString:F,Oo=function(){function e(){}return function(t){if(!Ui(t))return{};if(Wa)return Wa(t);e.prototype=t;var n=new e;return e.prototype=F,n}}();Qe.templateSettings={escape:je,evaluate:Ue,interpolate:Pe,variable:"",imports:{_:Qe}},Qe.prototype=ct.prototype,Qe.prototype.constructor=Qe,dt.prototype=Oo(ct.prototype),dt.prototype.constructor=dt,ft.prototype=Oo(ct.prototype),ft.prototype.constructor=ft,pt.prototype.clear=function(){this.__data__=vo?vo(null):{},this.size=0},pt.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},pt.prototype.get=function(e){var t=this.__data__;if(vo){var n=t[e];return n===z?F:n}return Ma.call(t,e)?t[e]:F},pt.prototype.has=function(e){var t=this.__data__;return vo?t[e]!==F:Ma.call(t,e)},pt.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=vo&&t===F?z:t,this},ht.prototype.clear=function(){this.__data__=[],this.size=0},ht.prototype.delete=function(e){var t=this.__data__,n=xt(t,e);return!(n<0||(n==t.length-1?t.pop():Va.call(t,n,1),--this.size,0))},ht.prototype.get=function(e){var t=this.__data__,n=xt(t,e);return n<0?F:t[n][1]},ht.prototype.has=function(e){return xt(this.__data__,e)>-1},ht.prototype.set=function(e,t){var n=this.__data__,r=xt(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},mt.prototype.clear=function(){this.size=0,this.__data__={hash:new pt,map:new(fo||ht),string:new pt}},mt.prototype.delete=function(e){var t=zr(this,e).delete(e);return this.size-=t?1:0,t},mt.prototype.get=function(e){return zr(this,e).get(e)},mt.prototype.has=function(e){return zr(this,e).has(e)},mt.prototype.set=function(e,t){var n=zr(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},vt.prototype.add=vt.prototype.push=function(e){return this.__data__.set(e,z),this},vt.prototype.has=function(e){return this.__data__.has(e)},gt.prototype.clear=function(){this.__data__=new ht,this.size=0},gt.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},gt.prototype.get=function(e){return this.__data__.get(e)},gt.prototype.has=function(e){return this.__data__.has(e)},gt.prototype.set=function(e,t){var n=this.__data__;if(n instanceof ht){var r=n.__data__;if(!fo||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new mt(r)}return n.set(e,t),this.size=n.size,this};var Ao=hr(jt),Mo=hr(Ut,!0),Io=mr(),To=mr(!0),Ro=go?function(e,t){return go.set(e,t),e}:la,$o=Ba?function(e,t){return Ba(e,"toString",{configurable:!0,enumerable:!1,value:sa(t),writable:!0})}:la,Yo=Nn,No=Za||function(e){return tn.clearTimeout(e)},Lo=ho&&1/E(new ho([,-0]))[1]==Q?function(e){return new ho(e)}:da,Eo=go?function(e){return go.get(e)}:da,jo=Xa?function(e){return null==e?[]:(e=ba(e),a(Xa(e),function(t){return Fa.call(e,t)}))}:pa,Uo=Xa?function(e){for(var t=[];e;)u(t,jo(e)),e=Pa(e);return t}:pa,Po=Ht;(co&&Po(new co(new ArrayBuffer(1)))!=we||fo&&Po(new fo)!=de||po&&Po(po.resolve())!=he||ho&&Po(new ho)!=ve||mo&&Po(new mo)!=_e)&&(Po=function(e){var t=Ht(e),n=t==pe?e.constructor:F,r=n?ci(n):"";if(r)switch(r){case _o:return we;case bo:return de;case wo:return he;case ko:return ve;case So:return _e}return t});var Wo=Oa?Li:ha,Fo=si(Ro),Vo=Ja||function(e,t){return tn.setTimeout(e,t)},zo=si($o),Ho=function(e){var t=Ii(e,function(e){return 500===n.size&&n.clear(),e}),n=t.cache;return t}(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ve,function(e,n,r,i){t.push(r?i.replace(Xe,"$1"):n||e)}),t}),Go=Nn(function(e,t){return Yi(e)?$t(e,Et(t,1,Yi,!0)):[]}),Bo=Nn(function(e,t){var n=gi(t);return Yi(n)&&(n=F),Yi(e)?$t(e,Et(t,1,Yi,!0),Vr(n,2)):[]}),Zo=Nn(function(e,t){var n=gi(t);return Yi(n)&&(n=F),Yi(e)?$t(e,Et(t,1,Yi,!0),F,n):[]}),qo=Nn(function(e){var t=l(e,er);return t.length&&t[0]===e[0]?nn(t):[]}),Jo=Nn(function(e){var t=gi(e),n=l(e,er);return t===gi(n)?t=F:n.pop(),n.length&&n[0]===e[0]?nn(n,Vr(t,2)):[]}),Qo=Nn(function(e){var t=gi(e),n=l(e,er);return(t="function"==typeof t?t:F)&&n.pop(),n.length&&n[0]===e[0]?nn(n,F,t):[]}),Ko=Nn(yi),Xo=jr(function(e,t){var n=null==e?0:e.length,r=At(e,t);return Rn(e,l(t,function(e){return Jr(e,n)?+e:e}).sort(sr)),r}),es=Nn(function(e){return Bn(Et(e,1,Yi,!0))}),ts=Nn(function(e){var t=gi(e);return Yi(t)&&(t=F),Bn(Et(e,1,Yi,!0),Vr(t,2))}),ns=Nn(function(e){var t=gi(e);return t="function"==typeof t?t:F,Bn(Et(e,1,Yi,!0),F,t)}),rs=Nn(function(e,t){return Yi(e)?$t(e,t):[]}),is=Nn(function(e){return Kn(a(e,Yi))}),as=Nn(function(e){var t=gi(e);return Yi(t)&&(t=F),Kn(a(e,Yi),Vr(t,2))}),os=Nn(function(e){var t=gi(e);return t="function"==typeof t?t:F,Kn(a(e,Yi),F,t)}),ss=Nn(bi),ls=Nn(function(e){var t=e.length,n=t>1?e[t-1]:F;return n="function"==typeof n?(e.pop(),n):F,wi(e,n)}),us=jr(function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,i=function(t){return At(t,e)};return!(t>1||this.__actions__.length)&&r instanceof ft&&Jr(n)?((r=r.slice(n,+n+(t?1:0))).__actions__.push({func:Si,args:[i],thisArg:F}),new dt(r,this.__chain__).thru(function(e){return t&&!e.length&&e.push(F),e})):this.thru(i)}),cs=fr(function(e,t,n){Ma.call(e,n)?++e[n]:Ot(e,n,1)}),ds=br(pi),fs=br(hi),ps=fr(function(e,t,n){Ma.call(e,n)?e[n].push(t):Ot(e,n,[t])}),hs=Nn(function(t,n,r){var i=-1,a="function"==typeof n,o=$i(t)?ma(t.length):[];return Ao(t,function(t){o[++i]=a?e(n,t,r):rn(t,n,r)}),o}),ms=fr(function(e,t,n){Ot(e,n,t)}),vs=fr(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]}),gs=Nn(function(e,t){if(null==e)return[];var n=t.length;return n>1&&Qr(e,t[0],t[1])?t=[]:n>2&&Qr(t[0],t[1],t[2])&&(t=[t[0]]),Mn(e,Et(t,1),[])}),ys=qa||function(){return tn.Date.now()},_s=Nn(function(e,t,n){var r=1;if(n.length){var i=L(n,Fr(_s));r|=B}return $r(e,r,t,n,i)}),bs=Nn(function(e,t,n){var r=3;if(n.length){var i=L(n,Fr(bs));r|=B}return $r(t,r,e,n,i)}),ws=Nn(function(e,t){return Rt(e,1,t)}),ks=Nn(function(e,t,n){return Rt(e,qi(t)||0,n)});Ii.Cache=mt;var Ss=Yo(function(t,n){var r=(n=1==n.length&&Is(n[0])?l(n[0],D(Vr())):l(Et(n,1),D(Vr()))).length;return Nn(function(i){for(var a=-1,o=ao(i.length,r);++a<o;)i[a]=n[a].call(this,i[a]);return e(t,this,i)})}),xs=Nn(function(e,t){return $r(e,B,F,t,L(t,Fr(xs)))}),Cs=Nn(function(e,t){return $r(e,Z,F,t,L(t,Fr(Cs)))}),Ds=jr(function(e,t){return $r(e,J,F,F,F,t)}),Os=Mr(Jt),As=Mr(function(e,t){return e>=t}),Ms=on(function(){return arguments}())?on:function(e){return Pi(e)&&Ma.call(e,"callee")&&!Fa.call(e,"callee")},Is=ma.isArray,Ts=ln?D(ln):function(e){return Pi(e)&&Ht(e)==be},Rs=eo||ha,$s=un?D(un):function(e){return Pi(e)&&Ht(e)==se},Ys=cn?D(cn):function(e){return Pi(e)&&Po(e)==de},Ns=dn?D(dn):function(e){return Pi(e)&&Ht(e)==me},Ls=fn?D(fn):function(e){return Pi(e)&&Po(e)==ve},Es=pn?D(pn):function(e){return Pi(e)&&ji(e.length)&&!!Zt[Ht(e)]},js=Mr(Sn),Us=Mr(function(e,t){return e<=t}),Ps=pr(function(e,t){if(ei(t)||$i(t))return dr(t,ea(t),e),F;for(var n in t)Ma.call(t,n)&&St(e,n,t[n])}),Ws=pr(function(e,t){dr(t,ta(t),e)}),Fs=pr(function(e,t,n,r){dr(t,ta(t),e,r)}),Vs=pr(function(e,t,n,r){dr(t,ea(t),e,r)}),zs=jr(At),Hs=Nn(function(e,t){e=ba(e);var n=-1,r=t.length,i=r>2?t[2]:F;for(i&&Qr(t[0],t[1],i)&&(r=1);++n<r;)for(var a=t[n],o=ta(a),s=-1,l=o.length;++s<l;){var u=o[s],c=e[u];(c===F||Ri(c,Da[u])&&!Ma.call(e,u))&&(e[u]=a[u])}return e}),Gs=Nn(function(t){return t.push(F,Nr),e(Qs,F,t)}),Bs=Sr(function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ra.call(t)),e[t]=n},sa(la)),Zs=Sr(function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=Ra.call(t)),Ma.call(e,t)?e[t].push(n):e[t]=[n]},Vr),qs=Nn(rn),Js=pr(function(e,t,n){On(e,t,n)}),Qs=pr(function(e,t,n,r){On(e,t,n,r)}),Ks=jr(function(e,t){var n={};if(null==e)return n;var r=!1;t=l(t,function(t){return t=nr(t,e),r||(r=t.length>1),t}),dr(e,Pr(e),n),r&&(n=It(n,7,Lr));for(var i=t.length;i--;)Zn(n,t[i]);return n}),Xs=jr(function(e,t){return null==e?{}:function(e,t){return In(e,t,function(t,n){return Xi(e,n)})}(e,t)}),el=Rr(ea),tl=Rr(ta),nl=gr(function(e,t,n){return t=t.toLowerCase(),e+(n?ia(t):t)}),rl=gr(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),il=gr(function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}),al=vr("toLowerCase"),ol=gr(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}),sl=gr(function(e,t,n){return e+(n?" ":"")+ul(t)}),ll=gr(function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}),ul=vr("toUpperCase"),cl=Nn(function(t,n){try{return e(t,F,n)}catch(e){return Ni(e)?e:new ga(e)}}),dl=jr(function(e,t){return n(t,function(t){t=ui(t),Ot(e,t,_s(e[t],e))}),e}),fl=wr(),pl=wr(!0),hl=Nn(function(e,t){return function(n){return rn(n,e,t)}}),ml=Nn(function(e,t){return function(n){return rn(e,n,t)}}),vl=Cr(l),gl=Cr(i),yl=Cr(f),_l=Ar(),bl=Ar(!0),wl=xr(function(e,t){return e+t},0),kl=Tr("ceil"),Sl=xr(function(e,t){return e/t},1),xl=Tr("floor"),Cl=xr(function(e,t){return e*t},1),Dl=Tr("round"),Ol=xr(function(e,t){return e-t},0);return Qe.after=function(e,t){if("function"!=typeof t)throw new Sa(V);return e=Bi(e),function(){if(--e<1)return t.apply(this,arguments)}},Qe.ary=Oi,Qe.assign=Ps,Qe.assignIn=Ws,Qe.assignInWith=Fs,Qe.assignWith=Vs,Qe.at=zs,Qe.before=Ai,Qe.bind=_s,Qe.bindAll=dl,Qe.bindKey=bs,Qe.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Is(e)?e:[e]},Qe.chain=ki,Qe.chunk=function(e,t,n){t=(n?Qr(e,t,n):t===F)?1:io(Bi(t),0);var r=null==e?0:e.length;if(!r||t<1)return[];for(var i=0,a=0,o=ma(Qa(r/t));i<r;)o[a++]=Pn(e,i,i+=t);return o},Qe.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,i=[];++t<n;){var a=e[t];a&&(i[r++]=a)}return i},Qe.concat=function(){var e=arguments.length;if(!e)return[];for(var t=ma(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return u(Is(n)?cr(n):[n],Et(t,1))},Qe.cond=function(t){var n=null==t?0:t.length,r=Vr();return t=n?l(t,function(e){if("function"!=typeof e[1])throw new Sa(V);return[r(e[0]),e[1]]}):[],Nn(function(r){for(var i=-1;++i<n;){var a=t[i];if(e(a[0],this,r))return e(a[1],this,r)}})},Qe.conforms=function(e){return function(e){var t=ea(e);return function(n){return Tt(n,e,t)}}(It(e,1))},Qe.constant=sa,Qe.countBy=cs,Qe.create=function(e,t){var n=Oo(e);return null==t?n:Dt(n,t)},Qe.curry=function e(t,n,r){var i=$r(t,8,F,F,F,F,F,n=r?F:n);return i.placeholder=e.placeholder,i},Qe.curryRight=function e(t,n,r){var i=$r(t,G,F,F,F,F,F,n=r?F:n);return i.placeholder=e.placeholder,i},Qe.debounce=Mi,Qe.defaults=Hs,Qe.defaultsDeep=Gs,Qe.defer=ws,Qe.delay=ks,Qe.difference=Go,Qe.differenceBy=Bo,Qe.differenceWith=Zo,Qe.drop=function(e,t,n){var r=null==e?0:e.length;return r?Pn(e,(t=n||t===F?1:Bi(t))<0?0:t,r):[]},Qe.dropRight=function(e,t,n){var r=null==e?0:e.length;return r?Pn(e,0,(t=r-(t=n||t===F?1:Bi(t)))<0?0:t):[]},Qe.dropRightWhile=function(e,t){return e&&e.length?Jn(e,Vr(t,3),!0,!0):[]},Qe.dropWhile=function(e,t){return e&&e.length?Jn(e,Vr(t,3),!0):[]},Qe.fill=function(e,t,n,r){var i=null==e?0:e.length;return i?(n&&"number"!=typeof n&&Qr(e,t,n)&&(n=0,r=i),function(e,t,n,r){var i=e.length;for((n=Bi(n))<0&&(n=-n>i?0:i+n),(r=r===F||r>i?i:Bi(r))<0&&(r+=i),r=n>r?0:Zi(r);n<r;)e[n++]=t;return e}(e,t,n,r)):[]},Qe.filter=function(e,t){return(Is(e)?a:Lt)(e,Vr(t,3))},Qe.flatMap=function(e,t){return Et(Di(e,t),1)},Qe.flatMapDeep=function(e,t){return Et(Di(e,t),Q)},Qe.flatMapDepth=function(e,t,n){return n=n===F?1:Bi(n),Et(Di(e,t),n)},Qe.flatten=mi,Qe.flattenDeep=function(e){return null!=e&&e.length?Et(e,Q):[]},Qe.flattenDepth=function(e,t){return null!=e&&e.length?Et(e,t=t===F?1:Bi(t)):[]},Qe.flip=function(e){return $r(e,512)},Qe.flow=fl,Qe.flowRight=pl,Qe.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var i=e[t];r[i[0]]=i[1]}return r},Qe.functions=function(e){return null==e?[]:Ft(e,ea(e))},Qe.functionsIn=function(e){return null==e?[]:Ft(e,ta(e))},Qe.groupBy=ps,Qe.initial=function(e){return null!=e&&e.length?Pn(e,0,-1):[]},Qe.intersection=qo,Qe.intersectionBy=Jo,Qe.intersectionWith=Qo,Qe.invert=Bs,Qe.invertBy=Zs,Qe.invokeMap=hs,Qe.iteratee=ua,Qe.keyBy=ms,Qe.keys=ea,Qe.keysIn=ta,Qe.map=Di,Qe.mapKeys=function(e,t){var n={};return t=Vr(t,3),jt(e,function(e,r,i){Ot(n,t(e,r,i),e)}),n},Qe.mapValues=function(e,t){var n={};return t=Vr(t,3),jt(e,function(e,r,i){Ot(n,r,t(e,r,i))}),n},Qe.matches=function(e){return Cn(It(e,1))},Qe.matchesProperty=function(e,t){return Dn(e,It(t,1))},Qe.memoize=Ii,Qe.merge=Js,Qe.mergeWith=Qs,Qe.method=hl,Qe.methodOf=ml,Qe.mixin=ca,Qe.negate=Ti,Qe.nthArg=function(e){return e=Bi(e),Nn(function(t){return An(t,e)})},Qe.omit=Ks,Qe.omitBy=function(e,t){return na(e,Ti(Vr(t)))},Qe.once=function(e){return Ai(2,e)},Qe.orderBy=function(e,t,n,r){return null==e?[]:(Is(t)||(t=null==t?[]:[t]),Is(n=r?F:n)||(n=null==n?[]:[n]),Mn(e,t,n))},Qe.over=vl,Qe.overArgs=Ss,Qe.overEvery=gl,Qe.overSome=yl,Qe.partial=xs,Qe.partialRight=Cs,Qe.partition=vs,Qe.pick=Xs,Qe.pickBy=na,Qe.property=fa,Qe.propertyOf=function(e){return function(t){return null==e?F:Vt(e,t)}},Qe.pull=Ko,Qe.pullAll=yi,Qe.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Tn(e,t,Vr(n,2)):e},Qe.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Tn(e,t,F,n):e},Qe.pullAt=Xo,Qe.range=_l,Qe.rangeRight=bl,Qe.rearg=Ds,Qe.reject=function(e,t){return(Is(e)?a:Lt)(e,Ti(Vr(t,3)))},Qe.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,i=[],a=e.length;for(t=Vr(t,3);++r<a;){var o=e[r];t(o,r,e)&&(n.push(o),i.push(r))}return Rn(e,i),n},Qe.rest=function(e,t){if("function"!=typeof e)throw new Sa(V);return Nn(e,t=t===F?t:Bi(t))},Qe.reverse=_i,Qe.sampleSize=function(e,t,n){return t=(n?Qr(e,t,n):t===F)?1:Bi(t),(Is(e)?bt:En)(e,t)},Qe.set=function(e,t,n){return null==e?e:jn(e,t,n)},Qe.setWith=function(e,t,n,r){return r="function"==typeof r?r:F,null==e?e:jn(e,t,n,r)},Qe.shuffle=function(e){return(Is(e)?wt:Un)(e)},Qe.slice=function(e,t,n){var r=null==e?0:e.length;return r?(n&&"number"!=typeof n&&Qr(e,t,n)?(t=0,n=r):(t=null==t?0:Bi(t),n=n===F?r:Bi(n)),Pn(e,t,n)):[]},Qe.sortBy=gs,Qe.sortedUniq=function(e){return e&&e.length?zn(e):[]},Qe.sortedUniqBy=function(e,t){return e&&e.length?zn(e,Vr(t,2)):[]},Qe.split=function(e,t,n){return n&&"number"!=typeof n&&Qr(e,t,n)&&(t=n=F),(n=n===F?ee:n>>>0)?(e=Qi(e))&&("string"==typeof t||null!=t&&!Ns(t))&&(!(t=Gn(t))&&R(e))?rr(U(e),0,n):e.split(t,n):[]},Qe.spread=function(t,n){if("function"!=typeof t)throw new Sa(V);return n=null==n?0:io(Bi(n),0),Nn(function(r){var i=r[n],a=rr(r,0,n);return i&&u(a,i),e(t,this,a)})},Qe.tail=function(e){var t=null==e?0:e.length;return t?Pn(e,1,t):[]},Qe.take=function(e,t,n){return e&&e.length?Pn(e,0,(t=n||t===F?1:Bi(t))<0?0:t):[]},Qe.takeRight=function(e,t,n){var r=null==e?0:e.length;return r?Pn(e,(t=r-(t=n||t===F?1:Bi(t)))<0?0:t,r):[]},Qe.takeRightWhile=function(e,t){return e&&e.length?Jn(e,Vr(t,3),!1,!0):[]},Qe.takeWhile=function(e,t){return e&&e.length?Jn(e,Vr(t,3)):[]},Qe.tap=function(e,t){return t(e),e},Qe.throttle=function(e,t,n){var r=!0,i=!0;if("function"!=typeof e)throw new Sa(V);return Ui(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),Mi(e,t,{leading:r,maxWait:t,trailing:i})},Qe.thru=Si,Qe.toArray=Hi,Qe.toPairs=el,Qe.toPairsIn=tl,Qe.toPath=function(e){return Is(e)?l(e,ui):zi(e)?[e]:cr(Ho(Qi(e)))},Qe.toPlainObject=Ji,Qe.transform=function(e,t,r){var i=Is(e),a=i||Rs(e)||Es(e);if(t=Vr(t,4),null==r){var o=e&&e.constructor;r=a?i?new o:[]:Ui(e)&&Li(o)?Oo(Pa(e)):{}}return(a?n:jt)(e,function(e,n,i){return t(r,e,n,i)}),r},Qe.unary=function(e){return Oi(e,1)},Qe.union=es,Qe.unionBy=ts,Qe.unionWith=ns,Qe.uniq=function(e){return e&&e.length?Bn(e):[]},Qe.uniqBy=function(e,t){return e&&e.length?Bn(e,Vr(t,2)):[]},Qe.uniqWith=function(e,t){return t="function"==typeof t?t:F,e&&e.length?Bn(e,F,t):[]},Qe.unset=function(e,t){return null==e||Zn(e,t)},Qe.unzip=bi,Qe.unzipWith=wi,Qe.update=function(e,t,n){return null==e?e:qn(e,t,tr(n))},Qe.updateWith=function(e,t,n,r){return r="function"==typeof r?r:F,null==e?e:qn(e,t,tr(n),r)},Qe.values=ra,Qe.valuesIn=function(e){return null==e?[]:O(e,ta(e))},Qe.without=rs,Qe.words=oa,Qe.wrap=function(e,t){return xs(tr(t),e)},Qe.xor=is,Qe.xorBy=as,Qe.xorWith=os,Qe.zip=ss,Qe.zipObject=function(e,t){return Xn(e||[],t||[],St)},Qe.zipObjectDeep=function(e,t){return Xn(e||[],t||[],jn)},Qe.zipWith=ls,Qe.entries=el,Qe.entriesIn=tl,Qe.extend=Ws,Qe.extendWith=Fs,ca(Qe,Qe),Qe.add=wl,Qe.attempt=cl,Qe.camelCase=nl,Qe.capitalize=ia,Qe.ceil=kl,Qe.clamp=function(e,t,n){return n===F&&(n=t,t=F),n!==F&&(n=(n=qi(n))==n?n:0),t!==F&&(t=(t=qi(t))==t?t:0),Mt(qi(e),t,n)},Qe.clone=function(e){return It(e,4)},Qe.cloneDeep=function(e){return It(e,5)},Qe.cloneDeepWith=function(e,t){return It(e,5,t="function"==typeof t?t:F)},Qe.cloneWith=function(e,t){return It(e,4,t="function"==typeof t?t:F)},Qe.conformsTo=function(e,t){return null==t||Tt(e,t,ea(t))},Qe.deburr=aa,Qe.defaultTo=function(e,t){return null==e||e!=e?t:e},Qe.divide=Sl,Qe.endsWith=function(e,t,n){e=Qi(e),t=Gn(t);var r=e.length,i=n=n===F?r:Mt(Bi(n),0,r);return(n-=t.length)>=0&&e.slice(n,i)==t},Qe.eq=Ri,Qe.escape=function(e){return(e=Qi(e))&&Ee.test(e)?e.replace(Ne,vn):e},Qe.escapeRegExp=function(e){return(e=Qi(e))&&He.test(e)?e.replace(ze,"\\$&"):e},Qe.every=function(e,t,n){var r=Is(e)?i:Yt;return n&&Qr(e,t,n)&&(t=F),r(e,Vr(t,3))},Qe.find=ds,Qe.findIndex=pi,Qe.findKey=function(e,t){return h(e,Vr(t,3),jt)},Qe.findLast=fs,Qe.findLastIndex=hi,Qe.findLastKey=function(e,t){return h(e,Vr(t,3),Ut)},Qe.floor=xl,Qe.forEach=xi,Qe.forEachRight=Ci,Qe.forIn=function(e,t){return null==e?e:Io(e,Vr(t,3),ta)},Qe.forInRight=function(e,t){return null==e?e:To(e,Vr(t,3),ta)},Qe.forOwn=function(e,t){return e&&jt(e,Vr(t,3))},Qe.forOwnRight=function(e,t){return e&&Ut(e,Vr(t,3))},Qe.get=Ki,Qe.gt=Os,Qe.gte=As,Qe.has=function(e,t){return null!=e&&Br(e,t,Xt)},Qe.hasIn=Xi,Qe.head=vi,Qe.identity=la,Qe.includes=function(e,t,n,r){e=$i(e)?e:ra(e),n=n&&!r?Bi(n):0;var i=e.length;return n<0&&(n=io(i+n,0)),Vi(e)?n<=i&&e.indexOf(t,n)>-1:!!i&&v(e,t,n)>-1},Qe.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:Bi(n);return i<0&&(i=io(r+i,0)),v(e,t,i)},Qe.inRange=function(e,t,n){return t=Gi(t),n===F?(n=t,t=0):n=Gi(n),function(e,t,n){return e>=ao(t,n)&&e<io(t,n)}(e=qi(e),t,n)},Qe.invoke=qs,Qe.isArguments=Ms,Qe.isArray=Is,Qe.isArrayBuffer=Ts,Qe.isArrayLike=$i,Qe.isArrayLikeObject=Yi,Qe.isBoolean=function(e){return!0===e||!1===e||Pi(e)&&Ht(e)==oe},Qe.isBuffer=Rs,Qe.isDate=$s,Qe.isElement=function(e){return Pi(e)&&1===e.nodeType&&!Fi(e)},Qe.isEmpty=function(e){if(null==e)return!0;if($i(e)&&(Is(e)||"string"==typeof e||"function"==typeof e.splice||Rs(e)||Es(e)||Ms(e)))return!e.length;var t=Po(e);if(t==de||t==ve)return!e.size;if(ei(e))return!wn(e).length;for(var n in e)if(Ma.call(e,n))return!1;return!0},Qe.isEqual=function(e,t){return sn(e,t)},Qe.isEqualWith=function(e,t,n){var r=(n="function"==typeof n?n:F)?n(e,t):F;return r===F?sn(e,t,F,n):!!r},Qe.isError=Ni,Qe.isFinite=function(e){return"number"==typeof e&&to(e)},Qe.isFunction=Li,Qe.isInteger=Ei,Qe.isLength=ji,Qe.isMap=Ys,Qe.isMatch=function(e,t){return e===t||hn(e,t,Hr(t))},Qe.isMatchWith=function(e,t,n){return n="function"==typeof n?n:F,hn(e,t,Hr(t),n)},Qe.isNaN=function(e){return Wi(e)&&e!=+e},Qe.isNative=function(e){if(Wo(e))throw new ga("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return _n(e)},Qe.isNil=function(e){return null==e},Qe.isNull=function(e){return null===e},Qe.isNumber=Wi,Qe.isObject=Ui,Qe.isObjectLike=Pi,Qe.isPlainObject=Fi,Qe.isRegExp=Ns,Qe.isSafeInteger=function(e){return Ei(e)&&e>=-K&&e<=K},Qe.isSet=Ls,Qe.isString=Vi,Qe.isSymbol=zi,Qe.isTypedArray=Es,Qe.isUndefined=function(e){return e===F},Qe.isWeakMap=function(e){return Pi(e)&&Po(e)==_e},Qe.isWeakSet=function(e){return Pi(e)&&"[object WeakSet]"==Ht(e)},Qe.join=function(e,t){return null==e?"":no.call(e,t)},Qe.kebabCase=rl,Qe.last=gi,Qe.lastIndexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=r;return n!==F&&(i=(i=Bi(n))<0?io(r+i,0):ao(i,r-1)),t==t?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(e,t,i):m(e,y,i,!0)},Qe.lowerCase=il,Qe.lowerFirst=al,Qe.lt=js,Qe.lte=Us,Qe.max=function(e){return e&&e.length?Nt(e,la,Jt):F},Qe.maxBy=function(e,t){return e&&e.length?Nt(e,Vr(t,2),Jt):F},Qe.mean=function(e){return _(e,la)},Qe.meanBy=function(e,t){return _(e,Vr(t,2))},Qe.min=function(e){return e&&e.length?Nt(e,la,Sn):F},Qe.minBy=function(e,t){return e&&e.length?Nt(e,Vr(t,2),Sn):F},Qe.stubArray=pa,Qe.stubFalse=ha,Qe.stubObject=function(){return{}},Qe.stubString=function(){return""},Qe.stubTrue=function(){return!0},Qe.multiply=Cl,Qe.nth=function(e,t){return e&&e.length?An(e,Bi(t)):F},Qe.noConflict=function(){return tn._===this&&(tn._=Ya),this},Qe.noop=da,Qe.now=ys,Qe.pad=function(e,t,n){e=Qi(e);var r=(t=Bi(t))?j(e):0;if(!t||r>=t)return e;var i=(t-r)/2;return Dr(Ka(i),n)+e+Dr(Qa(i),n)},Qe.padEnd=function(e,t,n){e=Qi(e);var r=(t=Bi(t))?j(e):0;return t&&r<t?e+Dr(t-r,n):e},Qe.padStart=function(e,t,n){e=Qi(e);var r=(t=Bi(t))?j(e):0;return t&&r<t?Dr(t-r,n)+e:e},Qe.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),so(Qi(e).replace(Ge,""),t||0)},Qe.random=function(e,t,n){if(n&&"boolean"!=typeof n&&Qr(e,t,n)&&(t=n=F),n===F&&("boolean"==typeof t?(n=t,t=F):"boolean"==typeof e&&(n=e,e=F)),e===F&&t===F?(e=0,t=1):(e=Gi(e),t===F?(t=e,e=0):t=Gi(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var i=lo();return ao(e+i*(t-e+Qt("1e-"+((i+"").length-1))),t)}return $n(e,t)},Qe.reduce=function(e,t,n){var r=Is(e)?c:k,i=arguments.length<3;return r(e,Vr(t,4),n,i,Ao)},Qe.reduceRight=function(e,t,n){var r=Is(e)?d:k,i=arguments.length<3;return r(e,Vr(t,4),n,i,Mo)},Qe.repeat=function(e,t,n){return t=(n?Qr(e,t,n):t===F)?1:Bi(t),Yn(Qi(e),t)},Qe.replace=function(){var e=arguments,t=Qi(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Qe.result=function(e,t,n){var r=-1,i=(t=nr(t,e)).length;for(i||(i=1,e=F);++r<i;){var a=null==e?F:e[ui(t[r])];a===F&&(r=i,a=n),e=Li(a)?a.call(e):a}return e},Qe.round=Dl,Qe.runInContext=w,Qe.sample=function(e){return(Is(e)?_t:Ln)(e)},Qe.size=function(e){if(null==e)return 0;if($i(e))return Vi(e)?j(e):e.length;var t=Po(e);return t==de||t==ve?e.size:wn(e).length},Qe.snakeCase=ol,Qe.some=function(e,t,n){var r=Is(e)?f:Wn;return n&&Qr(e,t,n)&&(t=F),r(e,Vr(t,3))},Qe.sortedIndex=function(e,t){return Fn(e,t)},Qe.sortedIndexBy=function(e,t,n){return Vn(e,t,Vr(n,2))},Qe.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=Fn(e,t);if(r<n&&Ri(e[r],t))return r}return-1},Qe.sortedLastIndex=function(e,t){return Fn(e,t,!0)},Qe.sortedLastIndexBy=function(e,t,n){return Vn(e,t,Vr(n,2),!0)},Qe.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Fn(e,t,!0)-1;if(Ri(e[n],t))return n}return-1},Qe.startCase=sl,Qe.startsWith=function(e,t,n){return e=Qi(e),n=null==n?0:Mt(Bi(n),0,e.length),t=Gn(t),e.slice(n,n+t.length)==t},Qe.subtract=Ol,Qe.sum=function(e){return e&&e.length?S(e,la):0},Qe.sumBy=function(e,t){return e&&e.length?S(e,Vr(t,2)):0},Qe.template=function(e,t,n){var r=Qe.templateSettings;n&&Qr(e,t,n)&&(t=F),e=Qi(e),t=Fs({},t,r,Yr);var i,a,o=Fs({},t.imports,r.imports,Yr),s=ea(o),l=O(o,s),u=0,c=t.interpolate||lt,d="__p += '",f=wa((t.escape||lt).source+"|"+c.source+"|"+(c===Pe?et:lt).source+"|"+(t.evaluate||lt).source+"|$","g"),p="//# sourceURL="+(Ma.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Bt+"]")+"\n";e.replace(f,function(t,n,r,o,s,l){return r||(r=o),d+=e.slice(u,l).replace(ut,T),n&&(i=!0,d+="' +\n__e("+n+") +\n'"),s&&(a=!0,d+="';\n"+s+";\n__p += '"),r&&(d+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),u=l+t.length,t}),d+="';\n";var h=Ma.call(t,"variable")&&t.variable;if(h){if(Ke.test(h))throw new ga("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(a?d.replace(Te,""):d).replace(Re,"$1").replace($e,"$1;"),d="function("+(h||"obj")+") {\n"+(h?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var m=cl(function(){return ya(s,p+"return "+d).apply(F,l)});if(m.source=d,Ni(m))throw m;return m},Qe.times=function(e,t){if((e=Bi(e))<1||e>K)return[];var n=ee,r=ao(e,ee);t=Vr(t),e-=ee;for(var i=x(r,t);++n<e;)t(n);return i},Qe.toFinite=Gi,Qe.toInteger=Bi,Qe.toLength=Zi,Qe.toLower=function(e){return Qi(e).toLowerCase()},Qe.toNumber=qi,Qe.toSafeInteger=function(e){return e?Mt(Bi(e),-K,K):0===e?e:0},Qe.toString=Qi,Qe.toUpper=function(e){return Qi(e).toUpperCase()},Qe.trim=function(e,t,n){if((e=Qi(e))&&(n||t===F))return C(e);if(!e||!(t=Gn(t)))return e;var r=U(e),i=U(t);return rr(r,M(r,i),I(r,i)+1).join("")},Qe.trimEnd=function(e,t,n){if((e=Qi(e))&&(n||t===F))return e.slice(0,P(e)+1);if(!e||!(t=Gn(t)))return e;var r=U(e);return rr(r,0,I(r,U(t))+1).join("")},Qe.trimStart=function(e,t,n){if((e=Qi(e))&&(n||t===F))return e.replace(Ge,"");if(!e||!(t=Gn(t)))return e;var r=U(e);return rr(r,M(r,U(t))).join("")},Qe.truncate=function(e,t){var n=30,r="...";if(Ui(t)){var i="separator"in t?t.separator:i;n="length"in t?Bi(t.length):n,r="omission"in t?Gn(t.omission):r}var a=(e=Qi(e)).length;if(R(e)){var o=U(e);a=o.length}if(n>=a)return e;var s=n-j(r);if(s<1)return r;var l=o?rr(o,0,s).join(""):e.slice(0,s);if(i===F)return l+r;if(o&&(s+=l.length-s),Ns(i)){if(e.slice(s).search(i)){var u,c=l;for(i.global||(i=wa(i.source,Qi(tt.exec(i))+"g")),i.lastIndex=0;u=i.exec(c);)var d=u.index;l=l.slice(0,d===F?s:d)}}else if(e.indexOf(Gn(i),s)!=s){var f=l.lastIndexOf(i);f>-1&&(l=l.slice(0,f))}return l+r},Qe.unescape=function(e){return(e=Qi(e))&&Le.test(e)?e.replace(Ye,gn):e},Qe.uniqueId=function(e){var t=++Ia;return Qi(e)+t},Qe.upperCase=ll,Qe.upperFirst=ul,Qe.each=xi,Qe.eachRight=Ci,Qe.first=vi,ca(Qe,function(){var e={};return jt(Qe,function(t,n){Ma.call(Qe.prototype,n)||(e[n]=t)}),e}(),{chain:!1}),Qe.VERSION="4.17.21",n(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){Qe[e].placeholder=Qe}),n(["drop","take"],function(e,t){ft.prototype[e]=function(n){n=n===F?1:io(Bi(n),0);var r=this.__filtered__&&!t?new ft(this):this.clone();return r.__filtered__?r.__takeCount__=ao(n,r.__takeCount__):r.__views__.push({size:ao(n,ee),type:e+(r.__dir__<0?"Right":"")}),r},ft.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),n(["filter","map","takeWhile"],function(e,t){var n=t+1,r=1==n||3==n;ft.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Vr(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}}),n(["head","last"],function(e,t){var n="take"+(t?"Right":"");ft.prototype[e]=function(){return this[n](1).value()[0]}}),n(["initial","tail"],function(e,t){var n="drop"+(t?"":"Right");ft.prototype[e]=function(){return this.__filtered__?new ft(this):this[n](1)}}),ft.prototype.compact=function(){return this.filter(la)},ft.prototype.find=function(e){return this.filter(e).head()},ft.prototype.findLast=function(e){return this.reverse().find(e)},ft.prototype.invokeMap=Nn(function(e,t){return"function"==typeof e?new ft(this):this.map(function(n){return rn(n,e,t)})}),ft.prototype.reject=function(e){return this.filter(Ti(Vr(e)))},ft.prototype.slice=function(e,t){e=Bi(e);var n=this;return n.__filtered__&&(e>0||t<0)?new ft(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==F&&(n=(t=Bi(t))<0?n.dropRight(-t):n.take(t-e)),n)},ft.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},ft.prototype.toArray=function(){return this.take(ee)},jt(ft.prototype,function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),i=Qe[r?"take"+("last"==t?"Right":""):t],a=r||/^find/.test(t);i&&(Qe.prototype[t]=function(){var t=this.__wrapped__,o=r?[1]:arguments,s=t instanceof ft,l=o[0],c=s||Is(t),d=function(e){var t=i.apply(Qe,u([e],o));return r&&f?t[0]:t};c&&n&&"function"==typeof l&&1!=l.length&&(s=c=!1);var f=this.__chain__,p=!!this.__actions__.length,h=a&&!f,m=s&&!p;if(!a&&c){t=m?t:new ft(this);var v=e.apply(t,o);return v.__actions__.push({func:Si,args:[d],thisArg:F}),new dt(v,f)}return h&&m?e.apply(this,o):(v=this.thru(d),h?r?v.value()[0]:v.value():v)})}),n(["pop","push","shift","sort","splice","unshift"],function(e){var t=xa[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);Qe.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var i=this.value();return t.apply(Is(i)?i:[],e)}return this[n](function(n){return t.apply(Is(n)?n:[],e)})}}),jt(ft.prototype,function(e,t){var n=Qe[t];if(n){var r=n.name+"";Ma.call(yo,r)||(yo[r]=[]),yo[r].push({name:t,func:n})}}),yo[kr(F,2).name]=[{name:"wrapper",func:F}],ft.prototype.clone=function(){var e=new ft(this.__wrapped__);return e.__actions__=cr(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=cr(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=cr(this.__views__),e},ft.prototype.reverse=function(){if(this.__filtered__){var e=new ft(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},ft.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Is(e),r=t<0,i=n?e.length:0,a=function(e,t,n){for(var r=-1,i=n.length;++r<i;){var a=n[r],o=a.size;switch(a.type){case"drop":e+=o;break;case"dropRight":t-=o;break;case"take":t=ao(t,e+o);break;case"takeRight":e=io(e,t-o)}}return{start:e,end:t}}(0,i,this.__views__),o=a.start,s=a.end,l=s-o,u=r?s:o-1,c=this.__iteratees__,d=c.length,f=0,p=ao(l,this.__takeCount__);if(!n||!r&&i==l&&p==l)return Qn(e,this.__actions__);var h=[];e:for(;l--&&f<p;){for(var m=-1,v=e[u+=t];++m<d;){var g=c[m],y=g.iteratee,_=g.type,b=y(v);if(2==_)v=b;else if(!b){if(1==_)continue e;break e}}h[f++]=v}return h},Qe.prototype.at=us,Qe.prototype.chain=function(){return ki(this)},Qe.prototype.commit=function(){return new dt(this.value(),this.__chain__)},Qe.prototype.next=function(){this.__values__===F&&(this.__values__=Hi(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?F:this.__values__[this.__index__++]}},Qe.prototype.plant=function(e){for(var t,n=this;n instanceof ct;){var r=fi(n);r.__index__=0,r.__values__=F,t?i.__wrapped__=r:t=r;var i=r;n=n.__wrapped__}return i.__wrapped__=e,t},Qe.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof ft){var t=e;return this.__actions__.length&&(t=new ft(this)),(t=t.reverse()).__actions__.push({func:Si,args:[_i],thisArg:F}),new dt(t,this.__chain__)}return this.thru(_i)},Qe.prototype.toJSON=Qe.prototype.valueOf=Qe.prototype.value=function(){return Qn(this.__wrapped__,this.__actions__)},Qe.prototype.first=Qe.prototype.head,Ha&&(Qe.prototype[Ha]=function(){return this}),Qe}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(tn._=yn,define(function(){return yn})):rn?((rn.exports=yn)._=yn,nn._=yn):tn._=yn}).call(this),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.moment=t()}(this,function(){"use strict";var e;function t(){return e.apply(null,arguments)}function n(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function r(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function i(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function a(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;for(var t in e)if(i(e,t))return;return 1}function o(e){return void 0===e}function s(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function l(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function u(e,t){for(var n=[],r=e.length,i=0;i<r;++i)n.push(t(e[i],i));return n}function c(e,t){for(var n in t)i(t,n)&&(e[n]=t[n]);return i(t,"toString")&&(e.toString=t.toString),i(t,"valueOf")&&(e.valueOf=t.valueOf),e}function d(e,t,n,r){return Dt(e,t,n,r,!0).utc()}function f(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function p(e){var t,n,r=e._d&&!isNaN(e._d.getTime());return r&&(t=f(e),n=m.call(t.parsedDateParts,function(e){return null!=e}),r=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n),e._strict)&&(r=r&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e)?r:(e._isValid=r,e._isValid)}function h(e){var t=d(NaN);return null!=e?c(f(t),e):f(t).userInvalidated=!0,t}var m=Array.prototype.some||function(e){for(var t=Object(this),n=t.length>>>0,r=0;r<n;r++)if(r in t&&e.call(this,t[r],r,t))return!0;return!1},v=t.momentProperties=[],g=!1;function y(e,t){var n,r,i,a=v.length;if(o(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),o(t._i)||(e._i=t._i),o(t._f)||(e._f=t._f),o(t._l)||(e._l=t._l),o(t._strict)||(e._strict=t._strict),o(t._tzm)||(e._tzm=t._tzm),o(t._isUTC)||(e._isUTC=t._isUTC),o(t._offset)||(e._offset=t._offset),o(t._pf)||(e._pf=f(t)),o(t._locale)||(e._locale=t._locale),0<a)for(n=0;n<a;n++)o(i=t[r=v[n]])||(e[r]=i);return e}function _(e){y(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===g&&(g=!0,t.updateOffset(this),g=!1)}function b(e){return e instanceof _||null!=e&&null!=e._isAMomentObject}function w(e){!1===t.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function k(e,n){var r=!0;return c(function(){if(null!=t.deprecationHandler&&t.deprecationHandler(null,e),r){for(var a,o,s=[],l=arguments.length,u=0;u<l;u++){if(a="","object"==typeof arguments[u]){for(o in a+="\n["+u+"] ",arguments[0])i(arguments[0],o)&&(a+=o+": "+arguments[0][o]+", ");a=a.slice(0,-2)}else a=arguments[u];s.push(a)}w(e+"\nArguments: "+Array.prototype.slice.call(s).join("")+"\n"+(new Error).stack),r=!1}return n.apply(this,arguments)},n)}var S={};function x(e,n){null!=t.deprecationHandler&&t.deprecationHandler(e,n),S[e]||(w(n),S[e]=!0)}function C(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function D(e,t){var n,a=c({},e);for(n in t)i(t,n)&&(r(e[n])&&r(t[n])?(a[n]={},c(a[n],e[n]),c(a[n],t[n])):null!=t[n]?a[n]=t[n]:delete a[n]);for(n in e)i(e,n)&&!i(t,n)&&r(e[n])&&(a[n]=c({},a[n]));return a}function O(e){null!=e&&this.set(e)}t.suppressDeprecationWarnings=!1,t.deprecationHandler=null;var A=Object.keys||function(e){var t,n=[];for(t in e)i(e,t)&&n.push(t);return n};function M(e,t,n){var r=""+Math.abs(e);return(0<=e?n?"+":"":"-")+Math.pow(10,Math.max(0,t-r.length)).toString().substr(1)+r}var I=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,T=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,R={},$={};function Y(e,t,n,r){var i="string"==typeof r?function(){return this[r]()}:r;e&&($[e]=i),t&&($[t[0]]=function(){return M(i.apply(this,arguments),t[1],t[2])}),n&&($[n]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function N(e,t){return e.isValid()?(t=L(t,e.localeData()),R[t]=R[t]||function(e){for(var t,n=e.match(I),r=0,i=n.length;r<i;r++)$[n[r]]?n[r]=$[n[r]]:n[r]=(t=n[r]).match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"");return function(t){for(var r="",a=0;a<i;a++)r+=C(n[a])?n[a].call(t,e):n[a];return r}}(t),R[t](e)):e.localeData().invalidDate()}function L(e,t){var n=5;function r(e){return t.longDateFormat(e)||e}for(T.lastIndex=0;0<=n&&T.test(e);)e=e.replace(T,r),T.lastIndex=0,--n;return e}var E={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function j(e){return"string"==typeof e?E[e]||E[e.toLowerCase()]:void 0}function U(e){var t,n,r={};for(n in e)i(e,n)&&(t=j(n))&&(r[t]=e[n]);return r}var P={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1},W=/\d/,F=/\d\d/,V=/\d{3}/,z=/\d{4}/,H=/[+-]?\d{6}/,G=/\d\d?/,B=/\d\d\d\d?/,Z=/\d\d\d\d\d\d?/,q=/\d{1,3}/,J=/\d{1,4}/,Q=/[+-]?\d{1,6}/,K=/\d+/,X=/[+-]?\d+/,ee=/Z|[+-]\d\d:?\d\d/gi,te=/Z|[+-]\d\d(?::?\d\d)?/gi,ne=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,re=/^[1-9]\d?/,ie=/^([1-9]\d|\d)/;function ae(e,t,n){ce[e]=C(t)?t:function(e,r){return e&&n?n:t}}function oe(e,t){return i(ce,e)?ce[e](t._strict,t._locale):new RegExp(se(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,r,i){return t||n||r||i})))}function se(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function le(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function ue(e){var t=0;return 0!=(e=+e)&&isFinite(e)?le(e):t}var ce={},de={};function fe(e,t){var n,r,i=t;for("string"==typeof e&&(e=[e]),s(t)&&(i=function(e,n){n[t]=ue(e)}),r=e.length,n=0;n<r;n++)de[e[n]]=i}function pe(e,t){fe(e,function(e,n,r,i){r._w=r._w||{},t(e,r._w,r,i)})}function he(e){return e%4==0&&e%100!=0||e%400==0}var me=0,ve=1,ge=2,ye=3,_e=4,be=5,we=6,ke=7,Se=8;function xe(e){return he(e)?366:365}Y("Y",0,0,function(){var e=this.year();return e<=9999?M(e,4):"+"+e}),Y(0,["YY",2],0,function(){return this.year()%100}),Y(0,["YYYY",4],0,"year"),Y(0,["YYYYY",5],0,"year"),Y(0,["YYYYYY",6,!0],0,"year"),ae("Y",X),ae("YY",G,F),ae("YYYY",J,z),ae("YYYYY",Q,H),ae("YYYYYY",Q,H),fe(["YYYYY","YYYYYY"],me),fe("YYYY",function(e,n){n[me]=2===e.length?t.parseTwoDigitYear(e):ue(e)}),fe("YY",function(e,n){n[me]=t.parseTwoDigitYear(e)}),fe("Y",function(e,t){t[me]=parseInt(e,10)}),t.parseTwoDigitYear=function(e){return ue(e)+(68<ue(e)?1900:2e3)};var Ce,De=Oe("FullYear",!0);function Oe(e,n){return function(r){return null!=r?(Me(this,e,r),t.updateOffset(this,n),this):Ae(this,e)}}function Ae(e,t){if(!e.isValid())return NaN;var n=e._d,r=e._isUTC;switch(t){case"Milliseconds":return r?n.getUTCMilliseconds():n.getMilliseconds();case"Seconds":return r?n.getUTCSeconds():n.getSeconds();case"Minutes":return r?n.getUTCMinutes():n.getMinutes();case"Hours":return r?n.getUTCHours():n.getHours();case"Date":return r?n.getUTCDate():n.getDate();case"Day":return r?n.getUTCDay():n.getDay();case"Month":return r?n.getUTCMonth():n.getMonth();case"FullYear":return r?n.getUTCFullYear():n.getFullYear();default:return NaN}}function Me(e,t,n){var r,i,a;if(e.isValid()&&!isNaN(n)){switch(r=e._d,i=e._isUTC,t){case"Milliseconds":return i?r.setUTCMilliseconds(n):r.setMilliseconds(n);case"Seconds":return i?r.setUTCSeconds(n):r.setSeconds(n);case"Minutes":return i?r.setUTCMinutes(n):r.setMinutes(n);case"Hours":return i?r.setUTCHours(n):r.setHours(n);case"Date":return i?r.setUTCDate(n):r.setDate(n);case"FullYear":break;default:return}t=n,a=e.month(),e=29!==(e=e.date())||1!==a||he(t)?e:28,i?r.setUTCFullYear(t,a,e):r.setFullYear(t,a,e)}}function Ie(e,t){var n;return isNaN(e)||isNaN(t)?NaN:(e+=(t-(n=(t%(n=12)+n)%n))/12,1==n?he(e)?29:28:31-n%7%2)}Ce=Array.prototype.indexOf||function(e){for(var t=0;t<this.length;++t)if(this[t]===e)return t;return-1},Y("M",["MM",2],"Mo",function(){return this.month()+1}),Y("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),Y("MMMM",0,0,function(e){return this.localeData().months(this,e)}),ae("M",G,re),ae("MM",G,F),ae("MMM",function(e,t){return t.monthsShortRegex(e)}),ae("MMMM",function(e,t){return t.monthsRegex(e)}),fe(["M","MM"],function(e,t){t[ve]=ue(e)-1}),fe(["MMM","MMMM"],function(e,t,n,r){null!=(r=n._locale.monthsParse(e,r,n._strict))?t[ve]=r:f(n).invalidMonth=e});var Te="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Re="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),$e=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ye=ne,Ne=ne;function Le(e,t){if(e.isValid()){if("string"==typeof t)if(/^\d+$/.test(t))t=ue(t);else if(!s(t=e.localeData().monthsParse(t)))return;var n=(n=e.date())<29?n:Math.min(n,Ie(e.year(),t));e._isUTC?e._d.setUTCMonth(t,n):e._d.setMonth(t,n)}}function Ee(e){return null!=e?(Le(this,e),t.updateOffset(this,!0),this):Ae(this,"Month")}function je(){function e(e,t){return t.length-e.length}for(var t,n,r=[],i=[],a=[],o=0;o<12;o++)n=d([2e3,o]),t=se(this.monthsShort(n,"")),n=se(this.months(n,"")),r.push(t),i.push(n),a.push(n),a.push(t);r.sort(e),i.sort(e),a.sort(e),this._monthsRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+r.join("|")+")","i")}function Ue(e,t,n,r,i,a,o){var s;return e<100&&0<=e?(s=new Date(e+400,t,n,r,i,a,o),isFinite(s.getFullYear())&&s.setFullYear(e)):s=new Date(e,t,n,r,i,a,o),s}function Pe(e){var t;return e<100&&0<=e?((t=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,t)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function We(e,t,n){return(n=7+t-n)-(7+Pe(e,0,n).getUTCDay()-t)%7-1}function Fe(e,t,n,r,i){var a;n=(t=1+7*(t-1)+(7+n-r)%7+We(e,r,i))<=0?xe(a=e-1)+t:t>xe(e)?(a=e+1,t-xe(e)):(a=e,t);return{year:a,dayOfYear:n}}function Ve(e,t,n){var r,i,a=We(e.year(),t,n);return(a=Math.floor((e.dayOfYear()-a-1)/7)+1)<1?r=a+ze(i=e.year()-1,t,n):a>ze(e.year(),t,n)?(r=a-ze(e.year(),t,n),i=e.year()+1):(i=e.year(),r=a),{week:r,year:i}}function ze(e,t,n){var r=We(e,t,n);t=We(e+1,t,n);return(xe(e)-r+t)/7}function He(e,t){return e.slice(t,7).concat(e.slice(0,t))}Y("w",["ww",2],"wo","week"),Y("W",["WW",2],"Wo","isoWeek"),ae("w",G,re),ae("ww",G,F),ae("W",G,re),ae("WW",G,F),pe(["w","ww","W","WW"],function(e,t,n,r){t[r.substr(0,1)]=ue(e)}),Y("d",0,"do","day"),Y("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),Y("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),Y("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),Y("e",0,0,"weekday"),Y("E",0,0,"isoWeekday"),ae("d",G),ae("e",G),ae("E",G),ae("dd",function(e,t){return t.weekdaysMinRegex(e)}),ae("ddd",function(e,t){return t.weekdaysShortRegex(e)}),ae("dddd",function(e,t){return t.weekdaysRegex(e)}),pe(["dd","ddd","dddd"],function(e,t,n,r){null!=(r=n._locale.weekdaysParse(e,r,n._strict))?t.d=r:f(n).invalidWeekday=e}),pe(["d","e","E"],function(e,t,n,r){t[r]=ue(e)});var Ge="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Be="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ze="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),qe=ne,Je=ne,Qe=ne;function Ke(){function e(e,t){return t.length-e.length}for(var t,n,r,i=[],a=[],o=[],s=[],l=0;l<7;l++)r=d([2e3,1]).day(l),t=se(this.weekdaysMin(r,"")),n=se(this.weekdaysShort(r,"")),r=se(this.weekdays(r,"")),i.push(t),a.push(n),o.push(r),s.push(t),s.push(n),s.push(r);i.sort(e),a.sort(e),o.sort(e),s.sort(e),this._weekdaysRegex=new RegExp("^("+s.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function Xe(){return this.hours()%12||12}function et(e,t){Y(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function tt(e,t){return t._meridiemParse}Y("H",["HH",2],0,"hour"),Y("h",["hh",2],0,Xe),Y("k",["kk",2],0,function(){return this.hours()||24}),Y("hmm",0,0,function(){return""+Xe.apply(this)+M(this.minutes(),2)}),Y("hmmss",0,0,function(){return""+Xe.apply(this)+M(this.minutes(),2)+M(this.seconds(),2)}),Y("Hmm",0,0,function(){return""+this.hours()+M(this.minutes(),2)}),Y("Hmmss",0,0,function(){return""+this.hours()+M(this.minutes(),2)+M(this.seconds(),2)}),et("a",!0),et("A",!1),ae("a",tt),ae("A",tt),ae("H",G,ie),ae("h",G,re),ae("k",G,re),ae("HH",G,F),ae("hh",G,F),ae("kk",G,F),ae("hmm",B),ae("hmmss",Z),ae("Hmm",B),ae("Hmmss",Z),fe(["H","HH"],ye),fe(["k","kk"],function(e,t,n){e=ue(e),t[ye]=24===e?0:e}),fe(["a","A"],function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e}),fe(["h","hh"],function(e,t,n){t[ye]=ue(e),f(n).bigHour=!0}),fe("hmm",function(e,t,n){var r=e.length-2;t[ye]=ue(e.substr(0,r)),t[_e]=ue(e.substr(r)),f(n).bigHour=!0}),fe("hmmss",function(e,t,n){var r=e.length-4,i=e.length-2;t[ye]=ue(e.substr(0,r)),t[_e]=ue(e.substr(r,2)),t[be]=ue(e.substr(i)),f(n).bigHour=!0}),fe("Hmm",function(e,t,n){var r=e.length-2;t[ye]=ue(e.substr(0,r)),t[_e]=ue(e.substr(r))}),fe("Hmmss",function(e,t,n){var r=e.length-4,i=e.length-2;t[ye]=ue(e.substr(0,r)),t[_e]=ue(e.substr(r,2)),t[be]=ue(e.substr(i))}),ne=Oe("Hours",!0);var nt,rt={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Te,monthsShort:Re,week:{dow:0,doy:6},weekdays:Ge,weekdaysMin:Ze,weekdaysShort:Be,meridiemParse:/[ap]\.?m?\.?/i},it={},at={};function ot(e){return e&&e.toLowerCase().replace("_","-")}function st(e){var t,n;if(void 0===it[e]&&"undefined"!=typeof module&&module&&module.exports&&(n=e)&&n.match("^[^/\\\\]*$"))try{t=nt._abbr,require("./locale/"+e),lt(t)}catch(t){it[e]=null}return it[e]}function lt(e,t){return e&&((t=o(t)?ct(e):ut(e,t))?nt=t:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),nt._abbr}function ut(e,t){if(null===t)return delete it[e],null;var n,r=rt;if(t.abbr=e,null!=it[e])x("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),r=it[e]._config;else if(null!=t.parentLocale)if(null!=it[t.parentLocale])r=it[t.parentLocale]._config;else{if(null==(n=st(t.parentLocale)))return at[t.parentLocale]||(at[t.parentLocale]=[]),at[t.parentLocale].push({name:e,config:t}),null;r=n._config}return it[e]=new O(D(r,t)),at[e]&&at[e].forEach(function(e){ut(e.name,e.config)}),lt(e),it[e]}function ct(e){var t;if(!(e=e&&e._locale&&e._locale._abbr?e._locale._abbr:e))return nt;if(!n(e)){if(t=st(e))return t;e=[e]}return function(e){for(var t,n,r,i,a=0;a<e.length;){for(t=(i=ot(e[a]).split("-")).length,n=(n=ot(e[a+1]))?n.split("-"):null;0<t;){if(r=st(i.slice(0,t).join("-")))return r;if(n&&n.length>=t&&function(e,t){for(var n=Math.min(e.length,t.length),r=0;r<n;r+=1)if(e[r]!==t[r])return r;return n}(i,n)>=t-1)break;t--}a++}return nt}(e)}function dt(e){var t=e._a;return t&&-2===f(e).overflow&&(t=t[ve]<0||11<t[ve]?ve:t[ge]<1||t[ge]>Ie(t[me],t[ve])?ge:t[ye]<0||24<t[ye]||24===t[ye]&&(0!==t[_e]||0!==t[be]||0!==t[we])?ye:t[_e]<0||59<t[_e]?_e:t[be]<0||59<t[be]?be:t[we]<0||999<t[we]?we:-1,f(e)._overflowDayOfYear&&(t<me||ge<t)&&(t=ge),f(e)._overflowWeeks&&-1===t&&(t=ke),f(e)._overflowWeekday&&-1===t&&(t=Se),f(e).overflow=t),e}var ft=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ht=/Z|[+-]\d\d(?::?\d\d)?/,mt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],vt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],gt=/^\/?Date\((-?\d+)/i,yt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,_t={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function bt(e){var t,n,r,i,a,o,s=e._i,l=ft.exec(s)||pt.exec(s),u=(s=mt.length,vt.length);if(l){for(f(e).iso=!0,t=0,n=s;t<n;t++)if(mt[t][1].exec(l[1])){i=mt[t][0],r=!1!==mt[t][2];break}if(null==i)e._isValid=!1;else{if(l[3]){for(t=0,n=u;t<n;t++)if(vt[t][1].exec(l[3])){a=(l[2]||" ")+vt[t][0];break}if(null==a)return void(e._isValid=!1)}if(r||null==a){if(l[4]){if(!ht.exec(l[4]))return void(e._isValid=!1);o="Z"}e._f=i+(a||"")+(o||""),xt(e)}else e._isValid=!1}}else e._isValid=!1}function wt(e){var t,n,r=yt.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));r?(t=function(e,t,n,r,i,a){return e=[function(e){return(e=parseInt(e,10))<=49?2e3+e:e<=999?1900+e:e}(e),Re.indexOf(t),parseInt(n,10),parseInt(r,10),parseInt(i,10)],a&&e.push(parseInt(a,10)),e}(r[4],r[3],r[2],r[5],r[6],r[7]),function(e,t,n){if(!e||Be.indexOf(e)===new Date(t[0],t[1],t[2]).getDay())return 1;f(n).weekdayMismatch=!0,n._isValid=!1}(r[1],t,e)&&(e._a=t,e._tzm=(t=r[8],n=r[9],r=r[10],t?_t[t]:n?0:((t=parseInt(r,10))-(n=t%100))/100*60+n),e._d=Pe.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),f(e).rfc2822=!0)):e._isValid=!1}function kt(e,t,n){return null!=e?e:null!=t?t:n}function St(e){var n,r,i,a,o,s,l,u,c,d,p,h=[];if(!e._d){for(i=e,a=new Date(t.now()),r=i._useUTC?[a.getUTCFullYear(),a.getUTCMonth(),a.getUTCDate()]:[a.getFullYear(),a.getMonth(),a.getDate()],e._w&&null==e._a[ge]&&null==e._a[ve]&&(null!=(a=(i=e)._w).GG||null!=a.W||null!=a.E?(u=1,c=4,o=kt(a.GG,i._a[me],Ve(Ot(),1,4).year),s=kt(a.W,1),((l=kt(a.E,1))<1||7<l)&&(d=!0)):(u=i._locale._week.dow,c=i._locale._week.doy,p=Ve(Ot(),u,c),o=kt(a.gg,i._a[me],p.year),s=kt(a.w,p.week),null!=a.d?((l=a.d)<0||6<l)&&(d=!0):null!=a.e?(l=a.e+u,(a.e<0||6<a.e)&&(d=!0)):l=u),s<1||s>ze(o,u,c)?f(i)._overflowWeeks=!0:null!=d?f(i)._overflowWeekday=!0:(p=Fe(o,s,l,u,c),i._a[me]=p.year,i._dayOfYear=p.dayOfYear)),null!=e._dayOfYear&&(a=kt(e._a[me],r[me]),(e._dayOfYear>xe(a)||0===e._dayOfYear)&&(f(e)._overflowDayOfYear=!0),d=Pe(a,0,e._dayOfYear),e._a[ve]=d.getUTCMonth(),e._a[ge]=d.getUTCDate()),n=0;n<3&&null==e._a[n];++n)e._a[n]=h[n]=r[n];for(;n<7;n++)e._a[n]=h[n]=null==e._a[n]?2===n?1:0:e._a[n];24===e._a[ye]&&0===e._a[_e]&&0===e._a[be]&&0===e._a[we]&&(e._nextDay=!0,e._a[ye]=0),e._d=(e._useUTC?Pe:Ue).apply(null,h),o=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[ye]=24),e._w&&void 0!==e._w.d&&e._w.d!==o&&(f(e).weekdayMismatch=!0)}}function xt(e){if(e._f===t.ISO_8601)bt(e);else if(e._f===t.RFC_2822)wt(e);else{e._a=[],f(e).empty=!0;for(var n,r,a,o,s,l=""+e._i,u=l.length,c=0,d=L(e._f,e._locale).match(I)||[],p=d.length,h=0;h<p;h++)r=d[h],(n=(l.match(oe(r,e))||[])[0])&&(0<(a=l.substr(0,l.indexOf(n))).length&&f(e).unusedInput.push(a),l=l.slice(l.indexOf(n)+n.length),c+=n.length),$[r]?(n?f(e).empty=!1:f(e).unusedTokens.push(r),a=r,s=e,null!=(o=n)&&i(de,a)&&de[a](o,s._a,s,a)):e._strict&&!n&&f(e).unusedTokens.push(r);f(e).charsLeftOver=u-c,0<l.length&&f(e).unusedInput.push(l),e._a[ye]<=12&&!0===f(e).bigHour&&0<e._a[ye]&&(f(e).bigHour=void 0),f(e).parsedDateParts=e._a.slice(0),f(e).meridiem=e._meridiem,e._a[ye]=function(e,t,n){return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((e=e.isPM(n))&&t<12&&(t+=12),t=e||12!==t?t:0):t}(e._locale,e._a[ye],e._meridiem),null!==(u=f(e).era)&&(e._a[me]=e._locale.erasConvertYear(u,e._a[me])),St(e),dt(e)}}function Ct(e){var i,a,d,m=e._i,v=e._f;if(e._locale=e._locale||ct(e._l),null===m||void 0===v&&""===m)return h({nullInput:!0});if("string"==typeof m&&(e._i=m=e._locale.preparse(m)),b(m))return new _(dt(m));if(l(m))e._d=m;else if(n(v)){var g,w,k,S,x,C,D=e,O=!1,A=D._f.length;if(0===A)f(D).invalidFormat=!0,D._d=new Date(NaN);else{for(S=0;S<A;S++)x=0,C=!1,g=y({},D),null!=D._useUTC&&(g._useUTC=D._useUTC),g._f=D._f[S],xt(g),p(g)&&(C=!0),x=(x+=f(g).charsLeftOver)+10*f(g).unusedTokens.length,f(g).score=x,O?x<k&&(k=x,w=g):(null==k||x<k||C)&&(k=x,w=g,C)&&(O=!0);c(D,w||g)}}else v?xt(e):o(v=(m=e)._i)?m._d=new Date(t.now()):l(v)?m._d=new Date(v.valueOf()):"string"==typeof v?(a=m,null!==(i=gt.exec(a._i))?a._d=new Date(+i[1]):(bt(a),!1===a._isValid&&(delete a._isValid,wt(a),!1===a._isValid)&&(delete a._isValid,a._strict?a._isValid=!1:t.createFromInputFallback(a)))):n(v)?(m._a=u(v.slice(0),function(e){return parseInt(e,10)}),St(m)):r(v)?(i=m)._d||(d=void 0===(a=U(i._i)).day?a.date:a.day,i._a=u([a.year,a.month,d,a.hour,a.minute,a.second,a.millisecond],function(e){return e&&parseInt(e,10)}),St(i)):s(v)?m._d=new Date(v):t.createFromInputFallback(m);return p(e)||(e._d=null),e}function Dt(e,t,i,o,s){var l={};return!0!==t&&!1!==t||(o=t,t=void 0),!0!==i&&!1!==i||(o=i,i=void 0),(r(e)&&a(e)||n(e)&&0===e.length)&&(e=void 0),l._isAMomentObject=!0,l._useUTC=l._isUTC=s,l._l=i,l._i=e,l._f=t,l._strict=o,(s=new _(dt(Ct(s=l))))._nextDay&&(s.add(1,"d"),s._nextDay=void 0),s}function Ot(e,t,n,r){return Dt(e,t,n,r,!1)}function At(e,t){var r,i;if(!(t=1===t.length&&n(t[0])?t[0]:t).length)return Ot();for(r=t[0],i=1;i<t.length;++i)t[i].isValid()&&!t[i][e](r)||(r=t[i]);return r}t.createFromInputFallback=k("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),t.ISO_8601=function(){},t.RFC_2822=function(){},B=k("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=Ot.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:h()}),Z=k("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=Ot.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:h()});var Mt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function It(e){var t=(e=U(e)).year||0,n=e.quarter||0,r=e.month||0,a=e.week||e.isoWeek||0,o=e.day||0,s=e.hour||0,l=e.minute||0,u=e.second||0,c=e.millisecond||0;this._isValid=function(e){var t,n,r=!1,a=Mt.length;for(t in e)if(i(e,t)&&(-1===Ce.call(Mt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<a;++n)if(e[Mt[n]]){if(r)return!1;parseFloat(e[Mt[n]])!==ue(e[Mt[n]])&&(r=!0)}return!0}(e),this._milliseconds=+c+1e3*u+6e4*l+1e3*s*60*60,this._days=+o+7*a,this._months=+r+3*n+12*t,this._data={},this._locale=ct(),this._bubble()}function Tt(e){return e instanceof It}function Rt(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function $t(e,t){Y(e,0,0,function(){var e=this.utcOffset(),n="+";return e<0&&(e=-e,n="-"),n+M(~~(e/60),2)+t+M(~~e%60,2)})}$t("Z",":"),$t("ZZ",""),ae("Z",te),ae("ZZ",te),fe(["Z","ZZ"],function(e,t,n){n._useUTC=!0,n._tzm=Nt(te,e)});var Yt=/([\+\-]|\d\d)/gi;function Nt(e,t){return null===(t=(t||"").match(e))?null:0===(t=60*(e=((t[t.length-1]||[])+"").match(Yt)||["-",0,0])[1]+ue(e[2]))?0:"+"===e[0]?t:-t}function Lt(e,n){var r;return n._isUTC?(n=n.clone(),r=(b(e)||l(e)?e:Ot(e)).valueOf()-n.valueOf(),n._d.setTime(n._d.valueOf()+r),t.updateOffset(n,!1),n):Ot(e).local()}function Et(e){return-Math.round(e._d.getTimezoneOffset())}function jt(){return!!this.isValid()&&this._isUTC&&0===this._offset}t.updateOffset=function(){};var Ut=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Pt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Wt(e,t){var n,r=e;return Tt(e)?r={ms:e._milliseconds,d:e._days,M:e._months}:s(e)||!isNaN(+e)?(r={},t?r[t]=+e:r.milliseconds=+e):(t=Ut.exec(e))?(n="-"===t[1]?-1:1,r={y:0,d:ue(t[ge])*n,h:ue(t[ye])*n,m:ue(t[_e])*n,s:ue(t[be])*n,ms:ue(Rt(1e3*t[we]))*n}):(t=Pt.exec(e))?(n="-"===t[1]?-1:1,r={y:Ft(t[2],n),M:Ft(t[3],n),w:Ft(t[4],n),d:Ft(t[5],n),h:Ft(t[6],n),m:Ft(t[7],n),s:Ft(t[8],n)}):null==r?r={}:"object"==typeof r&&("from"in r||"to"in r)&&(t=function(e,t){var n;return e.isValid()&&t.isValid()?(t=Lt(t,e),e.isBefore(t)?n=Vt(e,t):((n=Vt(t,e)).milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}(Ot(r.from),Ot(r.to)),(r={}).ms=t.milliseconds,r.M=t.months),n=new It(r),Tt(e)&&i(e,"_locale")&&(n._locale=e._locale),Tt(e)&&i(e,"_isValid")&&(n._isValid=e._isValid),n}function Ft(e,t){return e=e&&parseFloat(e.replace(",",".")),(isNaN(e)?0:e)*t}function Vt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function zt(e,t){return function(n,r){var i;return null===r||isNaN(+r)||(x(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),i=n,n=r,r=i),Ht(this,Wt(n,r),e),this}}function Ht(e,n,r,i){var a=n._milliseconds,o=Rt(n._days);n=Rt(n._months);e.isValid()&&(i=null==i||i,n&&Le(e,Ae(e,"Month")+n*r),o&&Me(e,"Date",Ae(e,"Date")+o*r),a&&e._d.setTime(e._d.valueOf()+a*r),i)&&t.updateOffset(e,o||n)}function Gt(e){return"string"==typeof e||e instanceof String}function Bt(e,t){var n,r;return e.date()<t.date()?-Bt(t,e):-((n=12*(t.year()-e.year())+(t.month()-e.month()))+(t-(r=e.clone().add(n,"months"))<0?(t-r)/(r-e.clone().add(n-1,"months")):(t-r)/(e.clone().add(1+n,"months")-r)))||0}function Zt(e){return void 0===e?this._locale._abbr:(null!=(e=ct(e))&&(this._locale=e),this)}function qt(){return this._locale}Wt.fn=It.prototype,Wt.invalid=function(){return Wt(NaN)},Te=zt(1,"add"),Ge=zt(-1,"subtract"),t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]",Ze=k("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});var Jt=126227808e5;function Qt(e,t){return(e%t+t)%t}function Kt(e,t,n){return e<100&&0<=e?new Date(e+400,t,n)-Jt:new Date(e,t,n).valueOf()}function Xt(e,t,n){return e<100&&0<=e?Date.UTC(e+400,t,n)-Jt:Date.UTC(e,t,n)}function en(e,t){return t.erasAbbrRegex(e)}function tn(){for(var e,t,n,r=[],i=[],a=[],o=[],s=this.eras(),l=0,u=s.length;l<u;++l)e=se(s[l].name),t=se(s[l].abbr),n=se(s[l].narrow),i.push(e),r.push(t),a.push(n),o.push(e),o.push(t),o.push(n);this._erasRegex=new RegExp("^("+o.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+i.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+r.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+a.join("|")+")","i")}function nn(e,t){Y(0,[e,e.length],0,t)}function rn(e,t,n,r,i){var a;return null==e?Ve(this,r,i).year:(a=ze(e,r,i),function(e,t,n,r,i){return t=Pe((e=Fe(e,t,n,r,i)).year,0,e.dayOfYear),this.year(t.getUTCFullYear()),this.month(t.getUTCMonth()),this.date(t.getUTCDate()),this}.call(this,e,t=a<t?a:t,n,r,i))}Y("N",0,0,"eraAbbr"),Y("NN",0,0,"eraAbbr"),Y("NNN",0,0,"eraAbbr"),Y("NNNN",0,0,"eraName"),Y("NNNNN",0,0,"eraNarrow"),Y("y",["y",1],"yo","eraYear"),Y("y",["yy",2],0,"eraYear"),Y("y",["yyy",3],0,"eraYear"),Y("y",["yyyy",4],0,"eraYear"),ae("N",en),ae("NN",en),ae("NNN",en),ae("NNNN",function(e,t){return t.erasNameRegex(e)}),ae("NNNNN",function(e,t){return t.erasNarrowRegex(e)}),fe(["N","NN","NNN","NNNN","NNNNN"],function(e,t,n,r){(r=n._locale.erasParse(e,r,n._strict))?f(n).era=r:f(n).invalidEra=e}),ae("y",K),ae("yy",K),ae("yyy",K),ae("yyyy",K),ae("yo",function(e,t){return t._eraYearOrdinalRegex||K}),fe(["y","yy","yyy","yyyy"],me),fe(["yo"],function(e,t,n,r){var i;n._locale._eraYearOrdinalRegex&&(i=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[me]=n._locale.eraYearOrdinalParse(e,i):t[me]=parseInt(e,10)}),Y(0,["gg",2],0,function(){return this.weekYear()%100}),Y(0,["GG",2],0,function(){return this.isoWeekYear()%100}),nn("gggg","weekYear"),nn("ggggg","weekYear"),nn("GGGG","isoWeekYear"),nn("GGGGG","isoWeekYear"),ae("G",X),ae("g",X),ae("GG",G,F),ae("gg",G,F),ae("GGGG",J,z),ae("gggg",J,z),ae("GGGGG",Q,H),ae("ggggg",Q,H),pe(["gggg","ggggg","GGGG","GGGGG"],function(e,t,n,r){t[r.substr(0,2)]=ue(e)}),pe(["gg","GG"],function(e,n,r,i){n[i]=t.parseTwoDigitYear(e)}),Y("Q",0,"Qo","quarter"),ae("Q",W),fe("Q",function(e,t){t[ve]=3*(ue(e)-1)}),Y("D",["DD",2],"Do","date"),ae("D",G,re),ae("DD",G,F),ae("Do",function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient}),fe(["D","DD"],ge),fe("Do",function(e,t){t[ge]=ue(e.match(G)[0])}),J=Oe("Date",!0),Y("DDD",["DDDD",3],"DDDo","dayOfYear"),ae("DDD",q),ae("DDDD",V),fe(["DDD","DDDD"],function(e,t,n){n._dayOfYear=ue(e)}),Y("m",["mm",2],0,"minute"),ae("m",G,ie),ae("mm",G,F),fe(["m","mm"],_e);var an;z=Oe("Minutes",!1),Y("s",["ss",2],0,"second"),ae("s",G,ie),ae("ss",G,F),fe(["s","ss"],be),Q=Oe("Seconds",!1);for(Y("S",0,0,function(){return~~(this.millisecond()/100)}),Y(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),Y(0,["SSS",3],0,"millisecond"),Y(0,["SSSS",4],0,function(){return 10*this.millisecond()}),Y(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),Y(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),Y(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),Y(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),Y(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),ae("S",q,W),ae("SS",q,F),ae("SSS",q,V),an="SSSS";an.length<=9;an+="S")ae(an,K);function on(e,t){t[we]=ue(1e3*("0."+e))}for(an="S";an.length<=9;an+="S")fe(an,on);function sn(e){return e}function ln(e,t,n,r){var i=ct();r=d().set(r,t);return i[n](r,e)}function un(e,t,n){if(s(e)&&(t=e,e=void 0),e=e||"",null!=t)return ln(e,t,n,"month");for(var r=[],i=0;i<12;i++)r[i]=ln(e,i,n,"month");return r}function cn(e,t,n,r){"boolean"==typeof e?s(t)&&(n=t,t=void 0):(t=e,e=!1,s(n=t)&&(n=t,t=void 0)),t=t||"";var i,a=ct(),o=e?a._week.dow:0,l=[];if(null!=n)return ln(t,(n+o)%7,r,"day");for(i=0;i<7;i++)l[i]=ln(t,(i+o)%7,r,"day");return l}H=Oe("Milliseconds",!1),Y("z",0,0,"zoneAbbr"),Y("zz",0,0,"zoneName"),(re=_.prototype).add=Te,re.calendar=function(e,o){1===arguments.length&&(arguments[0]?function(e){return b(e)||l(e)||Gt(e)||s(e)||function(e){var t=n(e),r=!1;return t&&(r=0===e.filter(function(t){return!s(t)&&Gt(e)}).length),t&&r}(e)||function(e){var t,n=r(e)&&!a(e),o=!1,s=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],l=s.length;for(t=0;t<l;t+=1)o=o||i(e,s[t]);return n&&o}(e)||null==e}(arguments[0])?(e=arguments[0],o=void 0):function(e){for(var t=r(e)&&!a(e),n=!1,o=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],s=0;s<o.length;s+=1)n=n||i(e,o[s]);return t&&n}(arguments[0])&&(o=arguments[0],e=void 0):o=e=void 0);var u=Lt(e=e||Ot(),this).startOf("day");u=t.calendarFormat(this,u)||"sameElse",o=o&&(C(o[u])?o[u].call(this,e):o[u]);return this.format(o||this.localeData().calendar(u,this,Ot(e)))},re.clone=function(){return new _(this)},re.diff=function(e,t,n){var r,i,a;if(!this.isValid())return NaN;if(!(r=Lt(e,this)).isValid())return NaN;switch(i=6e4*(r.utcOffset()-this.utcOffset()),t=j(t)){case"year":a=Bt(this,r)/12;break;case"month":a=Bt(this,r);break;case"quarter":a=Bt(this,r)/3;break;case"second":a=(this-r)/1e3;break;case"minute":a=(this-r)/6e4;break;case"hour":a=(this-r)/36e5;break;case"day":a=(this-r-i)/864e5;break;case"week":a=(this-r-i)/6048e5;break;default:a=this-r}return n?a:le(a)},re.endOf=function(e){var n,r;if(void 0!==(e=j(e))&&"millisecond"!==e&&this.isValid()){switch(r=this._isUTC?Xt:Kt,e){case"year":n=r(this.year()+1,0,1)-1;break;case"quarter":n=r(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":n=r(this.year(),this.month()+1,1)-1;break;case"week":n=r(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":n=r(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":n=r(this.year(),this.month(),this.date()+1)-1;break;case"hour":n=this._d.valueOf(),n+=36e5-Qt(n+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":n=this._d.valueOf(),n+=6e4-Qt(n,6e4)-1;break;case"second":n=this._d.valueOf(),n+=1e3-Qt(n,1e3)-1}this._d.setTime(n),t.updateOffset(this,!0)}return this},re.format=function(e){return e=N(this,e=e||(this.isUtc()?t.defaultFormatUtc:t.defaultFormat)),this.localeData().postformat(e)},re.from=function(e,t){return this.isValid()&&(b(e)&&e.isValid()||Ot(e).isValid())?Wt({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},re.fromNow=function(e){return this.from(Ot(),e)},re.to=function(e,t){return this.isValid()&&(b(e)&&e.isValid()||Ot(e).isValid())?Wt({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},re.toNow=function(e){return this.to(Ot(),e)},re.get=function(e){return C(this[e=j(e)])?this[e]():this},re.invalidAt=function(){return f(this).overflow},re.isAfter=function(e,t){return e=b(e)?e:Ot(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=j(t)||"millisecond")?this.valueOf()>e.valueOf():e.valueOf()<this.clone().startOf(t).valueOf())},re.isBefore=function(e,t){return e=b(e)?e:Ot(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=j(t)||"millisecond")?this.valueOf()<e.valueOf():this.clone().endOf(t).valueOf()<e.valueOf())},re.isBetween=function(e,t,n,r){return e=b(e)?e:Ot(e),t=b(t)?t:Ot(t),!!(this.isValid()&&e.isValid()&&t.isValid())&&("("===(r=r||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===r[1]?this.isBefore(t,n):!this.isAfter(t,n))},re.isSame=function(e,t){e=b(e)?e:Ot(e);return!(!this.isValid()||!e.isValid())&&("millisecond"===(t=j(t)||"millisecond")?this.valueOf()===e.valueOf():(e=e.valueOf(),this.clone().startOf(t).valueOf()<=e&&e<=this.clone().endOf(t).valueOf()))},re.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},re.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},re.isValid=function(){return p(this)},re.lang=Ze,re.locale=Zt,re.localeData=qt,re.max=Z,re.min=B,re.parsingFlags=function(){return c({},f(this))},re.set=function(e,t){if("object"==typeof e)for(var n=function(e){var t,n=[];for(t in e)i(e,t)&&n.push({unit:t,priority:P[t]});return n.sort(function(e,t){return e.priority-t.priority}),n}(e=U(e)),r=n.length,a=0;a<r;a++)this[n[a].unit](e[n[a].unit]);else if(C(this[e=j(e)]))return this[e](t);return this},re.startOf=function(e){var n,r;if(void 0!==(e=j(e))&&"millisecond"!==e&&this.isValid()){switch(r=this._isUTC?Xt:Kt,e){case"year":n=r(this.year(),0,1);break;case"quarter":n=r(this.year(),this.month()-this.month()%3,1);break;case"month":n=r(this.year(),this.month(),1);break;case"week":n=r(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":n=r(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":n=r(this.year(),this.month(),this.date());break;case"hour":n=this._d.valueOf(),n-=Qt(n+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":n=this._d.valueOf(),n-=Qt(n,6e4);break;case"second":n=this._d.valueOf(),n-=Qt(n,1e3)}this._d.setTime(n),t.updateOffset(this,!0)}return this},re.subtract=Ge,re.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},re.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},re.toDate=function(){return new Date(this.valueOf())},re.toISOString=function(e){var t;return this.isValid()?(t=(e=!0!==e)?this.clone().utc():this).year()<0||9999<t.year()?N(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):C(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",N(t,"Z")):N(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ"):null},re.inspect=function(){var e,t,n;return this.isValid()?(t="moment",e="",this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z"),t="["+t+'("]',n=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(t+n+"-MM-DD[T]HH:mm:ss.SSS"+e+'[")]')):"moment.invalid(/* "+this._i+" */)"},"undefined"!=typeof Symbol&&null!=Symbol.for&&(re[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),re.toJSON=function(){return this.isValid()?this.toISOString():null},re.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},re.unix=function(){return Math.floor(this.valueOf()/1e3)},re.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},re.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},re.eraName=function(){for(var e,t=this.localeData().eras(),n=0,r=t.length;n<r;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].name;if(t[n].until<=e&&e<=t[n].since)return t[n].name}return""},re.eraNarrow=function(){for(var e,t=this.localeData().eras(),n=0,r=t.length;n<r;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].narrow;if(t[n].until<=e&&e<=t[n].since)return t[n].narrow}return""},re.eraAbbr=function(){for(var e,t=this.localeData().eras(),n=0,r=t.length;n<r;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].abbr;if(t[n].until<=e&&e<=t[n].since)return t[n].abbr}return""},re.eraYear=function(){for(var e,n,r=this.localeData().eras(),i=0,a=r.length;i<a;++i)if(e=r[i].since<=r[i].until?1:-1,n=this.clone().startOf("day").valueOf(),r[i].since<=n&&n<=r[i].until||r[i].until<=n&&n<=r[i].since)return(this.year()-t(r[i].since).year())*e+r[i].offset;return this.year()},re.year=De,re.isLeapYear=function(){return he(this.year())},re.weekYear=function(e){return rn.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},re.isoWeekYear=function(e){return rn.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},re.quarter=re.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},re.month=Ee,re.daysInMonth=function(){return Ie(this.year(),this.month())},re.week=re.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},re.isoWeek=re.isoWeeks=function(e){var t=Ve(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},re.weeksInYear=function(){var e=this.localeData()._week;return ze(this.year(),e.dow,e.doy)},re.weeksInWeekYear=function(){var e=this.localeData()._week;return ze(this.weekYear(),e.dow,e.doy)},re.isoWeeksInYear=function(){return ze(this.year(),1,4)},re.isoWeeksInISOWeekYear=function(){return ze(this.isoWeekYear(),1,4)},re.date=J,re.day=re.days=function(e){var t,n,r;return this.isValid()?(t=Ae(this,"Day"),null!=e?(n=e,r=this.localeData(),e="string"!=typeof n?n:isNaN(n)?"number"==typeof(n=r.weekdaysParse(n))?n:null:parseInt(n,10),this.add(e-t,"d")):t):null!=e?this:NaN},re.weekday=function(e){var t;return this.isValid()?(t=(this.day()+7-this.localeData()._week.dow)%7,null==e?t:this.add(e-t,"d")):null!=e?this:NaN},re.isoWeekday=function(e){var t,n;return this.isValid()?null!=e?(t=e,n=this.localeData(),n="string"==typeof t?n.weekdaysParse(t)%7||7:isNaN(t)?null:t,this.day(this.day()%7?n:n-7)):this.day()||7:null!=e?this:NaN},re.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},re.hour=re.hours=ne,re.minute=re.minutes=z,re.second=re.seconds=Q,re.millisecond=re.milliseconds=H,re.utcOffset=function(e,n,r){var i,a=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null==e)return this._isUTC?a:Et(this);if("string"==typeof e){if(null===(e=Nt(te,e)))return this}else Math.abs(e)<16&&!r&&(e*=60);return!this._isUTC&&n&&(i=Et(this)),this._offset=e,this._isUTC=!0,null!=i&&this.add(i,"m"),a!==e&&(!n||this._changeInProgress?Ht(this,Wt(e-a,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null)),this},re.utc=function(e){return this.utcOffset(0,e)},re.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e)&&this.subtract(Et(this),"m"),this},re.parseZone=function(){var e;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(e=Nt(ee,this._i))?this.utcOffset(e):this.utcOffset(0,!0)),this},re.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?Ot(e).utcOffset():0,(this.utcOffset()-e)%60==0)},re.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},re.isLocal=function(){return!!this.isValid()&&!this._isUTC},re.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},re.isUtc=jt,re.isUTC=jt,re.zoneAbbr=function(){return this._isUTC?"UTC":""},re.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},re.dates=k("dates accessor is deprecated. Use date instead.",J),re.months=k("months accessor is deprecated. Use month instead",Ee),re.years=k("years accessor is deprecated. Use year instead",De),re.zone=k("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(e,t){return null!=e?(this.utcOffset(e="string"!=typeof e?-e:e,t),this):-this.utcOffset()}),re.isDSTShifted=k("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){var e,t;return o(this._isDSTShifted)&&(y(e={},this),(e=Ct(e))._a?(t=(e._isUTC?d:Ot)(e._a),this._isDSTShifted=this.isValid()&&0<function(e,t){for(var n=Math.min(e.length,t.length),r=Math.abs(e.length-t.length),i=0,a=0;a<n;a++)ue(e[a])!==ue(t[a])&&i++;return i+r}(e._a,t.toArray())):this._isDSTShifted=!1),this._isDSTShifted}),(ie=O.prototype).calendar=function(e,t,n){return C(e=this._calendar[e]||this._calendar.sameElse)?e.call(t,n):e},ie.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(I).map(function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e}).join(""),this._longDateFormat[e])},ie.invalidDate=function(){return this._invalidDate},ie.ordinal=function(e){return this._ordinal.replace("%d",e)},ie.preparse=sn,ie.postformat=sn,ie.relativeTime=function(e,t,n,r){var i=this._relativeTime[n];return C(i)?i(e,t,n,r):i.replace(/%d/i,e)},ie.pastFuture=function(e,t){return C(e=this._relativeTime[0<e?"future":"past"])?e(t):e.replace(/%s/i,t)},ie.set=function(e){var t,n;for(n in e)i(e,n)&&(C(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},ie.eras=function(e,n){for(var r,i=this._eras||ct("en")._eras,a=0,o=i.length;a<o;++a){if("string"==typeof i[a].since)r=t(i[a].since).startOf("day"),i[a].since=r.valueOf();switch(typeof i[a].until){case"undefined":i[a].until=1/0;break;case"string":r=t(i[a].until).startOf("day").valueOf(),i[a].until=r.valueOf()}}return i},ie.erasParse=function(e,t,n){var r,i,a,o,s,l=this.eras();for(e=e.toUpperCase(),r=0,i=l.length;r<i;++r)if(a=l[r].name.toUpperCase(),o=l[r].abbr.toUpperCase(),s=l[r].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(o===e)return l[r];break;case"NNNN":if(a===e)return l[r];break;case"NNNNN":if(s===e)return l[r]}else if(0<=[a,o,s].indexOf(e))return l[r]},ie.erasConvertYear=function(e,n){var r=e.since<=e.until?1:-1;return void 0===n?t(e.since).year():t(e.since).year()+(n-e.offset)*r},ie.erasAbbrRegex=function(e){return i(this,"_erasAbbrRegex")||tn.call(this),e?this._erasAbbrRegex:this._erasRegex},ie.erasNameRegex=function(e){return i(this,"_erasNameRegex")||tn.call(this),e?this._erasNameRegex:this._erasRegex},ie.erasNarrowRegex=function(e){return i(this,"_erasNarrowRegex")||tn.call(this),e?this._erasNarrowRegex:this._erasRegex},ie.months=function(e,t){return e?(n(this._months)?this._months:this._months[(this._months.isFormat||$e).test(t)?"format":"standalone"])[e.month()]:n(this._months)?this._months:this._months.standalone},ie.monthsShort=function(e,t){return e?(n(this._monthsShort)?this._monthsShort:this._monthsShort[$e.test(t)?"format":"standalone"])[e.month()]:n(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},ie.monthsParse=function(e,t,n){var r,i;if(this._monthsParseExact)return function(e,t,n){var r,i,a;e=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],r=0;r<12;++r)a=d([2e3,r]),this._shortMonthsParse[r]=this.monthsShort(a,"").toLocaleLowerCase(),this._longMonthsParse[r]=this.months(a,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(i=Ce.call(this._shortMonthsParse,e))?i:null:-1!==(i=Ce.call(this._longMonthsParse,e))?i:null:"MMM"===t?-1!==(i=Ce.call(this._shortMonthsParse,e))||-1!==(i=Ce.call(this._longMonthsParse,e))?i:null:-1!==(i=Ce.call(this._longMonthsParse,e))||-1!==(i=Ce.call(this._shortMonthsParse,e))?i:null}.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),r=0;r<12;r++){if(i=d([2e3,r]),n&&!this._longMonthsParse[r]&&(this._longMonthsParse[r]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[r]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),n||this._monthsParse[r]||(i="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[r]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[r].test(e))return r;if(n&&"MMM"===t&&this._shortMonthsParse[r].test(e))return r;if(!n&&this._monthsParse[r].test(e))return r}},ie.monthsRegex=function(e){return this._monthsParseExact?(i(this,"_monthsRegex")||je.call(this),e?this._monthsStrictRegex:this._monthsRegex):(i(this,"_monthsRegex")||(this._monthsRegex=Ne),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},ie.monthsShortRegex=function(e){return this._monthsParseExact?(i(this,"_monthsRegex")||je.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(i(this,"_monthsShortRegex")||(this._monthsShortRegex=Ye),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},ie.week=function(e){return Ve(e,this._week.dow,this._week.doy).week},ie.firstDayOfYear=function(){return this._week.doy},ie.firstDayOfWeek=function(){return this._week.dow},ie.weekdays=function(e,t){return t=n(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"],!0===e?He(t,this._week.dow):e?t[e.day()]:t},ie.weekdaysMin=function(e){return!0===e?He(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},ie.weekdaysShort=function(e){return!0===e?He(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},ie.weekdaysParse=function(e,t,n){var r,i;if(this._weekdaysParseExact)return function(e,t,n){var r,i,a;e=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],r=0;r<7;++r)a=d([2e3,1]).day(r),this._minWeekdaysParse[r]=this.weekdaysMin(a,"").toLocaleLowerCase(),this._shortWeekdaysParse[r]=this.weekdaysShort(a,"").toLocaleLowerCase(),this._weekdaysParse[r]=this.weekdays(a,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(i=Ce.call(this._weekdaysParse,e))?i:null:"ddd"===t?-1!==(i=Ce.call(this._shortWeekdaysParse,e))?i:null:-1!==(i=Ce.call(this._minWeekdaysParse,e))?i:null:"dddd"===t?-1!==(i=Ce.call(this._weekdaysParse,e))||-1!==(i=Ce.call(this._shortWeekdaysParse,e))||-1!==(i=Ce.call(this._minWeekdaysParse,e))?i:null:"ddd"===t?-1!==(i=Ce.call(this._shortWeekdaysParse,e))||-1!==(i=Ce.call(this._weekdaysParse,e))||-1!==(i=Ce.call(this._minWeekdaysParse,e))?i:null:-1!==(i=Ce.call(this._minWeekdaysParse,e))||-1!==(i=Ce.call(this._weekdaysParse,e))||-1!==(i=Ce.call(this._shortWeekdaysParse,e))?i:null}.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),r=0;r<7;r++){if(i=d([2e3,1]).day(r),n&&!this._fullWeekdaysParse[r]&&(this._fullWeekdaysParse[r]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[r]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[r]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[r]||(i="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[r]=new RegExp(i.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[r].test(e))return r;if(n&&"ddd"===t&&this._shortWeekdaysParse[r].test(e))return r;if(n&&"dd"===t&&this._minWeekdaysParse[r].test(e))return r;if(!n&&this._weekdaysParse[r].test(e))return r}},ie.weekdaysRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Ke.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(i(this,"_weekdaysRegex")||(this._weekdaysRegex=qe),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},ie.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Ke.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(i(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=Je),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},ie.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(i(this,"_weekdaysRegex")||Ke.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(i(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Qe),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},ie.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},ie.meridiem=function(e,t,n){return 11<e?n?"pm":"PM":n?"am":"AM"},lt("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===ue(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),t.lang=k("moment.lang is deprecated. Use moment.locale instead.",lt),t.langData=k("moment.langData is deprecated. Use moment.localeData instead.",ct);var dn=Math.abs;function fn(e,t,n,r){return t=Wt(t,n),e._milliseconds+=r*t._milliseconds,e._days+=r*t._days,e._months+=r*t._months,e._bubble()}function pn(e){return e<0?Math.floor(e):Math.ceil(e)}function hn(e){return 4800*e/146097}function mn(e){return 146097*e/4800}function vn(e){return function(){return this.as(e)}}function gn(e){return function(){return this.isValid()?this._data[e]:NaN}}W=vn("ms"),F=vn("s"),q=vn("m"),V=vn("h"),Te=vn("d"),Z=vn("w"),B=vn("M"),Ge=vn("Q"),ne=vn("y"),z=W;Q=gn("milliseconds"),H=gn("seconds"),J=gn("minutes"),De=gn("hours"),ie=gn("days");var yn=gn("months"),_n=gn("years"),bn=Math.round,wn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};var kn=Math.abs;function Sn(e){return(0<e)-(e<0)||+e}function xn(){var e,t,n,r,i,a,o,s,l,u,c;return this.isValid()?(e=kn(this._milliseconds)/1e3,t=kn(this._days),n=kn(this._months),(s=this.asSeconds())?(r=le(e/60),i=le(r/60),e%=60,r%=60,a=le(n/12),n%=12,o=e?e.toFixed(3).replace(/\.?0+$/,""):"",l=Sn(this._months)!==Sn(s)?"-":"",u=Sn(this._days)!==Sn(s)?"-":"",c=Sn(this._milliseconds)!==Sn(s)?"-":"",(s<0?"-":"")+"P"+(a?l+a+"Y":"")+(n?l+n+"M":"")+(t?u+t+"D":"")+(i||r||e?"T":"")+(i?c+i+"H":"")+(r?c+r+"M":"")+(e?c+o+"S":"")):"P0D"):this.localeData().invalidDate()}var Cn=It.prototype;return Cn.isValid=function(){return this._isValid},Cn.abs=function(){var e=this._data;return this._milliseconds=dn(this._milliseconds),this._days=dn(this._days),this._months=dn(this._months),e.milliseconds=dn(e.milliseconds),e.seconds=dn(e.seconds),e.minutes=dn(e.minutes),e.hours=dn(e.hours),e.months=dn(e.months),e.years=dn(e.years),this},Cn.add=function(e,t){return fn(this,e,t,1)},Cn.subtract=function(e,t){return fn(this,e,t,-1)},Cn.as=function(e){if(!this.isValid())return NaN;var t,n,r=this._milliseconds;if("month"===(e=j(e))||"quarter"===e||"year"===e)switch(t=this._days+r/864e5,n=this._months+hn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(mn(this._months)),e){case"week":return t/7+r/6048e5;case"day":return t+r/864e5;case"hour":return 24*t+r/36e5;case"minute":return 1440*t+r/6e4;case"second":return 86400*t+r/1e3;case"millisecond":return Math.floor(864e5*t)+r;default:throw new Error("Unknown unit "+e)}},Cn.asMilliseconds=W,Cn.asSeconds=F,Cn.asMinutes=q,Cn.asHours=V,Cn.asDays=Te,Cn.asWeeks=Z,Cn.asMonths=B,Cn.asQuarters=Ge,Cn.asYears=ne,Cn.valueOf=z,Cn._bubble=function(){var e=this._milliseconds,t=this._days,n=this._months,r=this._data;return 0<=e&&0<=t&&0<=n||e<=0&&t<=0&&n<=0||(e+=864e5*pn(mn(n)+t),n=t=0),r.milliseconds=e%1e3,e=le(e/1e3),r.seconds=e%60,e=le(e/60),r.minutes=e%60,e=le(e/60),r.hours=e%24,t+=le(e/24),n+=e=le(hn(t)),t-=pn(mn(e)),e=le(n/12),n%=12,r.days=t,r.months=n,r.years=e,this},Cn.clone=function(){return Wt(this)},Cn.get=function(e){return e=j(e),this.isValid()?this[e+"s"]():NaN},Cn.milliseconds=Q,Cn.seconds=H,Cn.minutes=J,Cn.hours=De,Cn.days=ie,Cn.weeks=function(){return le(this.days()/7)},Cn.months=yn,Cn.years=_n,Cn.humanize=function(e,t){var n,r;return this.isValid()?(n=!1,r=wn,"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(n=e),"object"==typeof t&&(r=Object.assign({},wn,t),null!=t.s)&&null==t.ss&&(r.ss=t.s-1),t=function(e,t,n,r){var i=Wt(e).abs(),a=bn(i.as("s")),o=bn(i.as("m")),s=bn(i.as("h")),l=bn(i.as("d")),u=bn(i.as("M")),c=bn(i.as("w"));return i=bn(i.as("y")),a=(a<=n.ss?["s",a]:a<n.s&&["ss",a])||(o<=1?["m"]:o<n.m&&["mm",o])||(s<=1?["h"]:s<n.h&&["hh",s])||(l<=1?["d"]:l<n.d&&["dd",l]),(a=(a=null!=n.w?a||(c<=1?["w"]:c<n.w&&["ww",c]):a)||(u<=1?["M"]:u<n.M&&["MM",u])||(i<=1?["y"]:["yy",i]))[2]=t,a[3]=0<+e,a[4]=r,function(e,t,n,r,i){return i.relativeTime(t||1,!!n,e,r)}.apply(null,a)}(this,!n,r,e=this.localeData()),n&&(t=e.pastFuture(+this,t)),e.postformat(t)):this.localeData().invalidDate()},Cn.toISOString=xn,Cn.toString=xn,Cn.toJSON=xn,Cn.locale=Zt,Cn.localeData=qt,Cn.toIsoString=k("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",xn),Cn.lang=Ze,Y("X",0,0,"unix"),Y("x",0,0,"valueOf"),ae("x",X),ae("X",/[+-]?\d+(\.\d{1,3})?/),fe("X",function(e,t,n){n._d=new Date(1e3*parseFloat(e))}),fe("x",function(e,t,n){n._d=new Date(ue(e))}),t.version="2.30.1",e=Ot,t.fn=re,t.min=function(){return At("isBefore",[].slice.call(arguments,0))},t.max=function(){return At("isAfter",[].slice.call(arguments,0))},t.now=function(){return Date.now?Date.now():+new Date},t.utc=d,t.unix=function(e){return Ot(1e3*e)},t.months=function(e,t){return un(e,t,"months")},t.isDate=l,t.locale=lt,t.invalid=h,t.duration=Wt,t.isMoment=b,t.weekdays=function(e,t,n){return cn(e,t,n,"weekdays")},t.parseZone=function(){return Ot.apply(null,arguments).parseZone()},t.localeData=ct,t.isDuration=Tt,t.monthsShort=function(e,t){return un(e,t,"monthsShort")},t.weekdaysMin=function(e,t,n){return cn(e,t,n,"weekdaysMin")},t.defineLocale=ut,t.updateLocale=function(e,t){var n,r;return null!=t?(r=rt,null!=it[e]&&null!=it[e].parentLocale?it[e].set(D(it[e]._config,t)):(t=D(r=null!=(n=st(e))?n._config:r,t),null==n&&(t.abbr=e),(r=new O(t)).parentLocale=it[e],it[e]=r),lt(e)):null!=it[e]&&(null!=it[e].parentLocale?(it[e]=it[e].parentLocale,e===lt()&&lt(e)):null!=it[e]&&delete it[e]),it[e]},t.locales=function(){return A(it)},t.weekdaysShort=function(e,t,n){return cn(e,t,n,"weekdaysShort")},t.normalizeUnits=j,t.relativeTimeRounding=function(e){return void 0===e?bn:"function"==typeof e&&(bn=e,!0)},t.relativeTimeThreshold=function(e,t){return void 0!==wn[e]&&(void 0===t?wn[e]:(wn[e]=t,"s"===e&&(wn.ss=t-1),!0))},t.calendarFormat=function(e,t){return(e=e.diff(t,"days",!0))<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},t.prototype=re,t.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},t});var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.arrayIteratorImpl=function(e){var t=0;return function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}},$jscomp.arrayIterator=function(e){return{next:$jscomp.arrayIteratorImpl(e)}},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.ISOLATE_POLYFILLS=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(e,t,n){return e==Array.prototype||e==Object.prototype||(e[t]=n.value),e},$jscomp.getGlobal=function(e){e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var t=0;t<e.length;++t){var n=e[t];if(n&&n.Math==Math)return n}throw Error("Cannot find global object")},$jscomp.global=$jscomp.getGlobal(this),$jscomp.IS_SYMBOL_NATIVE="function"==typeof Symbol&&"symbol"==typeof Symbol("x"),$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE,$jscomp.polyfills={},$jscomp.propertyToPolyfillSymbol={},$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(e,t){var n=$jscomp.propertyToPolyfillSymbol[t];return null==n?e[t]:void 0!==(n=e[n])?n:e[t]};$jscomp.polyfill=function(e,t,n,r){t&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(e,t,n,r):$jscomp.polyfillUnisolated(e,t,n,r))},$jscomp.polyfillUnisolated=function(e,t,n,r){for(n=$jscomp.global,e=e.split("."),r=0;r<e.length-1;r++){var i=e[r];if(!(i in n))return;n=n[i]}(t=t(r=n[e=e[e.length-1]]))!=r&&null!=t&&$jscomp.defineProperty(n,e,{configurable:!0,writable:!0,value:t})},$jscomp.polyfillIsolated=function(e,t,n,r){var i=e.split(".");e=1===i.length,r=i[0],r=!e&&r in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var a=0;a<i.length-1;a++){var o=i[a];if(!(o in r))return;r=r[o]}i=i[i.length-1],null!=(t=t(n=$jscomp.IS_SYMBOL_NATIVE&&"es6"===n?r[i]:null))&&(e?$jscomp.defineProperty($jscomp.polyfills,i,{configurable:!0,writable:!0,value:t}):t!==n&&($jscomp.propertyToPolyfillSymbol[i]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(i):$jscomp.POLYFILL_PREFIX+i,i=$jscomp.propertyToPolyfillSymbol[i],$jscomp.defineProperty(r,i,{configurable:!0,writable:!0,value:t})))},$jscomp.initSymbol=function(){},$jscomp.polyfill("Symbol",function(e){if(e)return e;var t=function(e,t){this.$jscomp$symbol$id_=e,$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:t})};t.prototype.toString=function(){return this.$jscomp$symbol$id_};var n=0,r=function(e){if(this instanceof r)throw new TypeError("Symbol is not a constructor");return new t("jscomp_symbol_"+(e||"")+"_"+n++,e)};return r},"es6","es3"),$jscomp.initSymbolIterator=function(){},$jscomp.polyfill("Symbol.iterator",function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var t="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),n=0;n<t.length;n++){var r=$jscomp.global[t[n]];"function"==typeof r&&"function"!=typeof r.prototype[e]&&$jscomp.defineProperty(r.prototype,e,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return e},"es6","es3"),$jscomp.initSymbolAsyncIterator=function(){},$jscomp.iteratorPrototype=function(e){return(e={next:e})[Symbol.iterator]=function(){return this},e},function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t((e=e||self).VeeValidate={},e.Vue)}(this,function(e,t){function n(e,t,n,r){return new(n||(n=Promise))(function(i,a){function o(e){try{l(r.next(e))}catch(e){a(e)}}function s(e){try{l(r.throw(e))}catch(e){a(e)}}function l(e){e.done?i(e.value):new n(function(t){t(e.value)}).then(o,s)}l((r=r.apply(e,t||[])).next())})}function r(e,t){function n(n){return function(o){return function(n){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(a=2&n[0]?i.return:n[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,n[1])).done)return a;switch(i=0,a&&(n=[2&n[0],a.value]),n[0]){case 0:case 1:a=n;break;case 4:return s.label++,{value:n[1],done:!1};case 5:s.label++,i=n[1],n=[0];continue;case 7:n=s.ops.pop(),s.trys.pop();continue;default:if(!(a=s.trys,(a=0<a.length&&a[a.length-1])||6!==n[0]&&2!==n[0])){s=0;continue}if(3===n[0]&&(!a||n[1]>a[0]&&n[1]<a[3]))s.label=n[1];else if(6===n[0]&&s.label<a[1])s.label=a[1],a=n;else{if(!(a&&s.label<a[2])){a[2]&&s.ops.pop(),s.trys.pop();continue}s.label=a[2],s.ops.push(n)}}n=t.call(e,s)}catch(e){n=[6,e],i=0}finally{r=a=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,o])}}var r,i,a,o,s={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:n(0),throw:n(1),return:n(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o}function i(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;e=Array(e);var r=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,o=i.length;a<o;a++,r++)e[r]=i[a];return e}function a(e){return null==e}function o(e,t){if(e instanceof RegExp&&t instanceof RegExp)return o(e.source,t.source)&&o(e.flags,t.flags);if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!o(e[n],t[n]))return!1;return!0}return Z(e)&&Z(t)?Object.keys(e).every(function(n){return o(e[n],t[n])})&&Object.keys(t).every(function(n){return o(e[n],t[n])}):e!=e&&t!=t||e===t}function s(e){return""!==e&&!a(e)}function l(e){return"function"==typeof e}function u(e){return l(e)&&!!e.__locatorRef}function c(e,t){var n=Array.isArray(e)?e:f(e);if(l(n.findIndex))return n.findIndex(t);for(var r=0;r<n.length;r++)if(t(n[r],r))return r;return-1}function d(e,t){return-1!==e.indexOf(t)}function f(e){if(l(Array.from))return Array.from(e);for(var t=[],n=e.length,r=0;r<n;r++)t.push(e[r]);return t}function p(e){return l(Object.values)?Object.values(e):Object.keys(e).map(function(t){return e[t]})}function h(e,t){return Object.keys(t).forEach(function(n){Z(t[n])?(e[n]||(e[n]={}),h(e[n],t[n])):e[n]=t[n]}),e}function m(e){return e}function v(e,t,n){return void 0===t&&(t=0),void 0===n&&(n={cancelled:!1}),0===t?e:function(){for(var i=[],a=0;a<arguments.length;a++)i[a]=arguments[a];clearTimeout(r),r=setTimeout(function(){r=void 0,n.cancelled||e.apply(void 0,i)},t)};var r}function g(e,t){return e.replace(/{([^}]+)}/g,function(e,n){return n in t?t[n]:"{"+n+"}"})}function y(e){var t={};return Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),e?Z(e)&&e._$$isNormalized?e:Z(e)?Object.keys(e).reduce(function(t,n){var r=!0===e[n]?[]:Array.isArray(e[n])||Z(e[n])?e[n]:[e[n]];return!1!==e[n]&&(t[n]=_(n,r)),t},t):"string"!=typeof e?(console.warn("[vee-validate] rules must be either a string or an object."),t):e.split("|").reduce(function(e,t){var n=[],r=t.split(":")[0];return d(t,":")&&(n=t.split(":").slice(1).join(":").split(",")),r?(e[r]=_(r,n),e):e},t):t}function _(e,t){var n=J.getRuleDefinition(e);if(!n)return t;var r={};if(!n.params&&!Array.isArray(t))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(t)&&!n.params)return t;if(!n.params||n.params.length<t.length&&Array.isArray(t))var i,a=t.map(function(e,t){var r,a=null===(r=n.params)||void 0===r?void 0:r[t];return i=a||i,a||(a=i),a});else a=n.params;for(var o=0;o<a.length;o++){var s=a[o],l=s.default;Array.isArray(t)?o in t&&(l=t[o]):s.name in t?l=t[s.name]:1===a.length&&(l=t),s.isTarget&&(l=b(l,s.cast)),"string"==typeof l&&"@"===l[0]&&(l=b(l.slice(1),s.cast)),!u(l)&&s.cast&&(l=s.cast(l)),r[s.name]?(r[s.name]=Array.isArray(r[s.name])?r[s.name]:[r[s.name]],r[s.name].push(l)):r[s.name]=l}return r}function b(e,t){var n=function(n){return n=n[e],t?t(n):n};return n.__locatorRef=e,n}function w(e,t,i){var a,o,s,l,u,c;return void 0===i&&(i={}),n(this,void 0,void 0,function(){var n,d,f,p,h,m;return r(this,function(r){switch(r.label){case 0:return n=null===(a=i)||void 0===a?void 0:a.bails,d=null===(o=i)||void 0===o?void 0:o.skipIfEmpty,[4,k({name:(null===(s=i)||void 0===s?void 0:s.name)||"{field}",rules:y(t),bails:null==n||n,skipIfEmpty:null==d||d,forceRequired:!1,crossTable:(null===(l=i)||void 0===l?void 0:l.values)||{},names:(null===(u=i)||void 0===u?void 0:u.names)||{},customMessages:(null===(c=i)||void 0===c?void 0:c.customMessages)||{}},e,i)];case 1:return f=r.sent(),p=[],h={},m={},f.errors.forEach(function(e){var t=e.msg();p.push(t),h[e.rule]=t,m[e.rule]=e.msg}),[2,{valid:f.valid,errors:p,failedRules:h,regenerateMap:m}]}})})}function k(e,t,i){var a=void 0!==(i=(void 0===i?{}:i).isInitial)&&i;return n(this,void 0,void 0,function(){var n,i,o,s,l,u,c,d;return r(this,function(r){switch(r.label){case 0:return[4,S(e,t)];case 1:if(n=r.sent(),i=n.shouldSkip,o=n.errors,i)return[2,{valid:!o.length,errors:o}];s=Object.keys(e.rules).filter(function(e){return!J.isRequireRule(e)}),l=s.length,u=0,r.label=2;case 2:return u<l?a&&J.isLazy(s[u])?[3,4]:(c=s[u],[4,x(e,t,{name:c,params:e.rules[c]})]):[3,5];case 3:if(!(d=r.sent()).valid&&d.error&&(o.push(d.error),e.bails))return[2,{valid:!1,errors:o}];r.label=4;case 4:return u++,[3,2];case 5:return[2,{valid:!o.length,errors:o}]}})})}function S(e,t){return n(this,void 0,void 0,function(){var n,i,o,s,l,u,c,d,f;return r(this,function(r){switch(r.label){case 0:var p;n=Object.keys(e.rules).filter(J.isRequireRule),i=n.length,o=[],(p=a(t)||""===t)||(p=Array.isArray(t)&&0===t.length),l=(s=p)&&e.skipIfEmpty,u=!1,c=0,r.label=1;case 1:return c<i?(d=n[c],[4,x(e,t,{name:d,params:e.rules[d]})]):[3,4];case 2:if(f=r.sent(),!Z(f))throw Error("Require rules has to return an object (see docs)");if(f.required&&(u=!0),!f.valid&&f.error&&(o.push(f.error),e.bails))return[2,{shouldSkip:!0,errors:o}];r.label=3;case 3:return c++,[3,1];case 4:return s&&!u&&!e.skipIfEmpty||!e.bails&&!l?[2,{shouldSkip:!1,errors:o}]:[2,{shouldSkip:!u&&s,errors:o}]}})})}function x(e,t,i){return n(this,void 0,void 0,function(){var n,a,o,s,l;return r(this,function(r){switch(r.label){case 0:if(!(n=J.getRuleDefinition(i.name))||!n.validate)throw Error("No such validator '"+i.name+"' exists.");return a=n.castValue?n.castValue(t):t,o=function(e,t){if(Array.isArray(e))return e;var n={};return Object.keys(e).forEach(function(r){var i=e[r];i=u(i)?i(t):i,n[r]=i}),n}(i.params,e.crossTable),[4,n.validate(a,o)];case 1:return"string"==typeof(s=r.sent())?(l=B(B({},o||{}),{_field_:e.name,_value_:t,_rule_:i.name}),[2,{valid:!1,error:{rule:i.name,msg:function(){return g(s,l)}}}]):(Z(s)||(s={valid:s}),[2,{valid:s.valid,required:s.required,error:s.valid?void 0:C(e,t,n,i.name,o)}])}})})}function C(e,t,n,r,i){var a,o=null!=(a=e.customMessages[r])?a:n.message;a=function(e,t,n){if(t=t.params,!t||0>=t.filter(function(e){return e.isTarget}).length)return{};var r={},i=e.rules[n];for(!Array.isArray(i)&&Z(i)&&(i=t.map(function(e){return i[e.name]})),n=0;n<t.length;n++){var a=t[n],o=i[n];u(o)&&(o=o.__locatorRef,r[a.name]=e.names[o]||o,r["_"+a.name+"_"]=e.crossTable[o])}return r}(e,n,r),n=function(e,t,n,r){var i={},a=e.rules[n],o=t.params||[];return a?(Object.keys(a).forEach(function(t,n){var r=a[t];if(!u(r))return{};var s=o[n];if(!s)return{};r=r.__locatorRef,i[s.name]=e.names[r]||r,i["_"+s.name+"_"]=e.crossTable[r]}),{userTargets:i,userMessage:r}):{}}(e,n,r,o),o=n.userTargets;var s=n.userMessage,l=B(B(B(B({},i||{}),{_field_:e.name,_value_:t,_rule_:r}),a),o);return{msg:function(){var t=s||Q.defaultMessage,n=e.name;return t="function"==typeof t?t(n,l):g(t,B(B({},l),{_field_:n}))},rule:r}}function D(){ee.$emit("change:locale")}function O(e){if(e.data){var t=e.data;if("model"in t)return t.model;if(e.data.directives)return function(e,t){var n=Array.isArray(e)?e:f(e),r=c(n,t);return-1===r?void 0:n[r]}(e.data.directives,function(e){return"model"===e.name})}}function A(e){var t,n,r,i=O(e);return i?{value:i.value}:(i=(null===(t=I(e))||void 0===t?void 0:t.prop)||"value",null!==(n=e.componentOptions)&&void 0!==n&&n.propsData&&i in e.componentOptions.propsData?{value:e.componentOptions.propsData[i]}:null!==(r=e.data)&&void 0!==r&&r.domProps&&"value"in e.data.domProps?{value:e.data.domProps.value}:void 0)}function M(e){return Array.isArray(e)||void 0===A(e)?function(e){return Array.isArray(e)?e:Array.isArray(e.children)?e.children:e.componentOptions&&Array.isArray(e.componentOptions.children)?e.componentOptions.children:[]}(e).reduce(function(e,t){var n=M(t);return n.length&&e.push.apply(e,n),e},[]):[e]}function I(e){return e.componentOptions?e.componentOptions.Ctor.options.model:null}function T(e,t,n){a(e[t])?e[t]=[n]:l(e[t])&&e[t].fns?((e=e[t]).fns=Array.isArray(e.fns)?e.fns:[e.fns],d(e.fns,n)||e.fns.push(n)):(l(e[t])&&(e[t]=[e[t]]),Array.isArray(e[t])&&!d(e[t],n)&&e[t].push(n))}function R(e,t,n){e.componentOptions?e.componentOptions&&(e.componentOptions.listeners||(e.componentOptions.listeners={}),T(e.componentOptions.listeners,t,n)):(e.data||(e.data={}),a(e.data.on)&&(e.data.on={}),T(e.data.on,t,n))}function $(e,t){var n;return e.componentOptions?(I(e)||{event:"input"}).event:null!==(n=null==t?void 0:t.modifiers)&&void 0!==n&&n.lazy?"change":ne(e)?"input":"change"}function Y(e,t){return e.$scopedSlots.default?e.$scopedSlots.default(t)||[]:e.$slots.default||[]}function N(e){return B(B({},e.flags),{errors:e.errors,classes:e.classes,failedRules:e.failedRules,reset:function(){return e.reset()},validate:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.validate.apply(e,t)},ariaInput:{"aria-invalid":e.flags.invalid?"true":"false","aria-required":e.isRequired?"true":"false","aria-errormessage":"vee_"+e.id},ariaMsg:{id:"vee_"+e.id,"aria-live":e.errors.length?"assertive":"off"}})}function L(e,t){e.initialized||(e.initialValue=t);var n=!!(!e._ignoreImmediate&&e.immediate||e.value!==t&&e.normalizedEvents.length||e._needsValidation||!e.initialized&&void 0===t);if(e._needsValidation=!1,e.value=t,e._ignoreImmediate=!0,n){var r=function(){if(e.immediate||e.flags.validated)return j(e);e.validateSilent()};e.initialized?r():e.$once("hook:mounted",function(){return r()})}}function E(e){return(l(e.mode)?e.mode:X[e.mode])(e)}function j(e){var t=e.validateSilent();return e._pendingValidation=t,t.then(function(n){return t===e._pendingValidation&&(e.applyResult(n),e._pendingValidation=void 0),n})}function U(e){e.$veeOnInput||(e.$veeOnInput=function(t){e.syncValue(t),e.setFlags({dirty:!0,pristine:!1})});var t=e.$veeOnInput;e.$veeOnBlur||(e.$veeOnBlur=function(){e.setFlags({touched:!0,untouched:!1})});var n=e.$veeOnBlur,r=e.$veeHandler,i=E(e);return r&&e.$veeDebounce===e.debounce||(r=v(function(){e.$nextTick(function(){e._pendingReset||j(e),e._pendingReset=!1})},i.debounce||e.debounce),e.$veeHandler=r,e.$veeDebounce=e.debounce),{onInput:t,onBlur:n,onValidate:r}}function P(e){var t=e.$_veeObserver.refs;return e.fieldDeps.reduce(function(e,n){return t[n]?(e.values[n]=t[n].value,e.names[n]=t[n].name,e):e},{names:{},values:{}})}function W(e,t,n){void 0===n&&(n=!0);var r=e.$_veeObserver.refs;if(e._veeWatchers||(e._veeWatchers={}),!r[t]&&n)return e.$once("hook:mounted",function(){W(e,t,!1)});!l(e._veeWatchers[t])&&r[t]&&(e._veeWatchers[t]=r[t].$watch("value",function(){e.flags.validated&&(e._needsValidation=!0,e.validate())}))}function F(e){e.$_veeObserver&&e.$_veeObserver.unobserve(e.id,"observer")}function V(e){e.$_veeObserver&&e.$_veeObserver.observe(e,"observer")}function z(){return B(B({},{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1}),{valid:!0,invalid:!1})}function H(){for(var e=i(p(this.refs),this.observers),t={},n=z(),r={},a=e.length,o=0;o<a;o++){var s=e[o];Array.isArray(s.errors)?(t[s.id]=s.errors,r[s.id]=B({id:s.id,name:s.name,failedRules:s.failedRules},s.flags)):(t=B(B({},t),s.errors),r=B(B({},r),s.fields))}return ae.forEach(function(t){var r=t[0];n[r]=e[t[1]](function(e){return e.flags[r]})}),{errors:t,flags:n,fields:r}}t=t&&t.hasOwnProperty("default")?t.default:t;var G,B=function(){return B=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},B.apply(this,arguments)},Z=function(e){return null!==e&&e&&"object"==typeof e&&!Array.isArray(e)},q={},J=function(){function e(){}return e.extend=function(e,t){var n=function(e){var t;return null!==(t=e.params)&&void 0!==t&&t.length&&(e.params=e.params.map(function(e){return"string"==typeof e?{name:e}:e})),e}(t);q[e]=q[e]?h(q[e],t):B({lazy:!1,computesRequired:!1},n)},e.isLazy=function(e){var t;return!(null===(t=q[e])||void 0===t||!t.lazy)},e.isRequireRule=function(e){var t;return!(null===(t=q[e])||void 0===t||!t.computesRequired)},e.getRuleDefinition=function(e){return q[e]},e}(),Q=B({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),K=function(e){Q=B(B({},Q),e)},X={aggressive:function(){return{on:["input","blur"]}},eager:function(e){return e.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},ee=new t,te=function(){function e(e,t){this.container={},this.locale=e,this.merge(t)}return e.prototype.resolve=function(e,t,n){return this.format(this.locale,e,t,n)},e.prototype.format=function(e,t,n,r){var i,a,o,s,u,c,d,f;return(n=(null===(o=null===(a=null===(i=this.container[e])||void 0===i?void 0:i.fields)||void 0===a?void 0:a[t])||void 0===o?void 0:o[n])||(null===(u=null===(s=this.container[e])||void 0===s?void 0:s.messages)||void 0===u?void 0:u[n]))||(n="{field} is not valid"),t=null!=(f=null===(d=null===(c=this.container[e])||void 0===c?void 0:c.names)||void 0===d?void 0:d[t])?f:t,l(n)?n(t,r):g(n,B(B({},r),{_field_:t}))},e.prototype.merge=function(e){h(this.container,e)},e.prototype.hasRule=function(e){var t,n;return!(null===(n=null===(t=this.container[this.locale])||void 0===t?void 0:t.messages)||void 0===n||!n[e])},e}(),ne=function(e){var t,n=(null===(t=e.data)||void 0===t?void 0:t.attrs)||e.elm;return!("input"!==e.tag||n&&n.type)||"textarea"===e.tag||d("text password search email tel url number".split(" "),null==n?void 0:n.type)},re=0,ie=t.extend({inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver={refs:{},observe:function(e){this.refs[e.id]=e},unobserve:function(e){delete this.refs[e]}}),this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,default:""},name:{type:String,default:null},mode:{type:[String,Function],default:function(){return Q.mode}},rules:{type:[Object,String],default:null},immediate:{type:Boolean,default:!1},bails:{type:Boolean,default:function(){return Q.bails}},skipIfEmpty:{type:Boolean,default:function(){return Q.skipOptional}},debounce:{type:Number,default:0},tag:{type:String,default:"span"},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},customMessages:{type:Object,default:function(){return{}}}},watch:{rules:{deep:!0,handler:function(e,t){this._needsValidation=!o(e,t)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1},failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var e=this;return Object.keys(this.normalizedRules).reduce(function(t,n){var r=function(e){return Array.isArray(e)?e.filter(u):Object.keys(e).filter(function(t){return u(e[t])}).map(function(t){return e[t]})}(e.normalizedRules[n]).map(function(e){return e.__locatorRef});return t.push.apply(t,r),r.forEach(function(t){W(e,t)}),t},[])},normalizedEvents:function(){var e=this;return(E(this).on||[]).map(function(t){return"input"===t?e._inputEventName:t})},isRequired:function(){var e=B(B({},this._resolvedRules),this.normalizedRules);return e=Object.keys(e).some(J.isRequireRule),this.flags.required=!!e,e},classes:function(){return function(e,t){for(var n={},r=Object.keys(t),i=r.length,o=function(i){i=r[i];var o=e&&e[i]||i,s=t[i];if(a(s)||("valid"===i||"invalid"===i)&&!t.validated)return"continue";"string"==typeof o?n[o]=s:Array.isArray(o)&&o.forEach(function(e){n[e]=s})},s=0;s<i;s++)o(s);return n}(Q.classes,this.flags)},normalizedRules:function(){return y(this.rules)}},created:function(){var e=this,t=function(){if(e.flags.validated){var t=e._regenerateMap;if(t){var n=[],r={};Object.keys(t).forEach(function(e){var i=t[e]();n.push(i),r[e]=i}),e.applyResult({errors:n,failedRules:r,regenerateMap:t})}else e.validate()}};ee.$on("change:locale",t),this.$on("hook:beforeDestroy",function(){ee.$off("change:locale",t)})},render:function(e){var t=this;this.registerField();var n=N(this);return M(n=Y(this,n)).forEach(function(e){var n,r,i,a,l;if(Q.useConstraintAttrs){var u,c=null===(u=e.data)||void 0===u?void 0:u.attrs;if(d(["input","select","textarea"],e.tag)&&c)if(u={},"required"in c&&!1!==c.required&&J.getRuleDefinition("required")&&(u.required="checkbox"!==c.type||[!0]),ne(e)){c=B,u=B({},u);var f=null===(l=e.data)||void 0===l?void 0:l.attrs;l={},f&&("email"===f.type&&J.getRuleDefinition("email")&&(l.email=["multiple"in f]),f.pattern&&J.getRuleDefinition("regex")&&(l.regex=f.pattern),0<=f.maxlength&&J.getRuleDefinition("max")&&(l.max=f.maxlength),0<=f.minlength&&J.getRuleDefinition("min")&&(l.min=f.minlength),"number"===f.type&&(s(f.min)&&J.getRuleDefinition("min_value")&&(l.min_value=Number(f.min)),s(f.max)&&J.getRuleDefinition("max_value")&&(l.max_value=Number(f.max)))),l=y(c(u,l))}else l=y(u);else l={}}else l={};o(t._resolvedRules,l)||(t._needsValidation=!0),d(["input","select","textarea"],e.tag)&&(t.fieldName=(null===(r=null===(n=e.data)||void 0===n?void 0:n.attrs)||void 0===r?void 0:r.name)||(null===(a=null===(i=e.data)||void 0===i?void 0:i.attrs)||void 0===a?void 0:a.id)),t._resolvedRules=l,function(e,t){var n=A(t);e._inputEventName=e._inputEventName||$(t,O(t)),L(e,null==n?void 0:n.value);var r=(n=U(e)).onBlur,i=n.onValidate;R(t,e._inputEventName,n.onInput),R(t,"blur",r),e.normalizedEvents.forEach(function(e){R(t,e,i)}),e.initialized=!0}(t,e)}),this.slim&&1>=n.length?n[0]:e(this.tag,n)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(e){var t=this;Object.keys(e).forEach(function(n){t.flags[n]=e[n]})},syncValue:function(e){this.value=e=function(e){var t,n;return e&&("undefined"!=typeof Event&&l(Event)&&e instanceof Event||e&&e.srcElement)?"file"===(e=e.target).type&&e.files?f(e.files):null!==(t=e._vModifiers)&&void 0!==t&&t.number?(t=parseFloat(e.value))!=t?e.value:t:null!==(n=e._vModifiers)&&void 0!==n&&n.trim&&"string"==typeof e.value?e.value.trim():e.value:e}(e),this.flags.changed=this.initialValue!==e},reset:function(){var e=this;this.errors=[],this.initialValue=this.value;var t={untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,failed:!1};t.required=this.isRequired,this.setFlags(t),this.failedRules={},this.validateSilent(),this._pendingValidation=void 0,this._pendingReset=!0,setTimeout(function(){e._pendingReset=!1},this.debounce)},validate:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(this,void 0,void 0,function(){return r(this,function(t){return 0<e.length&&this.syncValue(e[0]),[2,j(this)]})})},validateSilent:function(){return n(this,void 0,void 0,function(){var e,t;return r(this,function(n){switch(n.label){case 0:return this.setFlags({pending:!0}),e=B(B({},this._resolvedRules),this.normalizedRules),Object.defineProperty(e,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,w(this.value,e,B(B({name:this.name||this.fieldName},P(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return t=n.sent(),this.setFlags({pending:!1,valid:t.valid,invalid:!t.valid}),[2,t]}})})},setErrors:function(e){this.applyResult({errors:e,failedRules:{}})},applyResult:function(e){var t=e.errors,n=e.failedRules;e=e.regenerateMap,this.errors=t,this._regenerateMap=e,this.failedRules=B({},n||{}),this.setFlags({valid:!t.length,passed:!t.length,invalid:!!t.length,failed:!!t.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var e=function(e){return e.vid?e.vid:e.name?e.name:e.id?e.id:e.fieldName?e.fieldName:"_vee_"+ ++re}(this),t=this.id;!this.isActive||t===e&&this.$_veeObserver.refs[t]||(t!==e&&this.$_veeObserver.refs[t]===this&&this.$_veeObserver.unobserve(t),this.id=e,this.$_veeObserver.observe(this))}}}),ae=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],oe=0,se=t.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver",default:function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,default:"span"},vid:{type:String,default:function(){return"obs_"+oe++}},slim:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:z(),fields:{}}},created:function(){var e=this;this.id=this.vid,V(this);var t=v(function(t){var n=t.flags,r=t.fields;e.errors=t.errors,e.flags=n,e.fields=r},16);this.$watch(H,t)},activated:function(){V(this)},deactivated:function(){F(this)},beforeDestroy:function(){F(this)},render:function(e){var t=Y(this,B(B({},this.flags),{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=t.length?t[0]:e(this.tag,{on:this.$listeners},t)},methods:{observe:function(e,t){var n;void 0===t&&(t="provider"),"observer"===t?this.observers.push(e):this.refs=B(B({},this.refs),((n={})[e.id]=e,n))},unobserve:function(e,t){if(void 0===t&&(t="provider"),"provider"===t)this.refs[e]&&this.$delete(this.refs,e);else{var n=c(this.observers,function(t){return t.id===e});-1!==n&&this.observers.splice(n,1)}},validate:function(e){var t=void 0!==(e=(void 0===e?{}:e).silent)&&e;return n(this,void 0,void 0,function(){return r(this,function(e){switch(e.label){case 0:return[4,Promise.all(i(p(this.refs).filter(function(e){return!e.disabled}).map(function(e){return e[t?"validateSilent":"validate"]().then(function(e){return e.valid})}),this.observers.filter(function(e){return!e.disabled}).map(function(e){return e.validate({silent:t})})))];case 1:return[2,e.sent().every(function(e){return e})]}})})},handleSubmit:function(e){return n(this,void 0,void 0,function(){return r(this,function(t){switch(t.label){case 0:return[4,this.validate()];case 1:return t.sent()&&e?[2,e()]:[2]}})})},reset:function(){return i(p(this.refs),this.observers).forEach(function(e){return e.reset()})},setErrors:function(e){var t=this;Object.keys(e).forEach(function(n){var r=t.refs[n];r&&(n="string"==typeof(n=e[n]||[])?[n]:n,r.setErrors(n))}),this.observers.forEach(function(t){t.setErrors(e)})}}});e.ValidationObserver=se,e.ValidationProvider=ie,e.configure=function(e){K(e)},e.extend=function(e,t){if(!l(t)&&!l(t.validate)&&!J.getRuleDefinition(e))throw Error("Extension Error: The validator '"+e+"' must be a function or have a 'validate' method.");"object"==typeof t?J.extend(e,t):J.extend(e,{validate:t})},e.localeChanged=D,e.localize=function(e,t){var n;G||(G=new te("en",{}),K({defaultMessage:function(e,t){return G.resolve(e,null==t?void 0:t._rule_,t||{})}})),"string"==typeof e?(G.locale=e,t&&G.merge(((n={})[e]=t,n)),D()):G.merge(e)},e.normalizeRules=y,e.setInteractionMode=function(e,t){if(K({mode:e}),t){if(!l(t))throw Error("A mode implementation must be a function");X[e]=t}},e.validate=w,e.version="3.2.3",e.withValidation=function(e,t){void 0===t&&(t=m);var n,r="options"in e?e.options:e,i=ie.options;i={name:(r.name||"AnonymousHoc")+"WithValidation",props:B({},i.props),data:i.data,computed:B({},i.computed),methods:B({},i.methods),beforeDestroy:i.beforeDestroy,inject:i.inject};var a=(null===(n=null==r?void 0:r.model)||void 0===n?void 0:n.event)||"input";return i.render=function(e){var n;this.registerField();var i=N(this),o=B({},this.$listeners),s=O(this.$vnode);this._inputEventName=this._inputEventName||$(this.$vnode,s);var l=A(this.$vnode);L(this,null==l?void 0:l.value);var u=(l=U(this)).onBlur,c=l.onValidate;return T(o,a,l.onInput),T(o,"blur",u),this.normalizedEvents.forEach(function(e){T(o,e,c)}),l=(I(this.$vnode)||{prop:"value"}).prop,i=B(B(B({},this.$attrs),((n={})[l]=null==s?void 0:s.value,n)),t(i)),e(r,{attrs:this.$attrs,props:i,on:o},function(e,t){return Object.keys(e).reduce(function(n,r){return e[r].forEach(function(n){n.context||(e[r].context=t,n.data||(n.data={}),n.data.slot=r)}),n.concat(e[r])},[])}(this.$slots,this.$vnode.context))},i},Object.defineProperty(e,"__esModule",{value:!0})}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).VeeValidateRules={})}(this,function(e){"use strict";var t={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},n={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},r={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},i={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},a=function(e,n){var r=(void 0===n?{}:n).locale,i=void 0===r?"":r;return Array.isArray(e)?e.every(function(e){return a(e,{locale:i})}):i?(t[i]||t.en).test(e):Object.keys(t).some(function(n){return t[n].test(e)})},o={validate:a,params:[{name:"locale"}]},s=function(e,t){var n=(void 0===t?{}:t).locale,r=void 0===n?"":n;return Array.isArray(e)?e.every(function(e){return s(e,{locale:r})}):r?(i[r]||i.en).test(e):Object.keys(i).some(function(t){return i[t].test(e)})},l={validate:s,params:[{name:"locale"}]},u=function(e,t){var n=(void 0===t?{}:t).locale,i=void 0===n?"":n;return Array.isArray(e)?e.every(function(e){return u(e,{locale:i})}):i?(r[i]||r.en).test(e):Object.keys(r).some(function(t){return r[t].test(e)})},c={validate:u,params:[{name:"locale"}]},d=function(e,t){var r=(void 0===t?{}:t).locale,i=void 0===r?"":r;return Array.isArray(e)?e.every(function(e){return d(e,{locale:i})}):i?(n[i]||n.en).test(e):Object.keys(n).some(function(t){return n[t].test(e)})},f={validate:d,params:[{name:"locale"}]},p=function(e,t){var n=void 0===t?{}:t,r=n.min,i=n.max;return Array.isArray(e)?e.every(function(e){return!!p(e,{min:r,max:i})}):Number(r)<=e&&Number(i)>=e},h={validate:p,params:[{name:"min"},{name:"max"}]},m={validate:function(e,t){var n=t.target;return String(e)===String(n)},params:[{name:"target",isTarget:!0}]},v=function(e,t){var n=t.length;if(Array.isArray(e))return e.every(function(e){return v(e,{length:n})});var r=String(e);return/^[0-9]*$/.test(r)&&r.length===n},g={validate:v,params:[{name:"length",cast:function(e){return Number(e)}}]},y={validate:function(e,t){var n=t.width,r=t.height,i=[];e=Array.isArray(e)?e:[e];for(var a=0;a<e.length;a++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(e[a].name))return Promise.resolve(!1);i.push(e[a])}return Promise.all(i.map(function(e){return t=e,i=n,a=r,o=window.URL||window.webkitURL,new Promise(function(e){var n=new Image;n.onerror=function(){return e(!1)},n.onload=function(){return e(n.width===i&&n.height===a)},n.src=o.createObjectURL(t)});var t,i,a,o})).then(function(e){return e.every(function(e){return e})})},params:[{name:"width",cast:function(e){return Number(e)}},{name:"height",cast:function(e){return Number(e)}}]},_={validate:function(e,t){var n=(void 0===t?{}:t).multiple,r=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return n&&!Array.isArray(e)&&(e=String(e).split(",").map(function(e){return e.trim()})),Array.isArray(e)?e.every(function(e){return r.test(String(e))}):r.test(String(e))},params:[{name:"multiple",default:!1}]};function b(e){return null==e}function w(e){return Array.isArray(e)&&0===e.length}function k(e){return"function"==typeof Array.from?Array.from(e):function(e){for(var t=[],n=e.length,r=0;r<n;r++)t.push(e[r]);return t}(e)}function S(e){return w(e)||-1!==[!1,null,void 0].indexOf(e)||!String(e).trim().length}var x=function(e,t){return Array.isArray(e)?e.every(function(e){return x(e,t)}):k(t).some(function(t){return t==e})},C={validate:x},D={validate:function(e,t){return!x(e,t)}},O={validate:function(e,t){var n=new RegExp(".("+t.join("|")+")$","i");return Array.isArray(e)?e.every(function(e){return n.test(e.name)}):n.test(e.name)}},A={validate:function(e){var t=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(e)?e.every(function(e){return t.test(e.name)}):t.test(e.name)}},M={validate:function(e){return Array.isArray(e)?e.every(function(e){return/^-?[0-9]+$/.test(String(e))}):/^-?[0-9]+$/.test(String(e))}},I={validate:function(e,t){var n=t.length;return!b(e)&&("number"==typeof e&&(e=String(e)),e.length||(e=k(e)),e.length===n)},params:[{name:"length",cast:function(e){return Number(e)}}]},T=function(e,t){var n=t.length;return b(e)?0<=n:Array.isArray(e)?e.every(function(e){return T(e,{length:n})}):String(e).length<=n},R={validate:T,params:[{name:"length",cast:function(e){return Number(e)}}]},$=function(e,t){var n=t.max;return!b(e)&&""!==e&&(Array.isArray(e)?0<e.length&&e.every(function(e){return $(e,{max:n})}):Number(e)<=n)},Y={validate:$,params:[{name:"max",cast:function(e){return Number(e)}}]},N={validate:function(e,t){var n=new RegExp(t.join("|").replace("*",".+")+"$","i");return Array.isArray(e)?e.every(function(e){return n.test(e.type)}):n.test(e.type)}},L=function(e,t){var n=t.length;return!b(e)&&(Array.isArray(e)?e.every(function(e){return L(e,{length:n})}):String(e).length>=n)},E={validate:L,params:[{name:"length",cast:function(e){return Number(e)}}]},j=function(e,t){var n=t.min;return!b(e)&&""!==e&&(Array.isArray(e)?0<e.length&&e.every(function(e){return j(e,{min:n})}):Number(e)>=n)},U={validate:j,params:[{name:"min",cast:function(e){return Number(e)}}]},P=/^[٠١٢٣٤٥٦٧٨٩]+$/,W=/^[0-9]+$/,F={validate:function(e){function t(e){var t=String(e);return W.test(t)||P.test(t)}return Array.isArray(e)?e.every(t):t(e)}},V=function(e,t){var n=t.regex;return Array.isArray(e)?e.every(function(e){return V(e,{regex:n})}):n.test(String(e))},z={validate:V,params:[{name:"regex",cast:function(e){return"string"==typeof e?new RegExp(e):e}}]},H={validate:function(e,t){var n=(void 0===t?{allowFalse:!0}:t).allowFalse,r={valid:!1,required:!0};return b(e)||w(e)||!1===e&&!n||(r.valid=!!String(e).trim().length),r},params:[{name:"allowFalse",default:!0}],computesRequired:!0},G={validate:function(e,t){var n,r=t.target,i=t.values;return(n=i&&i.length?(Array.isArray(i)||"string"!=typeof i||(i=[i]),i.some(function(e){return e==String(r).trim()})):!S(r))?{valid:!S(e),required:n}:{valid:!0,required:n}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},B={validate:function(e,t){var n=t.size;if(isNaN(n))return!1;var r=1024*n;if(!Array.isArray(e))return e.size<=r;for(var i=0;i<e.length;i++)if(e[i].size>r)return!1;return!0},params:[{name:"size",cast:function(e){return Number(e)}}]};e.alpha=o,e.alpha_dash=l,e.alpha_num=c,e.alpha_spaces=f,e.between=h,e.confirmed=m,e.digits=g,e.dimensions=y,e.email=_,e.excluded=D,e.ext=O,e.image=A,e.integer=M,e.is={validate:function(e,t){return e===t.other},params:[{name:"other"}]},e.is_not={validate:function(e,t){return e!==t.other},params:[{name:"other"}]},e.length=I,e.max=R,e.max_value=Y,e.mimes=N,e.min=E,e.min_value=U,e.numeric=F,e.oneOf=C,e.regex=z,e.required=H,e.required_if=G,e.size=B,Object.defineProperty(e,"__esModule",{value:!0})}),Vue.component("yuno-page-grid",{props:{authorizedRoles:{type:Array,required:!1,default:()=>[]},hasPageHeader:{type:Boolean,required:!1,default:!0},hasPageFooter:{type:Boolean,required:!1,default:!0},hasSearchBar:{type:Boolean,required:!1,default:!0},zohoMeta:{type:Object,required:!1,default:null},hasLHSMenu:{type:Boolean,required:!1,default:!0}},template:'\n        <div :class="[header.isMenuOpen ? \'menuOpen\' : \'\']">\n            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>\n            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>\n            <div class="pageGrid" :class="[!hasLHSMenu ? \'noLHSMenu\' : \'\']">\n                <yuno-header-v2 \n                    @userInfo="onUserInfo" \n                    @isMini="onMini" \n                    v-if="loginStatus && hasPageHeader"\n                >\n                </yuno-header-v2>\n                <slot name="aboveMain"></slot>\n                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? \'miniSidebar\' : \'\', loginStatus ? \'postLogin\' : \'preLogin\', loginStatus && !hasPageHeader && !hasPageFooter ? \'noHeaderFooter\' : \'\']">\n                    <template v-if="userInfo.loading">\n                        <div class="container hasTopGap">\n                            <figure class="infiniteSpinner">\n                                <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                            </figure>\n                        </div>\n                    </template>\n                    <template v-if="userInfo.success || !user.isLoggedin">\n                        <template v-if="isUserAuthorized">\n                            <slot name="main"></slot>     \n                        </template>\n                        <template v-else>\n                            <div class="container">\n                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>\n                            </div>\n                        </template>\n                    </template>\n                </main>\n            </div>\n            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> \n            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>\n            <slot name="belowFooter"></slot>\n        </div>\n    ',data:()=>({isMiniSidebar:!1,loginStatus:"0"!==isLoggedIn}),computed:{...Vuex.mapState(["userRole","userInfo","user","header","footer"]),isUserAuthorized:{get(){return!!YUNOCommon.findInArray(this.$props.authorizedRoles,this.userRole.data)||0===this.$props.authorizedRoles.length}},emptyStates:()=>({state:"notAuthorized"}),isPageLoading(){return this.userInfo.loading||this.header.loading||this.footer.loading},wpThemeURL(){return this.$store.state.themeURL}},async created(){},destroyed(){},mounted(){},methods:{onUserInfo(e){this.$emit("onUserInfo",e)},onMini(e){this.isMiniSidebar=e}}}),Vue.component("yuno-page-header",{props:{hasSearchBar:{type:Boolean,required:!1,default:!0}},template:'\n        <div class="yunoPageHeader">\n            <figure class="logo">\n                <img width="68" height="32" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n            </figure>\n            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>\n            <ul class="actions">\n                <li v-if="manageOrgSwitchVisiblity()">\n                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown \n                        v-model="selectedOrg" \n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="list"\n                        :class="[\'orgSwitchWrapper\']"\n                    >\n                        <template #trigger>\n                            <div class="orgSwitch">\n                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">\n                                <span class="name">{{ selectedOrg.name }}</span>\n                                <span class="icon"></span>\n                            </div>\n                        </template>\n                        <b-dropdown-item \n                            aria-role="menuitem"\n                            v-for="(org, i) in activeUser.org_id"\n                            :key="i"\n                            @click="manageOrg(org)"\n                            :value="org"\n                        >\n                            \n                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        \n                            \n                        </b-dropdown-item>\n                    </b-dropdown>\n                </li>\n                <li>\n                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>\n                    <b-dropdown\n                        v-model="navigation"\n                        position="is-bottom-left"\n                        v-if="header.success && userInfo.success"\n                        aria-role="menu"\n                    >\n                        <template #trigger>\n                            <div class="userIcon">\n                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                            </div>\n                        </template>\n                        <b-dropdown-item custom aria-role="menuitem" :class="[\'normal\']">\n                            <figure class="userCard">\n                                <div class="imgWrapper">\n                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">\n                                </div>\n                                <figcaption>\n                                    <h3>{{ activeUser.yuno_display_name }}</h3>\n                                    <p>{{ activeUser.email }}</p>\n                                    <p>{{ activeUser.role }}</p>\n                                </figcaption>\n                            </figure>\n                        </b-dropdown-item>\n                        <b-dropdown-item \n                            has-link \n                            aria-role="menuitem"\n                            v-for="(menu, i) in accountMenu.items"\n                            @click="manageMenuItem($event, menu)"\n                            :key="i"\n                        >\n                            <a :href="menu.url">\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        \n                            </a>\n                        </b-dropdown-item>\n                        \n                    </b-dropdown>\n                </li>\n            </ul>\n        </div>\n    ',data:()=>({navigation:"",selectedOrg:null,isLoading:!0}),computed:{...Vuex.mapState(["header","userInfo","userRole","subform3"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL},accountMenu(){return YUNOCommon.findObjectByKey(this.header.data,"section","Account")},activeUser(){return this.userInfo.data}},watch:{"userInfo.data":{handler(e,t){e!==t&&this.init()},deep:!0}},async created(){},destroyed(){},mounted(){},methods:{manageMenuItem(e,t){"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageOrgSwitchVisiblity(){return"org-admin"===this.userRole.data&&this.userInfo.data.org_id.length>1},manageOrg(e){this.updateActiveOrg(e.id)},orgUpdated(e){const t=e?.response?.data;201===t?.code?(sessionStorage.clear(),window.location.reload(!0)):t?.message&&console.log(t.message)},updateActiveOrg(e){this.$buefy.loading.open();const t={apiURL:YUNOCommon.config.academy("activeOrg",!1),module:"gotData",store:"subform3",payload:{user_id:isLoggedIn,org_id:e},callback:!0,callbackFunc:e=>this.orgUpdated(e)};this.dispatchData("postData",t)},dispatchData(e,t){this.$store.dispatch(e,t)},init(){if("org-admin"===this.userInfo.data.role){const e=YUNOCommon.findObjectByKey(this.userInfo.data.org_id,"id",Number(this.activeOrg()));this.selectedOrg=e}},searchBar(){return"Learner"===this.userRole.data},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e}}}),Vue.component("yuno-header-v2",{props:["data","options"],template:'\n        <div class="sidebarWrapper">\n            <div class="sidebar-page yunoSidebar" :class="[isMobile ? \'isMobile\' : \'isDesktop\', reduce ? \'collapseView\' : \'expandView\']">\n                <section class="sidebar-layout">\n                    <b-sidebar\n                        position="static"\n                        :mobile="mobile"\n                        :expand-on-hover="expandOnHover"\n                        :reduce="reduce"\n                        :delay="expandWithDelay ? 500 : null"\n                        type="is-light"\n                        open\n                    >\n                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? \'isMobile\' : \'isDesktop\']">\n                            <span class="material-icons">\n                                <template v-if="isMobile">\n                                    menu\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                        </a>\n                        <figure class="logo" v-if="!isPageGrid">\n                            <a href="#">\n                                <img width="106" height="50" :src="wpThemeURL + \'/assets/images/yunoLogo.svg\'" alt="Yuno Learning">\n                            </a>\n                        </figure>\n                        <yuno-main-nav\n                            :options="{\'isMini\': reduce}"\n                            :isPageGrid="isPageGrid"\n                        >\n                        </yuno-main-nav>\n                    </b-sidebar>\n                </section>\n                <b-modal \n                    :active.sync="config.unauthorizedModal" \n                    :width="450" \n                    :can-cancel="[\'escape\', \'x\']" \n                    :on-cancel="unauthorizedModalClose"\n                    class="yunoModal">\n                        <div class="modalHeader">\n                            <h2 class="modalTitle">Session Expired</h2>\n                        </div>\n                        <div class="modalBody">\n                            <div class="wrapper">\n                                <p>{{sessionExpired}}</p>\n                            </div>\n                        </div>\n                        <div class="modalFooter">\n                            <div class="unauthorizedLogin">\n                                <a \n                                    @click.prevent="setState()"\n                                    href="#">\n                                    <span class="g_icon"></span>\n                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>\n                                </a>\n                            </div>\n                        </div>\n                </b-modal>\n            </div>\n        </div>\n    ',data(){return{isMobile:!1,menuLoading:3,expandOnHover:!1,expandWithDelay:!1,mobile:"reduce",reduce:!1,tokenExpiry:{payload:{userID:isLoggedIn,token:this.$store.state.config.yunoAPIToken}},sessionExpired:YUNOCommon.config.errorMsg.sesstionExpired,storage:{name:"activeUser",version:1},isPageGrid:!0}},computed:{...Vuex.mapState(["user","userInfo","userRole","userProfile","config","header","apiTokenExpiryTime","apiTokenRefresh","referralCode"]),wpThemeURL(){return this.$store.state.themeURL},getHomeURL(){return this.$store.state.homeURL}},async created(){window.addEventListener("resize",this.manageOnResize),this.emitEvents()},destroyed(){window.removeEventListener("resize",this.manageOnResize)},mounted(){this.checkMenuState(),this.manageOnResize(),this.fetchModule()},methods:{emitEvents(){Event.$on("fetchReferralCode",()=>{this.referralCode.success=!1,this.referralCode.error=null,this.referralCode.errorData=[],this.referralCode.data=[],this.fetchReferralCode()})},manageOnResize(){window.outerWidth>=768?this.isMobile=!1:(this.isMobile=!0,this.reduce=!0)},isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){e.response.data.data}},fetchReferralCode(){const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"referralCode",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},checkMenuState(){this.$parent&&"yuno-page-grid"!==this.$parent.$options.name&&(this.isPageGrid=!1);const e=sessionStorage.getItem("isLHSMenu");null===e||this.isMobile?this.reduce=!1:(this.reduce="true"===e,this.sidebarToggle(!0))},sidebarToggle(e){e||(this.reduce?(sessionStorage.setItem("isLHSMenu",!1),this.reduce=!1):(sessionStorage.setItem("isLHSMenu",!0),this.reduce=!0)),this.$emit("isMini",this.reduce)},chooseAccountState(){localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0)},unauthorizedModalClose(){window.location.href="/logout"},fetchModule(){this.getStorage()},initTokenTime(e){let t=parseInt(e-10),n=parseInt(6e4*t);setTimeout(()=>{this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)},n)},doneRefreshAPIToken(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data;this.config.yunoAPIToken="Bearer "+t.token,this.tokenExpiry.payload.token="Bearer "+t.token,this.fetchAPITokenExpiryTime(this.tokenExpiry.payload)}},refreshAPIToken(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenRefresh(),module:"gotData",store:"apiTokenRefresh",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.doneRefreshAPIToken(e)}};this.$store.dispatch("postData",n)},gotAPITokenExpiryTime(e){if(void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){const t=e.response.data.data,n=10;if(t.minutes<=n){let e={user_id:isLoggedIn,id_token:this.config.yunoAPIToken};this.refreshAPIToken(e)}else this.initTokenTime(t.minutes)}},fetchAPITokenExpiryTime(e){const t=this,n={apiURL:YUNOCommon.config.apiTokenExpiry(isLoggedIn),module:"gotData",store:"apiTokenExpiryTime",payload:JSON.stringify(e),callback:!0,callbackFunc:function(e){return t.gotAPITokenExpiryTime(e)}};this.$store.dispatch("postData",n)},extractSlugFromURL(e){const t=e.replace(/\/$/,"").split("/");""===t[t.length-1]&&t.pop();return t[t.length-1]},manageCurrentPage(e){const t=e=>e.replace(/\/$/,""),n=t(window.location.origin+window.location.pathname);e.forEach(e=>{e.items.forEach(e=>{e.is_active=n===t(e.url);let r=!1;e.sub_items.forEach(i=>{i.is_active=n===t(i.url),i.is_active&&i.parent_id===e.id&&(r=!0)}),e.is_expended=!!r})})},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},gotPostLoginMenu(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";e?t=e.response.data.data:(t=this.header.data,this.header.success=!0),this.manageCurrentPage(t),this.header.data=t,this.setStorage(),this.$emit("menuLoaded")}},fetchPostLoginMenu(e){const t={userID:isLoggedIn,orgID:"org-admin"===this.userInfo.data.role?this.activeOrg():0},n=this,r={apiURL:YUNOCommon.config.header("menu",t),module:"gotData",store:"header",addToModule:!1,callback:!0,callbackFunc:function(e){return n.gotPostLoginMenu(e)}};this.$store.dispatch("fetchData",r)},manageOrgAdmin(e){const{host:t}=YUNOCommon.config,{has_org:n,org_id:r}=e;null===sessionStorage.getItem("activeOrg")&&(n?r.length>1?(window.location.href=`${t()}/select-an-organization`,sessionStorage.setItem("redirectURL",window.location.pathname+window.location.search)):sessionStorage.setItem("activeOrg",JSON.stringify(r[0].id)):window.location.href=`${t()}/create-organization-account`)},gotUserInfo(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code||!e){let t="";if(e?t=e.response.data.data:(t=this.userInfo.data,this.userInfo.success=!0),0!==this.header.data.length?this.gotPostLoginMenu(!1):this.fetchPostLoginMenu(t.role),this.userRole.data=t.role,this.userProfile.data=t,this.userProfile.success=!0,t.role,"Learner"===t.role&&this.fetchReferralCode(),"Learner"===t.role&&"pending"===t.is_signup_completed){const e=localStorage.getItem("userState");window.location.pathname+window.location.search!==e&&(window.location.href=YUNOCommon.config.host()+"/sign-up",setTimeout(()=>{localStorage.removeItem("skipSignUp")},10))}t.role,this.$emit("userInfo",t)}},fetchUserInfo(){const e=this,t={apiURL:YUNOCommon.config.userInfoAPI(isLoggedIn,!1),module:"gotData",store:"userInfo",callback:!0,callbackFunc:function(t){return e.gotUserInfo(t)}};this.$store.dispatch("fetchData",t)},getStorage(){const e=this.storage;let t=Number(JSON.parse(JSON.stringify(e.version)));lastStorage=e.name+"V"+--t,sessionStorage.removeItem(lastStorage);const n=sessionStorage.getItem(e.name+"V"+e.version);if(null!==n){const e=JSON.parse(n);this.header.data=e.menu}this.loginStatus()},setStorage(){const e=this.storage,t={menu:this.header.data};"completed"===this.userInfo.data.is_signup_completed&&sessionStorage.setItem(e.name+"V"+e.version,JSON.stringify(t))},loginStatus(){if(0!==Number(isLoggedIn))this.user.isLoggedin=!0,0!==this.userInfo.data.length?this.gotUserInfo(!1):this.fetchUserInfo(),this.$emit("login",this.user.isLoggedin);else{const e=this.storage;sessionStorage.removeItem(e.name+"V"+e.version),this.user.isLoggedin=!1,this.$emit("login",this.user.isLoggedin)}}}}),Vue.component("yuno-main-nav",{props:["data","options","isPageGrid"],template:'\n        <b-menu class="is-custom-mobile">\n            <nav class="menuWrapper">\n                <template v-if="header.loading || userInfo.loading">\n                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>\n                </template>\n                <template v-if="header.success">\n                    <template v-if="header.error">\n                        {{ header.errorData }}\n                    </template>\n                    <template v-else>\n                        <template v-if="isPageGrid">\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                                v-if="section.section !== \'Account\'"\n                            >       \n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list> \n                        </template>\n                        <template v-else>\n                            <b-menu-list \n                                :key="i"\n                                :label="section.section"\n                                v-for="(section, i) in header.data"\n                            >       \n                                <template v-if="section.section === \'Account\'">\n                                    <template v-if="header.loading">\n                                        <figure class="menuFooter loading">\n                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>\n                                            <figcaption>\n                                                <p class="userName"><b-skeleton active></b-skeleton></p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                    <template v-if="header.success">\n                                        <figure class="menuFooter" :class="[options.isMini ? \'isMini\' : \'\']">\n                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">\n                                            <figcaption>\n                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>\n                                                <p class="userEmail">{{ userInfo.data.email }}</p>\n                                            </figcaption>\n                                        </figure>\n                                    </template>\n                                </template>\n                                <template v-for="(menu, j) in section.items">\n                                    <b-menu-item \n                                        :key="\'menu-\' + j"\n                                        :href="menu.url"\n                                        :expanded="menu.is_expended"\n                                        :active="menu.is_active"\n                                        tag="a"\n                                        :class="[menu.sub_items.length !== 0  ? \'hasSubmenu\' : \'\', generateClass(menu), section.slug]"\n                                        @click="manageNavItem($event, menu)"\n                                    >\n                                        <template #label="props">\n                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">\n                                                <template v-if="props.expanded">\n                                                    arrow_drop_down\n                                                </template>\n                                                <template v-else>\n                                                    arrow_drop_up\n                                                </template>\n                                            </span>\n                                            <template v-if="menu.slug === \'generate-code\'">\n                                                <template v-if="referralCode.loading">\n                                                    <b-skeleton active></b-skeleton>\n                                                </template>\n                                                <template v-if="referralCode.success">\n                                                    <template v-if="referralCode.error">\n                                                        <template v-if="generateCode.loading">\n                                                            <b-skeleton active></b-skeleton>\n                                                        </template>\n                                                        <template v-else>\n                                                            <template v-if="options.isMini">\n                                                                <b-tooltip label="Generate Code"\n                                                                    type="is-dark"\n                                                                    position="is-right">\n                                                                    <div class="referralField" @click="generateReferralCode()">\n                                                                        <span class="referralIcon"></span>\n                                                                    </div>\n                                                                </b-tooltip>\n                                                            </template>\n                                                            <template v-else>\n                                                                <div class="referralField">\n                                                                    <span class="referralIcon"></span>\n                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">\n                                                                        Generate Code\n                                                                    </a>\n                                                                </div>\n                                                            </template>\n                                                        </template>\n                                                    </template>    \n                                                    <template v-else>\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip label="Referral Code"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <div class="referralField isMini">\n                                                                    <b-field>\n                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                    </b-field>\n                                                                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                        <span>Copy</span>\n                                                                    </a>\n                                                                </div>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="referralField">\n                                                                <span class="referralIcon"></span>\n                                                                <b-field>\n                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                                                                </b-field>\n                                                                <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                                                                    <span class="caption">Copy</span>\n                                                                </a>\n                                                            </div>\n                                                        </template>\n                                                    </template>    \n                                                </template>\n                                            </template>\n                                            <template v-else>\n                                                <template v-if="options.isMini">\n                                                    <b-tooltip :label="menu.label"\n                                                        type="is-dark"\n                                                        position="is-right">\n                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>\n                                                </template>\n                                            </template>\n                                        </template>\n                                        <template v-if="menu.sub_items !== undefined">\n                                            <template v-for="(submenu, k) in menu.sub_items">\n                                                <b-menu-item\n                                                    :key="\'submenu-\' + k"\n                                                    :active="submenu.is_active"\n                                                    :href="submenu.url"\n                                                    tag="a"\n                                                >\n                                                    <template #label="props">\n                                                        <template v-if="options.isMini">\n                                                            <b-tooltip :label="submenu.label"\n                                                                type="is-dark"\n                                                                position="is-right">\n                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>\n                                                            </b-tooltip>\n                                                        </template>\n                                                        <template v-else>\n                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>\n                                                        </template>\n                                                    </template>\n                                                </b-menu-item>\n                                            </template>\n                                        </template>\n                                    </b-menu-item>\n                                </template>\n                            </b-menu-list>  \n                        </template>\n                    </template>\n                </template>\n            </nav>\n        </b-menu>\n    ',data:()=>({menuLoading:3}),computed:{...Vuex.mapState(["userRole","userInfo","header","referralCode","generateCode"])},async created(){},mounted(){},methods:{gotReferralCode(e){if(this.generateCode.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;Event.$emit("fetchReferralCode")}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateReferralCode(){this.generateCode.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"generateCode",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("postData",t)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},manageNavItem(e,t){0!==t.sub_items.length&&e.preventDefault(),"generate-code"===t.slug&&e.preventDefault(),"Switch Account"===t.label&&(localStorage.setItem("userState",window.location.pathname),localStorage.setItem("isChooseAccountState",!0),sessionStorage.clear())},manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-\' + i"\n                        :active="menu.isActive"\n                        :expanded="menu.isExpanded"\n                        :class="[menu.submenu !== undefined ? \'hasSubmenu\' : \'\', generateClass(menu)]"\n                        :href="menu.url"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">\n                                <template v-if="props.expanded">\n                                    expand_more\n                                </template>\n                                <template v-else>\n                                    expand_less\n                                </template>\n                            </span>\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                        <template v-if="menu.submenu !== undefined">\n                            <template v-for="(submenu, j) in menu.submenu">\n                                <b-menu-item\n                                    :key="\'submenu-\' + j"\n                                    :active="submenu.isActive"\n                                    :href="submenu.url"\n                                    tag="a"\n                                >\n                                    <template #label="props">\n                                        <template v-if="options.isMini">\n                                            <b-tooltip :label="submenu.label"\n                                                type="is-dark"\n                                                position="is-right">\n                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>\n                                            </b-tooltip>\n                                        </template>\n                                        <template v-else>\n                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>\n                                        </template>\n                                    </template>\n                                </b-menu-item>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>  \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Learn":"Insights",generateClass:e=>e.label.replace(/\s/g,"").toLowerCase()}}),Vue.component("yuno-referral-code",{props:["data","options"],template:'\n        <div>\n            <template v-if="options.isMini">\n                <b-tooltip label="Referral Code"\n                    type="is-dark"\n                    position="is-right">\n                    <div class="referralField isMini">\n                        <b-field>\n                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                        </b-field>\n                        <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                            <span>Copy</span>\n                        </a>\n                    </div>\n                </b-tooltip>\n            </template>\n            <template v-else>\n                <div class="referralField">\n                    <span class="referralIcon"></span>\n                    <b-field>\n                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>\n                    </b-field>\n                    <a href="#" @click.prevent="copyToClipboard(\'referralCode\')">\n                        <span class="caption">Copy</span>\n                    </a>\n                </div>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-referral-code-generate",{props:["data","options"],template:'\n        <div class="fluid">\n            <template v-if="referralCode.error">\n                <template v-if="moduleWithoutTab.success">\n                    <template v-if="moduleWithoutTab.loading">\n                        <div class="referralField">\n                            <span class="referralIcon"></span>\n                            <b-skeleton active></b-skeleton>\n                        </div>\n                    </template>\n                    <template v-if="moduleWithoutTab.success">\n                        <yuno-referral-code :options="options"></yuno-referral-code>    \n                    </template>\n                </template>\n                <template v-else>\n                    <template v-if="options.isMini">\n                        <b-tooltip label="Generate Code"\n                            type="is-dark"\n                            position="is-right">\n                            <div class="referralField" @click="generateCode()">\n                                <span class="referralIcon"></span>\n                            </div>\n                        </b-tooltip>\n                    </template>\n                    <template v-else>\n                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">\n                            <span class="referralIcon"></span>\n                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">\n                                Generate Code\n                            </a>\n                        </div>\n                        <template v-if="moduleWithoutTab.loading">\n                            <div class="referralField">\n                                <span class="referralIcon"></span>\n                                <b-skeleton active></b-skeleton>\n                            </div>\n                        </template>\n                    </template>\n                </template>\n            </template>\n            <template v-else>\n                <yuno-referral-code :options="options"></yuno-referral-code>\n            </template>\n        </div>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})},gotReferralCode(e){if(void 0!==e.response&&void 0!==e.response.data&&200===e.response.data.code){const t=e.response.data.data;this.referralCode.data=t}},fetchReferralCode(){this.moduleWithoutTab.data=[],this.moduleWithoutTab.error=null,this.moduleWithoutTab.success=!1;const e=this,t={apiURL:YUNOCommon.config.referrerID(isLoggedIn,0),module:"gotData",store:"moduleWithoutTab",callback:!0,callbackFunc:function(t){return e.gotReferralCode(t)}};this.$store.dispatch("fetchData",t)},gotCode(e){if(this.moduleWithoutTab.loading=!1,void 0!==e.response&&void 0!==e.response.data&&201===e.response.data.code){e.response.data;this.fetchReferralCode()}else{const t=e.response.data;this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom",type:"is-danger"})}},generateCode(){this.moduleWithoutTab.loading=!0;const e=this,t={apiURL:YUNOCommon.config.generateRefferralCode(),module:"gotData",store:"moduleWithoutTab",payload:{user_id:Number(isLoggedIn),role:this.userRole.data},callback:!0,callbackFunc:function(t){return e.gotCode(t)}};this.$store.dispatch("postData",t)}}}),Vue.component("yuno-referral-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper referral">\n            <b-menu-list :label="manageLabel(userRole.data)">\n                <b-menu-item \n                    href="#"\n                    tag="a"\n                >\n                    <template #label="props">\n                        <template v-if="userRole.data === \'Instructor\'">\n                            <yuno-referral-code :options="options"></yuno-referral-code>\n                        </template>\n                        <template v-if="userRole.data === \'Learner\'">\n                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>\n                        </template>\n                    </template>\n                </b-menu-item>\n                <template v-for="(menu, i) in otherItems">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list>\n        </nav>\n    ',data:()=>({otherItems:[{label:"Earnings",slug:"earnings",role:["Instructor","Learner"],icon:"currency_rupee",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/earnings/",isActive:!1,callbackFunc:!1},{label:"How it works",slug:"howItWorks",role:["Instructor","Learner"],icon:"help_outline",iconType:"material-icons-outlined",url:YUNOCommon.config.pickHost()+"/how-it-works/",isActive:!1,callbackFunc:!1}]}),computed:{...Vuex.mapState(["userRole","referralCode","moduleWithoutTab"])},async created(){},mounted(){},methods:{manageLabel:e=>"Learner"===e?"Referral":"Referral Earnings",isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard"})}}}),Vue.component("yuno-static-menu",{props:["data","options"],template:'\n        <nav class="menuWrapper">\n            <b-menu-list label="Account">\n                <template v-for="(menu, i) in data">\n                    <b-menu-item \n                        :key="\'menu-static\' + i"\n                        :active="menu.isActive"\n                        :href="menu.url"\n                        v-if="isItemAvailable(menu.role)"\n                        tag="a"\n                    >\n                        <template #label="props">\n                            <template v-if="options.isMini">\n                                <b-tooltip :label="menu.label"\n                                    type="is-dark"\n                                    position="is-right">\n                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>\n                                </b-tooltip>\n                            </template>\n                            <template v-else>\n                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>\n                            </template>\n                        </template>\n                    </b-menu-item>\n                </template>\n            </b-menu-list> \n        </nav>\n    ',data:()=>({}),computed:{...Vuex.mapState(["userRole"])},async created(){},mounted(){},methods:{isItemAvailable(e){return!!YUNOCommon.findInArray(e,this.userRole.data)}}});const YUNOTable=(jQuery,{table:function(){Vue.component("yuno-table",{props:["data","options","tabindex"],template:'\n                <div class="yunoTable" :class="{\'container-fluid\': options.isFluid, \'container\': options.isFluid === undefined || !options.isFluid}">\n                    <template v-if="data.loading">\n                        <b-skeleton height="500px"></b-skeleton>\n                    </template>\n                    <template v-if="data.success">\n                        <ul class="nestedFilters" v-if="data.nestedTabs !== undefined">\n                            <li \n                                v-for="(nestedTab, nestedTabIndex) in data.nestedTabs"\n                                :key="nestedTabIndex"\n                                :class="{\'active\': nestedTab.isActive}">\n                                <a href="#" @click="manageNestedTabs($event, nestedTabIndex, data, nestedTab, tabindex)">{{nestedTab.label}}</a>\n                            </li>\n                        </ul>\n                        <div class="field clearFilters" v-if="data.appliedFilters !== undefined && data.appliedFilters.length !== 0">\n                            <b-tag\n                                v-for="(applied, appliedIndex) in data.appliedFilters"\n                                :key="appliedIndex"\n                                attached\n                                closable\n                                aria-close-label="Close tag"\n                                @close="onFilterClear(applied, tabindex)">\n                                <span v-html="applied.label"></span>\n                            </b-tag>\n                        </div>\n                        <div class="filterWrap" v-if="data.filters !== undefined">\n                            <template v-for="(filter, filterIndex) in data.filters">\n                                <template v-if="filter.isActive && filter.module === \'dropdown\'">\n                                    <b-dropdown\n                                        :key="filterIndex"\n                                        v-model="filter.selected"\n                                        :multiple="filter.canSelectMulti === undefined ? false : filter.canSelectMulti"\n                                        aria-role="list"\n                                        @change="onFilterChange($event, filter.type)"\n                                        class="filterMenu"\n                                        :class="filter.type === \'tableColumn\' ? \'selectColumns\' : \'\'">\n                                        <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">\n                                            <template v-if="filter.type === \'tableColumn\'">\n                                                <span><i class="fa fa-cog" aria-hidden="true"></i></span>\n                                            </template>\n                                            <template v-else>\n                                                <span>{{filter.selected}}</span>\n                                            </template>\n                                            <b-icon :icon="active ? \'menu-up\' : \'menu-down\'"></b-icon>\n                                        </button>\n                                        <b-dropdown-item v-if="filter.type === \'tableColumn\'" class="dropdownTitle">\n                                            Hide Columns\n                                        </b-dropdown-item>\n                                        <template v-for="(item, itemIndex) in filter.items">\n                                            <b-dropdown-item \n                                                @click="onFilterItemSelect(item.val, filter.type, item.label, item.default, tabindex)"\n                                                :value="filter.canSelectMulti === undefined ? item.label : item.val"\n                                                :key="itemIndex"\n                                                aria-role="listitem">\n                                                <span>{{item.label}}</span>\n                                            </b-dropdown-item>\n                                        </template>\n                                    </b-dropdown>\n                                </template>\n                                <template v-if="filter.isActive && filter.module === \'autocomplete\'">\n                                    <b-field :class="\'filter\'+filter.type">\n                                        <b-autocomplete\n                                            v-model="filter.selected"\n                                            :data="filteredAutocomplete(filter, \'search\')"\n                                            :placeholder="filter.placeholder"\n                                            field="search"\n                                            @select="onAutocompleteSelect($event, tabindex, filter.type)"\n                                            :clearable="true">\n                                            <template slot="empty">No results for {{filter.selected}}</template>\n                                            <template slot-scope="props">\n                                                <template v-if="filter.type === \'course\'">\n                                                    <div class="courseList">\n                                                        <figure class="img">\n                                                            <img v-if="props.option.image !== \'\'" width="40" height="40" :src="props.option.image" :alt="props.option.label">\n                                                            <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>\n                                                        </figure>\n                                                        <div class="courseContent">\n                                                            <h4 class="courseTitle" v-html="props.option.label"></h4>\n                                                            <div class="groupContent">\n                                                                <span class="instructorName" v-if="props.option.instructor">{{props.option.instructor}},</span>\n                                                                <span class="dateTime">{{props.option.date}}</span>\n                                                                <span class="batchLabel" v-if="props.option.batchLabel">({{props.option.batchLabel}})</span>\n                                                            </div>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                                <template v-else> \n                                                    <div class="commonList">\n                                                        <div class="courseContent">\n                                                            <h4 class="courseTitle" v-html="props.option.label"></h4>\n                                                        </div>\n                                                    </div>\n                                                </template>\n                                            </template>\n                                        </b-autocomplete>\n                                    </b-field>\n                                </template>\n                                <template v-if="filter.isActive && filter.module === \'autocompleteQuerySearch\'">\n                                    <b-field :class="\'filter\'+filter.type">\n                                        <b-autocomplete\n                                            v-model="filter.selected"\n                                            :data="filter.items"\n                                            :loading="filter.isLoading"\n                                            :placeholder="filter.placeholder"\n                                            field="search"\n                                            @typing="fetchQueryData($event, filter)"\n                                            @select="onAutocompleteSelect($event, tabindex, filter.type)"\n                                            :clearable="true">\n                                            <template slot="empty">No results for {{filter.selected}}</template>\n                                        </b-autocomplete>\n                                    </b-field>\n                                </template>\n                            </template>\n                        </div>\n                        <template v-if="data.error">\n                            <yuno-empty-states :options="{\'state\': \'dataNotFound\', \'description\': data.errorData}"></yuno-empty-states>\n                        </template>\n                            <b-table\n                                :class="{\'scrollable\': options.scrollable, \'tableInvisible\': data.error}"\n                                :loading="options.pageLoading"\n                                :paginated="true"\n                                :detailed="options.hasDetailed !== undefined ? options.hasDetailed : false"\n                                :backend-pagination="options.apiPaginated !== undefined ? options.apiPaginated : false"\n                                :total="options.totalResult !== undefined ? options.totalResult : 0"\n                                :sticky-header="options.isStickyHeader !== undefined ? options.isStickyHeader : false"\n                                :height="options.height !== undefined ? options.height : \'\'"\n                                @page-change="onPageChange($event, tabindex)"\n                                :per-page="options.perPage"\n                                :current-page="options.currentPage !== undefined ? options.currentPage : 1"\n                                ref="table"\n                                :data="data.data.rows"\n                                :default-sort="options.defaultSort"\n                                :default-sort-direction="options.sortDirection !== undefined ? options.sortDirection : \'asc\'"\n                                :striped="options.hasStriped !== undefined ? options.hasStriped : true">\n                                <template slot-scope="props">\n                                    <b-table-column \n                                        v-for="(col, colIndex) in data.data.columns" \n                                        :key="colIndex" \n                                        :field="col.field" \n                                        :visible="col.isActive === undefined ? true : col.isActive"\n                                        :label="col.label" \n                                        :sortable="col.sortable">\n                                            <template slot-scope="props" slot="header">\n                                                <template v-if="col.info_icon !== undefined && col.info_icon !== \'\'">\n                                                    {{props.column.label}}\n                                                    <b-tooltip :label="col.info_icon"\n                                                        type="is-dark"\n                                                        position="is-bottom">\n                                                        <i class="fa fa-info-circle" aria-hidden="true"></i>\n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    {{props.column.label}}\n                                                </template>\n                                            </template>\n                                            <template v-if="col.field === \'action\' || col.field === \'actions\'">\n                                                <div class="fieldVal ctaGroup noOverflow">\n                                                    <template v-for="(item, itemIndex) in props.row[\'actions\']">\n                                                        <b-tooltip :label="item.label"\n                                                            type="is-light"\n                                                            :key="itemIndex"\n                                                            position="is-left">\n                                                            <template v-if="item.isAnchor === undefined || !item.isAnchor">\n                                                                <b-button \n                                                                    :loading="item.isLoading !== undefined && item.isLoading ? true : false" \n                                                                    :disabled="item.isLoading !== undefined && item.isLoading ? true : false" \n                                                                    @click="onActionTrigger(props.row, item, tabindex)"\n                                                                    class="yunoPrimaryCTA small iconOnly">\n                                                                        <i \n                                                                            v-if="item.iconType === \'fa\'" \n                                                                            :class="\'fa \' +  item.icon" \n                                                                            aria-hidden="true">\n                                                                        </i>\n                                                                        <span class="material-icons" v-else>{{item.icon}}</span>\n                                                                </b-button>\n                                                            </template>\n                                                            <template v-else>\n                                                                <b-button \n                                                                    tag="a"\n                                                                    :href="item.href"\n                                                                    :target="item.target"\n                                                                    class="yunoPrimaryCTA small iconOnly anchor">\n                                                                        <i \n                                                                            v-if="item.iconType === \'fa\'" \n                                                                            :class="\'fa \' +  item.icon" \n                                                                            aria-hidden="true">\n                                                                        </i>\n                                                                </b-button>\n                                                            </template>\n                                                        </b-tooltip>\n                                                    </template>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.hasAction">\n                                                \n                                                    <template v-for="(item, itemIndex) in props.row[col.field]">\n                                                        <template v-if="item.hasClickToView">\n                                                            <div class="fieldVal ctaGroup noOverflow">\n                                                                <b-tooltip :label="item.label"\n                                                                    type="is-light"\n                                                                    :key="itemIndex"\n                                                                    position="is-left">\n                                                                    <template v-if="item.isAnchor === undefined || !item.isAnchor">\n                                                                        <b-button \n                                                                            :loading="item.isLoading !== undefined && item.isLoading ? true : false" \n                                                                            :disabled="item.isLoading !== undefined && item.isLoading ? true : false" \n                                                                            @click="onActionTrigger(props.row, item, tabindex)"\n                                                                            class="yunoPrimaryCTA small iconOnly">\n                                                                                <i \n                                                                                    v-if="item.iconType === \'fa\'" \n                                                                                    :class="\'fa \' +  item.icon" \n                                                                                    aria-hidden="true">\n                                                                                </i>\n                                                                        </b-button>\n                                                                    </template>\n                                                                    <template v-else>\n                                                                        <b-button \n                                                                            tag="a"\n                                                                            :href="item.href"\n                                                                            :target="item.target"\n                                                                            class="yunoPrimaryCTA small iconOnly anchor">\n                                                                                <i \n                                                                                    v-if="item.iconType === \'fa\'" \n                                                                                    :class="\'fa \' +  item.icon" \n                                                                                    aria-hidden="true">\n                                                                                </i>\n                                                                        </b-button>\n                                                                    </template>\n                                                                </b-tooltip>\n                                                            </div>\n                                                        </template>\n                                                        <template v-else>\n                                                            <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">{{ item.value }}</div>\n                                                        </template>\n                                                    </template>\n                                            </template>\n                                            <template v-else-if="col.field === \'learner\'">\n                                                <ul class="user">\n                                                    <li class="userImg"><img :src="props.row.learner.image" :alt="props.row.learner.name"></li>\n                                                    <li class="userName">\n                                                        <a :href="props.row.learner.profile_url" target="_blank">{{props.row.learner.name}}</a>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else-if="col.field === \'referrer\'">\n                                                <template v-if="props.row[col.field].id !== 0">\n                                                    <ul class="user">\n                                                        <li class="userImg"><img :src="props.row[col.field].image" :alt="props.row[col.field].name"></li>\n                                                        <li class="userName">\n                                                            {{props.row[col.field].name}}\n                                                        </li>\n                                                    </ul>\n                                                </template>\n                                                <template v-else>\n                                                    <div class="fieldVal" :class="[col.field]">NA</div>\n                                                </template>\n                                            </template>\n                                            <template v-else-if="col.field === \'attendance\' && props.row.attendance.percentage !== undefined">\n                                                <div class="fieldVal percentageBlock" :class="[col.field !== undefined ? col.field : \'\']">\n                                                    <b-progress \n                                                        :type="{\n                                                            \'is-red\': props.row.attendance.percentage <= 30,\n                                                            \'is-orange\': props.row.attendance.percentage > 30,\n                                                            \'is-yellow\': props.row.attendance.percentage > 50,\n                                                            \'is-lightGreen\': props.row.attendance.percentage > 70,\n                                                            \'is-darkGreen\': props.row.attendance.percentage > 80\n                                                        }"  \n                                                        format="percent"    \n                                                        :value="Number(props.row.attendance.percentage)">\n                                                        {{props.row.attendance.percentage}}\n                                                    </b-progress>\n                                                    <div class="percentage">{{props.row.attendance.percentage}}% <a href="#" @click="nestedTableModal($event, props.row, props.row[col.field])" v-if="props.row.attendance.attendanceModal !== undefined"><i class="fa fa-external-link-square" aria-hidden="true"></i></a></div>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.field === \'batch\' && props.row.batch.percentage !== undefined">\n                                                <div class="fieldVal percentageBlock" :class="[col.field !== undefined ? col.field : \'\']">\n                                                    <b-progress \n                                                        :type="{\n                                                            \'is-red\': props.row.batch.percentage <= 30,\n                                                            \'is-orange\': props.row.batch.percentage > 30,\n                                                            \'is-yellow\': props.row.batch.percentage > 50,\n                                                            \'is-lightGreen\': props.row.batch.percentage > 70,\n                                                            \'is-darkGreen\': props.row.batch.percentage > 80\n                                                        }"  \n                                                        format="percent"    \n                                                        :value="Number(props.row.batch.percentage)">\n                                                        {{props.row.batch.percentage}}\n                                                    </b-progress>\n                                                    <div class="percentage">{{props.row.batch.percentage}}%</div>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.field === \'name\' && props.row[\'image\'] !== undefined">\n                                                <ul class="user" :class="{\'hasTags\': props.row.fieldWithTags !== undefined}">\n                                                    <li class="userImg"><img :src="props.row[\'image\']" :alt="props.row[col.field]"></li>\n                                                    <li class="userName">\n                                                        <template v-if="col.customEvent !== undefined && col.customEvent">\n                                                            <a href="#" @click="onCustomEvent($event, props.row, col)">{{props.row[col.field]}}</a>\n                                                        </template>\n                                                        <template v-else>\n                                                            {{props.row[col.field]}}    \n                                                        </template>\n                                                        <template v-if="props.row.fieldWithTags !== undefined && props.row.fieldWithTags.field === \'name\'">\n                                                            <div class="tagsWrapper">\n                                                                <b-tag rounded\n                                                                    v-for="(tag, tagIndex) in props.row.fieldWithTags.tags" :key="tagIndex">\n                                                                    {{ tag }}\n                                                                </b-tag>\n                                                            </div>\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else-if="col.isLink !== undefined && col.isLink">\n                                                <template v-if="col.hasTooltip !== undefined && col.hasTooltip">\n                                                    <b-tooltip :label="props.row[col.field]"\n                                                        type="is-dark"\n                                                        :position="col.tooltipPosition === undefined ? \'is-left\' : col.tooltipPosition">\n                                                        <div class="fieldVal withLink" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']"><a :href="props.row[\'url\']" :target="col.linkTarget">{{ props.row[col.field] }}</a></div>    \n                                                    </b-tooltip>\n                                                </template>\n                                                <template v-else>\n                                                    <div class="fieldVal withLink" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']"><a :href="props.row[\'url\']" :target="col.linkTarget">{{ props.row[col.field] }}</a></div>    \n                                                </template>\n                                            </template>\n                                            <template v-else-if="props.row[col.field] !== undefined && col.copyToClipboard !== undefined && col.copyToClipboard">\n                                                <template v-if="props.row[col.field].isEmpty === undefined || !props.row[col.field].isEmpty">\n                                                    <div class="clipboard noField">\n                                                        <div class="fieldWrapper">\n                                                            <b-input :id="props.row[col.field].id" :value="props.row[col.field].url" readonly></b-input>\n                                                        </div>\n                                                        <b-tooltip :label="props.row[col.field].label"\n                                                            type="is-light"\n                                                            :position="props.row[col.field].position">\n                                                            <i @click="copyToClipboard(props.row[col.field].id)" class="fa trigger fa-clipboard" aria-hidden="true"></i>    \n                                                        </b-tooltip>\n                                                    </div>\n                                                </template>\n                                                <template v-else>\n                                                    <template v-if="col.withTag !== undefined && col.withTag">\n                                                        <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                                            <span class="tag" :class="[{\'is-success\': props.row[col.field].url === \'online\'}, {\'is-warning\': props.row[col.field].url === \'offline\'}]">{{ props.row[col.field].url }}</span>\n                                                        </div>\n                                                    </template>\n                                                    <template v-else>\n                                                        <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">{{ props.row[col.field].url }}</div>\n                                                    </template>\n                                                </template>\n                                            </template>\n                                            <template v-else-if="props.row[col.field] !== undefined && col.customColor !== undefined && col.customColor">\n                                                <template v-if="col.customEvent !== undefined && col.customEvent">\n                                                    <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\', props.row[col.field].colorClass !== undefined ? props.row[col.field].colorClass : \'\']"><a href="#" @click="onCustomEvent($event, props.row, col)">{{ props.row[col.field].val }}</a></div>\n                                                </template>\n                                                <template v-else>\n                                                    <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\', props.row[col.field].colorClass !== undefined ? props.row[col.field].colorClass : \'\']">{{ props.row[col.field].val }}</div>\n                                                </template>\n                                            </template>\n                                            <template v-else-if="props.row[col.field] !== undefined && col.userWithPic !== undefined && col.userWithPic">\n                                                <ul class="user">\n                                                    <li class="userImg"><img :src="props.row[col.field].image" :alt="props.row[col.field].name"></li>\n                                                    <li class="userName">{{props.row[col.field].name}}</li>\n                                                </ul>\n                                            </template>\n                                            <template v-else-if="col.hasArray !== undefined && col.hasArray">\n                                                <div class="fieldVal arrayList" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                                    <template v-for="(item, itemIndex) in props.row[col.field]">\n                                                        <span class="item">{{item}}</span>\n                                                    </template>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.hasTooltip !== undefined && col.hasTooltip">\n                                                <b-tooltip :label="props.row[col.field]"\n                                                    type="is-dark"\n                                                    :position="col.tooltipPosition === undefined ? \'is-left\' : col.tooltipPosition">\n                                                    <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">{{ props.row[col.field] }}</div>    \n                                                </b-tooltip>\n                                            </template>\n                                            <template v-else-if="col.hasTag !== undefined && col.hasTag">\n                                                <div :id="props.row.scrollID !== undefined ? props.row.scrollID : \'\'" class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                                    <span class="tag" \n                                                        :class="\n                                                            [\n                                                                {\'is-success\': props.row[col.field] === \'PAID\'}, \n                                                                {\'is-danger\': props.row[col.field] === \'EXPIRED\'},\n                                                                {\'is-warning\': props.row[col.field] === \'FAILED\'},\n                                                                {\'is-info\': props.row[col.field] === \'ISSUED\'},\n                                                                {\'is-info noBG\': col.hasSlot === true},\n                                                                {\'is-success\': props.row[col.field] === \'ACTIVE\'},\n                                                                {\'is-success noText\': props.row[col.field] === true},\n                                                                {\'is-light noText\': props.row[col.field] === false},\n                                                                {\'is-warning\': props.row[col.field] === \'INACTIVE\'}\n                                                            ]">\n                                                        {{ props.row[col.field] }}\n                                                    </span>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.isInstructor !== undefined && col.isInstructor">\n                                                <ul class="user">\n                                                    <li class="userImg"><img :src="props.row[\'instructor\'] !== undefined ? props.row[\'instructor\'].image : props.row[\'instructor_image\']" :alt="props.row[col.field]"></li>\n                                                    <li class="userName">\n                                                        <template v-if="props.row[\'instructor\'] !== undefined">\n                                                            {{props.row[\'instructor\'].name}}\n                                                        </template>\n                                                        <template v-else>\n                                                            {{props.row[col.field]}}\n                                                        </template>\n                                                    </li>\n                                                </ul>\n                                            </template>\n                                            <template v-else-if="col.isOrg !== undefined && col.isOrg">\n                                                <template v-if="props.row[\'org_admin\'].id !== 0 && props.row[\'org_admin\'].id !== \'\'">\n                                                    <ul class="user">\n                                                        <li class="userImg"><img :src="props.row[\'org_admin\'].image" :alt="props.row[\'org_admin\'].name"></li>\n                                                        <li class="userName">\n                                                            {{props.row[\'org_admin\'].name}}\n                                                        </li>\n                                                    </ul>\n                                                </template>\n                                                <template v-else>\n                                                    <div class="fieldVal">NA</div>\n                                                </template>\n                                            </template>\n                                            <template v-else-if="col.hasCombined !== undefined">\n                                                <div class="fieldVal" :class="[col.field !== undefined ? col.field : \'\']">\n                                                    <template v-if="props.row[col.field] === props.row[col.hasCombined.otherField]">\n                                                        <b-tag class="colorRed" rounded>{{ props.row[col.field] }} of {{ props.row[col.hasCombined.otherField] }}</b-tag>\n                                                    </template>\n                                                    <template v-else>\n                                                        <b-tag class="colorGreen" rounded>{{ props.row[col.field] }} of {{ props.row[col.hasCombined.otherField] }}</b-tag>\n                                                    </template>\n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.isCounselor !== undefined && col.isCounselor">\n                                                <template v-if="props.row[col.field] !== \'NA\'"> \n                                                    <ul class="user">\n                                                        <li class="userImg"><img :src="props.row[\'counselor_image\']" :alt="props.row[col.field]"></li>\n                                                        <li class="userName">{{props.row[col.field]}}</li>\n                                                    </ul>\n                                                </template>\n                                                <template v-else>\n                                                    {{props.row[col.field]}}\n                                                </template>\n                                            </template>\n                                            <template v-else-if="col.hasCounselor !== undefined && col.hasCounselor">\n                                                <template v-if="props.row.counselor.name !== \'NA\'"> \n                                                    <ul class="user">\n                                                        <li class="userImg"><img :src="props.row.counselor.image" :alt="props.row.counselor.name"></li>\n                                                        <li class="userName">{{props.row.counselor.name}}</li>\n                                                    </ul>\n                                                </template>\n                                                <template v-else> \n                                                    {{props.row.counselor.name}}\n                                                </template>\n                                            </template>\n                                            <template v-else-if="col.hasHierarchy !== undefined && col.hasHierarchy">\n                                                <div class="fieldVal hierarchyList" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">\n                                                    <template v-if="props.row[col.field].length > 1">\n                                                        <template v-for="(item, itemIndex) in props.row[col.field]">\n                                                            <template v-if="itemIndex === 0">\n                                                                <span class="item">{{item}} <b-tooltip :label="props.row.isExpand ? \'Click to collapse\' : \'Click to expand\'"\n                                                                    type="is-light"\n                                                                    position="is-left">\n                                                                    <i @click="toggleShowmore($event, props.row)" class="fa" :class="props.row.isExpand ? \'fa-minus-circle\' : \'fa-plus-circle\'" aria-hidden="true"></i>\n                                                                </b-tooltip></span>\n                                                            </template>\n                                                            <template v-else>\n                                                                <template v-if="props.row.isExpand">\n                                                                    <span class="item">{{item}}</span>\n                                                                </template>\n                                                            </template>\n                                                        </template>\n                                                    </template>\n                                                    <template v-else>\n                                                        <template v-for="(item, itemIndex) in props.row[col.field]">\n                                                            <template v-if="itemIndex === 0">\n                                                                <span class="item">{{item}}</span>\n                                                            </template>\n                                                        </template>\n                                                    </template>\n                                                    \n                                                </div>\n                                            </template>\n                                            <template v-else-if="col.customEvent !== undefined && col.customEvent">\n                                                <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']"><a href="#" @click="onCustomEvent($event, props.row, col)">{{ props.row[col.field] }}</a></div>\n                                            </template>\n                                            <template v-else>\n                                                <div class="fieldVal" :class="[col.allowWrap ? \'allowWrap\' : \'\', col.field !== undefined ? col.field : \'\']">{{ props.row[col.field] }}</div>\n                                            </template>    \n                                    </b-table-column>\n                                </template>\n                                <template slot="detail" slot-scope="props" v-if="options.hasDetailed !== undefined && options.hasDetailed">\n                                    <template v-if="props.row.detail.type === \'table-grid\'">\n                                        <b-table\n                                            :data="props.row.detail.data.rows">\n                                            <template slot-scope="propsrow">\n                                                <b-table-column \n                                                    v-for="(nestedCol, nestedColIndex) in props.row.detail.data.columns" \n                                                    :key="nestedColIndex" \n                                                    :field="nestedCol.field" \n                                                    :label="nestedCol.label" \n                                                    :sortable="nestedCol.sortable">\n                                                    <template v-if="nestedCol.field === \'batch\' && propsrow.row[nestedCol.field].attendanceModal !== undefined">\n                                                        <div class="fieldVal">\n                                                            <a href="#" @click="nestedTableModal($event, propsrow.row, propsrow.row[nestedCol.field], true)"><i class="fa fa-external-link-square" aria-hidden="true"></i> {{propsrow.row[nestedCol.field].label}}</a>\n                                                        </div>        \n                                                    </template>\n                                                    <template v-else-if="nestedCol.field === \'instructor\'">\n                                                        <ul class="user">\n                                                            <li class="userImg"><img :src="propsrow.row[nestedCol.field].image" :alt="propsrow.row[nestedCol.field].name"></li>\n                                                            <li class="userName">\n                                                                {{propsrow.row[nestedCol.field].name}}\n                                                            </li>\n                                                        </ul>\n                                                    </template>\n                                                    <template v-else-if="nestedCol.field === \'attendance\' && propsrow.row[nestedCol.field].percentage !== undefined">\n                                                        <div class="fieldVal percentageBlock">\n                                                            <b-progress \n                                                                :type="{\n                                                                    \'is-red\': propsrow.row[nestedCol.field].percentage <= 30,\n                                                                    \'is-orange\': propsrow.row[nestedCol.field].percentage > 30,\n                                                                    \'is-yellow\': propsrow.row[nestedCol.field].percentage > 50,\n                                                                    \'is-lightGreen\': propsrow.row[nestedCol.field].percentage > 70,\n                                                                    \'is-darkGreen\': propsrow.row[nestedCol.field].percentage > 80\n                                                                }"  \n                                                                format="percent"    \n                                                                :value="propsrow.row[nestedCol.field].percentage">\n                                                            </b-progress>\n                                                            <div class="percentage">{{propsrow.row[nestedCol.field].percentage}}%</div>\n                                                        </div>\n                                                    </template>\n                                                    <template v-else>\n                                                        <div class="fieldVal" :class="[nestedCol.field !== undefined ? nestedCol.field : \'\']">{{propsrow.row[nestedCol.field]}}</div>\n                                                    </template>\n                                                </b-table-column>\n                                            </template>\n                                        </b-table>\n                                    </template>\n                                    <template v-if="props.row.detail.type === \'list\'">\n                                        <template v-if="props.row.detail.data.length !== 0">\n                                            <ul class="detailList">\n                                                <li v-for="(item, itemIndex) in props.row.detail.data">\n                                                    <figure class="listImage">\n                                                        <img :src="item.image" :alt="item.name">\n                                                    </figure>\n                                                    <div class="listInfo">\n                                                        <p class="listTitle">{{item.name}}</p>\n                                                        <small>{{item.email}}</small>\n                                                    </div>\n                                                </li>\n                                            </ul> \n                                        </template>\n                                        <template v-else>\n                                            <p class="listMessage"><i class="fa fa-exclamation-circle" aria-hidden="true"></i> {{props.row.detail.message}}</p>\n                                        </template>\n                                    </template>\n                                </template>\n                            </b-table>\n                        \n                    </template>\n                </div>\n            ',data:()=>({}),computed:{},created(){},mounted(){},methods:{nestedTableModal(e,t,n,r){e.preventDefault(),Event.$emit("nestedTableModal",e,t,n,r)},toggleShowmore(e,t){e.preventDefault(),t.isExpand?t.isExpand=!1:t.isExpand=!0},filteredAutocomplete:(e,t)=>e.items.filter(n=>n[t].toString().toLowerCase().indexOf(e.selected.toLowerCase())>=0),scrollToEle(e){let t=YUNOCommon.heightOfEle(document.querySelectorAll(".yunoHeader")[0])+YUNOCommon.heightOfEle(document.querySelectorAll(".yunoTabNav")[0],!0)+10;jQuery([document.documentElement,document.body]).animate({scrollTop:jQuery("#"+e.url).offset().top-t},500)},copyToClipboard(e){let t=document.getElementById(e);t.select(),t.setSelectionRange(0,99999),document.execCommand("copy"),this.$buefy.toast.open({duration:1e3,message:"Copy to clipboard",position:"is-bottom"})},onCustomEvent(e,t,n){Event.$emit("onCustomEvent",e,t,n)},onPageChange(e,t){Event.$emit("onTablePageChange",e,t),this.$props.data.manageState&&this.manageState(this.$props.data,t,e)},onActionTrigger(e,t,n){Event.$emit("onActionTrigger",e,t,n)},onFilterChange(e,t){Event.$emit("onFilterChange",t,e)},manageState(e,t,n){let r=JSON.parse(JSON.stringify(e)),i=e.filters,a=YUNOCommon.findObjectByKey(i,"type","tableColumn");const o=function(e,t){for(let n=0;n<e.length;n++){const r=e[n];t.push(r)}},s={tab:r.tab,index:t,defaultFilters:[],appliedFilters:[],nestedTabs:"",hideColumns:[],page:!1!==n?n:1};if(null!==a&&o(a.selected,s.hideColumns),void 0!==e.nestedTabs){let t=YUNOCommon.findObjectByKey(e.nestedTabs,"isActive",!0);s.nestedTabs=t.value}o(r.defaultFilters,s.defaultFilters),o(r.appliedFilters,s.appliedFilters);const l=encodeURI(JSON.stringify(s));if(history.pushState){var u=window.location.protocol+"//"+window.location.host+window.location.pathname+"?state="+l;window.history.pushState({path:u},"",u)}},onFilterItemSelect(e,t,n,r,i){Event.$emit("onFilterItemSelect",e,t,n,r,i),this.$props.data.manageState&&this.manageState(this.$props.data,i,!1)},fetchQueryData:_.debounce(function(e,t){e.length>2?(t.isLoading=!0,Event.$emit("fetchQueryData",e,t)):t.data=[]},500),onAutocompleteSelect(e,t,n){Event.$emit("onAutocompleteSelect",e,t,n),this.$props.data.manageState&&this.manageState(this.$props.data,t,!1)},onFilterClear(e,t){Event.$emit("onFilterClear",e,t),this.$props.data.manageState&&this.manageState(this.$props.data,t,!1)},manageNestedTabs(e,t,n,r,i){event.preventDefault();let a=n.nestedTabs;for(let e=0;e<a.length;e++)a[e].isActive&&(a[e].isActive=!1);a[t].isActive=!0,Event.$emit("onNestedTabChange",n,r,i),this.$props.data.manageState&&this.manageState(n,i,!1)}}})}}),YUNOCreateBatch=function(){const e=["yuno-admin","yuno-category-admin","Instructor","org-admin"],t=[{label:"Monday",slug:"Mon"},{label:"Tuesday",slug:"Tue"},{label:"Wednesday",slug:"Wed"},{label:"Thursday",slug:"Thu"},{label:"Friday",slug:"Fri"},{label:"Saturday",slug:"Sat"},{label:"Sunday",slug:"Sun"}];return{createBatch:function(){YUNOCommon.assignVValidationObj({messages:{required:"This field is required",numeric:"Numbers only",min:"Minimum 10 numbers required",max:"Maximum 15 numbers required",is:"This field is required",is_not:"New batch shouldn't be same as current batch"}}),YUNOTable.table(),Vue.component("yuno-create-batch",{template:'\n                <yuno-page-grid\n                    :authorizedRoles="authorizedRoles"\n                    @onUserInfo="onUserInfo"\n                    :hasSearchBar="false"\n                >\n                    <template v-slot:main>\n                        <section id="createBatch" class="container-fluid formSection createBatch ">\n                            <template v-if="isFormLoading">\n                                <figure class="infiniteSpinner">\n                                    <img width="150" height="75" :src="wpThemeURL + \'/assets/images/infinite-spinner.svg\'" alt="Yuno Learning">\n                                </figure>\n                            </template>\n                            <template v-if="isFormReady">\n                                <template v-if="isEditBatch">\n                                    <h1 class="sectionTitle">Update Batch</h1>    \n                                </template>\n                                <template v-else>\n                                    <h1 class="sectionTitle">Create New Batch</h1>    \n                                </template>\n                                <validation-observer \n                                    tag="div" \n                                    ref="createBatchObserver" \n                                    v-slot="{ handleSubmit, invalid }">\n                                    <form id="createBatchForm" @submit.prevent="handleSubmit(initForm)">\n                                        <div class="row isRelative">\n                                            <div class="col-12 col-md-5 col-lg-5 noRelative">\n                                                <div class="formWrapper">\n                                                    <b-field label="Batch Label">\n                                                        <validation-provider \n                                                            :rules="{required:true}" \n                                                            v-slot="{ errors, classes }">\n                                                            <b-input :class="classes" \n                                                                placeholder="Add a batch title" \n                                                                v-model="payload.title">\n                                                            </b-input>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                    <b-field label="Course" id="courseList">\n                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                                            <b-taginput\n                                                                :class="classes"\n                                                                v-model="courseSelection.selected"\n                                                                :data="courseSelection.filtered"\n                                                                autocomplete\n                                                                :disabled="isEditBatch ? true : false"\n                                                                field="product_code"\n                                                                placeholder="Add a course"\n                                                                @add="onCourseSelect($event, \'add\')"\n                                                                @remove="onCourseSelect($event, \'remove\')"\n                                                                @keydown.native="onCourseKeypress($event)"\n                                                                @typing="getFilteredCourse">\n                                                                <template v-slot="props">\n                                                                    <span class="wrapper" :class="props.option.enroll_type">\n                                                                        <strong>{{props.option.product_code}} <small class="helper">({{props.option.enroll_type}})</small></strong>\n                                                                        <span class="courseData">\n                                                                            <span class="courseLabel">Duration:</span>\n                                                                            <span class="courseVal">{{props.option.duration_weeks}} Weeks</span>\n                                                                            <span class="courseLabel">Price:</span>\n                                                                            <span class="courseVal"><template v-if="props.option.unit_price !== \'0\'">&#8377;</template>{{props.option.unit_price}}</span>\n                                                                        </span>\n                                                                    </span>\n                                                                </template>\n                                                                <template #empty>\n                                                                    There are no items\n                                                                </template>\n                                                            </b-taginput>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                    <b-field label="Instructor" v-if="instructor.isActive">\n                                                        <template v-if="instructor.isLoading">\n                                                            <div class="smallLoader withField"></div>\n                                                        </template>\n                                                        <template v-if="mappedInstructor.success">\n                                                            <validation-provider \n                                                                tag="div"\n                                                                v-if="instructor.isField"\n                                                                :customMessages="{ isNotBlank: errorMsg.instructor }"\n                                                                :rules="{required:true, isNotBlank:instructor.selected}" \n                                                                v-slot="{ errors, classes }">\n                                                                <b-autocomplete\n                                                                    :class="classes"\n                                                                    v-model="instructor.current"\n                                                                    :data="filteredInstructor"\n                                                                    placeholder="Add instructor"\n                                                                    field="name"\n                                                                    @input="onInstructorChange"\n                                                                    @select="onInstructorSelect($event)"\n                                                                    :clearable="true">\n                                                                    <template slot="empty">No results for {{instructor.current}}</template>\n                                                                </b-autocomplete>\n                                                                <p class="error">{{errors[0]}}</p>\n                                                            </validation-provider>\n                                                        </template>\n                                                    </b-field>\n                                                    <b-field label="Start Date" v-if="courseDate">\n                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                                            <b-datepicker\n                                                                @input="onDatepick"\n                                                                :class="classes"\n                                                                :date-formatter="formatDate"\n                                                                v-model="payload.start_date"\n                                                                placeholder="Pick date"\n                                                                :mobile-native="false"\n                                                                :min-date="startDate.minDate"\n                                                                trap-focus>\n                                                            </b-datepicker>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                    <b-field label="End Date" v-if="courseDate">\n                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                                            <b-datepicker\n                                                                :class="classes"\n                                                                :date-formatter="formatDate"\n                                                                v-model="payload.end_date"\n                                                                :mobile-native="false"\n                                                                :min-date="endDate.minDate"\n                                                                :max-date="endDate.maxDate"\n                                                                trap-focus>\n                                                            </b-datepicker>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                    <b-field label="Days Of The Week">\n                                                        <validation-provider tag="div" class="makeItGrid" :rules="{required:true, minLength:1}" v-slot="{ errors, classes }">\n                                                            <template v-for="(day, dayIndex) in daysOfWeek">\n                                                                <div class="field" :key="dayIndex">\n                                                                    <b-checkbox\n                                                                        :class="classes"\n                                                                        :native-value="day.slug"\n                                                                        v-model="payload.class_days">\n                                                                        {{day.label}}\n                                                                    </b-checkbox>\n                                                                </div>\n                                                            </template>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                    <b-field label="Class Duration" v-if="userRole.data !== \'Instructor\'">\n                                                        <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">\n                                                            <b-select \n                                                                :class="classes"\n                                                                v-model="payload.duration"\n                                                                placeholder="Select">\n                                                                <option value="">Select</option>\n                                                                <option value="15">15 Minutes</option>\n                                                                <option value="30">30 Minutes</option>\n                                                                <option value="45">45 Minutes</option>\n                                                                <option value="60">1 Hour</option>\n                                                                <option value="75"> 1 Hour 15 Minutes</option>\n                                                                <option value="90">1 Hour 30 Minutes</option>\n                                                                <option value="105">1 Hour 45 Minutes</option>\n                                                                <option value="120">2 Hours</option>\n                                                            </b-select>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                    <b-field label="Class Time">\n                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">\n                                                            <b-timepicker\n                                                                :class="classes"\n                                                                v-model="payload.class_time"\n                                                                placeholder="Pick time"\n                                                                hour-format="12"\n                                                                :mobile-native="false"\n                                                                icon="clock">\n                                                            </b-timepicker>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                    <b-field label="Max Seats">\n                                                        <validation-provider tag="div" :rules="{required:false, numeric: true}" v-slot="{ errors, classes }">\n                                                            <b-input \n                                                                :disabled="true"\n                                                                v-model="payload.seats_max"\n                                                                :class="classes">\n                                                            </b-input>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                    <b-field>\n                                                        <validation-provider tag="div" :rules="{required:false}" v-slot="{ errors, classes }">\n                                                            <b-checkbox\n                                                                :class="classes"\n                                                                :native-value="payload.is_locked"\n                                                                v-model="payload.is_locked">\n                                                                <b-tooltip \n                                                                    type="is-dark"\n                                                                    label="New enrollments cannot be made in a locked batch"\n                                                                    :multilined="true"\n                                                                    position="is-top">\n                                                                    Lock this batch    \n                                                                </b-tooltip>\n                                                            </b-checkbox>\n                                                            <p class="error">{{errors[0]}}</p>\n                                                        </validation-provider>\n                                                    </b-field>\n                                                </div>\n                                                <div class="ctaWrapper">\n                                                    <b-button\n                                                        native-type="submit"\n                                                        :loading="form.isLoading ? true : false"\n                                                        :disabled="form.isLoading ? true : false"\n                                                        class="yunoSecondaryCTA">\n                                                        <template v-if="isEditBatch">\n                                                            Update Batch\n                                                        </template>\n                                                        <template v-else>\n                                                            Create Batch\n                                                        </template>\n                                                    </b-button>    \n                                                </div>\n                                            </div>\n                                        </div>\n                                    </form>\n                                </validation-observer>\n                            </template>\n                        </section>\n                    </template>\n                </yuno-page-grid>\n            ',data:()=>({isMiniSidebar:!1,tableOptions:{isFluid:!1,pageLoading:!1,apiPaginated:!1,totalResult:"",perPage:50,limit:20,offset:0,hasStriped:!1,isStickyHeader:!0,height:"600"},startDate:{minDate:new Date},endDate:{minDate:new Date,maxDate:new Date},disabledEnrollment:"",courseDate:!1,daysOfWeek:t,courseSelection:{isActive:!1,filtered:[],selected:[]},instructor:{selected:null,current:"",isLoading:!1,isActive:!1,isField:!1},errorMsg:{instructor:"Please select the instructor from list"},form:{isLoading:!1},payload:{title:"",created_by:isLoggedIn,course_id:[],start_date:new Date,end_date:new Date(""),class_time:new Date,instructor_id:"",duration:"",class_days:[],personalisation:"",seats_max:"",is_locked:!1,teaching_mode:"online"},authorizedRoles:e}),computed:{...Vuex.mapState(["user","userInfo","header","userProfile","userRole","footer","loader","allCourses","mappedInstructor","batchCreateUpdate","batchDetail","instructorAvailabilityGrid","capabilities"]),wpThemeURL(){return this.$store.state.themeURL},isUserAuthorized:{get(){if(!YUNOCommon.findInArray(this.authorizedRoles,this.userRole.data))return!1;const e=this.capabilities.data.isGovernGangs;return"org-admin"===this.userRole.data||e}},isPageLoading:{get(){return this.userInfo.loading||this.capabilities.loading}},isPageReady:{get(){return!this.user.isLoggedin||this.userInfo.success&&this.capabilities.success}},isFormLoading:{get(){const e=!1!==YUNOCommon.getQueryParameter("isEdit");return this.allCourses.loading||this.capabilities.loading||e&&this.batchDetail.loading}},isFormReady:{get(){const e=!1!==YUNOCommon.getQueryParameter("isEdit"),t=this.allCourses.success&&this.capabilities.success;return e?t&&this.batchDetail.success:t}},emptyStates:()=>({state:"notAuthorized"}),isEditBatch(){const e=YUNOCommon.getQueryParameter("isEdit");return!1!==e&&(this.allCourses.success&&this.initEdit(e),!0)},filteredInstructor:{get(){return this.mappedInstructor.data.filter(e=>e.name.toString().toLowerCase().indexOf(this.instructor.current.toLowerCase())>=0)}}},async created(){this.emitEvents()},mounted(){},methods:{onLogin(e){},onUserInfo(e){YUNOCommon.findInArray(this.authorizedRoles,e.role)&&(this.fetchCapabilities(!1),"Instructor"===e.role&&this.manageInstructorCase())},onMini(e){this.isMiniSidebar=e},onMenuLoaded(){},gotCapabilities(e,t){if(t){this.capabilities.loading=!1,this.capabilities.success=!0;const e={"yuno-admin":{isClassesWisdom:!0,isEntranceWisdom:!0,isGangsWisdom:!0,isGovernClasses:!0,isGovernLearners:!0,isGovernGangs:!0,isSysopWisdom:!0,isPagoWisdom:!0},Counselor:{isEntranceWisdom:!0,isClassesWisdom:!0,isGangsWisdom:!0,isGovernClasses:!0,isGovernLearners:!0,isSysopWisdom:!0,isPagoWisdom:!0}};e[this.userRole.data]&&(this.capabilities.data=e[this.userRole.data]),this.authorizedUser(this.userRole.data)}else 200===e.response?.data?.code&&(this.capabilities.data=e.response.data.data,this.authorizedUser(this.userRole.data))},fetchCapabilities(e){if(e)this.gotCapabilities(!1,e);else{const e={apiURL:YUNOCommon.config.capabilitiesAPI(isLoggedIn,!1),module:"gotData",store:"capabilities",addToModule:"false",callback:!0,callbackFunc:e=>this.gotCapabilities(e)};this.$store.dispatch("fetchData",e)}},manageInstructorCase(){this.payload.instructor_id=isLoggedIn,this.payload.duration=60},emitEvents(){Event.$on("gotUserMenu",()=>{if(this.user.isLoggedin){const e=this.userRole.data;this.fetchCapabilities(!1),"Instructor"===e&&this.manageInstructorCase()}})},enrollmentType(e){this.disabledEnrollment="rolling"===e?"fixed":"rolling",this.courseSelection.selected=[],this.courseSelection.filtered=[],this.courseSelection.isActive=!0,this.payload.end_date=new Date(""),this.payload.start_date=new Date,this.payload.course_id=[]},formatDate:e=>moment(e).format("MMMM DD, YYYY"),onDatepick(e){this.payload.end_date=new Date(this.generateClassEndDate()),this.endDate.maxDate=new Date(this.generateClassEndDate()),this.endDate.minDate=new Date(e);const t=new Date;this.startDate.minDate=new Date(t.setDate(t.getDate()-1))},onInstructorChange(e){""===e&&(this.payload.instructor_id="")},onInstructorSelect(e){null!==e?(this.instructor.selected=e,this.payload.instructor_id=e.instructor_id):(this.instructor.selected=null,this.payload.instructor_id="")},gotInstructors(e,t){let n=this.mappedInstructor;if(this.instructor.isLoading=!1,201===e.response?.data?.code){this.instructor.isField=!0;!1!==YUNOCommon.getQueryParameter("isEdit")&&this.preFillInstructor(t)}else n.data=[],n.error=null,n.errorData=[],this.instructor.isActive=!0,this.instructor.isField=!0,this.$buefy.snackbar.open({duration:5e3,message:YUNOCommon.config.errorMsg.notMapped,type:"is-warning",position:"is-top",actionText:"Ok"})},fetchMappedInstructor(e,t){let n={course_id:[]};for(let t=0;t<e.length;t++){const r=e[t];n.course_id.push(r)}const r={apiURL:YUNOCommon.config.listOfMappedInstructorAPI(),module:"gotData",store:"mappedInstructor",payload:JSON.stringify(n),headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:e=>this.gotInstructors(e,t)};this.$store.dispatch("postData",r)},generateClassEndDate(){let e=this.courseSelection.selected,t=[],n=this.payload.start_date;for(let n=0;n<e.length;n++){const r=e[n];t.push(r.duration_weeks)}let r=Math.max(...t),i=new Date(n);return i.setDate(i.getDate()+7*r),i},manageMaxSeats(){if(0!==this.courseSelection.selected.length){const e=this.courseSelection.selected.map(e=>e.max_seats).filter(e=>void 0!==e);this.payload.seats_max=e.length>0?Math.max(...e):""}else this.payload.seats_max=""},_manageCourseElements(){let e=this.payload.course_id;"Instructor"!==this.userRole.data&&(0!==this.courseSelection.selected.length?(this.instructor.isActive=!0,this.instructor.isField=!1,this.instructor.isLoading=!0,this.fetchMappedInstructor(e)):(this.instructor.current="",this.instructor.selected=null,this.payload.instructor_id="")),this.payload.end_date=new Date(this.generateClassEndDate()),this.endDate.maxDate=new Date(this.generateClassEndDate())},addCourse(e){const{payload:t,courseSelection:n,allCourses:r}=this;let i=n.selected[0].enroll_type;this.disabledEnrollment="rolling"===i?"fixed":"rolling",e.enroll_type===this.disabledEnrollment?YUNOCommon.removeObjInArr(n.selected,"post_id",e.post_id):(t.course_id.push(e.post_id),YUNOCommon.removeObjInArr(r.data,"post_id",e.post_id),this._manageCourseElements(),this.courseDate=!0)},removeCourse(e){const{payload:t,courseSelection:n,allCourses:r}=this;YUNOCommon.removeValInArr(t.course_id,e.post_id),r.data.push(e),this._manageCourseElements(),0!==n.selected.length?(t.end_date=new Date(this.generateClassEndDate()),this.endDate.maxDate=new Date(this.generateClassEndDate())):(this.courseDate=!1,this.disabledEnrollment="")},onCourseSelect(e,t){"add"===t?this.addCourse(e):this.removeCourse(e),this.manageMaxSeats()},onCourseKeypress(e){"ArrowDown"!==e.key&&"ArrowUp"!==e.key||this.manageItems(this.courseSelection.filtered)},manageItems(e){let t=document.querySelectorAll("#courseList .dropdown-menu .dropdown-item");for(let n=0;n<t.length;n++){const r=t[n],i=e[n];void 0!==i&&i.enroll_type===this.disabledEnrollment?r.classList.add("disabled"):r.classList.remove("disabled")}},getFilteredCourse(e){this.courseSelection.filtered=this.allCourses.data.filter(t=>t.product_code.toString().toLowerCase().indexOf(e.toLowerCase())>=0),this.$nextTick(()=>{this.manageItems(this.courseSelection.filtered)})},preFillInstructor(e){let t=this.mappedInstructor.data,n=this.payload,r=YUNOCommon.findObjectByKey(t,"instructor_id",String(e));null!==r&&(this.onInstructorSelect(r),this.instructor.current=r.name,this.instructor.selected=r,n.instructor_id=r.instructor_id)},preFillCourses(e,t){const n=this.allCourses.data,r=this.payload,i=this.courseSelection.selected,a=this.instructor;for(let o=0;o<e.length;o++){const s=Number(e[o]),l=YUNOCommon.findObjectByKey(n,"post_id",s);i.push(l),r.course_id.push(s),"Instructor"===this.userRole.data?this.payload.instructor_id=t:0!==i.length?(a.isActive=!0,a.isField=!1,a.isLoading=!0,this.fetchMappedInstructor(r.course_id,t)):(a.current="",a.selected=null,this.payload.instructor_id="")}},gotBatch(e){if(200===e.response?.data?.code){const t=e.response.data.data,n=this.payload,r=t.course.map(e=>e.id),i=t.instructor.id;this.preFillCourses(r,i),n.title=t.title,n.start_date=new Date(t.start_end.start_date.time),n.end_date=new Date(t.start_end.end_date.time),n.class_time=new Date(t.class_time.start_time.time),n.duration="Instructor"!==this.userRole.data?t.class_time.duration:"60",n.class_days=t.class_days.map(e=>e.day),n.seats_max=t.seats.max,n.is_locked=t.is_locked,this.courseSelection.isActive=!0,this.courseDate=!0;const a=new Date;let o=new Date(t.start_end.end_date.time);this.endDate.minDate=new Date(o.setDate(o.getDate()-1)),this.endDate.maxDate=new Date(a.getFullYear()+18,a.getMonth(),a.getDate())}},fetchBatch(e){const t={apiURL:YUNOCommon.config.batch("update",{batchID:e}),module:"gotData",store:"batchDetail",callback:!0,callbackFunc:e=>this.gotBatch(e)};this.$store.dispatch("fetchData",t)},initEdit(e){YUNOCommon.findInArray(this.authorizedRoles,this.userRole.data)&&this.fetchBatch(e)},resetForm(){let e=this.payload;this.$refs.createBatchObserver.reset(),this.courseSelection.filtered=[],this.courseSelection.selected=[],this.instructor.selected=null,this.instructor.current="",this.instructor.isLoading=!1,this.instructor.isActive=!1,this.instructor.isField=!1,this.courseSelection.isActive=!1,this.courseDate=!1,e.title="",e.created_by=isLoggedIn,e.course_id=[],e.start_date=new Date,e.end_date=new Date(""),e.class_time=new Date,e.seats_max="",e.is_locked=!1,e.instructor_id="",e.duration="",e.class_days=[],e.batch_db_id="";"org-admin"===this.userInfo.data.role&&(e.created_by=this.activeOrg())},manageBatch(e){this.form.isLoading=!1;let t="",n=YUNOCommon.getQueryParameter("isEdit");if(t=!1!==n?"Update Batch":"Create Batch",201===e.response?.data?.code){let t=e.response.data;this.resetForm(),!1!==n&&this.fetchBatch(n),this.$buefy.toast.open({duration:5e3,message:`${t.message}`,position:"is-bottom"}),this.fetchCourses()}else this.$buefy.dialog.alert({title:t,message:this.batchCreateUpdate.errorData,confirmText:"Ok"})},initForm(){this.form.isLoading=!0;const e=YUNOCommon.getQueryParameter("isEdit"),t=YUNOCommon.getQueryParameter("classSize");let n={...this.payload,batch_db_id:!1!==e?e:void 0,personalisation:t};"org-admin"===this.userInfo.data.role&&(n.created_by=this.activeOrg()),e&&(n.batch_db_id=e);const r={apiURL:this.getFormURL(),module:"gotData",store:"batchCreateUpdate",payload:JSON.stringify(n),headers:{accept:"application/json","content-type":"application/json"},callback:!0,callbackFunc:e=>this.manageBatch(e)};this.$store.dispatch(!1!==e?"putData":"postData",r)},getFormURL(){const e=this.userInfo.data.role,t=YUNOCommon.getQueryParameter("isEdit");if(!["org-admin","yuno-admin","Instructor"].includes(e))return null;const n=!1!==t,r=n?"update":"create",i=!!n&&{batchID:t};return YUNOCommon.config.batch(r,i)},activeOrg(){const e=this.userInfo.data.current_state.org_id;if(e)return e},getGroupType:e=>e?2===e.length?"both":1===e.length?YUNOCommon.findInArray(e,"1-1")?"one_to_one":"one_to_many":null:null,getGroupTypeFromElement:e=>e.one_to_one&&e.one_to_many?"both":e.one_to_many?"one_to_many":e.one_to_one?"one_to_one":null,transformCourseData(e){return"org-admin"===this.userInfo.data.role?(e.enroll_type=e.enrollment_type,e.post_id=e.course_id,e.product_code=e.title,e.group_type=this.getGroupType(e.personalization)):e.group_type=this.getGroupTypeFromElement(e.group_type),e},gotCourses(e){if(200!==e.response?.data?.code)return;const t=e.response.data.data,n=Number(YUNOCommon.getQueryParameter("courseid")),r=t.map(this.transformCourseData);if(n){const e=r.find(e=>e.post_id===n);e&&this.courseSelection.selected.push(e)}this.allCourses.data=r,this.isEditBatch},fetchCourses(){const e={apiURL:this.getCoursesURL(),module:"gotData",store:"allCourses",callback:!0,addToModule:!1,callbackFunc:e=>this.gotCourses(e)};this.$store.dispatch("fetchData",e)},getCoursesURL(){const e=this.userInfo.data.role,t=YUNOCommon.getQueryParameter("classSize");return{"org-admin":YUNOCommon.config.org("courses",this.activeOrg(),isLoggedIn,0,"all",!1,!1,!1,!1,"all","all","list-view",100,0),"yuno-admin":YUNOCommon.config.allCoursesAPI(t),Instructor:YUNOCommon.config.mappedCoursesAPI(isLoggedIn,t)}[e]||null},authorizedUser(e){(YUNOCommon.findInArray(this.authorizedRoles,e)||this.capabilities.data.isGovernGangs)&&this.fetchCourses()},additionalRow(e){for(let t=0;t<e.length;t++){const n=e[t];if(n.sun||n.mon||n.tue||n.wed||n.thu||n.fri||n.sat){n.scrollID="moveScrollTopHere";break}}},additionalCols(e){e.push({field:"slot",label:"",sortable:!0,hasSlot:!0});const t=["slot","sun","mon","tue","wed","thu","fri","sat"];for(const n of e)t.includes(n.field)&&(n.hasTag=!0)},scrollToActiveRow(){let e=document.querySelectorAll(".table-wrapper")[0],t=document.getElementById("moveScrollTopHere").parentElement.parentElement.offsetTop;e.scrollTop=t},gotResources(e){const t=this.instructorAvailabilityGrid;if(200===e.response?.data?.code){let n=e.response.data.data,r=n.rows,i=n.columns,a=e.response.data.count;this.additionalCols(i),this.additionalRow(r),this.tableOptions.pageLoading=!1,this.tableOptions.totalResult=a,t.data=n,this.$nextTick(()=>{this.scrollToActiveRow()})}else t.data=[],this.tableOptions.totalResult=0},fetchResources(e,t){this.instructorAvailabilityGrid.data=[],this.instructorAvailabilityGrid.success=!1;const n={apiURL:YUNOCommon.config.availabilityGridAPI(t),module:"gotData",store:"instructorAvailabilityGrid",moduleLoading:e,addToModule:!1,callback:!0,callbackFunc:e=>this.gotResources(e)};this.$store.dispatch("fetchData",n)},fetchFooter(){const e={apiURL:YUNOCommon.config.footerAPI(),module:"gotData",store:"footer",callback:!1};this.$store.dispatch("fetchData",e)}}})}}}(jQuery);