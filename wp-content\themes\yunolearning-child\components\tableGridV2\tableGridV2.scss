@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

.fontAwesomeIcon {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.material-icons-outlined {
    font-family: 'Material Icons Outlined';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
  
.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}

.ylIcon {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'yuno-icon' !important;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    
    /* Enable Ligatures ================ */
    letter-spacing: 0;
    -webkit-font-feature-settings: "liga";
    -moz-font-feature-settings: "liga=1";
    -moz-font-feature-settings: "liga";
    -ms-font-feature-settings: "liga" 1;
    font-feature-settings: "liga";
    -webkit-font-variant-ligatures: discretionary-ligatures;
    font-variant-ligatures: discretionary-ligatures;
  
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

.slide-in-right {
    -webkit-animation: slide-in-right 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
            animation: slide-in-right 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

@-webkit-keyframes slide-in-right {
    0% {
        -webkit-transform: translateX(1000px);
        transform: translateX(1000px);
        opacity: 0;
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slide-in-right {
    0% {
        -webkit-transform: translateX(1000px);
        transform: translateX(1000px);
        opacity: 0;
    }
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0);
        opacity: 1;
    }
}

.dark87 {
    @include setFontColor($primaryCopyColor, 0.87);
}

.dark60 {
    @include setFontColor($primaryCopyColor, 0.6); 
}

.dark38 {
    @include setFontColor($primaryCopyColor, 0.38);
}

.fontColorDark {
    color: $onSurface;
}

.fontColorDarkVariant {
    color: $onSurfaceVariant;
}

.largestTitle {
    @include setFont($headline3, 42px, 700, $gapSmall);

    @media (min-width: 768px) {
        @include setFont($headline1, 62px, 700, $gapSmall);
    }
}

.largerTitle {
    @include setFont($headline5, 28px, 500, $gapSmall);
    @extend .dark87;
}

.largeTitle {
    @include setFont($headline6, 24px, 500, 0);
}

.smallCaption {
    @include setFont($subtitle1, 24px, 500, 0);
}

.smallerCaption {
    @include setFont($body1, 25px, 500, $gapSmall);
}

.smallestCaption {
    @include setFont($caption1, 16px, 400, 0);
}

.body1 {
    @include setFont($body2, 20px, 400, 0);
    @extend .dark60;
}

.body2 {
    @include setFont($caption2, 16px, 400, 0);
    @extend .dark60;
}

.body3 {
    @include setFont($caption2, 16px, 400, $gap15);
    @extend .dark87;
}

.overline {
    @include setFont($overline, normal, 400, 0);
    text-transform: uppercase;
    letter-spacing: 1px;
    @extend .dark60;
}

.progress-wrapper {
    .progress-value {
        @include setFontColor($primaryCopyColor, 0.7);
    }

    .progress {
        border: 1px solid #FFF;
        height: 8px;
        
        &.is-orange::-webkit-progress-value {
            background: #FC9927;
        }

        &.is-red::-webkit-progress-value {
            background: #CA0813;
        }

        &.is-yellow::-webkit-progress-value {
            background: #F0C042;
        }

        &.is-lightGreen::-webkit-progress-value {
            background: #669D4F;
        }

        &.is-darkGreen::-webkit-progress-value {
            background: #356B21;
        }
    }
}

#app {
    

    .yunoTable {
        margin-top: $gapSmall;
    
        .note {
            @include setFont($subtitle1, 24px, 500, 0);
            @extend .dark60;
        }
    
        .table-mobile-sort {
            
    
            .field.has-addons {
                .control:not(.is-expanded) {
                    display: none;
                }
    
                .control:first-child:not(:only-child) .select select {
                    border-bottom-right-radius: 4px;
                    border-top-right-radius: 4px;
                }
            }
                
        }
    
        .b-table {
            .table-wrapper {
                overflow-x: auto;
                overflow-y: hidden
            }

            .field.table-mobile-sort { display: none}
    
            .table {
                thead {
                    @include setFont($subtitle2, normal, 500, 0);
                    @extend .dark87;
    
                    th {
                        padding: $gapSmall $gapSmall $gapSmall 0;

                        &.is-current-sort {
                            .icon {
                                .mdi-arrow-up {
                                    &:after {
                                        content: "\f077";
                                        @extend .fontAwesomeIcon;
                                        position: relative;
                                        top: -1px;
                                    }
                                }
                            }
                        }

                        .sort-icon {
                            &.is-invisible {
                                display: none
                            }
                        }

                        &.is-sortable {

                        }

                        .th-wrap {
                            .is-relative {
                                width: max-content;
                            }
                        }
                    }
                }
    
                tbody {
                    @include setFont($caption1, normal, 400, 0);
                    @extend .dark60;
    
                    tr {
                        &:hover {
                            .actions {
                                visibility: visible;
                            }
                        }
                    }
    
                    td {
                        vertical-align: middle;
                        padding: $gapSmaller $gapSmall $gapSmaller 0;
                        
                        div {
                            width: max-content;
                        }
                    }

                    .userWithPhoto {
                        display: flex;
                        align-items: center;
                        min-width: 60px;
                        width: max-content;
    
                        img {
                            width: 20px;
                            height: 20px;
                            border-radius: 50%;
                            margin-right: 5px;
                            font-size: 0;
                        }
                    }

                    .hasActiveInactive {
                        display: flex;
                        align-items: center;
                        width: max-content;

                        .material-icons {
                            font-size: 20px;
                            padding-right: $gapSmaller;
                        }

                        &.active {
                            color: #356B21;
                            font-weight: 500;
                        }

                        &.inactive {
                            color: #FC9927;
                            font-weight: 500;
                        }
                    }

                    .title {
                        @include setFont($caption1, normal, 400, 0);
                    }

                    .classDays {
                        display: flex;
                        flex-wrap: wrap;
                
                        li {
                            text-transform: uppercase;
                            font-size: $overline;
                            margin-right: $gapSmaller;
                            @include setBGColor($primary, 0.04);
                            border: 1px solid $primary;
                            padding: 3px $gapSmaller + 3;
                            border-radius: 100px;
                            flex: 0 0 35px;
                            text-align: center;
                            color: $primary;
                            margin-top: $gapSmaller;
                
                            @media (min-width: 1214px) {
                                margin-top: 0;    
                            }
                
                            &:last-child {
                                margin-right: 0;
                            }
                
                            &.disabled {
                                @include setBGColor($primaryCopyColor, 0.02);
                                @include setFontColor($primaryCopyColor, 0.38);
                                border-color: transparent;
                            }
                        }
                    }

                    .grid {
                        display: flex;
                        align-items: center;
                        margin: 0 (-3px);

                        .gridItem {
                            padding: 0 3px;
                        }

                        .itemIcon.material-icons {
                            font-size: 17px;
                            cursor: pointer;

                            &.active {
                                color: $primary;
                            }

                            &.inactive {
                                opacity: 0.5;
                            }
                        }

                        .material-icons-outlined, .material-icons {
                            font-size: 12px;
                        }
                    }

                    .percentageBlock {
                        display: flex;
                        align-items: center;
                        width: 150px;
                        justify-content: space-between;

                        .progress-wrapper {
                            width: 60%;
                            margin: 0;
                        }

                        .percentage {
                            a {
                                padding-left: $gapSmaller;
                            }
                        }

                        .material-icons {
                            font-size: 12px;
                            position: relative;
                            top: 2px;
                        }
                    }

                    .actions {
                        display: flex;
                        visibility: hidden;
        
                        li {
                            a {
                                display: block;
                                position: relative;
        
                                &:hover {
                                    text-decoration: none;
                                }
                            }
        
                            .itemLabel {
                                font-size: 0;
                                position: absolute;
                                left: -999999px;
                            }
        
                            .itemIcon {
                                font-size: 18px;
                                display: block;
                                padding: 2px 8px;
                            }
                        }
        
                        &.copyToClipboard {
                            .wrapper {
                                cursor: pointer;
                                color: $primary;
                            }
                        }
                    }
                }
    
                
            }
    
            .pagination {
                margin: 0;
    
                .pagination-ellipsis {
                    font-size: 0;
    
                    &::after {
                        content: "...";
                        font-size: 14px;
                        position: relative;
                        top: 17px;
                        color: #000;
                    }
                }
            }
    
            .pagination-link {
                color: $primaryCopyColor;
    
                &:hover {
                    text-decoration: none;
                }
    
                &.is-current {
                    background-color: $primary;
                    border-color: $primary;
                    color: $secondaryCopyColor;
                }
    
                &.pagination-previous, &.pagination-next {
                    .icon {
                        .mdi-chevron-left, .mdi-chevron-right {
                            &:after {
                                content: "\f060";
                                @extend .fontAwesomeIcon;
                            }
                        }
    
                        .mdi-chevron-right {
                            &:after {
                                content: "\f061";
                            }	
                        }
                    }
                }
            }
        }
    }
    
    .emptyStateV2 {
        display: flex;
        justify-content: center;
        margin-top: 5%;
    
        figure {
            display: flex;
            flex-direction: column;
            align-items: center;
    
            img {
                width: 80px;
                height: auto;
            }
    
            figcaption {
                margin-top: $gap15;
                @include setFont($subtitle1, 24px, 400, 0);
                color: $onSurfaceVariant;
            }
    
            .button {
                margin-top: $gap15;
            }
        }

        .ctaWrapper {
            display: flex;
            flex-direction: column;
        }
    }

    .yunoModal {
        &.drawerModal {
            justify-content: flex-start;
            align-items: flex-end;

            .scrollable {
                overflow-y: auto;
            }

            .drawerTitle {
                @include setFont($headline6, 28px, 500, $gap15);
            }

            .modal-close {
                background-color: transparent;
                @include setFontColor($primaryCopyColor, 0.6);
                right: 0;
                top: 0;

                &::after {
                    content: "\e5cd";
                    @extend .material-icons-outlined;
                    @include setFontColor($primaryCopyColor, 0.6);
                }
            }

            .modal-content {
                height: 100%;
                max-height: none;
                margin: 0;
                border-radius: 0;
                @extend .slide-in-right;
                @extend .dark87;
            }

            .learners {
                margin-bottom: $gap15;

                &.noBtmGap {
                    margin-bottom: 0;
                }

                .userImg {
                    display: flex;
                    align-items: center;
                    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
                    padding-bottom: $gap15;
                    margin-bottom: $gap15;

                    img {
                        $size: 60px;

                        width: $size;
                        height: $size;
                        border-radius: 50%;
                        font-size: 0;
                        background-color: $grey;
                    }

                    figcaption {
                        font-size: $subtitle1;
                        font-weight: 500;
                        margin-left: $gapSmall;

                        .primaryTitle {
                            font-size: $subtitle1;
                            line-height: auto;
                            @extend .dark87;
                            font-weight: 500;
                            text-transform: capitalize;
                        }

                        .secondaryCaption {
                            font-size: $caption1;
                            line-height: 16px;
                            @extend .dark60;
                        }
                    }

                    .inlineList {
                        margin-top: $gapSmall;

                        li {

                            @media (min-width: 768px) {
                                display: flex;
                            }
            
                            .listLabel {
                                font-size: $caption2;
                                font-weight: 500;
                                @extend .dark87
                            }
            
                            .value {
                                display: flex;
                                @extend .dark60;
            
                                @media (min-width: 768px) {
                                    margin-left: $gapSmall;
                                }
            
                                span {
                                    font-size: $caption1;
                                    &::after {
                                        content: "|";
                                        @include setFontColor($primaryCopyColor, 0.08);
                                        padding-left: $gapSmaller;
                                        margin-right: $gapSmaller;
                                    }
            
                                    &:last-child {
                                        &::after {
                                            display: none;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                &.larger {
                    .userImg {
                        img {
                            $size: 96px;
    
                            width: $size;
                            height: $size;
                            border-radius: 50%;
                        }
                    }
                }
            }

            .classDate {
                font-size: $subtitle2;
                @extend .dark60;
                font-weight: 500;
                margin-bottom: $gap15;  
            }

            .intro {
                .introLabel {
                    font-size: $overline;
                    @extend .dark38;
                    font-weight: 500;
                    text-transform: uppercase;
                    margin-bottom: $gapSmaller;
                    letter-spacing: 1.5px;
                }

                .primaryTitle {
                    font-size: $headline6;
                    font-weight: 500;
                    line-height: 24px;
                    margin-bottom: $gap15;
                }

                .secondaryTitle {
                    font-size: $subtitle2;
                    font-weight: 500;
                    line-height: 24px;
                    margin-bottom: $gap15;
                }
            }

            .stats {
                display: flex;
                margin-bottom: $gap15;

                li {
                    flex: 0 0 50%;

                    .itemValue {
                        font-size: $headline6;
                        line-height: 24px;
                        font-weight: 500;
                        margin-bottom: $gapSmaller;
                    }

                    .itemLabel {
                        font-size: $caption1;
                        line-height: 16px;
                        font-weight: 400;
                        @extend .dark60;
                    }
                }
            }

            .classDetail {
                border: 1px solid rgba(0, 0, 0, 0.08);
                border-radius: 4px;
                padding: $gap15;
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
                margin-top: $gap15;
                background: rgba(0, 0, 0, 0.02);

                .loaderWrapper {
                    height: 24px;
                }

                .smallLoader {
                    font-size: 4px;
                    margin: 0 auto;
                }

                .inform {
                    font-size: $subtitle2;
                    font-weight: 500;
                    line-height: 24px;
                    color: $primary;
                }

                .classDateV2 {
                    font-size: $overline;
                    @extend .dark38;
                    font-weight: 500;
                    text-transform: uppercase;
                    margin-bottom: $gapSmaller;
                    letter-spacing: 1.5px;
                }

                .classTitle {
                    font-size: $subtitle2;
                    line-height: 24px;
                    font-weight: 500;
                    margin-bottom: $gap15;
                }

                .classInfo {
                    display: flex;

                    li {
                        border-right: 1px solid rgba(0, 0, 0, 0.08);
                        padding-right: $gap15;
                        margin-right: $gap15;

                        &:last-child {
                            border-right: 0;
                        }
                    }

                    figure {
                        display: flex;
                    }

                    img {
                        $size: 24px;

                        width: $size;
                        height: $size;
                        border-radius: 50%;
                        font-size: 0;
                        margin-right: $gapSmall;
                    }

                    .itemLabel {
                        font-size: $overline;
                        @extend .dark38;
                        font-weight: 500;
                        text-transform: uppercase;
                        margin-bottom: $gapSmaller;
                        letter-spacing: 1.5px;
                    }

                    .itemValue {
                        font-size: $caption2;
                        line-height: 16px;
                        font-weight: 500;
                    }
                }

                .barWrapper {
                    margin-top: $gap15;

                    .barHeader {
                        display: flex;
                        justify-content: space-between;
                    }

                    .barValue {
                        font-size: $caption1;
                        @extend .dark60;
                        line-height: 16px;
                    }
                }
            }

            .classes {
                border: 1px solid rgba(0, 0, 0, 0.08);
                border-radius: 4px;
                padding: $gap15;
                border-top-left-radius: 0;
                border-top-right-radius: 0;

                .classesLabel {
                    font-size: $overline;
                    @extend .dark38;
                    font-weight: 500;
                    text-transform: uppercase;
                    margin-bottom: $gapSmaller;
                    letter-spacing: 1.5px;
                }

                .days {
                    margin: $gapSmaller (-12px) 0;
                    display: flex;
                    flex-wrap: wrap;

                    li {
                        $size: 32px;

                        padding: 0 12px;
                        margin-top: $gapSmall;

                        .disc {
                            width: $size;
                            height: $size;
                            border-radius: 50%;
                            @include setBGColor($primaryCopyColor, 0.08);
                            @extend .dark38;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            cursor: pointer;
                        }

                        &.active {
                            .disc {
                                background-color: rgba(37, 211, 102, 0.37);
                                color: white;

                                &:hover {
                                    background-color:rgba(37, 211, 102, 0.74);
                                }

                                &:active {
                                    background-color: #25D366;
                                }
                            }

                            &.isClicked {
                                .disc {
                                    background-color: #25D366;
                                }
                            } 
                        }

                        &.disable {
                            .disc {
                                &:hover {
                                    background-color:rgba(0, 0, 0, 0.38);
                                    color: white;
                                }

                                &:active {
                                    background-color: rgba(0, 0, 0, 0.87);
                                    color: white;
                                }
                            }

                            &.isClicked {
                                .disc {
                                    background-color: rgba(0, 0, 0, 0.87);
                                    color: white;
                                }
                            } 
                        }
                    }
                }
            }

            .yunoTable {
                padding: 0;
                border-radius: 4px;
                border: 1px solid;
                @include setBorderColor($primaryCopyColor, 0.1);
                min-height: 500px;
        
                .b-table {
                    .table-wrapper {
                        overflow-y: auto;
                        margin-bottom: $gap15;
        
                        &.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer):hover {
                            background-color: #FFF;
                        }
                    }
        
                    .level {
                        display: none;
                    }
                    thead {
                        tr {
                            th {
                                @media (min-width: 768px) {
                                    border-bottom: 1px solid #E6E6E6;
                                }	
                            }
                        }
                    }
                    tbody {
                        tr {
                            td {
                                @media (min-width: 768px) {
                                    border-top: 0;
                                }	
                            }
        
                            &.is-empty {
                                td {
                                    border-bottom: 0;
                                }
                            }
        
                            &:hover {
                                background-color: #FFF;
                            }
                        }
                    }
                }
            }

            .filterNav {
                > li {
                    > a {
                        font-size: $subtitle1;
                        @extend .dark87;
                        display: block;
                        padding: $gap15;
                        border-bottom: 1px solid;
                        @include setBorderColor($primaryCopyColor, 0.08);
                        font-weight: 500;
                        line-height: 24px;
        
                        &:hover {
                            text-decoration: none;
                        }
        
                        &.is-active {
                            // @include setBorderColor($primaryCopyColor, 0.87);
                            @include setBGColor($primaryCopyColor, 0.02);    
                        }
                    }
                }
            }

            .coursesMain {
                margin-bottom: $gapLargest;
        
                &.hideMe {
                    display: none;
                }
            }
        }

        .availabilityWrapper {
            .ctaWrapper {
                justify-content: flex-start;
            }
        }

        .hasScroll {
            min-height: 280px;
        }
    }

    .filtersWrapper {
        position: sticky;
        top: 0;
        z-index: 9;
        background-color: #FFF;

        .filterHeader {
            display: none;
        }

        &.mobileView {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0 $gap15 $gap15;

            .filterHeader {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
        }
    }

    .filters {
        display: flex;
        flex-wrap: wrap;
        margin: 0 (-$gap15);
        padding: 0;
        background-color: #FFFFFF;

        &.categoryFilter {
            margin-bottom: $gap15;

            @media (min-width: 768px) {
                margin-bottom: 0;
            }

            &.mobileView {
                margin-bottom: 0;
            }
        }

        &.otherFilters {
            // display: none;

            @media (min-width: 768px) {
                display: flex;
            }

            &.mobileView {
                display: block;
            }
        }

        &.noBtmGap {
            padding-bottom: 0;
        }

        &.noTopGap {
            padding-top: 0;
        }

        .yunoAutocompleteSearch {
            flex: 0 0 25%;
            margin: 0 0 0;
            padding: 0 0 0 $gap15;
            display: none;

            @media (min-width: 768px) {
                display: block;
                flex: 0 0 250px;
                margin: $gap15 0 0;
            }

            .field {
                .control {
                    input[type="text"] {
                        border-color: $grey;
                        font-weight: 500;
                        font-size: $subtitle2;
                        height: 43px;
                    }
    
                    .icon {
                        .mdi {
                            display: flex;
                            align-items: center;
                            
                            &::after {
                                content: "\e5c9";
                                @extend .material-icons;
                                color: $primary;
                            }
                        }
                    }
                }
            }

        }

        .yunoDropdown {
            flex: 0 0 25%;
            margin: 0 0 0;
            padding: 0 0 0 $gap15;
            display: none;

            &:first-child {
                display: block;
                flex: 0 0 calc(100% - 43px);

                @media (min-width: 768px) {
                    flex: 0 0 auto;
                }
            }

            @media (min-width: 768px) {
                display: block;
                flex: 0 0 auto;
                margin: $gap15 0 0;
            }
        }

        .yunoCheckbox {
            margin: 0 0 0;
            padding: 0 0 0 $gap15;
            display: none;

            @media (min-width: 768px) {
                display: flex;
                align-items: center;
                flex: 0 0 auto;
                margin: $gap15 0 0;
            }
        }

        &.mobileView {
            .yunoDropdown {
                flex: 0 0 100%;
                display: block;
                margin-bottom: $gap15;
    
                &:first-child {
                    display: block;
                    flex: 0 0 100%;
                }
            }

            .filterTrigger {
                display: none;
            }
        }

        .filterTrigger {
            display: block;
            align-self: center;
            padding-right: $gap15;

            .material-icons {
                font-size: 28px;
            }


            @media (min-width: 768px) {
                display: none;
            }
        }
    }

    .yunoDropdown {
        min-width: 0;

        &.slider {
            .dropdown-menu {
                padding: $gap15;
            }

            .b-slider.is-primary .b-slider-fill {
                background: $primary !important;
            }

            .b-tooltip.is-primary .tooltip-content {
                background: $primary !important;
            }
        }

        &.availability {
            .selectedItem {

            }                
        }

        .timesDays {
            padding-bottom: $gapSmall;

            .wrapper {
                padding: $gapSmall $gap15 0;
            }

            small {
                @extend .body1;
            }
            
            ul {
                display: flex;
                flex-wrap: wrap;
                margin: $gapSmall (-$gapSmaller) 0;

                li {
                    flex: 0 0 25%;
                    padding: 0 $gapSmaller $gapSmaller;


                    .inner {
                        text-align: center;
                        @include setFont($overline, normal, 400, 0);
                        border-radius: 4px;
                        border: 1px solid $grey;
                        padding: $gapSmaller;
                        cursor: pointer;

                        &:hover {
                            border-color: $primary;
                            color: $primary;
                        }
                    }

                    .material-icons {
                        display: block;
                        margin-bottom: $gapSmaller;
                    }

                    &.active {
                        .inner {
                            background-color: $primary;
                            color: white;
                        }
                    }
                }
            }
        }

        .dropdown {
            width: 100%;
        }

        .dropdown-trigger {
            width: 100%;
        }

        &.availability {
            .dropdown-menu {
                width: 300px;
                left: auto;
                right: 0;
            }    
        }

        

        .dropdown-menu {
            // width: 100%;
            padding: 0;
            max-height: 300px;
            overflow-y: auto;

            .dropdown-content {
                box-shadow: none;
                padding: 0;
            }

            a { 
                @extend .dark60;
                padding: $gapSmaller $gap15;

                &.dropdown-item {
                    &.is-active {
                        background-color: $primary;
                        color: white;
                    }
                }
            }

            
        }

        .labelWrapper {
            padding: $gapSmall $gapLargest + 5 $gapSmall $gap15;
            border: 1px solid $grey;
            border-radius: 4px;
            position: relative;
            cursor: pointer;

            .icon {
                position: absolute;
                right: 10px;
                top: calc(50% - 12px);
            }

            .placeHolder {
                @include setFont($caption2, normal, 400, 0);
                display: block;
                @extend .dark60;
            }

            .clearFilter {
                position: absolute;
                right: 35px;
                bottom: 2px;
            }

            .selectedItem {
                font-weight: 400;
                padding-top: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                text-wrap: nowrap;
                display: block;
                padding-right: 27px;
                font-size: $subtitle2;

                span {
                    flex-grow: 0;

                    &:last-child {
                        &::after {
                            display: none;
                        }    
                    }

                    &::after {
                        content: ",";
                        position: relative;
                        left: -2px;
                    }
                }

                &.hasGrid {
                    display: flex;
                    padding-right: 0;

                    .item {
                        flex-grow: 0;
                        display: flex;
                        position: relative;
                        padding-right: 27px;
                        margin-right: 5px;

                        &:last-child {
                            margin-right: 0;
                        }

                        span {
                            &::after {
                                display: none;
                            }
                        }
                    }

                    .clearFilter {
                        position: absolute;
                        right: 0;
                        top: -2px;
                    }
                }
            }

            
        }
        
        &.iconOnly {
            .labelWrapper {
                border: 0;
                padding: 0 $gapSmaller 0;

                .icon {
                    top: calc(50% - 14px);
                }
            }
        }

        &.multiSelect {
            .dropdown-menu {
                width: max-content;
                // left: auto;
                // right: 0;

                .listCaption {
                    @include setFont($subtitle2, 24px, 500, 0);
                    @extend .dark87;
                    padding: 0 $gap15;
                }

                .dropdown-item {
                    position: relative;
                    padding-left: 35px;

                    &::before {
                        content: "\e835";
                        @extend .material-icons-outlined;
                        position: absolute;
                        left: 13px;
                        top: 6px;
                        font-size: 18px;
                    }

                    &.is-active {
                        background-color: #FFF;
                        @extend .dark60;

                        &::before {
                            content: "\e834";
                            @extend .material-icons;
                            font-size: 18px;
                            color: $primary;
                        }
                    }
                }
            }
        }
        
        &.hideColumn {
            .dropdown-menu {
                left: auto;
                right: 0;

                .listCaption {
                    @include setFont($subtitle2, 24px, 500, 0);
                    @extend .dark87;
                    padding: 0 $gap15;
                }

                .dropdown-item {
                    position: relative;
                    padding-left: 35px;

                    &::before {
                        content: "\e835";
                        @extend .material-icons-outlined;
                        position: absolute;
                        left: 13px;
                        top: 6px;
                        font-size: 18px;
                    }

                    &.is-active {
                        background-color: #FFF;
                        @extend .dark60;

                        &::before {
                            content: "\e834";
                            @extend .material-icons;
                            font-size: 18px;
                            color: $primary;
                        }
                    }
                }
            }
        }
    }
}

