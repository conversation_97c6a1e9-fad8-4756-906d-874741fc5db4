<?php

namespace V4;

/**
 * Class InstructorFilter
 * Handles Instructor-related filter generation.
 *
 * @package V4
 * @since 1.0.0
 */
class InstructorFilter extends Filter
{
    protected $es;
    protected $userModel;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
        // Load necessary libraries and models as needed
        $this->loadLibary('locale');
        $this->loadLibary('elasticSearch', 'es');
        $this->loadLibary('schema');
        $this->loadModel('user');
    }

    /**
     * Generates filter data for "Featured" status of instructors.
     *
     * @param mixed $selectedValue The currently selected value for the filter.
     * @return array The filter configuration array.
     */
    public function generateFeaturedFilterData($selectedValue = "0") // Default to "0" (Not Featured)
    {
        $options = [
            ['id' => '1', 'label' => 'Yes'],
            ['id' => '0', 'label' => 'No'],
        ];
        $selectedItem = null;
        foreach ($options as $option) {
            if ($option['id'] == $selectedValue) {
                $selectedItem = $option;
                break;
            }
        }
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Featured',
            'placeholder' => 'Is Featured?',
            'tooltip' => 'Filter instructors by their featured status',
            'filter' => 'is_featured',
            'multiple' => false,
            'selected' => $selectedItem ? $selectedItem['id'] : "0",
            'items' => $options,
            'count' => count($options),
            'current' => $selectedItem ? $selectedItem['label'] : 'No',
            'loading' => false,
            'success' => true,
        ];
    }

    /**
     * Generates filter data for "Disabled" status of instructors.
     *
     * @param mixed $selectedValue The currently selected value for the filter.
     * @return array The filter configuration array.
     */
    public function generateDisabledFilterData($selectedValue = "0") // Default to "0" (Enabled)
    {
        $options = [
            ['id' => '1', 'label' => 'Yes'],
            ['id' => '0', 'label' => 'No'],
        ];
        $selectedItem = null;
        foreach ($options as $option) {
            if ($option['id'] == $selectedValue) {
                $selectedItem = $option;
                break;
            }
        }
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Disabled',
            'placeholder' => 'Is Disabled?',
            'tooltip' => 'Filter instructors by their account status (enabled/disabled)',
            'filter' => 'is_disabled',
            'multiple' => false,
            'selected' => $selectedItem ? $selectedItem['id'] : "0",
            'items' => $options,
            'count' => count($options),
            'current' => $selectedItem ? $selectedItem['label'] : 'No',
            'loading' => false,
            'success' => true,
        ];
    }

    private function parseIdList($value, $asInt = true): array
    {
        if (is_array($value)) {
            $value = array_filter(array_map('trim', $value));
            return $asInt ? array_map('intval', $value) : $value;
        }
        if (is_string($value) && !empty($value)) {
            $ids = array_filter(array_map('trim', explode(',', trim($value, '[] '))));
            return $asInt ? array_map('intval', $ids) : $ids;
        }
        return [];
    }

    /**
     * Generates category filter data for instructors.
     *
     * @param \WP_REST_Request $request The request object to extract selected values if any.
     * @return array The filter configuration array.
     */
    public function generateCategoryFilterData($request, $limit, $offset)
    {
        $selectedCategories = $this->parseIdList($request->get_param('category_ids') ?? []);
        $categoryResult = $this->getCategoryFilterTreeData($limit, $offset);

        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Categories',
            'placeholder' => 'Select Categories',
            'tooltip' => 'Filter instructors by course categories they are mapped to',
            'filter' => 'category',
            'multiple' => true,
            'selected' => $selectedCategories,
            'items' => $categoryResult['items'],
            'count' => $categoryResult['count'],
            'current' => '',
            'loading' => false,
            'success' => $categoryResult['count'] > 0,
        ];
    }

    /**
     * Flattens a category tree into a single-level array with indentation.
     */
    private function flattenTree(array $tree, array &$flatList, int $level = 0)
    {
        foreach ($tree as $node) {
            $node['level'] = $level;
            $originalNode = $node;
            unset($originalNode['children']);
            $flatList[] = $originalNode;
            if (!empty($node['children'])) {
                $this->flattenTree($node['children'], $flatList, $level + 1);
            }
        }
    }

    /**
     * Fetches hierarchical category data for filters from Elasticsearch.
     *
     * @param int|null $limit Optional. The maximum number of top-level categories to return.
     * @return array Hierarchical list of categories.
     */
    private function getCategoryFilterTreeData($limit, $offset)
    {
        $categoriesById = [];
        $categoryCounts = [];

        $collectAndCountCategories = function ($categories, $parentId = 0) use (&$categoriesById, &$categoryCounts, &$collectAndCountCategories) {
            foreach ($categories as $cat) {
                if (empty($cat['id']) || empty($cat['name'])) continue;

                if (!isset($categoriesById[$cat['id']])) {
                    $categoriesById[$cat['id']] = [
                        'id' => (int)$cat['id'],
                        'name' => $cat['name'],
                        'slug' => $cat['slug'],
                        'parent_id' => $parentId,
                    ];
                }
                $categoryCounts[(int)$cat['id']] = ($categoryCounts[(int)$cat['id']] ?? 0) + 1;

                if (!empty($cat['sub_category']) && is_array($cat['sub_category'])) {
                    $collectAndCountCategories($cat['sub_category']);
                }
            }
        };

        $esQuery = [
            "size" => 10000,
            "_source" => ["data.details.course_category"]
        ];

        $response = $this->es->customQuery($esQuery, 'course');

        if (isset($response['status_code']) && $response['status_code'] === 200 && !empty($response['body']['hits']['hits'])) {
            foreach ($response['body']['hits']['hits'] as $hit) {
                if (!empty($hit['_source']['data']['details']['course_category'])) {
                    $collectAndCountCategories($hit['_source']['data']['details']['course_category']);
                }
            }
        }

        $buildTree = function ($parentId = 0) use (&$categoriesById, &$categoryCounts, &$buildTree) {
            $branch = [];
            foreach ($categoriesById as $catId => $category) {
                if ($category['parent_id'] == $parentId) {
                    $children = $buildTree($catId);
                    $node = [
                        'id' => $category['id'],
                        'slug' => $category['slug'],
                        'label' => $category['name'],
                        'filter' => 'category',
                        'count' => $categoryCounts[$catId] ?? 0,
                        'parent_id' => $category['parent_id'],
                    ];
                    if ($children) {
                        $node['children'] = $children;
                    }
                    $branch[] = $node;
                }
            }
            return $branch;
        };

        $tree = $buildTree();
        $flatList = [];
        $this->flattenTree($tree, $flatList);

        $totalCategories = count($flatList);
        $paginatedItems = array_slice($flatList, $offset, $limit);

        return ['items' => $paginatedItems, 'count' => $totalCategories];
    }

    /**
     * Generates organization filter data for instructors.
     *
     * @param \WP_REST_Request $request The request object.
     * @return array The filter configuration array.
     */
    public function generateOrganizationFilterData($request, $limit, $offset)
    {
        $selectedOrgIds = $this->parseIdList($request->get_param('org_id'));
        $selectedOrg = $selectedOrgIds[0] ?? null;

        $orgResult = $this->getOrganizationFilterSelectData($limit, $offset);

        $currentValue = '';
        if ($selectedOrg !== null) {
            foreach ($orgResult['items'] as $item) {
                if (isset($item['id']) && $item['id'] == $selectedOrg) {
                    $currentValue = $item['label'] ?? '';
                    break;
                }
            }
        }

        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Organizations',
            'placeholder' => 'Select Organization',
            'tooltip' => 'Filter instructors by the organization they belong to',
            'filter' => 'org_id',
            'multiple' => false,
            'selected' => $selectedOrg,
            'items' => $orgResult['items'],
            'count' => $orgResult['count'],
            'current' => $currentValue,
            'loading' => false,
            'success' => $orgResult['count'] > 0,
        ];
    }

    /**
     * Fetches organization data for select/dropdown filters (moved from InstructorModel).
     * Changed name to _getOrganizationFilterSelectData to avoid conflict if another internal method is needed.
     *
     * @param int|null $limit The maximum number of organizations to fetch.
     * @return array List of organizations formatted for a select dropdown.
     */
    private function getOrganizationFilterSelectData($limit, $offset)
    {
        $queryParams = [
            '_source' => ['data.details.record_id', 'data.details.organisation_name'],
            'query' => ['match_all' => new \stdClass()],
            'size' => 10000,
            'track_total_hits' => true
        ];

        $response = $this->es->customQuery($queryParams, 'org');
        $allItems = [];

        if (isset($response['status_code']) && $response['status_code'] === 200 && !empty($response['body']['hits']['hits'])) {
            foreach ($response['body']['hits']['hits'] as $hit) {
                $details = $hit['_source']['data']['details'];
                if (!empty($details['record_id']) && !empty($details['organisation_name'])) {
                    $allItems[] = [
                        'id' => (int)$details['record_id'],
                        'label' => $details['organisation_name'],
                        'slug' => sanitize_title($details['organisation_name']),
                        'filter' => 'org_id',
                        'sub_title' => '',
                        'interval' => '',
                        'parent_id' => 0,
                        'icon' => ['type' => '', 'code' => '']
                    ];
                }
            }
        }

        $totalCount = count($allItems);
        $paginatedItems = array_slice($allItems, $offset, $limit);

        return ['items' => $paginatedItems, 'count' => $totalCount];
    }

    /**
     * Generates filter data for "Has Mapped Courses" status of instructors.
     *
     * @param mixed $selectedValue The currently selected value for the filter.
     * @return array The filter configuration array.
     */
    public function generateHasMappedCoursesFilterData($selectedValue = "0") // Default to "0" (No)
    {
        $options = [
            ['id' => '1', 'label' => 'Yes'],
            ['id' => '0', 'label' => 'No'],
        ];
        $selectedItem = null;
        foreach ($options as $option) {
            if ($option['id'] == $selectedValue) {
                $selectedItem = $option;
                break;
            }
        }
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Has Mapped Courses',
            'placeholder' => 'Has Mapped Courses?',
            'tooltip' => 'Filter for instructors who have courses mapped to them',
            'filter' => 'has_mapped_courses',
            'multiple' => false,
            'selected' => $selectedItem ? $selectedItem['id'] : "0",
            'items' => $options,
            'count' => count($options),
            'current' => $selectedItem ? $selectedItem['label'] : 'No',
            'loading' => false,
            'success' => true,
        ];
    }

    /**
     * Generates filter data for "Has Active Batches" status of instructors.
     *
     * @param mixed $selectedValue The currently selected value for the filter.
     * @return array The filter configuration array.
     */
    public function generateHasActiveBatchesFilterData($selectedValue = "0") // Default to "0" (No)
    {
        $options = [
            ['id' => '1', 'label' => 'Yes'],
            ['id' => '0', 'label' => 'No'],
        ];
        $selectedItem = null;
        foreach ($options as $option) {
            if ($option['id'] == $selectedValue) {
                $selectedItem = $option;
                break;
            }
        }
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Has Active Batches',
            'placeholder' => 'Has Active Batches?',
            'tooltip' => 'Filter for instructors who have active batches',
            'filter' => 'has_active_batches',
            'multiple' => false,
            'selected' => $selectedItem ? $selectedItem['id'] : "0",
            'items' => $options,
            'count' => count($options),
            'current' => $selectedItem ? $selectedItem['label'] : 'No',
            'loading' => false,
            'success' => true,
        ];
    }

    /**
     * Generates filter data for "Has Active Enrollments" status of instructors.
     *
     * @param mixed $selectedValue The currently selected value for the filter.
     * @return array The filter configuration array.
     */
    public function generateHasActiveEnrollmentsFilterData($selectedValue = "0") // Default to "0" (No)
    {
        $options = [
            ['id' => '1', 'label' => 'Yes'],
            ['id' => '0', 'label' => 'No'],
        ];
        $selectedItem = null;
        foreach ($options as $option) {
            if ($option['id'] == $selectedValue) {
                $selectedItem = $option;
                break;
            }
        }
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Has Active Enrollments',
            'placeholder' => 'Has Active Enrollments?',
            'tooltip' => 'Filter for instructors who have active enrollments in their batches',
            'filter' => 'has_active_enrollments',
            'multiple' => false,
            'selected' => $selectedItem ? $selectedItem['id'] : "0",
            'items' => $options,
            'count' => count($options),
            'current' => $selectedItem ? $selectedItem['label'] : 'No',
            'loading' => false,
            'success' => true,
        ];
    }

    /**
     * Generates filter data for "Has Past Enrollments" status of instructors.
     *
     * @param mixed $selectedValue The currently selected value for the filter.
     * @return array The filter configuration array.
     */
    public function generateHasPastEnrollmentsFilterData($selectedValue = "0") // Default to "0" (No)
    {
        $options = [
            ['id' => '1', 'label' => 'Yes'],
            ['id' => '0', 'label' => 'No'],
        ];
        $selectedItem = null;
        foreach ($options as $option) {
            if ($option['id'] == $selectedValue) {
                $selectedItem = $option;
                break;
            }
        }
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Has Past Enrollments',
            'placeholder' => 'Has Past Enrollments?',
            'tooltip' => 'Filter for instructors who have past enrollments in their batches',
            'filter' => 'has_past_enrollments',
            'multiple' => false,
            'selected' => $selectedItem ? $selectedItem['id'] : "0",
            'items' => $options,
            'count' => count($options),
            'current' => $selectedItem ? $selectedItem['label'] : 'No',
            'loading' => false,
            'success' => true,
        ];
    }

    /**
     * Generates filter data for "Has Working Hours" status of instructors.
     *
     * @param mixed $selectedValue The currently selected value for the filter.
     * @return array The filter configuration array.
     */
    public function generateHasWorkingHoursFilterData($selectedValue = "0")
    {
        $options = [
            ['id' => '1', 'label' => 'Yes'],
            ['id' => '0', 'label' => 'No'],
        ];
        $selectedItem = null;
        foreach ($options as $option) {
            if ($option['id'] == $selectedValue) {
                $selectedItem = $option;
                break;
            }
        }
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Has Working Hours',
            'placeholder' => 'Has Working Hours?',
            'tooltip' => 'Filter for instructors who have defined their working hours',
            'filter' => 'has_working_hours',
            'multiple' => false,
            'selected' => $selectedItem ? $selectedItem['id'] : "0",
            'items' => $options,
            'count' => count($options),
            'current' => $selectedItem ? $selectedItem['label'] : 'No',
            'loading' => false,
            'success' => true,
        ];
    }

    /**
     * Generates filter data for "Has Past Batches" status of instructors.
     *
     * @param mixed $selectedValue The currently selected value for the filter.
     * @return array The filter configuration array.
     */
    public function generateHasPastBatchesFilterData($selectedValue = "0")
    {
        $options = [
            ['id' => '1', 'label' => 'Yes'],
            ['id' => '0', 'label' => 'No'],
        ];
        $selectedItem = null;
        foreach ($options as $option) {
            if ($option['id'] == $selectedValue) {
                $selectedItem = $option;
                break;
            }
        }
        return [
            'ui_control_type' => 'DROPDOWN',
            'is_active' => true,
            'title' => 'Has Past Batches',
            'placeholder' => 'Has Past Batches?',
            'tooltip' => 'Filter for instructors who have past batches',
            'filter' => 'has_past_batches',
            'multiple' => false,
            'selected' => $selectedItem ? $selectedItem['id'] : "0",
            'items' => $options,
            'count' => count($options),
            'current' => $selectedItem ? $selectedItem['label'] : 'No',
            'loading' => false,
            'success' => true,
        ];
    }

    // Other filter generation methods will be added here

    public function generateBatchInstructorFilters($userId, $instructorId)
    {
        return [
            'filter' => 'instructor_id',
            'title' => 'Instructor',
            'is_active' => true,
            'multiple' => false,
            'placeholder' => 'Select Instructor',
            'ui_control_type' => 'query_suggestion',
            'selected' => $instructorId, //  Pre-select instructor
            'current' => '',
            'loading' => false,
            'success' => false,
            'is_disabled' => false,
            'items' => []
        ];
    }
}
