/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors
 */
(function(){function n(n,t,r){switch(r.length){case 0:return n.call(t);case 1:return n.call(t,r[0]);case 2:return n.call(t,r[0],r[1]);case 3:return n.call(t,r[0],r[1],r[2])}return n.apply(t,r)}function t(n,t,r,e){for(var u=-1,i=null==n?0:n.length;++u<i;){var o=n[u];t(e,o,r(o),n)}return e}function r(n,t){for(var r=-1,e=null==n?0:n.length;++r<e&&t(n[r],r,n)!==!1;);return n}function e(n,t){for(var r=null==n?0:n.length;r--&&t(n[r],r,n)!==!1;);return n}function u(n,t){for(var r=-1,e=null==n?0:n.length;++r<e;)if(!t(n[r],r,n))return!1;
return!0}function i(n,t){for(var r=-1,e=null==n?0:n.length,u=0,i=[];++r<e;){var o=n[r];t(o,r,n)&&(i[u++]=o)}return i}function o(n,t){return!!(null==n?0:n.length)&&y(n,t,0)>-1}function f(n,t,r){for(var e=-1,u=null==n?0:n.length;++e<u;)if(r(t,n[e]))return!0;return!1}function c(n,t){for(var r=-1,e=null==n?0:n.length,u=Array(e);++r<e;)u[r]=t(n[r],r,n);return u}function a(n,t){for(var r=-1,e=t.length,u=n.length;++r<e;)n[u+r]=t[r];return n}function l(n,t,r,e){var u=-1,i=null==n?0:n.length;for(e&&i&&(r=n[++u]);++u<i;)r=t(r,n[u],u,n);
return r}function s(n,t,r,e){var u=null==n?0:n.length;for(e&&u&&(r=n[--u]);u--;)r=t(r,n[u],u,n);return r}function h(n,t){for(var r=-1,e=null==n?0:n.length;++r<e;)if(t(n[r],r,n))return!0;return!1}function p(n){return n.split("")}function _(n){return n.match($t)||[]}function v(n,t,r){var e;return r(n,function(n,r,u){if(t(n,r,u))return e=r,!1}),e}function g(n,t,r,e){for(var u=n.length,i=r+(e?1:-1);e?i--:++i<u;)if(t(n[i],i,n))return i;return-1}function y(n,t,r){return t===t?Z(n,t,r):g(n,b,r)}function d(n,t,r,e){
for(var u=r-1,i=n.length;++u<i;)if(e(n[u],t))return u;return-1}function b(n){return n!==n}function w(n,t){var r=null==n?0:n.length;return r?k(n,t)/r:Cn}function m(n){return function(t){return null==t?X:t[n]}}function x(n){return function(t){return null==n?X:n[t]}}function j(n,t,r,e,u){return u(n,function(n,u,i){r=e?(e=!1,n):t(r,n,u,i)}),r}function A(n,t){var r=n.length;for(n.sort(t);r--;)n[r]=n[r].value;return n}function k(n,t){for(var r,e=-1,u=n.length;++e<u;){var i=t(n[e]);i!==X&&(r=r===X?i:r+i);
}return r}function O(n,t){for(var r=-1,e=Array(n);++r<n;)e[r]=t(r);return e}function I(n,t){return c(t,function(t){return[t,n[t]]})}function R(n){return n?n.slice(0,H(n)+1).replace(Lt,""):n}function z(n){return function(t){return n(t)}}function E(n,t){return c(t,function(t){return n[t]})}function S(n,t){return n.has(t)}function W(n,t){for(var r=-1,e=n.length;++r<e&&y(t,n[r],0)>-1;);return r}function L(n,t){for(var r=n.length;r--&&y(t,n[r],0)>-1;);return r}function C(n,t){for(var r=n.length,e=0;r--;)n[r]===t&&++e;
return e}function U(n){return"\\"+Yr[n]}function B(n,t){return null==n?X:n[t]}function T(n){return Nr.test(n)}function $(n){return Pr.test(n)}function D(n){for(var t,r=[];!(t=n.next()).done;)r.push(t.value);return r}function M(n){var t=-1,r=Array(n.size);return n.forEach(function(n,e){r[++t]=[e,n]}),r}function F(n,t){return function(r){return n(t(r))}}function N(n,t){for(var r=-1,e=n.length,u=0,i=[];++r<e;){var o=n[r];o!==t&&o!==cn||(n[r]=cn,i[u++]=r)}return i}function P(n){var t=-1,r=Array(n.size);
return n.forEach(function(n){r[++t]=n}),r}function q(n){var t=-1,r=Array(n.size);return n.forEach(function(n){r[++t]=[n,n]}),r}function Z(n,t,r){for(var e=r-1,u=n.length;++e<u;)if(n[e]===t)return e;return-1}function K(n,t,r){for(var e=r+1;e--;)if(n[e]===t)return e;return e}function V(n){return T(n)?J(n):_e(n)}function G(n){return T(n)?Y(n):p(n)}function H(n){for(var t=n.length;t--&&Ct.test(n.charAt(t)););return t}function J(n){for(var t=Mr.lastIndex=0;Mr.test(n);)++t;return t}function Y(n){return n.match(Mr)||[];
}function Q(n){return n.match(Fr)||[]}var X,nn="4.17.21",tn=200,rn="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",en="Expected a function",un="Invalid `variable` option passed into `_.template`",on="__lodash_hash_undefined__",fn=500,cn="__lodash_placeholder__",an=1,ln=2,sn=4,hn=1,pn=2,_n=1,vn=2,gn=4,yn=8,dn=16,bn=32,wn=64,mn=128,xn=256,jn=512,An=30,kn="...",On=800,In=16,Rn=1,zn=2,En=3,Sn=1/0,Wn=9007199254740991,Ln=1.7976931348623157e308,Cn=NaN,Un=4294967295,Bn=Un-1,Tn=Un>>>1,$n=[["ary",mn],["bind",_n],["bindKey",vn],["curry",yn],["curryRight",dn],["flip",jn],["partial",bn],["partialRight",wn],["rearg",xn]],Dn="[object Arguments]",Mn="[object Array]",Fn="[object AsyncFunction]",Nn="[object Boolean]",Pn="[object Date]",qn="[object DOMException]",Zn="[object Error]",Kn="[object Function]",Vn="[object GeneratorFunction]",Gn="[object Map]",Hn="[object Number]",Jn="[object Null]",Yn="[object Object]",Qn="[object Promise]",Xn="[object Proxy]",nt="[object RegExp]",tt="[object Set]",rt="[object String]",et="[object Symbol]",ut="[object Undefined]",it="[object WeakMap]",ot="[object WeakSet]",ft="[object ArrayBuffer]",ct="[object DataView]",at="[object Float32Array]",lt="[object Float64Array]",st="[object Int8Array]",ht="[object Int16Array]",pt="[object Int32Array]",_t="[object Uint8Array]",vt="[object Uint8ClampedArray]",gt="[object Uint16Array]",yt="[object Uint32Array]",dt=/\b__p \+= '';/g,bt=/\b(__p \+=) '' \+/g,wt=/(__e\(.*?\)|\b__t\)) \+\n'';/g,mt=/&(?:amp|lt|gt|quot|#39);/g,xt=/[&<>"']/g,jt=RegExp(mt.source),At=RegExp(xt.source),kt=/<%-([\s\S]+?)%>/g,Ot=/<%([\s\S]+?)%>/g,It=/<%=([\s\S]+?)%>/g,Rt=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,zt=/^\w*$/,Et=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,St=/[\\^$.*+?()[\]{}|]/g,Wt=RegExp(St.source),Lt=/^\s+/,Ct=/\s/,Ut=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Bt=/\{\n\/\* \[wrapped with (.+)\] \*/,Tt=/,? & /,$t=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Dt=/[()=,{}\[\]\/\s]/,Mt=/\\(\\)?/g,Ft=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Nt=/\w*$/,Pt=/^[-+]0x[0-9a-f]+$/i,qt=/^0b[01]+$/i,Zt=/^\[object .+?Constructor\]$/,Kt=/^0o[0-7]+$/i,Vt=/^(?:0|[1-9]\d*)$/,Gt=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Ht=/($^)/,Jt=/['\n\r\u2028\u2029\\]/g,Yt="\\ud800-\\udfff",Qt="\\u0300-\\u036f",Xt="\\ufe20-\\ufe2f",nr="\\u20d0-\\u20ff",tr=Qt+Xt+nr,rr="\\u2700-\\u27bf",er="a-z\\xdf-\\xf6\\xf8-\\xff",ur="\\xac\\xb1\\xd7\\xf7",ir="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",or="\\u2000-\\u206f",fr=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",cr="A-Z\\xc0-\\xd6\\xd8-\\xde",ar="\\ufe0e\\ufe0f",lr=ur+ir+or+fr,sr="['\u2019]",hr="["+Yt+"]",pr="["+lr+"]",_r="["+tr+"]",vr="\\d+",gr="["+rr+"]",yr="["+er+"]",dr="[^"+Yt+lr+vr+rr+er+cr+"]",br="\\ud83c[\\udffb-\\udfff]",wr="(?:"+_r+"|"+br+")",mr="[^"+Yt+"]",xr="(?:\\ud83c[\\udde6-\\uddff]){2}",jr="[\\ud800-\\udbff][\\udc00-\\udfff]",Ar="["+cr+"]",kr="\\u200d",Or="(?:"+yr+"|"+dr+")",Ir="(?:"+Ar+"|"+dr+")",Rr="(?:"+sr+"(?:d|ll|m|re|s|t|ve))?",zr="(?:"+sr+"(?:D|LL|M|RE|S|T|VE))?",Er=wr+"?",Sr="["+ar+"]?",Wr="(?:"+kr+"(?:"+[mr,xr,jr].join("|")+")"+Sr+Er+")*",Lr="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Cr="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Ur=Sr+Er+Wr,Br="(?:"+[gr,xr,jr].join("|")+")"+Ur,Tr="(?:"+[mr+_r+"?",_r,xr,jr,hr].join("|")+")",$r=RegExp(sr,"g"),Dr=RegExp(_r,"g"),Mr=RegExp(br+"(?="+br+")|"+Tr+Ur,"g"),Fr=RegExp([Ar+"?"+yr+"+"+Rr+"(?="+[pr,Ar,"$"].join("|")+")",Ir+"+"+zr+"(?="+[pr,Ar+Or,"$"].join("|")+")",Ar+"?"+Or+"+"+Rr,Ar+"+"+zr,Cr,Lr,vr,Br].join("|"),"g"),Nr=RegExp("["+kr+Yt+tr+ar+"]"),Pr=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,qr=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Zr=-1,Kr={};
Kr[at]=Kr[lt]=Kr[st]=Kr[ht]=Kr[pt]=Kr[_t]=Kr[vt]=Kr[gt]=Kr[yt]=!0,Kr[Dn]=Kr[Mn]=Kr[ft]=Kr[Nn]=Kr[ct]=Kr[Pn]=Kr[Zn]=Kr[Kn]=Kr[Gn]=Kr[Hn]=Kr[Yn]=Kr[nt]=Kr[tt]=Kr[rt]=Kr[it]=!1;var Vr={};Vr[Dn]=Vr[Mn]=Vr[ft]=Vr[ct]=Vr[Nn]=Vr[Pn]=Vr[at]=Vr[lt]=Vr[st]=Vr[ht]=Vr[pt]=Vr[Gn]=Vr[Hn]=Vr[Yn]=Vr[nt]=Vr[tt]=Vr[rt]=Vr[et]=Vr[_t]=Vr[vt]=Vr[gt]=Vr[yt]=!0,Vr[Zn]=Vr[Kn]=Vr[it]=!1;var Gr={"\xc0":"A","\xc1":"A","\xc2":"A","\xc3":"A","\xc4":"A","\xc5":"A","\xe0":"a","\xe1":"a","\xe2":"a","\xe3":"a","\xe4":"a","\xe5":"a",
"\xc7":"C","\xe7":"c","\xd0":"D","\xf0":"d","\xc8":"E","\xc9":"E","\xca":"E","\xcb":"E","\xe8":"e","\xe9":"e","\xea":"e","\xeb":"e","\xcc":"I","\xcd":"I","\xce":"I","\xcf":"I","\xec":"i","\xed":"i","\xee":"i","\xef":"i","\xd1":"N","\xf1":"n","\xd2":"O","\xd3":"O","\xd4":"O","\xd5":"O","\xd6":"O","\xd8":"O","\xf2":"o","\xf3":"o","\xf4":"o","\xf5":"o","\xf6":"o","\xf8":"o","\xd9":"U","\xda":"U","\xdb":"U","\xdc":"U","\xf9":"u","\xfa":"u","\xfb":"u","\xfc":"u","\xdd":"Y","\xfd":"y","\xff":"y","\xc6":"Ae",
"\xe6":"ae","\xde":"Th","\xfe":"th","\xdf":"ss","\u0100":"A","\u0102":"A","\u0104":"A","\u0101":"a","\u0103":"a","\u0105":"a","\u0106":"C","\u0108":"C","\u010a":"C","\u010c":"C","\u0107":"c","\u0109":"c","\u010b":"c","\u010d":"c","\u010e":"D","\u0110":"D","\u010f":"d","\u0111":"d","\u0112":"E","\u0114":"E","\u0116":"E","\u0118":"E","\u011a":"E","\u0113":"e","\u0115":"e","\u0117":"e","\u0119":"e","\u011b":"e","\u011c":"G","\u011e":"G","\u0120":"G","\u0122":"G","\u011d":"g","\u011f":"g","\u0121":"g",
"\u0123":"g","\u0124":"H","\u0126":"H","\u0125":"h","\u0127":"h","\u0128":"I","\u012a":"I","\u012c":"I","\u012e":"I","\u0130":"I","\u0129":"i","\u012b":"i","\u012d":"i","\u012f":"i","\u0131":"i","\u0134":"J","\u0135":"j","\u0136":"K","\u0137":"k","\u0138":"k","\u0139":"L","\u013b":"L","\u013d":"L","\u013f":"L","\u0141":"L","\u013a":"l","\u013c":"l","\u013e":"l","\u0140":"l","\u0142":"l","\u0143":"N","\u0145":"N","\u0147":"N","\u014a":"N","\u0144":"n","\u0146":"n","\u0148":"n","\u014b":"n","\u014c":"O",
"\u014e":"O","\u0150":"O","\u014d":"o","\u014f":"o","\u0151":"o","\u0154":"R","\u0156":"R","\u0158":"R","\u0155":"r","\u0157":"r","\u0159":"r","\u015a":"S","\u015c":"S","\u015e":"S","\u0160":"S","\u015b":"s","\u015d":"s","\u015f":"s","\u0161":"s","\u0162":"T","\u0164":"T","\u0166":"T","\u0163":"t","\u0165":"t","\u0167":"t","\u0168":"U","\u016a":"U","\u016c":"U","\u016e":"U","\u0170":"U","\u0172":"U","\u0169":"u","\u016b":"u","\u016d":"u","\u016f":"u","\u0171":"u","\u0173":"u","\u0174":"W","\u0175":"w",
"\u0176":"Y","\u0177":"y","\u0178":"Y","\u0179":"Z","\u017b":"Z","\u017d":"Z","\u017a":"z","\u017c":"z","\u017e":"z","\u0132":"IJ","\u0133":"ij","\u0152":"Oe","\u0153":"oe","\u0149":"'n","\u017f":"s"},Hr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Jr={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},Yr={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Qr=parseFloat,Xr=parseInt,ne="object"==typeof global&&global&&global.Object===Object&&global,te="object"==typeof self&&self&&self.Object===Object&&self,re=ne||te||Function("return this")(),ee="object"==typeof exports&&exports&&!exports.nodeType&&exports,ue=ee&&"object"==typeof module&&module&&!module.nodeType&&module,ie=ue&&ue.exports===ee,oe=ie&&ne.process,fe=function(){
try{var n=ue&&ue.require&&ue.require("util").types;return n?n:oe&&oe.binding&&oe.binding("util")}catch(n){}}(),ce=fe&&fe.isArrayBuffer,ae=fe&&fe.isDate,le=fe&&fe.isMap,se=fe&&fe.isRegExp,he=fe&&fe.isSet,pe=fe&&fe.isTypedArray,_e=m("length"),ve=x(Gr),ge=x(Hr),ye=x(Jr),de=function p(x){function Z(n){if(cc(n)&&!bh(n)&&!(n instanceof Ct)){if(n instanceof Y)return n;if(bl.call(n,"__wrapped__"))return eo(n)}return new Y(n)}function J(){}function Y(n,t){this.__wrapped__=n,this.__actions__=[],this.__chain__=!!t,
this.__index__=0,this.__values__=X}function Ct(n){this.__wrapped__=n,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Un,this.__views__=[]}function $t(){var n=new Ct(this.__wrapped__);return n.__actions__=Tu(this.__actions__),n.__dir__=this.__dir__,n.__filtered__=this.__filtered__,n.__iteratees__=Tu(this.__iteratees__),n.__takeCount__=this.__takeCount__,n.__views__=Tu(this.__views__),n}function Yt(){if(this.__filtered__){var n=new Ct(this);n.__dir__=-1,
n.__filtered__=!0}else n=this.clone(),n.__dir__*=-1;return n}function Qt(){var n=this.__wrapped__.value(),t=this.__dir__,r=bh(n),e=t<0,u=r?n.length:0,i=Oi(0,u,this.__views__),o=i.start,f=i.end,c=f-o,a=e?f:o-1,l=this.__iteratees__,s=l.length,h=0,p=Hl(c,this.__takeCount__);if(!r||!e&&u==c&&p==c)return wu(n,this.__actions__);var _=[];n:for(;c--&&h<p;){a+=t;for(var v=-1,g=n[a];++v<s;){var y=l[v],d=y.iteratee,b=y.type,w=d(g);if(b==zn)g=w;else if(!w){if(b==Rn)continue n;break n}}_[h++]=g}return _}function Xt(n){
var t=-1,r=null==n?0:n.length;for(this.clear();++t<r;){var e=n[t];this.set(e[0],e[1])}}function nr(){this.__data__=is?is(null):{},this.size=0}function tr(n){var t=this.has(n)&&delete this.__data__[n];return this.size-=t?1:0,t}function rr(n){var t=this.__data__;if(is){var r=t[n];return r===on?X:r}return bl.call(t,n)?t[n]:X}function er(n){var t=this.__data__;return is?t[n]!==X:bl.call(t,n)}function ur(n,t){var r=this.__data__;return this.size+=this.has(n)?0:1,r[n]=is&&t===X?on:t,this}function ir(n){
var t=-1,r=null==n?0:n.length;for(this.clear();++t<r;){var e=n[t];this.set(e[0],e[1])}}function or(){this.__data__=[],this.size=0}function fr(n){var t=this.__data__,r=Wr(t,n);return!(r<0)&&(r==t.length-1?t.pop():Ll.call(t,r,1),--this.size,!0)}function cr(n){var t=this.__data__,r=Wr(t,n);return r<0?X:t[r][1]}function ar(n){return Wr(this.__data__,n)>-1}function lr(n,t){var r=this.__data__,e=Wr(r,n);return e<0?(++this.size,r.push([n,t])):r[e][1]=t,this}function sr(n){var t=-1,r=null==n?0:n.length;for(this.clear();++t<r;){
var e=n[t];this.set(e[0],e[1])}}function hr(){this.size=0,this.__data__={hash:new Xt,map:new(ts||ir),string:new Xt}}function pr(n){var t=xi(this,n).delete(n);return this.size-=t?1:0,t}function _r(n){return xi(this,n).get(n)}function vr(n){return xi(this,n).has(n)}function gr(n,t){var r=xi(this,n),e=r.size;return r.set(n,t),this.size+=r.size==e?0:1,this}function yr(n){var t=-1,r=null==n?0:n.length;for(this.__data__=new sr;++t<r;)this.add(n[t])}function dr(n){return this.__data__.set(n,on),this}function br(n){
return this.__data__.has(n)}function wr(n){this.size=(this.__data__=new ir(n)).size}function mr(){this.__data__=new ir,this.size=0}function xr(n){var t=this.__data__,r=t.delete(n);return this.size=t.size,r}function jr(n){return this.__data__.get(n)}function Ar(n){return this.__data__.has(n)}function kr(n,t){var r=this.__data__;if(r instanceof ir){var e=r.__data__;if(!ts||e.length<tn-1)return e.push([n,t]),this.size=++r.size,this;r=this.__data__=new sr(e)}return r.set(n,t),this.size=r.size,this}function Or(n,t){
var r=bh(n),e=!r&&dh(n),u=!r&&!e&&mh(n),i=!r&&!e&&!u&&Oh(n),o=r||e||u||i,f=o?O(n.length,hl):[],c=f.length;for(var a in n)!t&&!bl.call(n,a)||o&&("length"==a||u&&("offset"==a||"parent"==a)||i&&("buffer"==a||"byteLength"==a||"byteOffset"==a)||Ci(a,c))||f.push(a);return f}function Ir(n){var t=n.length;return t?n[tu(0,t-1)]:X}function Rr(n,t){return Xi(Tu(n),Mr(t,0,n.length))}function zr(n){return Xi(Tu(n))}function Er(n,t,r){(r===X||Gf(n[t],r))&&(r!==X||t in n)||Br(n,t,r)}function Sr(n,t,r){var e=n[t];
bl.call(n,t)&&Gf(e,r)&&(r!==X||t in n)||Br(n,t,r)}function Wr(n,t){for(var r=n.length;r--;)if(Gf(n[r][0],t))return r;return-1}function Lr(n,t,r,e){return ys(n,function(n,u,i){t(e,n,r(n),i)}),e}function Cr(n,t){return n&&$u(t,Pc(t),n)}function Ur(n,t){return n&&$u(t,qc(t),n)}function Br(n,t,r){"__proto__"==t&&Tl?Tl(n,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):n[t]=r}function Tr(n,t){for(var r=-1,e=t.length,u=il(e),i=null==n;++r<e;)u[r]=i?X:Mc(n,t[r]);return u}function Mr(n,t,r){return n===n&&(r!==X&&(n=n<=r?n:r),
t!==X&&(n=n>=t?n:t)),n}function Fr(n,t,e,u,i,o){var f,c=t&an,a=t&ln,l=t&sn;if(e&&(f=i?e(n,u,i,o):e(n)),f!==X)return f;if(!fc(n))return n;var s=bh(n);if(s){if(f=zi(n),!c)return Tu(n,f)}else{var h=zs(n),p=h==Kn||h==Vn;if(mh(n))return Iu(n,c);if(h==Yn||h==Dn||p&&!i){if(f=a||p?{}:Ei(n),!c)return a?Mu(n,Ur(f,n)):Du(n,Cr(f,n))}else{if(!Vr[h])return i?n:{};f=Si(n,h,c)}}o||(o=new wr);var _=o.get(n);if(_)return _;o.set(n,f),kh(n)?n.forEach(function(r){f.add(Fr(r,t,e,r,n,o))}):jh(n)&&n.forEach(function(r,u){
f.set(u,Fr(r,t,e,u,n,o))});var v=l?a?di:yi:a?qc:Pc,g=s?X:v(n);return r(g||n,function(r,u){g&&(u=r,r=n[u]),Sr(f,u,Fr(r,t,e,u,n,o))}),f}function Nr(n){var t=Pc(n);return function(r){return Pr(r,n,t)}}function Pr(n,t,r){var e=r.length;if(null==n)return!e;for(n=ll(n);e--;){var u=r[e],i=t[u],o=n[u];if(o===X&&!(u in n)||!i(o))return!1}return!0}function Gr(n,t,r){if("function"!=typeof n)throw new pl(en);return Ws(function(){n.apply(X,r)},t)}function Hr(n,t,r,e){var u=-1,i=o,a=!0,l=n.length,s=[],h=t.length;
if(!l)return s;r&&(t=c(t,z(r))),e?(i=f,a=!1):t.length>=tn&&(i=S,a=!1,t=new yr(t));n:for(;++u<l;){var p=n[u],_=null==r?p:r(p);if(p=e||0!==p?p:0,a&&_===_){for(var v=h;v--;)if(t[v]===_)continue n;s.push(p)}else i(t,_,e)||s.push(p)}return s}function Jr(n,t){var r=!0;return ys(n,function(n,e,u){return r=!!t(n,e,u)}),r}function Yr(n,t,r){for(var e=-1,u=n.length;++e<u;){var i=n[e],o=t(i);if(null!=o&&(f===X?o===o&&!bc(o):r(o,f)))var f=o,c=i}return c}function ne(n,t,r,e){var u=n.length;for(r=kc(r),r<0&&(r=-r>u?0:u+r),
e=e===X||e>u?u:kc(e),e<0&&(e+=u),e=r>e?0:Oc(e);r<e;)n[r++]=t;return n}function te(n,t){var r=[];return ys(n,function(n,e,u){t(n,e,u)&&r.push(n)}),r}function ee(n,t,r,e,u){var i=-1,o=n.length;for(r||(r=Li),u||(u=[]);++i<o;){var f=n[i];t>0&&r(f)?t>1?ee(f,t-1,r,e,u):a(u,f):e||(u[u.length]=f)}return u}function ue(n,t){return n&&bs(n,t,Pc)}function oe(n,t){return n&&ws(n,t,Pc)}function fe(n,t){return i(t,function(t){return uc(n[t])})}function _e(n,t){t=ku(t,n);for(var r=0,e=t.length;null!=n&&r<e;)n=n[no(t[r++])];
return r&&r==e?n:X}function de(n,t,r){var e=t(n);return bh(n)?e:a(e,r(n))}function we(n){return null==n?n===X?ut:Jn:Bl&&Bl in ll(n)?ki(n):Ki(n)}function me(n,t){return n>t}function xe(n,t){return null!=n&&bl.call(n,t)}function je(n,t){return null!=n&&t in ll(n)}function Ae(n,t,r){return n>=Hl(t,r)&&n<Gl(t,r)}function ke(n,t,r){for(var e=r?f:o,u=n[0].length,i=n.length,a=i,l=il(i),s=1/0,h=[];a--;){var p=n[a];a&&t&&(p=c(p,z(t))),s=Hl(p.length,s),l[a]=!r&&(t||u>=120&&p.length>=120)?new yr(a&&p):X}p=n[0];
var _=-1,v=l[0];n:for(;++_<u&&h.length<s;){var g=p[_],y=t?t(g):g;if(g=r||0!==g?g:0,!(v?S(v,y):e(h,y,r))){for(a=i;--a;){var d=l[a];if(!(d?S(d,y):e(n[a],y,r)))continue n}v&&v.push(y),h.push(g)}}return h}function Oe(n,t,r,e){return ue(n,function(n,u,i){t(e,r(n),u,i)}),e}function Ie(t,r,e){r=ku(r,t),t=Gi(t,r);var u=null==t?t:t[no(jo(r))];return null==u?X:n(u,t,e)}function Re(n){return cc(n)&&we(n)==Dn}function ze(n){return cc(n)&&we(n)==ft}function Ee(n){return cc(n)&&we(n)==Pn}function Se(n,t,r,e,u){
return n===t||(null==n||null==t||!cc(n)&&!cc(t)?n!==n&&t!==t:We(n,t,r,e,Se,u))}function We(n,t,r,e,u,i){var o=bh(n),f=bh(t),c=o?Mn:zs(n),a=f?Mn:zs(t);c=c==Dn?Yn:c,a=a==Dn?Yn:a;var l=c==Yn,s=a==Yn,h=c==a;if(h&&mh(n)){if(!mh(t))return!1;o=!0,l=!1}if(h&&!l)return i||(i=new wr),o||Oh(n)?pi(n,t,r,e,u,i):_i(n,t,c,r,e,u,i);if(!(r&hn)){var p=l&&bl.call(n,"__wrapped__"),_=s&&bl.call(t,"__wrapped__");if(p||_){var v=p?n.value():n,g=_?t.value():t;return i||(i=new wr),u(v,g,r,e,i)}}return!!h&&(i||(i=new wr),vi(n,t,r,e,u,i));
}function Le(n){return cc(n)&&zs(n)==Gn}function Ce(n,t,r,e){var u=r.length,i=u,o=!e;if(null==n)return!i;for(n=ll(n);u--;){var f=r[u];if(o&&f[2]?f[1]!==n[f[0]]:!(f[0]in n))return!1}for(;++u<i;){f=r[u];var c=f[0],a=n[c],l=f[1];if(o&&f[2]){if(a===X&&!(c in n))return!1}else{var s=new wr;if(e)var h=e(a,l,c,n,t,s);if(!(h===X?Se(l,a,hn|pn,e,s):h))return!1}}return!0}function Ue(n){return!(!fc(n)||Di(n))&&(uc(n)?kl:Zt).test(to(n))}function Be(n){return cc(n)&&we(n)==nt}function Te(n){return cc(n)&&zs(n)==tt;
}function $e(n){return cc(n)&&oc(n.length)&&!!Kr[we(n)]}function De(n){return"function"==typeof n?n:null==n?La:"object"==typeof n?bh(n)?Ze(n[0],n[1]):qe(n):Fa(n)}function Me(n){if(!Mi(n))return Vl(n);var t=[];for(var r in ll(n))bl.call(n,r)&&"constructor"!=r&&t.push(r);return t}function Fe(n){if(!fc(n))return Zi(n);var t=Mi(n),r=[];for(var e in n)("constructor"!=e||!t&&bl.call(n,e))&&r.push(e);return r}function Ne(n,t){return n<t}function Pe(n,t){var r=-1,e=Hf(n)?il(n.length):[];return ys(n,function(n,u,i){
e[++r]=t(n,u,i)}),e}function qe(n){var t=ji(n);return 1==t.length&&t[0][2]?Ni(t[0][0],t[0][1]):function(r){return r===n||Ce(r,n,t)}}function Ze(n,t){return Bi(n)&&Fi(t)?Ni(no(n),t):function(r){var e=Mc(r,n);return e===X&&e===t?Nc(r,n):Se(t,e,hn|pn)}}function Ke(n,t,r,e,u){n!==t&&bs(t,function(i,o){if(u||(u=new wr),fc(i))Ve(n,t,o,r,Ke,e,u);else{var f=e?e(Ji(n,o),i,o+"",n,t,u):X;f===X&&(f=i),Er(n,o,f)}},qc)}function Ve(n,t,r,e,u,i,o){var f=Ji(n,r),c=Ji(t,r),a=o.get(c);if(a)return Er(n,r,a),X;var l=i?i(f,c,r+"",n,t,o):X,s=l===X;
if(s){var h=bh(c),p=!h&&mh(c),_=!h&&!p&&Oh(c);l=c,h||p||_?bh(f)?l=f:Jf(f)?l=Tu(f):p?(s=!1,l=Iu(c,!0)):_?(s=!1,l=Wu(c,!0)):l=[]:gc(c)||dh(c)?(l=f,dh(f)?l=Rc(f):fc(f)&&!uc(f)||(l=Ei(c))):s=!1}s&&(o.set(c,l),u(l,c,e,i,o),o.delete(c)),Er(n,r,l)}function Ge(n,t){var r=n.length;if(r)return t+=t<0?r:0,Ci(t,r)?n[t]:X}function He(n,t,r){t=t.length?c(t,function(n){return bh(n)?function(t){return _e(t,1===n.length?n[0]:n)}:n}):[La];var e=-1;return t=c(t,z(mi())),A(Pe(n,function(n,r,u){return{criteria:c(t,function(t){
return t(n)}),index:++e,value:n}}),function(n,t){return Cu(n,t,r)})}function Je(n,t){return Ye(n,t,function(t,r){return Nc(n,r)})}function Ye(n,t,r){for(var e=-1,u=t.length,i={};++e<u;){var o=t[e],f=_e(n,o);r(f,o)&&fu(i,ku(o,n),f)}return i}function Qe(n){return function(t){return _e(t,n)}}function Xe(n,t,r,e){var u=e?d:y,i=-1,o=t.length,f=n;for(n===t&&(t=Tu(t)),r&&(f=c(n,z(r)));++i<o;)for(var a=0,l=t[i],s=r?r(l):l;(a=u(f,s,a,e))>-1;)f!==n&&Ll.call(f,a,1),Ll.call(n,a,1);return n}function nu(n,t){for(var r=n?t.length:0,e=r-1;r--;){
var u=t[r];if(r==e||u!==i){var i=u;Ci(u)?Ll.call(n,u,1):yu(n,u)}}return n}function tu(n,t){return n+Nl(Ql()*(t-n+1))}function ru(n,t,r,e){for(var u=-1,i=Gl(Fl((t-n)/(r||1)),0),o=il(i);i--;)o[e?i:++u]=n,n+=r;return o}function eu(n,t){var r="";if(!n||t<1||t>Wn)return r;do t%2&&(r+=n),t=Nl(t/2),t&&(n+=n);while(t);return r}function uu(n,t){return Ls(Vi(n,t,La),n+"")}function iu(n){return Ir(ra(n))}function ou(n,t){var r=ra(n);return Xi(r,Mr(t,0,r.length))}function fu(n,t,r,e){if(!fc(n))return n;t=ku(t,n);
for(var u=-1,i=t.length,o=i-1,f=n;null!=f&&++u<i;){var c=no(t[u]),a=r;if("__proto__"===c||"constructor"===c||"prototype"===c)return n;if(u!=o){var l=f[c];a=e?e(l,c,f):X,a===X&&(a=fc(l)?l:Ci(t[u+1])?[]:{})}Sr(f,c,a),f=f[c]}return n}function cu(n){return Xi(ra(n))}function au(n,t,r){var e=-1,u=n.length;t<0&&(t=-t>u?0:u+t),r=r>u?u:r,r<0&&(r+=u),u=t>r?0:r-t>>>0,t>>>=0;for(var i=il(u);++e<u;)i[e]=n[e+t];return i}function lu(n,t){var r;return ys(n,function(n,e,u){return r=t(n,e,u),!r}),!!r}function su(n,t,r){
var e=0,u=null==n?e:n.length;if("number"==typeof t&&t===t&&u<=Tn){for(;e<u;){var i=e+u>>>1,o=n[i];null!==o&&!bc(o)&&(r?o<=t:o<t)?e=i+1:u=i}return u}return hu(n,t,La,r)}function hu(n,t,r,e){var u=0,i=null==n?0:n.length;if(0===i)return 0;t=r(t);for(var o=t!==t,f=null===t,c=bc(t),a=t===X;u<i;){var l=Nl((u+i)/2),s=r(n[l]),h=s!==X,p=null===s,_=s===s,v=bc(s);if(o)var g=e||_;else g=a?_&&(e||h):f?_&&h&&(e||!p):c?_&&h&&!p&&(e||!v):!p&&!v&&(e?s<=t:s<t);g?u=l+1:i=l}return Hl(i,Bn)}function pu(n,t){for(var r=-1,e=n.length,u=0,i=[];++r<e;){
var o=n[r],f=t?t(o):o;if(!r||!Gf(f,c)){var c=f;i[u++]=0===o?0:o}}return i}function _u(n){return"number"==typeof n?n:bc(n)?Cn:+n}function vu(n){if("string"==typeof n)return n;if(bh(n))return c(n,vu)+"";if(bc(n))return vs?vs.call(n):"";var t=n+"";return"0"==t&&1/n==-Sn?"-0":t}function gu(n,t,r){var e=-1,u=o,i=n.length,c=!0,a=[],l=a;if(r)c=!1,u=f;else if(i>=tn){var s=t?null:ks(n);if(s)return P(s);c=!1,u=S,l=new yr}else l=t?[]:a;n:for(;++e<i;){var h=n[e],p=t?t(h):h;if(h=r||0!==h?h:0,c&&p===p){for(var _=l.length;_--;)if(l[_]===p)continue n;
t&&l.push(p),a.push(h)}else u(l,p,r)||(l!==a&&l.push(p),a.push(h))}return a}function yu(n,t){return t=ku(t,n),n=Gi(n,t),null==n||delete n[no(jo(t))]}function du(n,t,r,e){return fu(n,t,r(_e(n,t)),e)}function bu(n,t,r,e){for(var u=n.length,i=e?u:-1;(e?i--:++i<u)&&t(n[i],i,n););return r?au(n,e?0:i,e?i+1:u):au(n,e?i+1:0,e?u:i)}function wu(n,t){var r=n;return r instanceof Ct&&(r=r.value()),l(t,function(n,t){return t.func.apply(t.thisArg,a([n],t.args))},r)}function mu(n,t,r){var e=n.length;if(e<2)return e?gu(n[0]):[];
for(var u=-1,i=il(e);++u<e;)for(var o=n[u],f=-1;++f<e;)f!=u&&(i[u]=Hr(i[u]||o,n[f],t,r));return gu(ee(i,1),t,r)}function xu(n,t,r){for(var e=-1,u=n.length,i=t.length,o={};++e<u;){r(o,n[e],e<i?t[e]:X)}return o}function ju(n){return Jf(n)?n:[]}function Au(n){return"function"==typeof n?n:La}function ku(n,t){return bh(n)?n:Bi(n,t)?[n]:Cs(Ec(n))}function Ou(n,t,r){var e=n.length;return r=r===X?e:r,!t&&r>=e?n:au(n,t,r)}function Iu(n,t){if(t)return n.slice();var r=n.length,e=zl?zl(r):new n.constructor(r);
return n.copy(e),e}function Ru(n){var t=new n.constructor(n.byteLength);return new Rl(t).set(new Rl(n)),t}function zu(n,t){return new n.constructor(t?Ru(n.buffer):n.buffer,n.byteOffset,n.byteLength)}function Eu(n){var t=new n.constructor(n.source,Nt.exec(n));return t.lastIndex=n.lastIndex,t}function Su(n){return _s?ll(_s.call(n)):{}}function Wu(n,t){return new n.constructor(t?Ru(n.buffer):n.buffer,n.byteOffset,n.length)}function Lu(n,t){if(n!==t){var r=n!==X,e=null===n,u=n===n,i=bc(n),o=t!==X,f=null===t,c=t===t,a=bc(t);
if(!f&&!a&&!i&&n>t||i&&o&&c&&!f&&!a||e&&o&&c||!r&&c||!u)return 1;if(!e&&!i&&!a&&n<t||a&&r&&u&&!e&&!i||f&&r&&u||!o&&u||!c)return-1}return 0}function Cu(n,t,r){for(var e=-1,u=n.criteria,i=t.criteria,o=u.length,f=r.length;++e<o;){var c=Lu(u[e],i[e]);if(c){if(e>=f)return c;return c*("desc"==r[e]?-1:1)}}return n.index-t.index}function Uu(n,t,r,e){for(var u=-1,i=n.length,o=r.length,f=-1,c=t.length,a=Gl(i-o,0),l=il(c+a),s=!e;++f<c;)l[f]=t[f];for(;++u<o;)(s||u<i)&&(l[r[u]]=n[u]);for(;a--;)l[f++]=n[u++];return l;
}function Bu(n,t,r,e){for(var u=-1,i=n.length,o=-1,f=r.length,c=-1,a=t.length,l=Gl(i-f,0),s=il(l+a),h=!e;++u<l;)s[u]=n[u];for(var p=u;++c<a;)s[p+c]=t[c];for(;++o<f;)(h||u<i)&&(s[p+r[o]]=n[u++]);return s}function Tu(n,t){var r=-1,e=n.length;for(t||(t=il(e));++r<e;)t[r]=n[r];return t}function $u(n,t,r,e){var u=!r;r||(r={});for(var i=-1,o=t.length;++i<o;){var f=t[i],c=e?e(r[f],n[f],f,r,n):X;c===X&&(c=n[f]),u?Br(r,f,c):Sr(r,f,c)}return r}function Du(n,t){return $u(n,Is(n),t)}function Mu(n,t){return $u(n,Rs(n),t);
}function Fu(n,r){return function(e,u){var i=bh(e)?t:Lr,o=r?r():{};return i(e,n,mi(u,2),o)}}function Nu(n){return uu(function(t,r){var e=-1,u=r.length,i=u>1?r[u-1]:X,o=u>2?r[2]:X;for(i=n.length>3&&"function"==typeof i?(u--,i):X,o&&Ui(r[0],r[1],o)&&(i=u<3?X:i,u=1),t=ll(t);++e<u;){var f=r[e];f&&n(t,f,e,i)}return t})}function Pu(n,t){return function(r,e){if(null==r)return r;if(!Hf(r))return n(r,e);for(var u=r.length,i=t?u:-1,o=ll(r);(t?i--:++i<u)&&e(o[i],i,o)!==!1;);return r}}function qu(n){return function(t,r,e){
for(var u=-1,i=ll(t),o=e(t),f=o.length;f--;){var c=o[n?f:++u];if(r(i[c],c,i)===!1)break}return t}}function Zu(n,t,r){function e(){return(this&&this!==re&&this instanceof e?i:n).apply(u?r:this,arguments)}var u=t&_n,i=Gu(n);return e}function Ku(n){return function(t){t=Ec(t);var r=T(t)?G(t):X,e=r?r[0]:t.charAt(0),u=r?Ou(r,1).join(""):t.slice(1);return e[n]()+u}}function Vu(n){return function(t){return l(Ra(ca(t).replace($r,"")),n,"")}}function Gu(n){return function(){var t=arguments;switch(t.length){
case 0:return new n;case 1:return new n(t[0]);case 2:return new n(t[0],t[1]);case 3:return new n(t[0],t[1],t[2]);case 4:return new n(t[0],t[1],t[2],t[3]);case 5:return new n(t[0],t[1],t[2],t[3],t[4]);case 6:return new n(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new n(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var r=gs(n.prototype),e=n.apply(r,t);return fc(e)?e:r}}function Hu(t,r,e){function u(){for(var o=arguments.length,f=il(o),c=o,a=wi(u);c--;)f[c]=arguments[c];var l=o<3&&f[0]!==a&&f[o-1]!==a?[]:N(f,a);
return o-=l.length,o<e?oi(t,r,Qu,u.placeholder,X,f,l,X,X,e-o):n(this&&this!==re&&this instanceof u?i:t,this,f)}var i=Gu(t);return u}function Ju(n){return function(t,r,e){var u=ll(t);if(!Hf(t)){var i=mi(r,3);t=Pc(t),r=function(n){return i(u[n],n,u)}}var o=n(t,r,e);return o>-1?u[i?t[o]:o]:X}}function Yu(n){return gi(function(t){var r=t.length,e=r,u=Y.prototype.thru;for(n&&t.reverse();e--;){var i=t[e];if("function"!=typeof i)throw new pl(en);if(u&&!o&&"wrapper"==bi(i))var o=new Y([],!0)}for(e=o?e:r;++e<r;){
i=t[e];var f=bi(i),c="wrapper"==f?Os(i):X;o=c&&$i(c[0])&&c[1]==(mn|yn|bn|xn)&&!c[4].length&&1==c[9]?o[bi(c[0])].apply(o,c[3]):1==i.length&&$i(i)?o[f]():o.thru(i)}return function(){var n=arguments,e=n[0];if(o&&1==n.length&&bh(e))return o.plant(e).value();for(var u=0,i=r?t[u].apply(this,n):e;++u<r;)i=t[u].call(this,i);return i}})}function Qu(n,t,r,e,u,i,o,f,c,a){function l(){for(var y=arguments.length,d=il(y),b=y;b--;)d[b]=arguments[b];if(_)var w=wi(l),m=C(d,w);if(e&&(d=Uu(d,e,u,_)),i&&(d=Bu(d,i,o,_)),
y-=m,_&&y<a){return oi(n,t,Qu,l.placeholder,r,d,N(d,w),f,c,a-y)}var x=h?r:this,j=p?x[n]:n;return y=d.length,f?d=Hi(d,f):v&&y>1&&d.reverse(),s&&c<y&&(d.length=c),this&&this!==re&&this instanceof l&&(j=g||Gu(j)),j.apply(x,d)}var s=t&mn,h=t&_n,p=t&vn,_=t&(yn|dn),v=t&jn,g=p?X:Gu(n);return l}function Xu(n,t){return function(r,e){return Oe(r,n,t(e),{})}}function ni(n,t){return function(r,e){var u;if(r===X&&e===X)return t;if(r!==X&&(u=r),e!==X){if(u===X)return e;"string"==typeof r||"string"==typeof e?(r=vu(r),
e=vu(e)):(r=_u(r),e=_u(e)),u=n(r,e)}return u}}function ti(t){return gi(function(r){return r=c(r,z(mi())),uu(function(e){var u=this;return t(r,function(t){return n(t,u,e)})})})}function ri(n,t){t=t===X?" ":vu(t);var r=t.length;if(r<2)return r?eu(t,n):t;var e=eu(t,Fl(n/V(t)));return T(t)?Ou(G(e),0,n).join(""):e.slice(0,n)}function ei(t,r,e,u){function i(){for(var r=-1,c=arguments.length,a=-1,l=u.length,s=il(l+c),h=this&&this!==re&&this instanceof i?f:t;++a<l;)s[a]=u[a];for(;c--;)s[a++]=arguments[++r];
return n(h,o?e:this,s)}var o=r&_n,f=Gu(t);return i}function ui(n){return function(t,r,e){return e&&"number"!=typeof e&&Ui(t,r,e)&&(r=e=X),t=Ac(t),r===X?(r=t,t=0):r=Ac(r),e=e===X?t<r?1:-1:Ac(e),ru(t,r,e,n)}}function ii(n){return function(t,r){return"string"==typeof t&&"string"==typeof r||(t=Ic(t),r=Ic(r)),n(t,r)}}function oi(n,t,r,e,u,i,o,f,c,a){var l=t&yn,s=l?o:X,h=l?X:o,p=l?i:X,_=l?X:i;t|=l?bn:wn,t&=~(l?wn:bn),t&gn||(t&=~(_n|vn));var v=[n,t,u,p,s,_,h,f,c,a],g=r.apply(X,v);return $i(n)&&Ss(g,v),g.placeholder=e,
Yi(g,n,t)}function fi(n){var t=al[n];return function(n,r){if(n=Ic(n),r=null==r?0:Hl(kc(r),292),r&&Zl(n)){var e=(Ec(n)+"e").split("e");return e=(Ec(t(e[0]+"e"+(+e[1]+r)))+"e").split("e"),+(e[0]+"e"+(+e[1]-r))}return t(n)}}function ci(n){return function(t){var r=zs(t);return r==Gn?M(t):r==tt?q(t):I(t,n(t))}}function ai(n,t,r,e,u,i,o,f){var c=t&vn;if(!c&&"function"!=typeof n)throw new pl(en);var a=e?e.length:0;if(a||(t&=~(bn|wn),e=u=X),o=o===X?o:Gl(kc(o),0),f=f===X?f:kc(f),a-=u?u.length:0,t&wn){var l=e,s=u;
e=u=X}var h=c?X:Os(n),p=[n,t,r,e,u,l,s,i,o,f];if(h&&qi(p,h),n=p[0],t=p[1],r=p[2],e=p[3],u=p[4],f=p[9]=p[9]===X?c?0:n.length:Gl(p[9]-a,0),!f&&t&(yn|dn)&&(t&=~(yn|dn)),t&&t!=_n)_=t==yn||t==dn?Hu(n,t,f):t!=bn&&t!=(_n|bn)||u.length?Qu.apply(X,p):ei(n,t,r,e);else var _=Zu(n,t,r);return Yi((h?ms:Ss)(_,p),n,t)}function li(n,t,r,e){return n===X||Gf(n,gl[r])&&!bl.call(e,r)?t:n}function si(n,t,r,e,u,i){return fc(n)&&fc(t)&&(i.set(t,n),Ke(n,t,X,si,i),i.delete(t)),n}function hi(n){return gc(n)?X:n}function pi(n,t,r,e,u,i){
var o=r&hn,f=n.length,c=t.length;if(f!=c&&!(o&&c>f))return!1;var a=i.get(n),l=i.get(t);if(a&&l)return a==t&&l==n;var s=-1,p=!0,_=r&pn?new yr:X;for(i.set(n,t),i.set(t,n);++s<f;){var v=n[s],g=t[s];if(e)var y=o?e(g,v,s,t,n,i):e(v,g,s,n,t,i);if(y!==X){if(y)continue;p=!1;break}if(_){if(!h(t,function(n,t){if(!S(_,t)&&(v===n||u(v,n,r,e,i)))return _.push(t)})){p=!1;break}}else if(v!==g&&!u(v,g,r,e,i)){p=!1;break}}return i.delete(n),i.delete(t),p}function _i(n,t,r,e,u,i,o){switch(r){case ct:if(n.byteLength!=t.byteLength||n.byteOffset!=t.byteOffset)return!1;
n=n.buffer,t=t.buffer;case ft:return!(n.byteLength!=t.byteLength||!i(new Rl(n),new Rl(t)));case Nn:case Pn:case Hn:return Gf(+n,+t);case Zn:return n.name==t.name&&n.message==t.message;case nt:case rt:return n==t+"";case Gn:var f=M;case tt:var c=e&hn;if(f||(f=P),n.size!=t.size&&!c)return!1;var a=o.get(n);if(a)return a==t;e|=pn,o.set(n,t);var l=pi(f(n),f(t),e,u,i,o);return o.delete(n),l;case et:if(_s)return _s.call(n)==_s.call(t)}return!1}function vi(n,t,r,e,u,i){var o=r&hn,f=yi(n),c=f.length;if(c!=yi(t).length&&!o)return!1;
for(var a=c;a--;){var l=f[a];if(!(o?l in t:bl.call(t,l)))return!1}var s=i.get(n),h=i.get(t);if(s&&h)return s==t&&h==n;var p=!0;i.set(n,t),i.set(t,n);for(var _=o;++a<c;){l=f[a];var v=n[l],g=t[l];if(e)var y=o?e(g,v,l,t,n,i):e(v,g,l,n,t,i);if(!(y===X?v===g||u(v,g,r,e,i):y)){p=!1;break}_||(_="constructor"==l)}if(p&&!_){var d=n.constructor,b=t.constructor;d!=b&&"constructor"in n&&"constructor"in t&&!("function"==typeof d&&d instanceof d&&"function"==typeof b&&b instanceof b)&&(p=!1)}return i.delete(n),
i.delete(t),p}function gi(n){return Ls(Vi(n,X,_o),n+"")}function yi(n){return de(n,Pc,Is)}function di(n){return de(n,qc,Rs)}function bi(n){for(var t=n.name+"",r=fs[t],e=bl.call(fs,t)?r.length:0;e--;){var u=r[e],i=u.func;if(null==i||i==n)return u.name}return t}function wi(n){return(bl.call(Z,"placeholder")?Z:n).placeholder}function mi(){var n=Z.iteratee||Ca;return n=n===Ca?De:n,arguments.length?n(arguments[0],arguments[1]):n}function xi(n,t){var r=n.__data__;return Ti(t)?r["string"==typeof t?"string":"hash"]:r.map;
}function ji(n){for(var t=Pc(n),r=t.length;r--;){var e=t[r],u=n[e];t[r]=[e,u,Fi(u)]}return t}function Ai(n,t){var r=B(n,t);return Ue(r)?r:X}function ki(n){var t=bl.call(n,Bl),r=n[Bl];try{n[Bl]=X;var e=!0}catch(n){}var u=xl.call(n);return e&&(t?n[Bl]=r:delete n[Bl]),u}function Oi(n,t,r){for(var e=-1,u=r.length;++e<u;){var i=r[e],o=i.size;switch(i.type){case"drop":n+=o;break;case"dropRight":t-=o;break;case"take":t=Hl(t,n+o);break;case"takeRight":n=Gl(n,t-o)}}return{start:n,end:t}}function Ii(n){var t=n.match(Bt);
return t?t[1].split(Tt):[]}function Ri(n,t,r){t=ku(t,n);for(var e=-1,u=t.length,i=!1;++e<u;){var o=no(t[e]);if(!(i=null!=n&&r(n,o)))break;n=n[o]}return i||++e!=u?i:(u=null==n?0:n.length,!!u&&oc(u)&&Ci(o,u)&&(bh(n)||dh(n)))}function zi(n){var t=n.length,r=new n.constructor(t);return t&&"string"==typeof n[0]&&bl.call(n,"index")&&(r.index=n.index,r.input=n.input),r}function Ei(n){return"function"!=typeof n.constructor||Mi(n)?{}:gs(El(n))}function Si(n,t,r){var e=n.constructor;switch(t){case ft:return Ru(n);
case Nn:case Pn:return new e(+n);case ct:return zu(n,r);case at:case lt:case st:case ht:case pt:case _t:case vt:case gt:case yt:return Wu(n,r);case Gn:return new e;case Hn:case rt:return new e(n);case nt:return Eu(n);case tt:return new e;case et:return Su(n)}}function Wi(n,t){var r=t.length;if(!r)return n;var e=r-1;return t[e]=(r>1?"& ":"")+t[e],t=t.join(r>2?", ":" "),n.replace(Ut,"{\n/* [wrapped with "+t+"] */\n")}function Li(n){return bh(n)||dh(n)||!!(Cl&&n&&n[Cl])}function Ci(n,t){var r=typeof n;
return t=null==t?Wn:t,!!t&&("number"==r||"symbol"!=r&&Vt.test(n))&&n>-1&&n%1==0&&n<t}function Ui(n,t,r){if(!fc(r))return!1;var e=typeof t;return!!("number"==e?Hf(r)&&Ci(t,r.length):"string"==e&&t in r)&&Gf(r[t],n)}function Bi(n,t){if(bh(n))return!1;var r=typeof n;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=n&&!bc(n))||(zt.test(n)||!Rt.test(n)||null!=t&&n in ll(t))}function Ti(n){var t=typeof n;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==n:null===n}function $i(n){
var t=bi(n),r=Z[t];if("function"!=typeof r||!(t in Ct.prototype))return!1;if(n===r)return!0;var e=Os(r);return!!e&&n===e[0]}function Di(n){return!!ml&&ml in n}function Mi(n){var t=n&&n.constructor;return n===("function"==typeof t&&t.prototype||gl)}function Fi(n){return n===n&&!fc(n)}function Ni(n,t){return function(r){return null!=r&&(r[n]===t&&(t!==X||n in ll(r)))}}function Pi(n){var t=Cf(n,function(n){return r.size===fn&&r.clear(),n}),r=t.cache;return t}function qi(n,t){var r=n[1],e=t[1],u=r|e,i=u<(_n|vn|mn),o=e==mn&&r==yn||e==mn&&r==xn&&n[7].length<=t[8]||e==(mn|xn)&&t[7].length<=t[8]&&r==yn;
if(!i&&!o)return n;e&_n&&(n[2]=t[2],u|=r&_n?0:gn);var f=t[3];if(f){var c=n[3];n[3]=c?Uu(c,f,t[4]):f,n[4]=c?N(n[3],cn):t[4]}return f=t[5],f&&(c=n[5],n[5]=c?Bu(c,f,t[6]):f,n[6]=c?N(n[5],cn):t[6]),f=t[7],f&&(n[7]=f),e&mn&&(n[8]=null==n[8]?t[8]:Hl(n[8],t[8])),null==n[9]&&(n[9]=t[9]),n[0]=t[0],n[1]=u,n}function Zi(n){var t=[];if(null!=n)for(var r in ll(n))t.push(r);return t}function Ki(n){return xl.call(n)}function Vi(t,r,e){return r=Gl(r===X?t.length-1:r,0),function(){for(var u=arguments,i=-1,o=Gl(u.length-r,0),f=il(o);++i<o;)f[i]=u[r+i];
i=-1;for(var c=il(r+1);++i<r;)c[i]=u[i];return c[r]=e(f),n(t,this,c)}}function Gi(n,t){return t.length<2?n:_e(n,au(t,0,-1))}function Hi(n,t){for(var r=n.length,e=Hl(t.length,r),u=Tu(n);e--;){var i=t[e];n[e]=Ci(i,r)?u[i]:X}return n}function Ji(n,t){if(("constructor"!==t||"function"!=typeof n[t])&&"__proto__"!=t)return n[t]}function Yi(n,t,r){var e=t+"";return Ls(n,Wi(e,ro(Ii(e),r)))}function Qi(n){var t=0,r=0;return function(){var e=Jl(),u=In-(e-r);if(r=e,u>0){if(++t>=On)return arguments[0]}else t=0;
return n.apply(X,arguments)}}function Xi(n,t){var r=-1,e=n.length,u=e-1;for(t=t===X?e:t;++r<t;){var i=tu(r,u),o=n[i];n[i]=n[r],n[r]=o}return n.length=t,n}function no(n){if("string"==typeof n||bc(n))return n;var t=n+"";return"0"==t&&1/n==-Sn?"-0":t}function to(n){if(null!=n){try{return dl.call(n)}catch(n){}try{return n+""}catch(n){}}return""}function ro(n,t){return r($n,function(r){var e="_."+r[0];t&r[1]&&!o(n,e)&&n.push(e)}),n.sort()}function eo(n){if(n instanceof Ct)return n.clone();var t=new Y(n.__wrapped__,n.__chain__);
return t.__actions__=Tu(n.__actions__),t.__index__=n.__index__,t.__values__=n.__values__,t}function uo(n,t,r){t=(r?Ui(n,t,r):t===X)?1:Gl(kc(t),0);var e=null==n?0:n.length;if(!e||t<1)return[];for(var u=0,i=0,o=il(Fl(e/t));u<e;)o[i++]=au(n,u,u+=t);return o}function io(n){for(var t=-1,r=null==n?0:n.length,e=0,u=[];++t<r;){var i=n[t];i&&(u[e++]=i)}return u}function oo(){var n=arguments.length;if(!n)return[];for(var t=il(n-1),r=arguments[0],e=n;e--;)t[e-1]=arguments[e];return a(bh(r)?Tu(r):[r],ee(t,1));
}function fo(n,t,r){var e=null==n?0:n.length;return e?(t=r||t===X?1:kc(t),au(n,t<0?0:t,e)):[]}function co(n,t,r){var e=null==n?0:n.length;return e?(t=r||t===X?1:kc(t),t=e-t,au(n,0,t<0?0:t)):[]}function ao(n,t){return n&&n.length?bu(n,mi(t,3),!0,!0):[]}function lo(n,t){return n&&n.length?bu(n,mi(t,3),!0):[]}function so(n,t,r,e){var u=null==n?0:n.length;return u?(r&&"number"!=typeof r&&Ui(n,t,r)&&(r=0,e=u),ne(n,t,r,e)):[]}function ho(n,t,r){var e=null==n?0:n.length;if(!e)return-1;var u=null==r?0:kc(r);
return u<0&&(u=Gl(e+u,0)),g(n,mi(t,3),u)}function po(n,t,r){var e=null==n?0:n.length;if(!e)return-1;var u=e-1;return r!==X&&(u=kc(r),u=r<0?Gl(e+u,0):Hl(u,e-1)),g(n,mi(t,3),u,!0)}function _o(n){return(null==n?0:n.length)?ee(n,1):[]}function vo(n){return(null==n?0:n.length)?ee(n,Sn):[]}function go(n,t){return(null==n?0:n.length)?(t=t===X?1:kc(t),ee(n,t)):[]}function yo(n){for(var t=-1,r=null==n?0:n.length,e={};++t<r;){var u=n[t];e[u[0]]=u[1]}return e}function bo(n){return n&&n.length?n[0]:X}function wo(n,t,r){
var e=null==n?0:n.length;if(!e)return-1;var u=null==r?0:kc(r);return u<0&&(u=Gl(e+u,0)),y(n,t,u)}function mo(n){return(null==n?0:n.length)?au(n,0,-1):[]}function xo(n,t){return null==n?"":Kl.call(n,t)}function jo(n){var t=null==n?0:n.length;return t?n[t-1]:X}function Ao(n,t,r){var e=null==n?0:n.length;if(!e)return-1;var u=e;return r!==X&&(u=kc(r),u=u<0?Gl(e+u,0):Hl(u,e-1)),t===t?K(n,t,u):g(n,b,u,!0)}function ko(n,t){return n&&n.length?Ge(n,kc(t)):X}function Oo(n,t){return n&&n.length&&t&&t.length?Xe(n,t):n;
}function Io(n,t,r){return n&&n.length&&t&&t.length?Xe(n,t,mi(r,2)):n}function Ro(n,t,r){return n&&n.length&&t&&t.length?Xe(n,t,X,r):n}function zo(n,t){var r=[];if(!n||!n.length)return r;var e=-1,u=[],i=n.length;for(t=mi(t,3);++e<i;){var o=n[e];t(o,e,n)&&(r.push(o),u.push(e))}return nu(n,u),r}function Eo(n){return null==n?n:Xl.call(n)}function So(n,t,r){var e=null==n?0:n.length;return e?(r&&"number"!=typeof r&&Ui(n,t,r)?(t=0,r=e):(t=null==t?0:kc(t),r=r===X?e:kc(r)),au(n,t,r)):[]}function Wo(n,t){
return su(n,t)}function Lo(n,t,r){return hu(n,t,mi(r,2))}function Co(n,t){var r=null==n?0:n.length;if(r){var e=su(n,t);if(e<r&&Gf(n[e],t))return e}return-1}function Uo(n,t){return su(n,t,!0)}function Bo(n,t,r){return hu(n,t,mi(r,2),!0)}function To(n,t){if(null==n?0:n.length){var r=su(n,t,!0)-1;if(Gf(n[r],t))return r}return-1}function $o(n){return n&&n.length?pu(n):[]}function Do(n,t){return n&&n.length?pu(n,mi(t,2)):[]}function Mo(n){var t=null==n?0:n.length;return t?au(n,1,t):[]}function Fo(n,t,r){
return n&&n.length?(t=r||t===X?1:kc(t),au(n,0,t<0?0:t)):[]}function No(n,t,r){var e=null==n?0:n.length;return e?(t=r||t===X?1:kc(t),t=e-t,au(n,t<0?0:t,e)):[]}function Po(n,t){return n&&n.length?bu(n,mi(t,3),!1,!0):[]}function qo(n,t){return n&&n.length?bu(n,mi(t,3)):[]}function Zo(n){return n&&n.length?gu(n):[]}function Ko(n,t){return n&&n.length?gu(n,mi(t,2)):[]}function Vo(n,t){return t="function"==typeof t?t:X,n&&n.length?gu(n,X,t):[]}function Go(n){if(!n||!n.length)return[];var t=0;return n=i(n,function(n){
if(Jf(n))return t=Gl(n.length,t),!0}),O(t,function(t){return c(n,m(t))})}function Ho(t,r){if(!t||!t.length)return[];var e=Go(t);return null==r?e:c(e,function(t){return n(r,X,t)})}function Jo(n,t){return xu(n||[],t||[],Sr)}function Yo(n,t){return xu(n||[],t||[],fu)}function Qo(n){var t=Z(n);return t.__chain__=!0,t}function Xo(n,t){return t(n),n}function nf(n,t){return t(n)}function tf(){return Qo(this)}function rf(){return new Y(this.value(),this.__chain__)}function ef(){this.__values__===X&&(this.__values__=jc(this.value()));
var n=this.__index__>=this.__values__.length;return{done:n,value:n?X:this.__values__[this.__index__++]}}function uf(){return this}function of(n){for(var t,r=this;r instanceof J;){var e=eo(r);e.__index__=0,e.__values__=X,t?u.__wrapped__=e:t=e;var u=e;r=r.__wrapped__}return u.__wrapped__=n,t}function ff(){var n=this.__wrapped__;if(n instanceof Ct){var t=n;return this.__actions__.length&&(t=new Ct(this)),t=t.reverse(),t.__actions__.push({func:nf,args:[Eo],thisArg:X}),new Y(t,this.__chain__)}return this.thru(Eo);
}function cf(){return wu(this.__wrapped__,this.__actions__)}function af(n,t,r){var e=bh(n)?u:Jr;return r&&Ui(n,t,r)&&(t=X),e(n,mi(t,3))}function lf(n,t){return(bh(n)?i:te)(n,mi(t,3))}function sf(n,t){return ee(yf(n,t),1)}function hf(n,t){return ee(yf(n,t),Sn)}function pf(n,t,r){return r=r===X?1:kc(r),ee(yf(n,t),r)}function _f(n,t){return(bh(n)?r:ys)(n,mi(t,3))}function vf(n,t){return(bh(n)?e:ds)(n,mi(t,3))}function gf(n,t,r,e){n=Hf(n)?n:ra(n),r=r&&!e?kc(r):0;var u=n.length;return r<0&&(r=Gl(u+r,0)),
dc(n)?r<=u&&n.indexOf(t,r)>-1:!!u&&y(n,t,r)>-1}function yf(n,t){return(bh(n)?c:Pe)(n,mi(t,3))}function df(n,t,r,e){return null==n?[]:(bh(t)||(t=null==t?[]:[t]),r=e?X:r,bh(r)||(r=null==r?[]:[r]),He(n,t,r))}function bf(n,t,r){var e=bh(n)?l:j,u=arguments.length<3;return e(n,mi(t,4),r,u,ys)}function wf(n,t,r){var e=bh(n)?s:j,u=arguments.length<3;return e(n,mi(t,4),r,u,ds)}function mf(n,t){return(bh(n)?i:te)(n,Uf(mi(t,3)))}function xf(n){return(bh(n)?Ir:iu)(n)}function jf(n,t,r){return t=(r?Ui(n,t,r):t===X)?1:kc(t),
(bh(n)?Rr:ou)(n,t)}function Af(n){return(bh(n)?zr:cu)(n)}function kf(n){if(null==n)return 0;if(Hf(n))return dc(n)?V(n):n.length;var t=zs(n);return t==Gn||t==tt?n.size:Me(n).length}function Of(n,t,r){var e=bh(n)?h:lu;return r&&Ui(n,t,r)&&(t=X),e(n,mi(t,3))}function If(n,t){if("function"!=typeof t)throw new pl(en);return n=kc(n),function(){if(--n<1)return t.apply(this,arguments)}}function Rf(n,t,r){return t=r?X:t,t=n&&null==t?n.length:t,ai(n,mn,X,X,X,X,t)}function zf(n,t){var r;if("function"!=typeof t)throw new pl(en);
return n=kc(n),function(){return--n>0&&(r=t.apply(this,arguments)),n<=1&&(t=X),r}}function Ef(n,t,r){t=r?X:t;var e=ai(n,yn,X,X,X,X,X,t);return e.placeholder=Ef.placeholder,e}function Sf(n,t,r){t=r?X:t;var e=ai(n,dn,X,X,X,X,X,t);return e.placeholder=Sf.placeholder,e}function Wf(n,t,r){function e(t){var r=h,e=p;return h=p=X,d=t,v=n.apply(e,r)}function u(n){return d=n,g=Ws(f,t),b?e(n):v}function i(n){var r=n-y,e=n-d,u=t-r;return w?Hl(u,_-e):u}function o(n){var r=n-y,e=n-d;return y===X||r>=t||r<0||w&&e>=_;
}function f(){var n=fh();return o(n)?c(n):(g=Ws(f,i(n)),X)}function c(n){return g=X,m&&h?e(n):(h=p=X,v)}function a(){g!==X&&As(g),d=0,h=y=p=g=X}function l(){return g===X?v:c(fh())}function s(){var n=fh(),r=o(n);if(h=arguments,p=this,y=n,r){if(g===X)return u(y);if(w)return As(g),g=Ws(f,t),e(y)}return g===X&&(g=Ws(f,t)),v}var h,p,_,v,g,y,d=0,b=!1,w=!1,m=!0;if("function"!=typeof n)throw new pl(en);return t=Ic(t)||0,fc(r)&&(b=!!r.leading,w="maxWait"in r,_=w?Gl(Ic(r.maxWait)||0,t):_,m="trailing"in r?!!r.trailing:m),
s.cancel=a,s.flush=l,s}function Lf(n){return ai(n,jn)}function Cf(n,t){if("function"!=typeof n||null!=t&&"function"!=typeof t)throw new pl(en);var r=function(){var e=arguments,u=t?t.apply(this,e):e[0],i=r.cache;if(i.has(u))return i.get(u);var o=n.apply(this,e);return r.cache=i.set(u,o)||i,o};return r.cache=new(Cf.Cache||sr),r}function Uf(n){if("function"!=typeof n)throw new pl(en);return function(){var t=arguments;switch(t.length){case 0:return!n.call(this);case 1:return!n.call(this,t[0]);case 2:
return!n.call(this,t[0],t[1]);case 3:return!n.call(this,t[0],t[1],t[2])}return!n.apply(this,t)}}function Bf(n){return zf(2,n)}function Tf(n,t){if("function"!=typeof n)throw new pl(en);return t=t===X?t:kc(t),uu(n,t)}function $f(t,r){if("function"!=typeof t)throw new pl(en);return r=null==r?0:Gl(kc(r),0),uu(function(e){var u=e[r],i=Ou(e,0,r);return u&&a(i,u),n(t,this,i)})}function Df(n,t,r){var e=!0,u=!0;if("function"!=typeof n)throw new pl(en);return fc(r)&&(e="leading"in r?!!r.leading:e,u="trailing"in r?!!r.trailing:u),
Wf(n,t,{leading:e,maxWait:t,trailing:u})}function Mf(n){return Rf(n,1)}function Ff(n,t){return ph(Au(t),n)}function Nf(){if(!arguments.length)return[];var n=arguments[0];return bh(n)?n:[n]}function Pf(n){return Fr(n,sn)}function qf(n,t){return t="function"==typeof t?t:X,Fr(n,sn,t)}function Zf(n){return Fr(n,an|sn)}function Kf(n,t){return t="function"==typeof t?t:X,Fr(n,an|sn,t)}function Vf(n,t){return null==t||Pr(n,t,Pc(t))}function Gf(n,t){return n===t||n!==n&&t!==t}function Hf(n){return null!=n&&oc(n.length)&&!uc(n);
}function Jf(n){return cc(n)&&Hf(n)}function Yf(n){return n===!0||n===!1||cc(n)&&we(n)==Nn}function Qf(n){return cc(n)&&1===n.nodeType&&!gc(n)}function Xf(n){if(null==n)return!0;if(Hf(n)&&(bh(n)||"string"==typeof n||"function"==typeof n.splice||mh(n)||Oh(n)||dh(n)))return!n.length;var t=zs(n);if(t==Gn||t==tt)return!n.size;if(Mi(n))return!Me(n).length;for(var r in n)if(bl.call(n,r))return!1;return!0}function nc(n,t){return Se(n,t)}function tc(n,t,r){r="function"==typeof r?r:X;var e=r?r(n,t):X;return e===X?Se(n,t,X,r):!!e;
}function rc(n){if(!cc(n))return!1;var t=we(n);return t==Zn||t==qn||"string"==typeof n.message&&"string"==typeof n.name&&!gc(n)}function ec(n){return"number"==typeof n&&Zl(n)}function uc(n){if(!fc(n))return!1;var t=we(n);return t==Kn||t==Vn||t==Fn||t==Xn}function ic(n){return"number"==typeof n&&n==kc(n)}function oc(n){return"number"==typeof n&&n>-1&&n%1==0&&n<=Wn}function fc(n){var t=typeof n;return null!=n&&("object"==t||"function"==t)}function cc(n){return null!=n&&"object"==typeof n}function ac(n,t){
return n===t||Ce(n,t,ji(t))}function lc(n,t,r){return r="function"==typeof r?r:X,Ce(n,t,ji(t),r)}function sc(n){return vc(n)&&n!=+n}function hc(n){if(Es(n))throw new fl(rn);return Ue(n)}function pc(n){return null===n}function _c(n){return null==n}function vc(n){return"number"==typeof n||cc(n)&&we(n)==Hn}function gc(n){if(!cc(n)||we(n)!=Yn)return!1;var t=El(n);if(null===t)return!0;var r=bl.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&dl.call(r)==jl}function yc(n){
return ic(n)&&n>=-Wn&&n<=Wn}function dc(n){return"string"==typeof n||!bh(n)&&cc(n)&&we(n)==rt}function bc(n){return"symbol"==typeof n||cc(n)&&we(n)==et}function wc(n){return n===X}function mc(n){return cc(n)&&zs(n)==it}function xc(n){return cc(n)&&we(n)==ot}function jc(n){if(!n)return[];if(Hf(n))return dc(n)?G(n):Tu(n);if(Ul&&n[Ul])return D(n[Ul]());var t=zs(n);return(t==Gn?M:t==tt?P:ra)(n)}function Ac(n){if(!n)return 0===n?n:0;if(n=Ic(n),n===Sn||n===-Sn){return(n<0?-1:1)*Ln}return n===n?n:0}function kc(n){
var t=Ac(n),r=t%1;return t===t?r?t-r:t:0}function Oc(n){return n?Mr(kc(n),0,Un):0}function Ic(n){if("number"==typeof n)return n;if(bc(n))return Cn;if(fc(n)){var t="function"==typeof n.valueOf?n.valueOf():n;n=fc(t)?t+"":t}if("string"!=typeof n)return 0===n?n:+n;n=R(n);var r=qt.test(n);return r||Kt.test(n)?Xr(n.slice(2),r?2:8):Pt.test(n)?Cn:+n}function Rc(n){return $u(n,qc(n))}function zc(n){return n?Mr(kc(n),-Wn,Wn):0===n?n:0}function Ec(n){return null==n?"":vu(n)}function Sc(n,t){var r=gs(n);return null==t?r:Cr(r,t);
}function Wc(n,t){return v(n,mi(t,3),ue)}function Lc(n,t){return v(n,mi(t,3),oe)}function Cc(n,t){return null==n?n:bs(n,mi(t,3),qc)}function Uc(n,t){return null==n?n:ws(n,mi(t,3),qc)}function Bc(n,t){return n&&ue(n,mi(t,3))}function Tc(n,t){return n&&oe(n,mi(t,3))}function $c(n){return null==n?[]:fe(n,Pc(n))}function Dc(n){return null==n?[]:fe(n,qc(n))}function Mc(n,t,r){var e=null==n?X:_e(n,t);return e===X?r:e}function Fc(n,t){return null!=n&&Ri(n,t,xe)}function Nc(n,t){return null!=n&&Ri(n,t,je);
}function Pc(n){return Hf(n)?Or(n):Me(n)}function qc(n){return Hf(n)?Or(n,!0):Fe(n)}function Zc(n,t){var r={};return t=mi(t,3),ue(n,function(n,e,u){Br(r,t(n,e,u),n)}),r}function Kc(n,t){var r={};return t=mi(t,3),ue(n,function(n,e,u){Br(r,e,t(n,e,u))}),r}function Vc(n,t){return Gc(n,Uf(mi(t)))}function Gc(n,t){if(null==n)return{};var r=c(di(n),function(n){return[n]});return t=mi(t),Ye(n,r,function(n,r){return t(n,r[0])})}function Hc(n,t,r){t=ku(t,n);var e=-1,u=t.length;for(u||(u=1,n=X);++e<u;){var i=null==n?X:n[no(t[e])];
i===X&&(e=u,i=r),n=uc(i)?i.call(n):i}return n}function Jc(n,t,r){return null==n?n:fu(n,t,r)}function Yc(n,t,r,e){return e="function"==typeof e?e:X,null==n?n:fu(n,t,r,e)}function Qc(n,t,e){var u=bh(n),i=u||mh(n)||Oh(n);if(t=mi(t,4),null==e){var o=n&&n.constructor;e=i?u?new o:[]:fc(n)&&uc(o)?gs(El(n)):{}}return(i?r:ue)(n,function(n,r,u){return t(e,n,r,u)}),e}function Xc(n,t){return null==n||yu(n,t)}function na(n,t,r){return null==n?n:du(n,t,Au(r))}function ta(n,t,r,e){return e="function"==typeof e?e:X,
null==n?n:du(n,t,Au(r),e)}function ra(n){return null==n?[]:E(n,Pc(n))}function ea(n){return null==n?[]:E(n,qc(n))}function ua(n,t,r){return r===X&&(r=t,t=X),r!==X&&(r=Ic(r),r=r===r?r:0),t!==X&&(t=Ic(t),t=t===t?t:0),Mr(Ic(n),t,r)}function ia(n,t,r){return t=Ac(t),r===X?(r=t,t=0):r=Ac(r),n=Ic(n),Ae(n,t,r)}function oa(n,t,r){if(r&&"boolean"!=typeof r&&Ui(n,t,r)&&(t=r=X),r===X&&("boolean"==typeof t?(r=t,t=X):"boolean"==typeof n&&(r=n,n=X)),n===X&&t===X?(n=0,t=1):(n=Ac(n),t===X?(t=n,n=0):t=Ac(t)),n>t){
var e=n;n=t,t=e}if(r||n%1||t%1){var u=Ql();return Hl(n+u*(t-n+Qr("1e-"+((u+"").length-1))),t)}return tu(n,t)}function fa(n){return Qh(Ec(n).toLowerCase())}function ca(n){return n=Ec(n),n&&n.replace(Gt,ve).replace(Dr,"")}function aa(n,t,r){n=Ec(n),t=vu(t);var e=n.length;r=r===X?e:Mr(kc(r),0,e);var u=r;return r-=t.length,r>=0&&n.slice(r,u)==t}function la(n){return n=Ec(n),n&&At.test(n)?n.replace(xt,ge):n}function sa(n){return n=Ec(n),n&&Wt.test(n)?n.replace(St,"\\$&"):n}function ha(n,t,r){n=Ec(n),t=kc(t);
var e=t?V(n):0;if(!t||e>=t)return n;var u=(t-e)/2;return ri(Nl(u),r)+n+ri(Fl(u),r)}function pa(n,t,r){n=Ec(n),t=kc(t);var e=t?V(n):0;return t&&e<t?n+ri(t-e,r):n}function _a(n,t,r){n=Ec(n),t=kc(t);var e=t?V(n):0;return t&&e<t?ri(t-e,r)+n:n}function va(n,t,r){return r||null==t?t=0:t&&(t=+t),Yl(Ec(n).replace(Lt,""),t||0)}function ga(n,t,r){return t=(r?Ui(n,t,r):t===X)?1:kc(t),eu(Ec(n),t)}function ya(){var n=arguments,t=Ec(n[0]);return n.length<3?t:t.replace(n[1],n[2])}function da(n,t,r){return r&&"number"!=typeof r&&Ui(n,t,r)&&(t=r=X),
(r=r===X?Un:r>>>0)?(n=Ec(n),n&&("string"==typeof t||null!=t&&!Ah(t))&&(t=vu(t),!t&&T(n))?Ou(G(n),0,r):n.split(t,r)):[]}function ba(n,t,r){return n=Ec(n),r=null==r?0:Mr(kc(r),0,n.length),t=vu(t),n.slice(r,r+t.length)==t}function wa(n,t,r){var e=Z.templateSettings;r&&Ui(n,t,r)&&(t=X),n=Ec(n),t=Sh({},t,e,li);var u,i,o=Sh({},t.imports,e.imports,li),f=Pc(o),c=E(o,f),a=0,l=t.interpolate||Ht,s="__p += '",h=sl((t.escape||Ht).source+"|"+l.source+"|"+(l===It?Ft:Ht).source+"|"+(t.evaluate||Ht).source+"|$","g"),p="//# sourceURL="+(bl.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Zr+"]")+"\n";
n.replace(h,function(t,r,e,o,f,c){return e||(e=o),s+=n.slice(a,c).replace(Jt,U),r&&(u=!0,s+="' +\n__e("+r+") +\n'"),f&&(i=!0,s+="';\n"+f+";\n__p += '"),e&&(s+="' +\n((__t = ("+e+")) == null ? '' : __t) +\n'"),a=c+t.length,t}),s+="';\n";var _=bl.call(t,"variable")&&t.variable;if(_){if(Dt.test(_))throw new fl(un)}else s="with (obj) {\n"+s+"\n}\n";s=(i?s.replace(dt,""):s).replace(bt,"$1").replace(wt,"$1;"),s="function("+(_||"obj")+") {\n"+(_?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(u?", __e = _.escape":"")+(i?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+s+"return __p\n}";
var v=Xh(function(){return cl(f,p+"return "+s).apply(X,c)});if(v.source=s,rc(v))throw v;return v}function ma(n){return Ec(n).toLowerCase()}function xa(n){return Ec(n).toUpperCase()}function ja(n,t,r){if(n=Ec(n),n&&(r||t===X))return R(n);if(!n||!(t=vu(t)))return n;var e=G(n),u=G(t);return Ou(e,W(e,u),L(e,u)+1).join("")}function Aa(n,t,r){if(n=Ec(n),n&&(r||t===X))return n.slice(0,H(n)+1);if(!n||!(t=vu(t)))return n;var e=G(n);return Ou(e,0,L(e,G(t))+1).join("")}function ka(n,t,r){if(n=Ec(n),n&&(r||t===X))return n.replace(Lt,"");
if(!n||!(t=vu(t)))return n;var e=G(n);return Ou(e,W(e,G(t))).join("")}function Oa(n,t){var r=An,e=kn;if(fc(t)){var u="separator"in t?t.separator:u;r="length"in t?kc(t.length):r,e="omission"in t?vu(t.omission):e}n=Ec(n);var i=n.length;if(T(n)){var o=G(n);i=o.length}if(r>=i)return n;var f=r-V(e);if(f<1)return e;var c=o?Ou(o,0,f).join(""):n.slice(0,f);if(u===X)return c+e;if(o&&(f+=c.length-f),Ah(u)){if(n.slice(f).search(u)){var a,l=c;for(u.global||(u=sl(u.source,Ec(Nt.exec(u))+"g")),u.lastIndex=0;a=u.exec(l);)var s=a.index;
c=c.slice(0,s===X?f:s)}}else if(n.indexOf(vu(u),f)!=f){var h=c.lastIndexOf(u);h>-1&&(c=c.slice(0,h))}return c+e}function Ia(n){return n=Ec(n),n&&jt.test(n)?n.replace(mt,ye):n}function Ra(n,t,r){return n=Ec(n),t=r?X:t,t===X?$(n)?Q(n):_(n):n.match(t)||[]}function za(t){var r=null==t?0:t.length,e=mi();return t=r?c(t,function(n){if("function"!=typeof n[1])throw new pl(en);return[e(n[0]),n[1]]}):[],uu(function(e){for(var u=-1;++u<r;){var i=t[u];if(n(i[0],this,e))return n(i[1],this,e)}})}function Ea(n){
return Nr(Fr(n,an))}function Sa(n){return function(){return n}}function Wa(n,t){return null==n||n!==n?t:n}function La(n){return n}function Ca(n){return De("function"==typeof n?n:Fr(n,an))}function Ua(n){return qe(Fr(n,an))}function Ba(n,t){return Ze(n,Fr(t,an))}function Ta(n,t,e){var u=Pc(t),i=fe(t,u);null!=e||fc(t)&&(i.length||!u.length)||(e=t,t=n,n=this,i=fe(t,Pc(t)));var o=!(fc(e)&&"chain"in e&&!e.chain),f=uc(n);return r(i,function(r){var e=t[r];n[r]=e,f&&(n.prototype[r]=function(){var t=this.__chain__;
if(o||t){var r=n(this.__wrapped__);return(r.__actions__=Tu(this.__actions__)).push({func:e,args:arguments,thisArg:n}),r.__chain__=t,r}return e.apply(n,a([this.value()],arguments))})}),n}function $a(){return re._===this&&(re._=Al),this}function Da(){}function Ma(n){return n=kc(n),uu(function(t){return Ge(t,n)})}function Fa(n){return Bi(n)?m(no(n)):Qe(n)}function Na(n){return function(t){return null==n?X:_e(n,t)}}function Pa(){return[]}function qa(){return!1}function Za(){return{}}function Ka(){return"";
}function Va(){return!0}function Ga(n,t){if(n=kc(n),n<1||n>Wn)return[];var r=Un,e=Hl(n,Un);t=mi(t),n-=Un;for(var u=O(e,t);++r<n;)t(r);return u}function Ha(n){return bh(n)?c(n,no):bc(n)?[n]:Tu(Cs(Ec(n)))}function Ja(n){var t=++wl;return Ec(n)+t}function Ya(n){return n&&n.length?Yr(n,La,me):X}function Qa(n,t){return n&&n.length?Yr(n,mi(t,2),me):X}function Xa(n){return w(n,La)}function nl(n,t){return w(n,mi(t,2))}function tl(n){return n&&n.length?Yr(n,La,Ne):X}function rl(n,t){return n&&n.length?Yr(n,mi(t,2),Ne):X;
}function el(n){return n&&n.length?k(n,La):0}function ul(n,t){return n&&n.length?k(n,mi(t,2)):0}x=null==x?re:be.defaults(re.Object(),x,be.pick(re,qr));var il=x.Array,ol=x.Date,fl=x.Error,cl=x.Function,al=x.Math,ll=x.Object,sl=x.RegExp,hl=x.String,pl=x.TypeError,_l=il.prototype,vl=cl.prototype,gl=ll.prototype,yl=x["__core-js_shared__"],dl=vl.toString,bl=gl.hasOwnProperty,wl=0,ml=function(){var n=/[^.]+$/.exec(yl&&yl.keys&&yl.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}(),xl=gl.toString,jl=dl.call(ll),Al=re._,kl=sl("^"+dl.call(bl).replace(St,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ol=ie?x.Buffer:X,Il=x.Symbol,Rl=x.Uint8Array,zl=Ol?Ol.allocUnsafe:X,El=F(ll.getPrototypeOf,ll),Sl=ll.create,Wl=gl.propertyIsEnumerable,Ll=_l.splice,Cl=Il?Il.isConcatSpreadable:X,Ul=Il?Il.iterator:X,Bl=Il?Il.toStringTag:X,Tl=function(){
try{var n=Ai(ll,"defineProperty");return n({},"",{}),n}catch(n){}}(),$l=x.clearTimeout!==re.clearTimeout&&x.clearTimeout,Dl=ol&&ol.now!==re.Date.now&&ol.now,Ml=x.setTimeout!==re.setTimeout&&x.setTimeout,Fl=al.ceil,Nl=al.floor,Pl=ll.getOwnPropertySymbols,ql=Ol?Ol.isBuffer:X,Zl=x.isFinite,Kl=_l.join,Vl=F(ll.keys,ll),Gl=al.max,Hl=al.min,Jl=ol.now,Yl=x.parseInt,Ql=al.random,Xl=_l.reverse,ns=Ai(x,"DataView"),ts=Ai(x,"Map"),rs=Ai(x,"Promise"),es=Ai(x,"Set"),us=Ai(x,"WeakMap"),is=Ai(ll,"create"),os=us&&new us,fs={},cs=to(ns),as=to(ts),ls=to(rs),ss=to(es),hs=to(us),ps=Il?Il.prototype:X,_s=ps?ps.valueOf:X,vs=ps?ps.toString:X,gs=function(){
function n(){}return function(t){if(!fc(t))return{};if(Sl)return Sl(t);n.prototype=t;var r=new n;return n.prototype=X,r}}();Z.templateSettings={escape:kt,evaluate:Ot,interpolate:It,variable:"",imports:{_:Z}},Z.prototype=J.prototype,Z.prototype.constructor=Z,Y.prototype=gs(J.prototype),Y.prototype.constructor=Y,Ct.prototype=gs(J.prototype),Ct.prototype.constructor=Ct,Xt.prototype.clear=nr,Xt.prototype.delete=tr,Xt.prototype.get=rr,Xt.prototype.has=er,Xt.prototype.set=ur,ir.prototype.clear=or,ir.prototype.delete=fr,
ir.prototype.get=cr,ir.prototype.has=ar,ir.prototype.set=lr,sr.prototype.clear=hr,sr.prototype.delete=pr,sr.prototype.get=_r,sr.prototype.has=vr,sr.prototype.set=gr,yr.prototype.add=yr.prototype.push=dr,yr.prototype.has=br,wr.prototype.clear=mr,wr.prototype.delete=xr,wr.prototype.get=jr,wr.prototype.has=Ar,wr.prototype.set=kr;var ys=Pu(ue),ds=Pu(oe,!0),bs=qu(),ws=qu(!0),ms=os?function(n,t){return os.set(n,t),n}:La,xs=Tl?function(n,t){return Tl(n,"toString",{configurable:!0,enumerable:!1,value:Sa(t),
writable:!0})}:La,js=uu,As=$l||function(n){return re.clearTimeout(n)},ks=es&&1/P(new es([,-0]))[1]==Sn?function(n){return new es(n)}:Da,Os=os?function(n){return os.get(n)}:Da,Is=Pl?function(n){return null==n?[]:(n=ll(n),i(Pl(n),function(t){return Wl.call(n,t)}))}:Pa,Rs=Pl?function(n){for(var t=[];n;)a(t,Is(n)),n=El(n);return t}:Pa,zs=we;(ns&&zs(new ns(new ArrayBuffer(1)))!=ct||ts&&zs(new ts)!=Gn||rs&&zs(rs.resolve())!=Qn||es&&zs(new es)!=tt||us&&zs(new us)!=it)&&(zs=function(n){var t=we(n),r=t==Yn?n.constructor:X,e=r?to(r):"";
if(e)switch(e){case cs:return ct;case as:return Gn;case ls:return Qn;case ss:return tt;case hs:return it}return t});var Es=yl?uc:qa,Ss=Qi(ms),Ws=Ml||function(n,t){return re.setTimeout(n,t)},Ls=Qi(xs),Cs=Pi(function(n){var t=[];return 46===n.charCodeAt(0)&&t.push(""),n.replace(Et,function(n,r,e,u){t.push(e?u.replace(Mt,"$1"):r||n)}),t}),Us=uu(function(n,t){return Jf(n)?Hr(n,ee(t,1,Jf,!0)):[]}),Bs=uu(function(n,t){var r=jo(t);return Jf(r)&&(r=X),Jf(n)?Hr(n,ee(t,1,Jf,!0),mi(r,2)):[]}),Ts=uu(function(n,t){
var r=jo(t);return Jf(r)&&(r=X),Jf(n)?Hr(n,ee(t,1,Jf,!0),X,r):[]}),$s=uu(function(n){var t=c(n,ju);return t.length&&t[0]===n[0]?ke(t):[]}),Ds=uu(function(n){var t=jo(n),r=c(n,ju);return t===jo(r)?t=X:r.pop(),r.length&&r[0]===n[0]?ke(r,mi(t,2)):[]}),Ms=uu(function(n){var t=jo(n),r=c(n,ju);return t="function"==typeof t?t:X,t&&r.pop(),r.length&&r[0]===n[0]?ke(r,X,t):[]}),Fs=uu(Oo),Ns=gi(function(n,t){var r=null==n?0:n.length,e=Tr(n,t);return nu(n,c(t,function(n){return Ci(n,r)?+n:n}).sort(Lu)),e}),Ps=uu(function(n){
return gu(ee(n,1,Jf,!0))}),qs=uu(function(n){var t=jo(n);return Jf(t)&&(t=X),gu(ee(n,1,Jf,!0),mi(t,2))}),Zs=uu(function(n){var t=jo(n);return t="function"==typeof t?t:X,gu(ee(n,1,Jf,!0),X,t)}),Ks=uu(function(n,t){return Jf(n)?Hr(n,t):[]}),Vs=uu(function(n){return mu(i(n,Jf))}),Gs=uu(function(n){var t=jo(n);return Jf(t)&&(t=X),mu(i(n,Jf),mi(t,2))}),Hs=uu(function(n){var t=jo(n);return t="function"==typeof t?t:X,mu(i(n,Jf),X,t)}),Js=uu(Go),Ys=uu(function(n){var t=n.length,r=t>1?n[t-1]:X;return r="function"==typeof r?(n.pop(),
r):X,Ho(n,r)}),Qs=gi(function(n){var t=n.length,r=t?n[0]:0,e=this.__wrapped__,u=function(t){return Tr(t,n)};return!(t>1||this.__actions__.length)&&e instanceof Ct&&Ci(r)?(e=e.slice(r,+r+(t?1:0)),e.__actions__.push({func:nf,args:[u],thisArg:X}),new Y(e,this.__chain__).thru(function(n){return t&&!n.length&&n.push(X),n})):this.thru(u)}),Xs=Fu(function(n,t,r){bl.call(n,r)?++n[r]:Br(n,r,1)}),nh=Ju(ho),th=Ju(po),rh=Fu(function(n,t,r){bl.call(n,r)?n[r].push(t):Br(n,r,[t])}),eh=uu(function(t,r,e){var u=-1,i="function"==typeof r,o=Hf(t)?il(t.length):[];
return ys(t,function(t){o[++u]=i?n(r,t,e):Ie(t,r,e)}),o}),uh=Fu(function(n,t,r){Br(n,r,t)}),ih=Fu(function(n,t,r){n[r?0:1].push(t)},function(){return[[],[]]}),oh=uu(function(n,t){if(null==n)return[];var r=t.length;return r>1&&Ui(n,t[0],t[1])?t=[]:r>2&&Ui(t[0],t[1],t[2])&&(t=[t[0]]),He(n,ee(t,1),[])}),fh=Dl||function(){return re.Date.now()},ch=uu(function(n,t,r){var e=_n;if(r.length){var u=N(r,wi(ch));e|=bn}return ai(n,e,t,r,u)}),ah=uu(function(n,t,r){var e=_n|vn;if(r.length){var u=N(r,wi(ah));e|=bn;
}return ai(t,e,n,r,u)}),lh=uu(function(n,t){return Gr(n,1,t)}),sh=uu(function(n,t,r){return Gr(n,Ic(t)||0,r)});Cf.Cache=sr;var hh=js(function(t,r){r=1==r.length&&bh(r[0])?c(r[0],z(mi())):c(ee(r,1),z(mi()));var e=r.length;return uu(function(u){for(var i=-1,o=Hl(u.length,e);++i<o;)u[i]=r[i].call(this,u[i]);return n(t,this,u)})}),ph=uu(function(n,t){return ai(n,bn,X,t,N(t,wi(ph)))}),_h=uu(function(n,t){return ai(n,wn,X,t,N(t,wi(_h)))}),vh=gi(function(n,t){return ai(n,xn,X,X,X,t)}),gh=ii(me),yh=ii(function(n,t){
return n>=t}),dh=Re(function(){return arguments}())?Re:function(n){return cc(n)&&bl.call(n,"callee")&&!Wl.call(n,"callee")},bh=il.isArray,wh=ce?z(ce):ze,mh=ql||qa,xh=ae?z(ae):Ee,jh=le?z(le):Le,Ah=se?z(se):Be,kh=he?z(he):Te,Oh=pe?z(pe):$e,Ih=ii(Ne),Rh=ii(function(n,t){return n<=t}),zh=Nu(function(n,t){if(Mi(t)||Hf(t))return $u(t,Pc(t),n),X;for(var r in t)bl.call(t,r)&&Sr(n,r,t[r])}),Eh=Nu(function(n,t){$u(t,qc(t),n)}),Sh=Nu(function(n,t,r,e){$u(t,qc(t),n,e)}),Wh=Nu(function(n,t,r,e){$u(t,Pc(t),n,e);
}),Lh=gi(Tr),Ch=uu(function(n,t){n=ll(n);var r=-1,e=t.length,u=e>2?t[2]:X;for(u&&Ui(t[0],t[1],u)&&(e=1);++r<e;)for(var i=t[r],o=qc(i),f=-1,c=o.length;++f<c;){var a=o[f],l=n[a];(l===X||Gf(l,gl[a])&&!bl.call(n,a))&&(n[a]=i[a])}return n}),Uh=uu(function(t){return t.push(X,si),n(Mh,X,t)}),Bh=Xu(function(n,t,r){null!=t&&"function"!=typeof t.toString&&(t=xl.call(t)),n[t]=r},Sa(La)),Th=Xu(function(n,t,r){null!=t&&"function"!=typeof t.toString&&(t=xl.call(t)),bl.call(n,t)?n[t].push(r):n[t]=[r]},mi),$h=uu(Ie),Dh=Nu(function(n,t,r){
Ke(n,t,r)}),Mh=Nu(function(n,t,r,e){Ke(n,t,r,e)}),Fh=gi(function(n,t){var r={};if(null==n)return r;var e=!1;t=c(t,function(t){return t=ku(t,n),e||(e=t.length>1),t}),$u(n,di(n),r),e&&(r=Fr(r,an|ln|sn,hi));for(var u=t.length;u--;)yu(r,t[u]);return r}),Nh=gi(function(n,t){return null==n?{}:Je(n,t)}),Ph=ci(Pc),qh=ci(qc),Zh=Vu(function(n,t,r){return t=t.toLowerCase(),n+(r?fa(t):t)}),Kh=Vu(function(n,t,r){return n+(r?"-":"")+t.toLowerCase()}),Vh=Vu(function(n,t,r){return n+(r?" ":"")+t.toLowerCase()}),Gh=Ku("toLowerCase"),Hh=Vu(function(n,t,r){
return n+(r?"_":"")+t.toLowerCase()}),Jh=Vu(function(n,t,r){return n+(r?" ":"")+Qh(t)}),Yh=Vu(function(n,t,r){return n+(r?" ":"")+t.toUpperCase()}),Qh=Ku("toUpperCase"),Xh=uu(function(t,r){try{return n(t,X,r)}catch(n){return rc(n)?n:new fl(n)}}),np=gi(function(n,t){return r(t,function(t){t=no(t),Br(n,t,ch(n[t],n))}),n}),tp=Yu(),rp=Yu(!0),ep=uu(function(n,t){return function(r){return Ie(r,n,t)}}),up=uu(function(n,t){return function(r){return Ie(n,r,t)}}),ip=ti(c),op=ti(u),fp=ti(h),cp=ui(),ap=ui(!0),lp=ni(function(n,t){
return n+t},0),sp=fi("ceil"),hp=ni(function(n,t){return n/t},1),pp=fi("floor"),_p=ni(function(n,t){return n*t},1),vp=fi("round"),gp=ni(function(n,t){return n-t},0);return Z.after=If,Z.ary=Rf,Z.assign=zh,Z.assignIn=Eh,Z.assignInWith=Sh,Z.assignWith=Wh,Z.at=Lh,Z.before=zf,Z.bind=ch,Z.bindAll=np,Z.bindKey=ah,Z.castArray=Nf,Z.chain=Qo,Z.chunk=uo,Z.compact=io,Z.concat=oo,Z.cond=za,Z.conforms=Ea,Z.constant=Sa,Z.countBy=Xs,Z.create=Sc,Z.curry=Ef,Z.curryRight=Sf,Z.debounce=Wf,Z.defaults=Ch,Z.defaultsDeep=Uh,
Z.defer=lh,Z.delay=sh,Z.difference=Us,Z.differenceBy=Bs,Z.differenceWith=Ts,Z.drop=fo,Z.dropRight=co,Z.dropRightWhile=ao,Z.dropWhile=lo,Z.fill=so,Z.filter=lf,Z.flatMap=sf,Z.flatMapDeep=hf,Z.flatMapDepth=pf,Z.flatten=_o,Z.flattenDeep=vo,Z.flattenDepth=go,Z.flip=Lf,Z.flow=tp,Z.flowRight=rp,Z.fromPairs=yo,Z.functions=$c,Z.functionsIn=Dc,Z.groupBy=rh,Z.initial=mo,Z.intersection=$s,Z.intersectionBy=Ds,Z.intersectionWith=Ms,Z.invert=Bh,Z.invertBy=Th,Z.invokeMap=eh,Z.iteratee=Ca,Z.keyBy=uh,Z.keys=Pc,Z.keysIn=qc,
Z.map=yf,Z.mapKeys=Zc,Z.mapValues=Kc,Z.matches=Ua,Z.matchesProperty=Ba,Z.memoize=Cf,Z.merge=Dh,Z.mergeWith=Mh,Z.method=ep,Z.methodOf=up,Z.mixin=Ta,Z.negate=Uf,Z.nthArg=Ma,Z.omit=Fh,Z.omitBy=Vc,Z.once=Bf,Z.orderBy=df,Z.over=ip,Z.overArgs=hh,Z.overEvery=op,Z.overSome=fp,Z.partial=ph,Z.partialRight=_h,Z.partition=ih,Z.pick=Nh,Z.pickBy=Gc,Z.property=Fa,Z.propertyOf=Na,Z.pull=Fs,Z.pullAll=Oo,Z.pullAllBy=Io,Z.pullAllWith=Ro,Z.pullAt=Ns,Z.range=cp,Z.rangeRight=ap,Z.rearg=vh,Z.reject=mf,Z.remove=zo,Z.rest=Tf,
Z.reverse=Eo,Z.sampleSize=jf,Z.set=Jc,Z.setWith=Yc,Z.shuffle=Af,Z.slice=So,Z.sortBy=oh,Z.sortedUniq=$o,Z.sortedUniqBy=Do,Z.split=da,Z.spread=$f,Z.tail=Mo,Z.take=Fo,Z.takeRight=No,Z.takeRightWhile=Po,Z.takeWhile=qo,Z.tap=Xo,Z.throttle=Df,Z.thru=nf,Z.toArray=jc,Z.toPairs=Ph,Z.toPairsIn=qh,Z.toPath=Ha,Z.toPlainObject=Rc,Z.transform=Qc,Z.unary=Mf,Z.union=Ps,Z.unionBy=qs,Z.unionWith=Zs,Z.uniq=Zo,Z.uniqBy=Ko,Z.uniqWith=Vo,Z.unset=Xc,Z.unzip=Go,Z.unzipWith=Ho,Z.update=na,Z.updateWith=ta,Z.values=ra,Z.valuesIn=ea,
Z.without=Ks,Z.words=Ra,Z.wrap=Ff,Z.xor=Vs,Z.xorBy=Gs,Z.xorWith=Hs,Z.zip=Js,Z.zipObject=Jo,Z.zipObjectDeep=Yo,Z.zipWith=Ys,Z.entries=Ph,Z.entriesIn=qh,Z.extend=Eh,Z.extendWith=Sh,Ta(Z,Z),Z.add=lp,Z.attempt=Xh,Z.camelCase=Zh,Z.capitalize=fa,Z.ceil=sp,Z.clamp=ua,Z.clone=Pf,Z.cloneDeep=Zf,Z.cloneDeepWith=Kf,Z.cloneWith=qf,Z.conformsTo=Vf,Z.deburr=ca,Z.defaultTo=Wa,Z.divide=hp,Z.endsWith=aa,Z.eq=Gf,Z.escape=la,Z.escapeRegExp=sa,Z.every=af,Z.find=nh,Z.findIndex=ho,Z.findKey=Wc,Z.findLast=th,Z.findLastIndex=po,
Z.findLastKey=Lc,Z.floor=pp,Z.forEach=_f,Z.forEachRight=vf,Z.forIn=Cc,Z.forInRight=Uc,Z.forOwn=Bc,Z.forOwnRight=Tc,Z.get=Mc,Z.gt=gh,Z.gte=yh,Z.has=Fc,Z.hasIn=Nc,Z.head=bo,Z.identity=La,Z.includes=gf,Z.indexOf=wo,Z.inRange=ia,Z.invoke=$h,Z.isArguments=dh,Z.isArray=bh,Z.isArrayBuffer=wh,Z.isArrayLike=Hf,Z.isArrayLikeObject=Jf,Z.isBoolean=Yf,Z.isBuffer=mh,Z.isDate=xh,Z.isElement=Qf,Z.isEmpty=Xf,Z.isEqual=nc,Z.isEqualWith=tc,Z.isError=rc,Z.isFinite=ec,Z.isFunction=uc,Z.isInteger=ic,Z.isLength=oc,Z.isMap=jh,
Z.isMatch=ac,Z.isMatchWith=lc,Z.isNaN=sc,Z.isNative=hc,Z.isNil=_c,Z.isNull=pc,Z.isNumber=vc,Z.isObject=fc,Z.isObjectLike=cc,Z.isPlainObject=gc,Z.isRegExp=Ah,Z.isSafeInteger=yc,Z.isSet=kh,Z.isString=dc,Z.isSymbol=bc,Z.isTypedArray=Oh,Z.isUndefined=wc,Z.isWeakMap=mc,Z.isWeakSet=xc,Z.join=xo,Z.kebabCase=Kh,Z.last=jo,Z.lastIndexOf=Ao,Z.lowerCase=Vh,Z.lowerFirst=Gh,Z.lt=Ih,Z.lte=Rh,Z.max=Ya,Z.maxBy=Qa,Z.mean=Xa,Z.meanBy=nl,Z.min=tl,Z.minBy=rl,Z.stubArray=Pa,Z.stubFalse=qa,Z.stubObject=Za,Z.stubString=Ka,
Z.stubTrue=Va,Z.multiply=_p,Z.nth=ko,Z.noConflict=$a,Z.noop=Da,Z.now=fh,Z.pad=ha,Z.padEnd=pa,Z.padStart=_a,Z.parseInt=va,Z.random=oa,Z.reduce=bf,Z.reduceRight=wf,Z.repeat=ga,Z.replace=ya,Z.result=Hc,Z.round=vp,Z.runInContext=p,Z.sample=xf,Z.size=kf,Z.snakeCase=Hh,Z.some=Of,Z.sortedIndex=Wo,Z.sortedIndexBy=Lo,Z.sortedIndexOf=Co,Z.sortedLastIndex=Uo,Z.sortedLastIndexBy=Bo,Z.sortedLastIndexOf=To,Z.startCase=Jh,Z.startsWith=ba,Z.subtract=gp,Z.sum=el,Z.sumBy=ul,Z.template=wa,Z.times=Ga,Z.toFinite=Ac,Z.toInteger=kc,
Z.toLength=Oc,Z.toLower=ma,Z.toNumber=Ic,Z.toSafeInteger=zc,Z.toString=Ec,Z.toUpper=xa,Z.trim=ja,Z.trimEnd=Aa,Z.trimStart=ka,Z.truncate=Oa,Z.unescape=Ia,Z.uniqueId=Ja,Z.upperCase=Yh,Z.upperFirst=Qh,Z.each=_f,Z.eachRight=vf,Z.first=bo,Ta(Z,function(){var n={};return ue(Z,function(t,r){bl.call(Z.prototype,r)||(n[r]=t)}),n}(),{chain:!1}),Z.VERSION=nn,r(["bind","bindKey","curry","curryRight","partial","partialRight"],function(n){Z[n].placeholder=Z}),r(["drop","take"],function(n,t){Ct.prototype[n]=function(r){
r=r===X?1:Gl(kc(r),0);var e=this.__filtered__&&!t?new Ct(this):this.clone();return e.__filtered__?e.__takeCount__=Hl(r,e.__takeCount__):e.__views__.push({size:Hl(r,Un),type:n+(e.__dir__<0?"Right":"")}),e},Ct.prototype[n+"Right"]=function(t){return this.reverse()[n](t).reverse()}}),r(["filter","map","takeWhile"],function(n,t){var r=t+1,e=r==Rn||r==En;Ct.prototype[n]=function(n){var t=this.clone();return t.__iteratees__.push({iteratee:mi(n,3),type:r}),t.__filtered__=t.__filtered__||e,t}}),r(["head","last"],function(n,t){
var r="take"+(t?"Right":"");Ct.prototype[n]=function(){return this[r](1).value()[0]}}),r(["initial","tail"],function(n,t){var r="drop"+(t?"":"Right");Ct.prototype[n]=function(){return this.__filtered__?new Ct(this):this[r](1)}}),Ct.prototype.compact=function(){return this.filter(La)},Ct.prototype.find=function(n){return this.filter(n).head()},Ct.prototype.findLast=function(n){return this.reverse().find(n)},Ct.prototype.invokeMap=uu(function(n,t){return"function"==typeof n?new Ct(this):this.map(function(r){
return Ie(r,n,t)})}),Ct.prototype.reject=function(n){return this.filter(Uf(mi(n)))},Ct.prototype.slice=function(n,t){n=kc(n);var r=this;return r.__filtered__&&(n>0||t<0)?new Ct(r):(n<0?r=r.takeRight(-n):n&&(r=r.drop(n)),t!==X&&(t=kc(t),r=t<0?r.dropRight(-t):r.take(t-n)),r)},Ct.prototype.takeRightWhile=function(n){return this.reverse().takeWhile(n).reverse()},Ct.prototype.toArray=function(){return this.take(Un)},ue(Ct.prototype,function(n,t){var r=/^(?:filter|find|map|reject)|While$/.test(t),e=/^(?:head|last)$/.test(t),u=Z[e?"take"+("last"==t?"Right":""):t],i=e||/^find/.test(t);
u&&(Z.prototype[t]=function(){var t=this.__wrapped__,o=e?[1]:arguments,f=t instanceof Ct,c=o[0],l=f||bh(t),s=function(n){var t=u.apply(Z,a([n],o));return e&&h?t[0]:t};l&&r&&"function"==typeof c&&1!=c.length&&(f=l=!1);var h=this.__chain__,p=!!this.__actions__.length,_=i&&!h,v=f&&!p;if(!i&&l){t=v?t:new Ct(this);var g=n.apply(t,o);return g.__actions__.push({func:nf,args:[s],thisArg:X}),new Y(g,h)}return _&&v?n.apply(this,o):(g=this.thru(s),_?e?g.value()[0]:g.value():g)})}),r(["pop","push","shift","sort","splice","unshift"],function(n){
var t=_l[n],r=/^(?:push|sort|unshift)$/.test(n)?"tap":"thru",e=/^(?:pop|shift)$/.test(n);Z.prototype[n]=function(){var n=arguments;if(e&&!this.__chain__){var u=this.value();return t.apply(bh(u)?u:[],n)}return this[r](function(r){return t.apply(bh(r)?r:[],n)})}}),ue(Ct.prototype,function(n,t){var r=Z[t];if(r){var e=r.name+"";bl.call(fs,e)||(fs[e]=[]),fs[e].push({name:t,func:r})}}),fs[Qu(X,vn).name]=[{name:"wrapper",func:X}],Ct.prototype.clone=$t,Ct.prototype.reverse=Yt,Ct.prototype.value=Qt,Z.prototype.at=Qs,
Z.prototype.chain=tf,Z.prototype.commit=rf,Z.prototype.next=ef,Z.prototype.plant=of,Z.prototype.reverse=ff,Z.prototype.toJSON=Z.prototype.valueOf=Z.prototype.value=cf,Z.prototype.first=Z.prototype.head,Ul&&(Z.prototype[Ul]=uf),Z},be=de();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(re._=be,define(function(){return be})):ue?((ue.exports=be)._=be,ee._=be):re._=be}).call(this);
/**
 * Minified by jsDelivr 
 * Original file: /npm/moment@2.30.1/moment.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.moment=t()}(this,function(){"use strict";var H;function _(){return H.apply(null,arguments)}function y(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function F(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function c(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function L(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;for(var t in e)if(c(e,t))return;return 1}function g(e){return void 0===e}function w(e){return"number"==typeof e||"[object Number]"===Object.prototype.toString.call(e)}function V(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function G(e,t){for(var n=[],s=e.length,i=0;i<s;++i)n.push(t(e[i],i));return n}function E(e,t){for(var n in t)c(t,n)&&(e[n]=t[n]);return c(t,"toString")&&(e.toString=t.toString),c(t,"valueOf")&&(e.valueOf=t.valueOf),e}function l(e,t,n,s){return Wt(e,t,n,s,!0).utc()}function p(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function A(e){var t,n,s=e._d&&!isNaN(e._d.getTime());return s&&(t=p(e),n=j.call(t.parsedDateParts,function(e){return null!=e}),s=t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&n),e._strict)&&(s=s&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e)?s:(e._isValid=s,e._isValid)}function I(e){var t=l(NaN);return null!=e?E(p(t),e):p(t).userInvalidated=!0,t}var j=Array.prototype.some||function(e){for(var t=Object(this),n=t.length>>>0,s=0;s<n;s++)if(s in t&&e.call(this,t[s],s,t))return!0;return!1},Z=_.momentProperties=[],z=!1;function q(e,t){var n,s,i,r=Z.length;if(g(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),g(t._i)||(e._i=t._i),g(t._f)||(e._f=t._f),g(t._l)||(e._l=t._l),g(t._strict)||(e._strict=t._strict),g(t._tzm)||(e._tzm=t._tzm),g(t._isUTC)||(e._isUTC=t._isUTC),g(t._offset)||(e._offset=t._offset),g(t._pf)||(e._pf=p(t)),g(t._locale)||(e._locale=t._locale),0<r)for(n=0;n<r;n++)g(i=t[s=Z[n]])||(e[s]=i);return e}function $(e){q(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===z&&(z=!0,_.updateOffset(this),z=!1)}function k(e){return e instanceof $||null!=e&&null!=e._isAMomentObject}function B(e){!1===_.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function e(r,a){var o=!0;return E(function(){if(null!=_.deprecationHandler&&_.deprecationHandler(null,r),o){for(var e,t,n=[],s=arguments.length,i=0;i<s;i++){if(e="","object"==typeof arguments[i]){for(t in e+="\n["+i+"] ",arguments[0])c(arguments[0],t)&&(e+=t+": "+arguments[0][t]+", ");e=e.slice(0,-2)}else e=arguments[i];n.push(e)}B(r+"\nArguments: "+Array.prototype.slice.call(n).join("")+"\n"+(new Error).stack),o=!1}return a.apply(this,arguments)},a)}var J={};function Q(e,t){null!=_.deprecationHandler&&_.deprecationHandler(e,t),J[e]||(B(t),J[e]=!0)}function a(e){return"undefined"!=typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function X(e,t){var n,s=E({},e);for(n in t)c(t,n)&&(F(e[n])&&F(t[n])?(s[n]={},E(s[n],e[n]),E(s[n],t[n])):null!=t[n]?s[n]=t[n]:delete s[n]);for(n in e)c(e,n)&&!c(t,n)&&F(e[n])&&(s[n]=E({},s[n]));return s}function K(e){null!=e&&this.set(e)}_.suppressDeprecationWarnings=!1,_.deprecationHandler=null;var ee=Object.keys||function(e){var t,n=[];for(t in e)c(e,t)&&n.push(t);return n};function r(e,t,n){var s=""+Math.abs(e);return(0<=e?n?"+":"":"-")+Math.pow(10,Math.max(0,t-s.length)).toString().substr(1)+s}var te=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,ne=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,se={},ie={};function s(e,t,n,s){var i="string"==typeof s?function(){return this[s]()}:s;e&&(ie[e]=i),t&&(ie[t[0]]=function(){return r(i.apply(this,arguments),t[1],t[2])}),n&&(ie[n]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function re(e,t){return e.isValid()?(t=ae(t,e.localeData()),se[t]=se[t]||function(s){for(var e,i=s.match(te),t=0,r=i.length;t<r;t++)ie[i[t]]?i[t]=ie[i[t]]:i[t]=(e=i[t]).match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"");return function(e){for(var t="",n=0;n<r;n++)t+=a(i[n])?i[n].call(e,s):i[n];return t}}(t),se[t](e)):e.localeData().invalidDate()}function ae(e,t){var n=5;function s(e){return t.longDateFormat(e)||e}for(ne.lastIndex=0;0<=n&&ne.test(e);)e=e.replace(ne,s),ne.lastIndex=0,--n;return e}var oe={D:"date",dates:"date",date:"date",d:"day",days:"day",day:"day",e:"weekday",weekdays:"weekday",weekday:"weekday",E:"isoWeekday",isoweekdays:"isoWeekday",isoweekday:"isoWeekday",DDD:"dayOfYear",dayofyears:"dayOfYear",dayofyear:"dayOfYear",h:"hour",hours:"hour",hour:"hour",ms:"millisecond",milliseconds:"millisecond",millisecond:"millisecond",m:"minute",minutes:"minute",minute:"minute",M:"month",months:"month",month:"month",Q:"quarter",quarters:"quarter",quarter:"quarter",s:"second",seconds:"second",second:"second",gg:"weekYear",weekyears:"weekYear",weekyear:"weekYear",GG:"isoWeekYear",isoweekyears:"isoWeekYear",isoweekyear:"isoWeekYear",w:"week",weeks:"week",week:"week",W:"isoWeek",isoweeks:"isoWeek",isoweek:"isoWeek",y:"year",years:"year",year:"year"};function o(e){return"string"==typeof e?oe[e]||oe[e.toLowerCase()]:void 0}function ue(e){var t,n,s={};for(n in e)c(e,n)&&(t=o(n))&&(s[t]=e[n]);return s}var le={date:9,day:11,weekday:11,isoWeekday:11,dayOfYear:4,hour:13,millisecond:16,minute:14,month:8,quarter:7,second:15,weekYear:1,isoWeekYear:1,week:5,isoWeek:5,year:1};var de=/\d/,t=/\d\d/,he=/\d{3}/,ce=/\d{4}/,fe=/[+-]?\d{6}/,n=/\d\d?/,me=/\d\d\d\d?/,_e=/\d\d\d\d\d\d?/,ye=/\d{1,3}/,ge=/\d{1,4}/,we=/[+-]?\d{1,6}/,pe=/\d+/,ke=/[+-]?\d+/,Me=/Z|[+-]\d\d:?\d\d/gi,ve=/Z|[+-]\d\d(?::?\d\d)?/gi,i=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,u=/^[1-9]\d?/,d=/^([1-9]\d|\d)/;function h(e,n,s){Ye[e]=a(n)?n:function(e,t){return e&&s?s:n}}function De(e,t){return c(Ye,e)?Ye[e](t._strict,t._locale):new RegExp(f(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,s,i){return t||n||s||i})))}function f(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function m(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function M(e){var e=+e,t=0;return t=0!=e&&isFinite(e)?m(e):t}var Ye={},Se={};function v(e,n){var t,s,i=n;for("string"==typeof e&&(e=[e]),w(n)&&(i=function(e,t){t[n]=M(e)}),s=e.length,t=0;t<s;t++)Se[e[t]]=i}function Oe(e,i){v(e,function(e,t,n,s){n._w=n._w||{},i(e,n._w,n,s)})}function be(e){return e%4==0&&e%100!=0||e%400==0}var D=0,Y=1,S=2,O=3,b=4,T=5,Te=6,xe=7,Ne=8;function We(e){return be(e)?366:365}s("Y",0,0,function(){var e=this.year();return e<=9999?r(e,4):"+"+e}),s(0,["YY",2],0,function(){return this.year()%100}),s(0,["YYYY",4],0,"year"),s(0,["YYYYY",5],0,"year"),s(0,["YYYYYY",6,!0],0,"year"),h("Y",ke),h("YY",n,t),h("YYYY",ge,ce),h("YYYYY",we,fe),h("YYYYYY",we,fe),v(["YYYYY","YYYYYY"],D),v("YYYY",function(e,t){t[D]=2===e.length?_.parseTwoDigitYear(e):M(e)}),v("YY",function(e,t){t[D]=_.parseTwoDigitYear(e)}),v("Y",function(e,t){t[D]=parseInt(e,10)}),_.parseTwoDigitYear=function(e){return M(e)+(68<M(e)?1900:2e3)};var x,Pe=Re("FullYear",!0);function Re(t,n){return function(e){return null!=e?(Ue(this,t,e),_.updateOffset(this,n),this):Ce(this,t)}}function Ce(e,t){if(!e.isValid())return NaN;var n=e._d,s=e._isUTC;switch(t){case"Milliseconds":return s?n.getUTCMilliseconds():n.getMilliseconds();case"Seconds":return s?n.getUTCSeconds():n.getSeconds();case"Minutes":return s?n.getUTCMinutes():n.getMinutes();case"Hours":return s?n.getUTCHours():n.getHours();case"Date":return s?n.getUTCDate():n.getDate();case"Day":return s?n.getUTCDay():n.getDay();case"Month":return s?n.getUTCMonth():n.getMonth();case"FullYear":return s?n.getUTCFullYear():n.getFullYear();default:return NaN}}function Ue(e,t,n){var s,i,r;if(e.isValid()&&!isNaN(n)){switch(s=e._d,i=e._isUTC,t){case"Milliseconds":return i?s.setUTCMilliseconds(n):s.setMilliseconds(n);case"Seconds":return i?s.setUTCSeconds(n):s.setSeconds(n);case"Minutes":return i?s.setUTCMinutes(n):s.setMinutes(n);case"Hours":return i?s.setUTCHours(n):s.setHours(n);case"Date":return i?s.setUTCDate(n):s.setDate(n);case"FullYear":break;default:return}t=n,r=e.month(),e=29!==(e=e.date())||1!==r||be(t)?e:28,i?s.setUTCFullYear(t,r,e):s.setFullYear(t,r,e)}}function He(e,t){var n;return isNaN(e)||isNaN(t)?NaN:(n=(t%(n=12)+n)%n,e+=(t-n)/12,1==n?be(e)?29:28:31-n%7%2)}x=Array.prototype.indexOf||function(e){for(var t=0;t<this.length;++t)if(this[t]===e)return t;return-1},s("M",["MM",2],"Mo",function(){return this.month()+1}),s("MMM",0,0,function(e){return this.localeData().monthsShort(this,e)}),s("MMMM",0,0,function(e){return this.localeData().months(this,e)}),h("M",n,u),h("MM",n,t),h("MMM",function(e,t){return t.monthsShortRegex(e)}),h("MMMM",function(e,t){return t.monthsRegex(e)}),v(["M","MM"],function(e,t){t[Y]=M(e)-1}),v(["MMM","MMMM"],function(e,t,n,s){s=n._locale.monthsParse(e,s,n._strict);null!=s?t[Y]=s:p(n).invalidMonth=e});var Fe="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Le="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Ve=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Ge=i,Ee=i;function Ae(e,t){if(e.isValid()){if("string"==typeof t)if(/^\d+$/.test(t))t=M(t);else if(!w(t=e.localeData().monthsParse(t)))return;var n=(n=e.date())<29?n:Math.min(n,He(e.year(),t));e._isUTC?e._d.setUTCMonth(t,n):e._d.setMonth(t,n)}}function Ie(e){return null!=e?(Ae(this,e),_.updateOffset(this,!0),this):Ce(this,"Month")}function je(){function e(e,t){return t.length-e.length}for(var t,n,s=[],i=[],r=[],a=0;a<12;a++)n=l([2e3,a]),t=f(this.monthsShort(n,"")),n=f(this.months(n,"")),s.push(t),i.push(n),r.push(n),r.push(t);s.sort(e),i.sort(e),r.sort(e),this._monthsRegex=new RegExp("^("+r.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+s.join("|")+")","i")}function Ze(e,t,n,s,i,r,a){var o;return e<100&&0<=e?(o=new Date(e+400,t,n,s,i,r,a),isFinite(o.getFullYear())&&o.setFullYear(e)):o=new Date(e,t,n,s,i,r,a),o}function ze(e){var t;return e<100&&0<=e?((t=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,t)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function qe(e,t,n){n=7+t-n;return n-(7+ze(e,0,n).getUTCDay()-t)%7-1}function $e(e,t,n,s,i){var r,t=1+7*(t-1)+(7+n-s)%7+qe(e,s,i),n=t<=0?We(r=e-1)+t:t>We(e)?(r=e+1,t-We(e)):(r=e,t);return{year:r,dayOfYear:n}}function Be(e,t,n){var s,i,r=qe(e.year(),t,n),r=Math.floor((e.dayOfYear()-r-1)/7)+1;return r<1?s=r+N(i=e.year()-1,t,n):r>N(e.year(),t,n)?(s=r-N(e.year(),t,n),i=e.year()+1):(i=e.year(),s=r),{week:s,year:i}}function N(e,t,n){var s=qe(e,t,n),t=qe(e+1,t,n);return(We(e)-s+t)/7}s("w",["ww",2],"wo","week"),s("W",["WW",2],"Wo","isoWeek"),h("w",n,u),h("ww",n,t),h("W",n,u),h("WW",n,t),Oe(["w","ww","W","WW"],function(e,t,n,s){t[s.substr(0,1)]=M(e)});function Je(e,t){return e.slice(t,7).concat(e.slice(0,t))}s("d",0,"do","day"),s("dd",0,0,function(e){return this.localeData().weekdaysMin(this,e)}),s("ddd",0,0,function(e){return this.localeData().weekdaysShort(this,e)}),s("dddd",0,0,function(e){return this.localeData().weekdays(this,e)}),s("e",0,0,"weekday"),s("E",0,0,"isoWeekday"),h("d",n),h("e",n),h("E",n),h("dd",function(e,t){return t.weekdaysMinRegex(e)}),h("ddd",function(e,t){return t.weekdaysShortRegex(e)}),h("dddd",function(e,t){return t.weekdaysRegex(e)}),Oe(["dd","ddd","dddd"],function(e,t,n,s){s=n._locale.weekdaysParse(e,s,n._strict);null!=s?t.d=s:p(n).invalidWeekday=e}),Oe(["d","e","E"],function(e,t,n,s){t[s]=M(e)});var Qe="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Xe="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ke="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),et=i,tt=i,nt=i;function st(){function e(e,t){return t.length-e.length}for(var t,n,s,i=[],r=[],a=[],o=[],u=0;u<7;u++)s=l([2e3,1]).day(u),t=f(this.weekdaysMin(s,"")),n=f(this.weekdaysShort(s,"")),s=f(this.weekdays(s,"")),i.push(t),r.push(n),a.push(s),o.push(t),o.push(n),o.push(s);i.sort(e),r.sort(e),a.sort(e),o.sort(e),this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+a.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+i.join("|")+")","i")}function it(){return this.hours()%12||12}function rt(e,t){s(e,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)})}function at(e,t){return t._meridiemParse}s("H",["HH",2],0,"hour"),s("h",["hh",2],0,it),s("k",["kk",2],0,function(){return this.hours()||24}),s("hmm",0,0,function(){return""+it.apply(this)+r(this.minutes(),2)}),s("hmmss",0,0,function(){return""+it.apply(this)+r(this.minutes(),2)+r(this.seconds(),2)}),s("Hmm",0,0,function(){return""+this.hours()+r(this.minutes(),2)}),s("Hmmss",0,0,function(){return""+this.hours()+r(this.minutes(),2)+r(this.seconds(),2)}),rt("a",!0),rt("A",!1),h("a",at),h("A",at),h("H",n,d),h("h",n,u),h("k",n,u),h("HH",n,t),h("hh",n,t),h("kk",n,t),h("hmm",me),h("hmmss",_e),h("Hmm",me),h("Hmmss",_e),v(["H","HH"],O),v(["k","kk"],function(e,t,n){e=M(e);t[O]=24===e?0:e}),v(["a","A"],function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e}),v(["h","hh"],function(e,t,n){t[O]=M(e),p(n).bigHour=!0}),v("hmm",function(e,t,n){var s=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s)),p(n).bigHour=!0}),v("hmmss",function(e,t,n){var s=e.length-4,i=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s,2)),t[T]=M(e.substr(i)),p(n).bigHour=!0}),v("Hmm",function(e,t,n){var s=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s))}),v("Hmmss",function(e,t,n){var s=e.length-4,i=e.length-2;t[O]=M(e.substr(0,s)),t[b]=M(e.substr(s,2)),t[T]=M(e.substr(i))});i=Re("Hours",!0);var ot,ut={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Fe,monthsShort:Le,week:{dow:0,doy:6},weekdays:Qe,weekdaysMin:Ke,weekdaysShort:Xe,meridiemParse:/[ap]\.?m?\.?/i},W={},lt={};function dt(e){return e&&e.toLowerCase().replace("_","-")}function ht(e){for(var t,n,s,i,r=0;r<e.length;){for(t=(i=dt(e[r]).split("-")).length,n=(n=dt(e[r+1]))?n.split("-"):null;0<t;){if(s=ct(i.slice(0,t).join("-")))return s;if(n&&n.length>=t&&function(e,t){for(var n=Math.min(e.length,t.length),s=0;s<n;s+=1)if(e[s]!==t[s])return s;return n}(i,n)>=t-1)break;t--}r++}return ot}function ct(t){var e,n;if(void 0===W[t]&&"undefined"!=typeof module&&module&&module.exports&&(n=t)&&n.match("^[^/\\\\]*$"))try{e=ot._abbr,require("./locale/"+t),ft(e)}catch(e){W[t]=null}return W[t]}function ft(e,t){return e&&((t=g(t)?P(e):mt(e,t))?ot=t:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),ot._abbr}function mt(e,t){if(null===t)return delete W[e],null;var n,s=ut;if(t.abbr=e,null!=W[e])Q("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),s=W[e]._config;else if(null!=t.parentLocale)if(null!=W[t.parentLocale])s=W[t.parentLocale]._config;else{if(null==(n=ct(t.parentLocale)))return lt[t.parentLocale]||(lt[t.parentLocale]=[]),lt[t.parentLocale].push({name:e,config:t}),null;s=n._config}return W[e]=new K(X(s,t)),lt[e]&&lt[e].forEach(function(e){mt(e.name,e.config)}),ft(e),W[e]}function P(e){var t;if(!(e=e&&e._locale&&e._locale._abbr?e._locale._abbr:e))return ot;if(!y(e)){if(t=ct(e))return t;e=[e]}return ht(e)}function _t(e){var t=e._a;return t&&-2===p(e).overflow&&(t=t[Y]<0||11<t[Y]?Y:t[S]<1||t[S]>He(t[D],t[Y])?S:t[O]<0||24<t[O]||24===t[O]&&(0!==t[b]||0!==t[T]||0!==t[Te])?O:t[b]<0||59<t[b]?b:t[T]<0||59<t[T]?T:t[Te]<0||999<t[Te]?Te:-1,p(e)._overflowDayOfYear&&(t<D||S<t)&&(t=S),p(e)._overflowWeeks&&-1===t&&(t=xe),p(e)._overflowWeekday&&-1===t&&(t=Ne),p(e).overflow=t),e}var yt=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,gt=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,wt=/Z|[+-]\d\d(?::?\d\d)?/,pt=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],kt=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],Mt=/^\/?Date\((-?\d+)/i,vt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,Dt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function Yt(e){var t,n,s,i,r,a,o=e._i,u=yt.exec(o)||gt.exec(o),o=pt.length,l=kt.length;if(u){for(p(e).iso=!0,t=0,n=o;t<n;t++)if(pt[t][1].exec(u[1])){i=pt[t][0],s=!1!==pt[t][2];break}if(null==i)e._isValid=!1;else{if(u[3]){for(t=0,n=l;t<n;t++)if(kt[t][1].exec(u[3])){r=(u[2]||" ")+kt[t][0];break}if(null==r)return void(e._isValid=!1)}if(s||null==r){if(u[4]){if(!wt.exec(u[4]))return void(e._isValid=!1);a="Z"}e._f=i+(r||"")+(a||""),xt(e)}else e._isValid=!1}}else e._isValid=!1}function St(e,t,n,s,i,r){e=[function(e){e=parseInt(e,10);{if(e<=49)return 2e3+e;if(e<=999)return 1900+e}return e}(e),Le.indexOf(t),parseInt(n,10),parseInt(s,10),parseInt(i,10)];return r&&e.push(parseInt(r,10)),e}function Ot(e){var t,n,s=vt.exec(e._i.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));s?(t=St(s[4],s[3],s[2],s[5],s[6],s[7]),function(e,t,n){if(!e||Xe.indexOf(e)===new Date(t[0],t[1],t[2]).getDay())return 1;p(n).weekdayMismatch=!0,n._isValid=!1}(s[1],t,e)&&(e._a=t,e._tzm=(t=s[8],n=s[9],s=s[10],t?Dt[t]:n?0:60*(((t=parseInt(s,10))-(n=t%100))/100)+n),e._d=ze.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),p(e).rfc2822=!0)):e._isValid=!1}function bt(e,t,n){return null!=e?e:null!=t?t:n}function Tt(e){var t,n,s,i,r,a,o,u,l,d,h,c=[];if(!e._d){for(s=e,i=new Date(_.now()),n=s._useUTC?[i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()]:[i.getFullYear(),i.getMonth(),i.getDate()],e._w&&null==e._a[S]&&null==e._a[Y]&&(null!=(i=(s=e)._w).GG||null!=i.W||null!=i.E?(u=1,l=4,r=bt(i.GG,s._a[D],Be(R(),1,4).year),a=bt(i.W,1),((o=bt(i.E,1))<1||7<o)&&(d=!0)):(u=s._locale._week.dow,l=s._locale._week.doy,h=Be(R(),u,l),r=bt(i.gg,s._a[D],h.year),a=bt(i.w,h.week),null!=i.d?((o=i.d)<0||6<o)&&(d=!0):null!=i.e?(o=i.e+u,(i.e<0||6<i.e)&&(d=!0)):o=u),a<1||a>N(r,u,l)?p(s)._overflowWeeks=!0:null!=d?p(s)._overflowWeekday=!0:(h=$e(r,a,o,u,l),s._a[D]=h.year,s._dayOfYear=h.dayOfYear)),null!=e._dayOfYear&&(i=bt(e._a[D],n[D]),(e._dayOfYear>We(i)||0===e._dayOfYear)&&(p(e)._overflowDayOfYear=!0),d=ze(i,0,e._dayOfYear),e._a[Y]=d.getUTCMonth(),e._a[S]=d.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=c[t]=n[t];for(;t<7;t++)e._a[t]=c[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[O]&&0===e._a[b]&&0===e._a[T]&&0===e._a[Te]&&(e._nextDay=!0,e._a[O]=0),e._d=(e._useUTC?ze:Ze).apply(null,c),r=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[O]=24),e._w&&void 0!==e._w.d&&e._w.d!==r&&(p(e).weekdayMismatch=!0)}}function xt(e){if(e._f===_.ISO_8601)Yt(e);else if(e._f===_.RFC_2822)Ot(e);else{e._a=[],p(e).empty=!0;for(var t,n,s,i,r,a=""+e._i,o=a.length,u=0,l=ae(e._f,e._locale).match(te)||[],d=l.length,h=0;h<d;h++)n=l[h],(t=(a.match(De(n,e))||[])[0])&&(0<(s=a.substr(0,a.indexOf(t))).length&&p(e).unusedInput.push(s),a=a.slice(a.indexOf(t)+t.length),u+=t.length),ie[n]?(t?p(e).empty=!1:p(e).unusedTokens.push(n),s=n,r=e,null!=(i=t)&&c(Se,s)&&Se[s](i,r._a,r,s)):e._strict&&!t&&p(e).unusedTokens.push(n);p(e).charsLeftOver=o-u,0<a.length&&p(e).unusedInput.push(a),e._a[O]<=12&&!0===p(e).bigHour&&0<e._a[O]&&(p(e).bigHour=void 0),p(e).parsedDateParts=e._a.slice(0),p(e).meridiem=e._meridiem,e._a[O]=function(e,t,n){if(null==n)return t;return null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((e=e.isPM(n))&&t<12&&(t+=12),t=e||12!==t?t:0):t}(e._locale,e._a[O],e._meridiem),null!==(o=p(e).era)&&(e._a[D]=e._locale.erasConvertYear(o,e._a[D])),Tt(e),_t(e)}}function Nt(e){var t,n,s,i=e._i,r=e._f;if(e._locale=e._locale||P(e._l),null===i||void 0===r&&""===i)return I({nullInput:!0});if("string"==typeof i&&(e._i=i=e._locale.preparse(i)),k(i))return new $(_t(i));if(V(i))e._d=i;else if(y(r)){var a,o,u,l,d,h,c=e,f=!1,m=c._f.length;if(0===m)p(c).invalidFormat=!0,c._d=new Date(NaN);else{for(l=0;l<m;l++)d=0,h=!1,a=q({},c),null!=c._useUTC&&(a._useUTC=c._useUTC),a._f=c._f[l],xt(a),A(a)&&(h=!0),d=(d+=p(a).charsLeftOver)+10*p(a).unusedTokens.length,p(a).score=d,f?d<u&&(u=d,o=a):(null==u||d<u||h)&&(u=d,o=a,h)&&(f=!0);E(c,o||a)}}else if(r)xt(e);else if(g(r=(i=e)._i))i._d=new Date(_.now());else V(r)?i._d=new Date(r.valueOf()):"string"==typeof r?(n=i,null!==(t=Mt.exec(n._i))?n._d=new Date(+t[1]):(Yt(n),!1===n._isValid&&(delete n._isValid,Ot(n),!1===n._isValid)&&(delete n._isValid,n._strict?n._isValid=!1:_.createFromInputFallback(n)))):y(r)?(i._a=G(r.slice(0),function(e){return parseInt(e,10)}),Tt(i)):F(r)?(t=i)._d||(s=void 0===(n=ue(t._i)).day?n.date:n.day,t._a=G([n.year,n.month,s,n.hour,n.minute,n.second,n.millisecond],function(e){return e&&parseInt(e,10)}),Tt(t)):w(r)?i._d=new Date(r):_.createFromInputFallback(i);return A(e)||(e._d=null),e}function Wt(e,t,n,s,i){var r={};return!0!==t&&!1!==t||(s=t,t=void 0),!0!==n&&!1!==n||(s=n,n=void 0),(F(e)&&L(e)||y(e)&&0===e.length)&&(e=void 0),r._isAMomentObject=!0,r._useUTC=r._isUTC=i,r._l=n,r._i=e,r._f=t,r._strict=s,(i=new $(_t(Nt(i=r))))._nextDay&&(i.add(1,"d"),i._nextDay=void 0),i}function R(e,t,n,s){return Wt(e,t,n,s,!1)}_.createFromInputFallback=e("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))}),_.ISO_8601=function(){},_.RFC_2822=function(){};me=e("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=R.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:I()}),_e=e("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var e=R.apply(null,arguments);return this.isValid()&&e.isValid()?this<e?this:e:I()});function Pt(e,t){var n,s;if(!(t=1===t.length&&y(t[0])?t[0]:t).length)return R();for(n=t[0],s=1;s<t.length;++s)t[s].isValid()&&!t[s][e](n)||(n=t[s]);return n}var Rt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Ct(e){var e=ue(e),t=e.year||0,n=e.quarter||0,s=e.month||0,i=e.week||e.isoWeek||0,r=e.day||0,a=e.hour||0,o=e.minute||0,u=e.second||0,l=e.millisecond||0;this._isValid=function(e){var t,n,s=!1,i=Rt.length;for(t in e)if(c(e,t)&&(-1===x.call(Rt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<i;++n)if(e[Rt[n]]){if(s)return!1;parseFloat(e[Rt[n]])!==M(e[Rt[n]])&&(s=!0)}return!0}(e),this._milliseconds=+l+1e3*u+6e4*o+1e3*a*60*60,this._days=+r+7*i,this._months=+s+3*n+12*t,this._data={},this._locale=P(),this._bubble()}function Ut(e){return e instanceof Ct}function Ht(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function Ft(e,n){s(e,0,0,function(){var e=this.utcOffset(),t="+";return e<0&&(e=-e,t="-"),t+r(~~(e/60),2)+n+r(~~e%60,2)})}Ft("Z",":"),Ft("ZZ",""),h("Z",ve),h("ZZ",ve),v(["Z","ZZ"],function(e,t,n){n._useUTC=!0,n._tzm=Vt(ve,e)});var Lt=/([\+\-]|\d\d)/gi;function Vt(e,t){var t=(t||"").match(e);return null===t?null:0===(t=60*(e=((t[t.length-1]||[])+"").match(Lt)||["-",0,0])[1]+M(e[2]))?0:"+"===e[0]?t:-t}function Gt(e,t){var n;return t._isUTC?(t=t.clone(),n=(k(e)||V(e)?e:R(e)).valueOf()-t.valueOf(),t._d.setTime(t._d.valueOf()+n),_.updateOffset(t,!1),t):R(e).local()}function Et(e){return-Math.round(e._d.getTimezoneOffset())}function At(){return!!this.isValid()&&this._isUTC&&0===this._offset}_.updateOffset=function(){};var It=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,jt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function C(e,t){var n,s=e;return Ut(e)?s={ms:e._milliseconds,d:e._days,M:e._months}:w(e)||!isNaN(+e)?(s={},t?s[t]=+e:s.milliseconds=+e):(t=It.exec(e))?(n="-"===t[1]?-1:1,s={y:0,d:M(t[S])*n,h:M(t[O])*n,m:M(t[b])*n,s:M(t[T])*n,ms:M(Ht(1e3*t[Te]))*n}):(t=jt.exec(e))?(n="-"===t[1]?-1:1,s={y:Zt(t[2],n),M:Zt(t[3],n),w:Zt(t[4],n),d:Zt(t[5],n),h:Zt(t[6],n),m:Zt(t[7],n),s:Zt(t[8],n)}):null==s?s={}:"object"==typeof s&&("from"in s||"to"in s)&&(t=function(e,t){var n;if(!e.isValid()||!t.isValid())return{milliseconds:0,months:0};t=Gt(t,e),e.isBefore(t)?n=zt(e,t):((n=zt(t,e)).milliseconds=-n.milliseconds,n.months=-n.months);return n}(R(s.from),R(s.to)),(s={}).ms=t.milliseconds,s.M=t.months),n=new Ct(s),Ut(e)&&c(e,"_locale")&&(n._locale=e._locale),Ut(e)&&c(e,"_isValid")&&(n._isValid=e._isValid),n}function Zt(e,t){e=e&&parseFloat(e.replace(",","."));return(isNaN(e)?0:e)*t}function zt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function qt(s,i){return function(e,t){var n;return null===t||isNaN(+t)||(Q(i,"moment()."+i+"(period, number) is deprecated. Please use moment()."+i+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),n=e,e=t,t=n),$t(this,C(e,t),s),this}}function $t(e,t,n,s){var i=t._milliseconds,r=Ht(t._days),t=Ht(t._months);e.isValid()&&(s=null==s||s,t&&Ae(e,Ce(e,"Month")+t*n),r&&Ue(e,"Date",Ce(e,"Date")+r*n),i&&e._d.setTime(e._d.valueOf()+i*n),s)&&_.updateOffset(e,r||t)}C.fn=Ct.prototype,C.invalid=function(){return C(NaN)};Fe=qt(1,"add"),Qe=qt(-1,"subtract");function Bt(e){return"string"==typeof e||e instanceof String}function Jt(e){return k(e)||V(e)||Bt(e)||w(e)||function(t){var e=y(t),n=!1;e&&(n=0===t.filter(function(e){return!w(e)&&Bt(t)}).length);return e&&n}(e)||function(e){var t,n,s=F(e)&&!L(e),i=!1,r=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"],a=r.length;for(t=0;t<a;t+=1)n=r[t],i=i||c(e,n);return s&&i}(e)||null==e}function Qt(e,t){var n,s;return e.date()<t.date()?-Qt(t,e):-((n=12*(t.year()-e.year())+(t.month()-e.month()))+(t-(s=e.clone().add(n,"months"))<0?(t-s)/(s-e.clone().add(n-1,"months")):(t-s)/(e.clone().add(1+n,"months")-s)))||0}function Xt(e){return void 0===e?this._locale._abbr:(null!=(e=P(e))&&(this._locale=e),this)}_.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",_.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";Ke=e("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(e){return void 0===e?this.localeData():this.locale(e)});function Kt(){return this._locale}var en=126227808e5;function tn(e,t){return(e%t+t)%t}function nn(e,t,n){return e<100&&0<=e?new Date(e+400,t,n)-en:new Date(e,t,n).valueOf()}function sn(e,t,n){return e<100&&0<=e?Date.UTC(e+400,t,n)-en:Date.UTC(e,t,n)}function rn(e,t){return t.erasAbbrRegex(e)}function an(){for(var e,t,n,s=[],i=[],r=[],a=[],o=this.eras(),u=0,l=o.length;u<l;++u)e=f(o[u].name),t=f(o[u].abbr),n=f(o[u].narrow),i.push(e),s.push(t),r.push(n),a.push(e),a.push(t),a.push(n);this._erasRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+i.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+s.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+r.join("|")+")","i")}function on(e,t){s(0,[e,e.length],0,t)}function un(e,t,n,s,i){var r;return null==e?Be(this,s,i).year:(r=N(e,s,i),function(e,t,n,s,i){e=$e(e,t,n,s,i),t=ze(e.year,0,e.dayOfYear);return this.year(t.getUTCFullYear()),this.month(t.getUTCMonth()),this.date(t.getUTCDate()),this}.call(this,e,t=r<t?r:t,n,s,i))}s("N",0,0,"eraAbbr"),s("NN",0,0,"eraAbbr"),s("NNN",0,0,"eraAbbr"),s("NNNN",0,0,"eraName"),s("NNNNN",0,0,"eraNarrow"),s("y",["y",1],"yo","eraYear"),s("y",["yy",2],0,"eraYear"),s("y",["yyy",3],0,"eraYear"),s("y",["yyyy",4],0,"eraYear"),h("N",rn),h("NN",rn),h("NNN",rn),h("NNNN",function(e,t){return t.erasNameRegex(e)}),h("NNNNN",function(e,t){return t.erasNarrowRegex(e)}),v(["N","NN","NNN","NNNN","NNNNN"],function(e,t,n,s){s=n._locale.erasParse(e,s,n._strict);s?p(n).era=s:p(n).invalidEra=e}),h("y",pe),h("yy",pe),h("yyy",pe),h("yyyy",pe),h("yo",function(e,t){return t._eraYearOrdinalRegex||pe}),v(["y","yy","yyy","yyyy"],D),v(["yo"],function(e,t,n,s){var i;n._locale._eraYearOrdinalRegex&&(i=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[D]=n._locale.eraYearOrdinalParse(e,i):t[D]=parseInt(e,10)}),s(0,["gg",2],0,function(){return this.weekYear()%100}),s(0,["GG",2],0,function(){return this.isoWeekYear()%100}),on("gggg","weekYear"),on("ggggg","weekYear"),on("GGGG","isoWeekYear"),on("GGGGG","isoWeekYear"),h("G",ke),h("g",ke),h("GG",n,t),h("gg",n,t),h("GGGG",ge,ce),h("gggg",ge,ce),h("GGGGG",we,fe),h("ggggg",we,fe),Oe(["gggg","ggggg","GGGG","GGGGG"],function(e,t,n,s){t[s.substr(0,2)]=M(e)}),Oe(["gg","GG"],function(e,t,n,s){t[s]=_.parseTwoDigitYear(e)}),s("Q",0,"Qo","quarter"),h("Q",de),v("Q",function(e,t){t[Y]=3*(M(e)-1)}),s("D",["DD",2],"Do","date"),h("D",n,u),h("DD",n,t),h("Do",function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient}),v(["D","DD"],S),v("Do",function(e,t){t[S]=M(e.match(n)[0])});ge=Re("Date",!0);s("DDD",["DDDD",3],"DDDo","dayOfYear"),h("DDD",ye),h("DDDD",he),v(["DDD","DDDD"],function(e,t,n){n._dayOfYear=M(e)}),s("m",["mm",2],0,"minute"),h("m",n,d),h("mm",n,t),v(["m","mm"],b);var ln,ce=Re("Minutes",!1),we=(s("s",["ss",2],0,"second"),h("s",n,d),h("ss",n,t),v(["s","ss"],T),Re("Seconds",!1));for(s("S",0,0,function(){return~~(this.millisecond()/100)}),s(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),s(0,["SSS",3],0,"millisecond"),s(0,["SSSS",4],0,function(){return 10*this.millisecond()}),s(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),s(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),s(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),s(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),s(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),h("S",ye,de),h("SS",ye,t),h("SSS",ye,he),ln="SSSS";ln.length<=9;ln+="S")h(ln,pe);function dn(e,t){t[Te]=M(1e3*("0."+e))}for(ln="S";ln.length<=9;ln+="S")v(ln,dn);fe=Re("Milliseconds",!1),s("z",0,0,"zoneAbbr"),s("zz",0,0,"zoneName");u=$.prototype;function hn(e){return e}u.add=Fe,u.calendar=function(e,t){1===arguments.length&&(arguments[0]?Jt(arguments[0])?(e=arguments[0],t=void 0):function(e){for(var t=F(e)&&!L(e),n=!1,s=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"],i=0;i<s.length;i+=1)n=n||c(e,s[i]);return t&&n}(arguments[0])&&(t=arguments[0],e=void 0):t=e=void 0);var e=e||R(),n=Gt(e,this).startOf("day"),n=_.calendarFormat(this,n)||"sameElse",t=t&&(a(t[n])?t[n].call(this,e):t[n]);return this.format(t||this.localeData().calendar(n,this,R(e)))},u.clone=function(){return new $(this)},u.diff=function(e,t,n){var s,i,r;if(!this.isValid())return NaN;if(!(s=Gt(e,this)).isValid())return NaN;switch(i=6e4*(s.utcOffset()-this.utcOffset()),t=o(t)){case"year":r=Qt(this,s)/12;break;case"month":r=Qt(this,s);break;case"quarter":r=Qt(this,s)/3;break;case"second":r=(this-s)/1e3;break;case"minute":r=(this-s)/6e4;break;case"hour":r=(this-s)/36e5;break;case"day":r=(this-s-i)/864e5;break;case"week":r=(this-s-i)/6048e5;break;default:r=this-s}return n?r:m(r)},u.endOf=function(e){var t,n;if(void 0!==(e=o(e))&&"millisecond"!==e&&this.isValid()){switch(n=this._isUTC?sn:nn,e){case"year":t=n(this.year()+1,0,1)-1;break;case"quarter":t=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=n(this.year(),this.month()+1,1)-1;break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-tn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-tn(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-tn(t,1e3)-1;break}this._d.setTime(t),_.updateOffset(this,!0)}return this},u.format=function(e){return e=e||(this.isUtc()?_.defaultFormatUtc:_.defaultFormat),e=re(this,e),this.localeData().postformat(e)},u.from=function(e,t){return this.isValid()&&(k(e)&&e.isValid()||R(e).isValid())?C({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},u.fromNow=function(e){return this.from(R(),e)},u.to=function(e,t){return this.isValid()&&(k(e)&&e.isValid()||R(e).isValid())?C({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},u.toNow=function(e){return this.to(R(),e)},u.get=function(e){return a(this[e=o(e)])?this[e]():this},u.invalidAt=function(){return p(this).overflow},u.isAfter=function(e,t){return e=k(e)?e:R(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()>e.valueOf():e.valueOf()<this.clone().startOf(t).valueOf())},u.isBefore=function(e,t){return e=k(e)?e:R(e),!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()<e.valueOf():this.clone().endOf(t).valueOf()<e.valueOf())},u.isBetween=function(e,t,n,s){return e=k(e)?e:R(e),t=k(t)?t:R(t),!!(this.isValid()&&e.isValid()&&t.isValid())&&("("===(s=s||"()")[0]?this.isAfter(e,n):!this.isBefore(e,n))&&(")"===s[1]?this.isBefore(t,n):!this.isAfter(t,n))},u.isSame=function(e,t){var e=k(e)?e:R(e);return!(!this.isValid()||!e.isValid())&&("millisecond"===(t=o(t)||"millisecond")?this.valueOf()===e.valueOf():(e=e.valueOf(),this.clone().startOf(t).valueOf()<=e&&e<=this.clone().endOf(t).valueOf()))},u.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},u.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},u.isValid=function(){return A(this)},u.lang=Ke,u.locale=Xt,u.localeData=Kt,u.max=_e,u.min=me,u.parsingFlags=function(){return E({},p(this))},u.set=function(e,t){if("object"==typeof e)for(var n=function(e){var t,n=[];for(t in e)c(e,t)&&n.push({unit:t,priority:le[t]});return n.sort(function(e,t){return e.priority-t.priority}),n}(e=ue(e)),s=n.length,i=0;i<s;i++)this[n[i].unit](e[n[i].unit]);else if(a(this[e=o(e)]))return this[e](t);return this},u.startOf=function(e){var t,n;if(void 0!==(e=o(e))&&"millisecond"!==e&&this.isValid()){switch(n=this._isUTC?sn:nn,e){case"year":t=n(this.year(),0,1);break;case"quarter":t=n(this.year(),this.month()-this.month()%3,1);break;case"month":t=n(this.year(),this.month(),1);break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=n(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=tn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=tn(t,6e4);break;case"second":t=this._d.valueOf(),t-=tn(t,1e3);break}this._d.setTime(t),_.updateOffset(this,!0)}return this},u.subtract=Qe,u.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},u.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},u.toDate=function(){return new Date(this.valueOf())},u.toISOString=function(e){var t;return this.isValid()?(t=(e=!0!==e)?this.clone().utc():this).year()<0||9999<t.year()?re(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):a(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",re(t,"Z")):re(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ"):null},u.inspect=function(){var e,t,n;return this.isValid()?(t="moment",e="",this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z"),t="["+t+'("]',n=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",this.format(t+n+"-MM-DD[T]HH:mm:ss.SSS"+(e+'[")]'))):"moment.invalid(/* "+this._i+" */)"},"undefined"!=typeof Symbol&&null!=Symbol.for&&(u[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),u.toJSON=function(){return this.isValid()?this.toISOString():null},u.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},u.unix=function(){return Math.floor(this.valueOf()/1e3)},u.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},u.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},u.eraName=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].name;if(t[n].until<=e&&e<=t[n].since)return t[n].name}return""},u.eraNarrow=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].narrow;if(t[n].until<=e&&e<=t[n].since)return t[n].narrow}return""},u.eraAbbr=function(){for(var e,t=this.localeData().eras(),n=0,s=t.length;n<s;++n){if(e=this.clone().startOf("day").valueOf(),t[n].since<=e&&e<=t[n].until)return t[n].abbr;if(t[n].until<=e&&e<=t[n].since)return t[n].abbr}return""},u.eraYear=function(){for(var e,t,n=this.localeData().eras(),s=0,i=n.length;s<i;++s)if(e=n[s].since<=n[s].until?1:-1,t=this.clone().startOf("day").valueOf(),n[s].since<=t&&t<=n[s].until||n[s].until<=t&&t<=n[s].since)return(this.year()-_(n[s].since).year())*e+n[s].offset;return this.year()},u.year=Pe,u.isLeapYear=function(){return be(this.year())},u.weekYear=function(e){return un.call(this,e,this.week(),this.weekday()+this.localeData()._week.dow,this.localeData()._week.dow,this.localeData()._week.doy)},u.isoWeekYear=function(e){return un.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},u.quarter=u.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},u.month=Ie,u.daysInMonth=function(){return He(this.year(),this.month())},u.week=u.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},u.isoWeek=u.isoWeeks=function(e){var t=Be(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},u.weeksInYear=function(){var e=this.localeData()._week;return N(this.year(),e.dow,e.doy)},u.weeksInWeekYear=function(){var e=this.localeData()._week;return N(this.weekYear(),e.dow,e.doy)},u.isoWeeksInYear=function(){return N(this.year(),1,4)},u.isoWeeksInISOWeekYear=function(){return N(this.isoWeekYear(),1,4)},u.date=ge,u.day=u.days=function(e){var t,n,s;return this.isValid()?(t=Ce(this,"Day"),null!=e?(n=e,s=this.localeData(),e="string"!=typeof n?n:isNaN(n)?"number"==typeof(n=s.weekdaysParse(n))?n:null:parseInt(n,10),this.add(e-t,"d")):t):null!=e?this:NaN},u.weekday=function(e){var t;return this.isValid()?(t=(this.day()+7-this.localeData()._week.dow)%7,null==e?t:this.add(e-t,"d")):null!=e?this:NaN},u.isoWeekday=function(e){var t,n;return this.isValid()?null!=e?(t=e,n=this.localeData(),n="string"==typeof t?n.weekdaysParse(t)%7||7:isNaN(t)?null:t,this.day(this.day()%7?n:n-7)):this.day()||7:null!=e?this:NaN},u.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},u.hour=u.hours=i,u.minute=u.minutes=ce,u.second=u.seconds=we,u.millisecond=u.milliseconds=fe,u.utcOffset=function(e,t,n){var s,i=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null==e)return this._isUTC?i:Et(this);if("string"==typeof e){if(null===(e=Vt(ve,e)))return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&t&&(s=Et(this)),this._offset=e,this._isUTC=!0,null!=s&&this.add(s,"m"),i!==e&&(!t||this._changeInProgress?$t(this,C(e-i,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,_.updateOffset(this,!0),this._changeInProgress=null)),this},u.utc=function(e){return this.utcOffset(0,e)},u.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e)&&this.subtract(Et(this),"m"),this},u.parseZone=function(){var e;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(e=Vt(Me,this._i))?this.utcOffset(e):this.utcOffset(0,!0)),this},u.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?R(e).utcOffset():0,(this.utcOffset()-e)%60==0)},u.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},u.isLocal=function(){return!!this.isValid()&&!this._isUTC},u.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},u.isUtc=At,u.isUTC=At,u.zoneAbbr=function(){return this._isUTC?"UTC":""},u.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},u.dates=e("dates accessor is deprecated. Use date instead.",ge),u.months=e("months accessor is deprecated. Use month instead",Ie),u.years=e("years accessor is deprecated. Use year instead",Pe),u.zone=e("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(e,t){return null!=e?(this.utcOffset(e="string"!=typeof e?-e:e,t),this):-this.utcOffset()}),u.isDSTShifted=e("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){var e,t;return g(this._isDSTShifted)&&(q(e={},this),(e=Nt(e))._a?(t=(e._isUTC?l:R)(e._a),this._isDSTShifted=this.isValid()&&0<function(e,t,n){for(var s=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),r=0,a=0;a<s;a++)(n&&e[a]!==t[a]||!n&&M(e[a])!==M(t[a]))&&r++;return r+i}(e._a,t.toArray())):this._isDSTShifted=!1),this._isDSTShifted});d=K.prototype;function cn(e,t,n,s){var i=P(),s=l().set(s,t);return i[n](s,e)}function fn(e,t,n){if(w(e)&&(t=e,e=void 0),e=e||"",null!=t)return cn(e,t,n,"month");for(var s=[],i=0;i<12;i++)s[i]=cn(e,i,n,"month");return s}function mn(e,t,n,s){t=("boolean"==typeof e?w(t)&&(n=t,t=void 0):(t=e,e=!1,w(n=t)&&(n=t,t=void 0)),t||"");var i,r=P(),a=e?r._week.dow:0,o=[];if(null!=n)return cn(t,(n+a)%7,s,"day");for(i=0;i<7;i++)o[i]=cn(t,(i+a)%7,s,"day");return o}d.calendar=function(e,t,n){return a(e=this._calendar[e]||this._calendar.sameElse)?e.call(t,n):e},d.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(te).map(function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e}).join(""),this._longDateFormat[e])},d.invalidDate=function(){return this._invalidDate},d.ordinal=function(e){return this._ordinal.replace("%d",e)},d.preparse=hn,d.postformat=hn,d.relativeTime=function(e,t,n,s){var i=this._relativeTime[n];return a(i)?i(e,t,n,s):i.replace(/%d/i,e)},d.pastFuture=function(e,t){return a(e=this._relativeTime[0<e?"future":"past"])?e(t):e.replace(/%s/i,t)},d.set=function(e){var t,n;for(n in e)c(e,n)&&(a(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},d.eras=function(e,t){for(var n,s=this._eras||P("en")._eras,i=0,r=s.length;i<r;++i){switch(typeof s[i].since){case"string":n=_(s[i].since).startOf("day"),s[i].since=n.valueOf();break}switch(typeof s[i].until){case"undefined":s[i].until=1/0;break;case"string":n=_(s[i].until).startOf("day").valueOf(),s[i].until=n.valueOf();break}}return s},d.erasParse=function(e,t,n){var s,i,r,a,o,u=this.eras();for(e=e.toUpperCase(),s=0,i=u.length;s<i;++s)if(r=u[s].name.toUpperCase(),a=u[s].abbr.toUpperCase(),o=u[s].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(a===e)return u[s];break;case"NNNN":if(r===e)return u[s];break;case"NNNNN":if(o===e)return u[s];break}else if(0<=[r,a,o].indexOf(e))return u[s]},d.erasConvertYear=function(e,t){var n=e.since<=e.until?1:-1;return void 0===t?_(e.since).year():_(e.since).year()+(t-e.offset)*n},d.erasAbbrRegex=function(e){return c(this,"_erasAbbrRegex")||an.call(this),e?this._erasAbbrRegex:this._erasRegex},d.erasNameRegex=function(e){return c(this,"_erasNameRegex")||an.call(this),e?this._erasNameRegex:this._erasRegex},d.erasNarrowRegex=function(e){return c(this,"_erasNarrowRegex")||an.call(this),e?this._erasNarrowRegex:this._erasRegex},d.months=function(e,t){return e?(y(this._months)?this._months:this._months[(this._months.isFormat||Ve).test(t)?"format":"standalone"])[e.month()]:y(this._months)?this._months:this._months.standalone},d.monthsShort=function(e,t){return e?(y(this._monthsShort)?this._monthsShort:this._monthsShort[Ve.test(t)?"format":"standalone"])[e.month()]:y(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},d.monthsParse=function(e,t,n){var s,i;if(this._monthsParseExact)return function(e,t,n){var s,i,r,e=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],s=0;s<12;++s)r=l([2e3,s]),this._shortMonthsParse[s]=this.monthsShort(r,"").toLocaleLowerCase(),this._longMonthsParse[s]=this.months(r,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(i=x.call(this._shortMonthsParse,e))?i:null:-1!==(i=x.call(this._longMonthsParse,e))?i:null:"MMM"===t?-1!==(i=x.call(this._shortMonthsParse,e))||-1!==(i=x.call(this._longMonthsParse,e))?i:null:-1!==(i=x.call(this._longMonthsParse,e))||-1!==(i=x.call(this._shortMonthsParse,e))?i:null}.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),s=0;s<12;s++){if(i=l([2e3,s]),n&&!this._longMonthsParse[s]&&(this._longMonthsParse[s]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[s]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),n||this._monthsParse[s]||(i="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[s]=new RegExp(i.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[s].test(e))return s;if(n&&"MMM"===t&&this._shortMonthsParse[s].test(e))return s;if(!n&&this._monthsParse[s].test(e))return s}},d.monthsRegex=function(e){return this._monthsParseExact?(c(this,"_monthsRegex")||je.call(this),e?this._monthsStrictRegex:this._monthsRegex):(c(this,"_monthsRegex")||(this._monthsRegex=Ee),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},d.monthsShortRegex=function(e){return this._monthsParseExact?(c(this,"_monthsRegex")||je.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(c(this,"_monthsShortRegex")||(this._monthsShortRegex=Ge),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},d.week=function(e){return Be(e,this._week.dow,this._week.doy).week},d.firstDayOfYear=function(){return this._week.doy},d.firstDayOfWeek=function(){return this._week.dow},d.weekdays=function(e,t){return t=y(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"],!0===e?Je(t,this._week.dow):e?t[e.day()]:t},d.weekdaysMin=function(e){return!0===e?Je(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},d.weekdaysShort=function(e){return!0===e?Je(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},d.weekdaysParse=function(e,t,n){var s,i;if(this._weekdaysParseExact)return function(e,t,n){var s,i,r,e=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],s=0;s<7;++s)r=l([2e3,1]).day(s),this._minWeekdaysParse[s]=this.weekdaysMin(r,"").toLocaleLowerCase(),this._shortWeekdaysParse[s]=this.weekdaysShort(r,"").toLocaleLowerCase(),this._weekdaysParse[s]=this.weekdays(r,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(i=x.call(this._weekdaysParse,e))?i:null:"ddd"===t?-1!==(i=x.call(this._shortWeekdaysParse,e))?i:null:-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:"dddd"===t?-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._shortWeekdaysParse,e))||-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:"ddd"===t?-1!==(i=x.call(this._shortWeekdaysParse,e))||-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._minWeekdaysParse,e))?i:null:-1!==(i=x.call(this._minWeekdaysParse,e))||-1!==(i=x.call(this._weekdaysParse,e))||-1!==(i=x.call(this._shortWeekdaysParse,e))?i:null}.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),s=0;s<7;s++){if(i=l([2e3,1]).day(s),n&&!this._fullWeekdaysParse[s]&&(this._fullWeekdaysParse[s]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[s]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[s]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[s]||(i="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[s]=new RegExp(i.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[s].test(e))return s;if(n&&"ddd"===t&&this._shortWeekdaysParse[s].test(e))return s;if(n&&"dd"===t&&this._minWeekdaysParse[s].test(e))return s;if(!n&&this._weekdaysParse[s].test(e))return s}},d.weekdaysRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(c(this,"_weekdaysRegex")||(this._weekdaysRegex=et),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},d.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(c(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=tt),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},d.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(c(this,"_weekdaysRegex")||st.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(c(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=nt),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},d.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},d.meridiem=function(e,t,n){return 11<e?n?"pm":"PM":n?"am":"AM"},ft("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===M(e%100/10)?"th":1==t?"st":2==t?"nd":3==t?"rd":"th")}}),_.lang=e("moment.lang is deprecated. Use moment.locale instead.",ft),_.langData=e("moment.langData is deprecated. Use moment.localeData instead.",P);var _n=Math.abs;function yn(e,t,n,s){t=C(t,n);return e._milliseconds+=s*t._milliseconds,e._days+=s*t._days,e._months+=s*t._months,e._bubble()}function gn(e){return e<0?Math.floor(e):Math.ceil(e)}function wn(e){return 4800*e/146097}function pn(e){return 146097*e/4800}function kn(e){return function(){return this.as(e)}}de=kn("ms"),t=kn("s"),ye=kn("m"),he=kn("h"),Fe=kn("d"),_e=kn("w"),me=kn("M"),Qe=kn("Q"),i=kn("y"),ce=de;function Mn(e){return function(){return this.isValid()?this._data[e]:NaN}}var we=Mn("milliseconds"),fe=Mn("seconds"),ge=Mn("minutes"),Pe=Mn("hours"),d=Mn("days"),vn=Mn("months"),Dn=Mn("years");var Yn=Math.round,Sn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function On(e,t,n,s){var i=C(e).abs(),r=Yn(i.as("s")),a=Yn(i.as("m")),o=Yn(i.as("h")),u=Yn(i.as("d")),l=Yn(i.as("M")),d=Yn(i.as("w")),i=Yn(i.as("y")),r=(r<=n.ss?["s",r]:r<n.s&&["ss",r])||(a<=1?["m"]:a<n.m&&["mm",a])||(o<=1?["h"]:o<n.h&&["hh",o])||(u<=1?["d"]:u<n.d&&["dd",u]);return(r=(r=null!=n.w?r||(d<=1?["w"]:d<n.w&&["ww",d]):r)||(l<=1?["M"]:l<n.M&&["MM",l])||(i<=1?["y"]:["yy",i]))[2]=t,r[3]=0<+e,r[4]=s,function(e,t,n,s,i){return i.relativeTime(t||1,!!n,e,s)}.apply(null,r)}var bn=Math.abs;function Tn(e){return(0<e)-(e<0)||+e}function xn(){var e,t,n,s,i,r,a,o,u,l,d;return this.isValid()?(e=bn(this._milliseconds)/1e3,t=bn(this._days),n=bn(this._months),(o=this.asSeconds())?(s=m(e/60),i=m(s/60),e%=60,s%=60,r=m(n/12),n%=12,a=e?e.toFixed(3).replace(/\.?0+$/,""):"",u=Tn(this._months)!==Tn(o)?"-":"",l=Tn(this._days)!==Tn(o)?"-":"",d=Tn(this._milliseconds)!==Tn(o)?"-":"",(o<0?"-":"")+"P"+(r?u+r+"Y":"")+(n?u+n+"M":"")+(t?l+t+"D":"")+(i||s||e?"T":"")+(i?d+i+"H":"")+(s?d+s+"M":"")+(e?d+a+"S":"")):"P0D"):this.localeData().invalidDate()}var U=Ct.prototype;return U.isValid=function(){return this._isValid},U.abs=function(){var e=this._data;return this._milliseconds=_n(this._milliseconds),this._days=_n(this._days),this._months=_n(this._months),e.milliseconds=_n(e.milliseconds),e.seconds=_n(e.seconds),e.minutes=_n(e.minutes),e.hours=_n(e.hours),e.months=_n(e.months),e.years=_n(e.years),this},U.add=function(e,t){return yn(this,e,t,1)},U.subtract=function(e,t){return yn(this,e,t,-1)},U.as=function(e){if(!this.isValid())return NaN;var t,n,s=this._milliseconds;if("month"===(e=o(e))||"quarter"===e||"year"===e)switch(t=this._days+s/864e5,n=this._months+wn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(pn(this._months)),e){case"week":return t/7+s/6048e5;case"day":return t+s/864e5;case"hour":return 24*t+s/36e5;case"minute":return 1440*t+s/6e4;case"second":return 86400*t+s/1e3;case"millisecond":return Math.floor(864e5*t)+s;default:throw new Error("Unknown unit "+e)}},U.asMilliseconds=de,U.asSeconds=t,U.asMinutes=ye,U.asHours=he,U.asDays=Fe,U.asWeeks=_e,U.asMonths=me,U.asQuarters=Qe,U.asYears=i,U.valueOf=ce,U._bubble=function(){var e=this._milliseconds,t=this._days,n=this._months,s=this._data;return 0<=e&&0<=t&&0<=n||e<=0&&t<=0&&n<=0||(e+=864e5*gn(pn(n)+t),n=t=0),s.milliseconds=e%1e3,e=m(e/1e3),s.seconds=e%60,e=m(e/60),s.minutes=e%60,e=m(e/60),s.hours=e%24,t+=m(e/24),n+=e=m(wn(t)),t-=gn(pn(e)),e=m(n/12),n%=12,s.days=t,s.months=n,s.years=e,this},U.clone=function(){return C(this)},U.get=function(e){return e=o(e),this.isValid()?this[e+"s"]():NaN},U.milliseconds=we,U.seconds=fe,U.minutes=ge,U.hours=Pe,U.days=d,U.weeks=function(){return m(this.days()/7)},U.months=vn,U.years=Dn,U.humanize=function(e,t){var n,s;return this.isValid()?(n=!1,s=Sn,"object"==typeof e&&(t=e,e=!1),"boolean"==typeof e&&(n=e),"object"==typeof t&&(s=Object.assign({},Sn,t),null!=t.s)&&null==t.ss&&(s.ss=t.s-1),e=this.localeData(),t=On(this,!n,s,e),n&&(t=e.pastFuture(+this,t)),e.postformat(t)):this.localeData().invalidDate()},U.toISOString=xn,U.toString=xn,U.toJSON=xn,U.locale=Xt,U.localeData=Kt,U.toIsoString=e("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",xn),U.lang=Ke,s("X",0,0,"unix"),s("x",0,0,"valueOf"),h("x",ke),h("X",/[+-]?\d+(\.\d{1,3})?/),v("X",function(e,t,n){n._d=new Date(1e3*parseFloat(e))}),v("x",function(e,t,n){n._d=new Date(M(e))}),_.version="2.30.1",H=R,_.fn=u,_.min=function(){return Pt("isBefore",[].slice.call(arguments,0))},_.max=function(){return Pt("isAfter",[].slice.call(arguments,0))},_.now=function(){return Date.now?Date.now():+new Date},_.utc=l,_.unix=function(e){return R(1e3*e)},_.months=function(e,t){return fn(e,t,"months")},_.isDate=V,_.locale=ft,_.invalid=I,_.duration=C,_.isMoment=k,_.weekdays=function(e,t,n){return mn(e,t,n,"weekdays")},_.parseZone=function(){return R.apply(null,arguments).parseZone()},_.localeData=P,_.isDuration=Ut,_.monthsShort=function(e,t){return fn(e,t,"monthsShort")},_.weekdaysMin=function(e,t,n){return mn(e,t,n,"weekdaysMin")},_.defineLocale=mt,_.updateLocale=function(e,t){var n,s;return null!=t?(s=ut,null!=W[e]&&null!=W[e].parentLocale?W[e].set(X(W[e]._config,t)):(t=X(s=null!=(n=ct(e))?n._config:s,t),null==n&&(t.abbr=e),(s=new K(t)).parentLocale=W[e],W[e]=s),ft(e)):null!=W[e]&&(null!=W[e].parentLocale?(W[e]=W[e].parentLocale,e===ft()&&ft(e)):null!=W[e]&&delete W[e]),W[e]},_.locales=function(){return ee(W)},_.weekdaysShort=function(e,t,n){return mn(e,t,n,"weekdaysShort")},_.normalizeUnits=o,_.relativeTimeRounding=function(e){return void 0===e?Yn:"function"==typeof e&&(Yn=e,!0)},_.relativeTimeThreshold=function(e,t){return void 0!==Sn[e]&&(void 0===t?Sn[e]:(Sn[e]=t,"s"===e&&(Sn.ss=t-1),!0))},_.calendarFormat=function(e,t){return(e=e.diff(t,"days",!0))<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},_.prototype=u,_.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},_});
//# sourceMappingURL=moment.min.js.map
/*
 MIT
 *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
*****************************************************************************/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(m){var p=0;return function(){return p<m.length?{done:!1,value:m[p++]}:{done:!0}}};$jscomp.arrayIterator=function(m){return{next:$jscomp.arrayIteratorImpl(m)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(m,p,t){if(m==Array.prototype||m==Object.prototype)return m;m[p]=t.value;return m};$jscomp.getGlobal=function(m){m=["object"==typeof globalThis&&globalThis,m,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var p=0;p<m.length;++p){var t=m[p];if(t&&t.Math==Math)return t}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(m,p){var t=$jscomp.propertyToPolyfillSymbol[p];if(null==t)return m[p];t=m[t];return void 0!==t?t:m[p]};
$jscomp.polyfill=function(m,p,t,u){p&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(m,p,t,u):$jscomp.polyfillUnisolated(m,p,t,u))};$jscomp.polyfillUnisolated=function(m,p,t,u){t=$jscomp.global;m=m.split(".");for(u=0;u<m.length-1;u++){var v=m[u];if(!(v in t))return;t=t[v]}m=m[m.length-1];u=t[m];p=p(u);p!=u&&null!=p&&$jscomp.defineProperty(t,m,{configurable:!0,writable:!0,value:p})};
$jscomp.polyfillIsolated=function(m,p,t,u){var v=m.split(".");m=1===v.length;u=v[0];u=!m&&u in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var z=0;z<v.length-1;z++){var A=v[z];if(!(A in u))return;u=u[A]}v=v[v.length-1];t=$jscomp.IS_SYMBOL_NATIVE&&"es6"===t?u[v]:null;p=p(t);null!=p&&(m?$jscomp.defineProperty($jscomp.polyfills,v,{configurable:!0,writable:!0,value:p}):p!==t&&($jscomp.propertyToPolyfillSymbol[v]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(v):$jscomp.POLYFILL_PREFIX+v,v=
$jscomp.propertyToPolyfillSymbol[v],$jscomp.defineProperty(u,v,{configurable:!0,writable:!0,value:p})))};$jscomp.initSymbol=function(){};
$jscomp.polyfill("Symbol",function(m){if(m)return m;var p=function(v,z){this.$jscomp$symbol$id_=v;$jscomp.defineProperty(this,"description",{configurable:!0,writable:!0,value:z})};p.prototype.toString=function(){return this.$jscomp$symbol$id_};var t=0,u=function(v){if(this instanceof u)throw new TypeError("Symbol is not a constructor");return new p("jscomp_symbol_"+(v||"")+"_"+t++,v)};return u},"es6","es3");$jscomp.initSymbolIterator=function(){};
$jscomp.polyfill("Symbol.iterator",function(m){if(m)return m;m=Symbol("Symbol.iterator");for(var p="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),t=0;t<p.length;t++){var u=$jscomp.global[p[t]];"function"===typeof u&&"function"!=typeof u.prototype[m]&&$jscomp.defineProperty(u.prototype,m,{configurable:!0,writable:!0,value:function(){return $jscomp.iteratorPrototype($jscomp.arrayIteratorImpl(this))}})}return m},"es6",
"es3");$jscomp.initSymbolAsyncIterator=function(){};$jscomp.iteratorPrototype=function(m){m={next:m};m[Symbol.iterator]=function(){return this};return m};
(function(m,p){"object"===typeof exports&&"undefined"!==typeof module?p(exports,require("vue")):"function"===typeof define&&define.amd?define(["exports","vue"],p):(m=m||self,p(m.VeeValidate={},m.Vue))})(this,function(m,p){function t(a,b,c,d){return new (c||(c=Promise))(function(e,k){function h(g){try{l(d.next(g))}catch(n){k(n)}}function f(g){try{l(d["throw"](g))}catch(n){k(n)}}function l(g){g.done?e(g.value):(new c(function(n){n(g.value)})).then(h,f)}l((d=d.apply(a,b||[])).next())})}function u(a,
b){function c(g){return function(n){return d([g,n])}}function d(g){if(k)throw new TypeError("Generator is already executing.");for(;e;)try{if(k=1,h&&(f=g[0]&2?h["return"]:g[0]?h["throw"]||((f=h["return"])&&f.call(h),0):h.next)&&!(f=f.call(h,g[1])).done)return f;if(h=0,f)g=[g[0]&2,f.value];switch(g[0]){case 0:case 1:f=g;break;case 4:return e.label++,{value:g[1],done:!1};case 5:e.label++;h=g[1];g=[0];continue;case 7:g=e.ops.pop();e.trys.pop();continue;default:if(!(f=e.trys,f=0<f.length&&f[f.length-
1])&&(6===g[0]||2===g[0])){e=0;continue}if(3===g[0]&&(!f||g[1]>f[0]&&g[1]<f[3]))e.label=g[1];else if(6===g[0]&&e.label<f[1])e.label=f[1],f=g;else if(f&&e.label<f[2])e.label=f[2],e.ops.push(g);else{f[2]&&e.ops.pop();e.trys.pop();continue}}g=b.call(a,e)}catch(n){g=[6,n],h=0}finally{k=f=0}if(g[0]&5)throw g[1];return{value:g[0]?g[1]:void 0,done:!0}}var e={label:0,sent:function(){if(f[0]&1)throw f[1];return f[1]},trys:[],ops:[]},k,h,f,l;return l={next:c(0),"throw":c(1),"return":c(2)},"function"===typeof Symbol&&
(l[Symbol.iterator]=function(){return this}),l}function v(){for(var a=0,b=0,c=arguments.length;b<c;b++)a+=arguments[b].length;a=Array(a);var d=0;for(b=0;b<c;b++)for(var e=arguments[b],k=0,h=e.length;k<h;k++,d++)a[d]=e[k];return a}function z(a){return null===a||void 0===a}function A(a,b){if(a instanceof RegExp&&b instanceof RegExp)return A(a.source,b.source)&&A(a.flags,b.flags);if(Array.isArray(a)&&Array.isArray(b)){if(a.length!==b.length)return!1;for(var c=0;c<a.length;c++)if(!A(a[c],b[c]))return!1;
return!0}return B(a)&&B(b)?Object.keys(a).every(function(d){return A(a[d],b[d])})&&Object.keys(b).every(function(d){return A(a[d],b[d])}):a!==a&&b!==b?!0:a===b}function Z(a){return""===a?!1:!z(a)}function y(a){return"function"===typeof a}function E(a){return y(a)&&!!a.__locatorRef}function aa(a,b){var c=Array.isArray(a)?a:M(a);if(y(c.findIndex))return c.findIndex(b);for(var d=0;d<c.length;d++)if(b(c[d],d))return d;return-1}function za(a,b){var c=Array.isArray(a)?a:M(a),d=aa(c,b);return-1===d?void 0:
c[d]}function F(a,b){return-1!==a.indexOf(b)}function M(a){if(y(Array.from))return Array.from(a);for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}function N(a){return y(Object.values)?Object.values(a):Object.keys(a).map(function(b){return a[b]})}function O(a,b){Object.keys(b).forEach(function(c){B(b[c])?(a[c]||(a[c]={}),O(a[c],b[c])):a[c]=b[c]});return a}function P(){return{untouched:!0,touched:!1,dirty:!1,pristine:!0,valid:!1,invalid:!1,validated:!1,pending:!1,required:!1,changed:!1,passed:!1,
failed:!1}}function Aa(a){return a}function ba(a,b,c){void 0===b&&(b=0);void 0===c&&(c={cancelled:!1});if(0===b)return a;var d;return function(){for(var e=[],k=0;k<arguments.length;k++)e[k]=arguments[k];clearTimeout(d);d=setTimeout(function(){d=void 0;c.cancelled||a.apply(void 0,e)},b)}}function Q(a,b){return a.replace(/{([^}]+)}/g,function(c,d){return d in b?b[d]:"{"+d+"}"})}function Ba(a){var b;if(null===(b=a.params)||void 0===b?0:b.length)a.params=a.params.map(function(c){return"string"===typeof c?
{name:c}:c});return a}function I(a){var b={};Object.defineProperty(b,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1});return a?B(a)&&a._$$isNormalized?a:B(a)?Object.keys(a).reduce(function(c,d){var e=!0===a[d]?[]:Array.isArray(a[d])?a[d]:B(a[d])?a[d]:[a[d]];!1!==a[d]&&(c[d]=ca(d,e));return c},b):"string"!==typeof a?(console.warn("[vee-validate] rules must be either a string or an object."),b):a.split("|").reduce(function(c,d){var e=[],k=d.split(":")[0];F(d,":")&&(e=d.split(":").slice(1).join(":").split(","));
if(!k)return c;c[k]=ca(k,e);return c},b):b}function ca(a,b){var c=x.getRuleDefinition(a);if(!c)return b;var d={};if(!c.params&&!Array.isArray(b))throw Error("You provided an object params to a rule that has no defined schema.");if(Array.isArray(b)&&!c.params)return b;if(!c.params||c.params.length<b.length&&Array.isArray(b)){var e;var k=b.map(function(g,n){var q,w=null===(q=c.params)||void 0===q?void 0:q[n];e=w||e;w||(w=e);return w})}else k=c.params;for(var h=0;h<k.length;h++){var f=k[h],l=f["default"];
Array.isArray(b)?h in b&&(l=b[h]):f.name in b?l=b[f.name]:1===k.length&&(l=b);f.isTarget&&(l=da(l,f.cast));"string"===typeof l&&"@"===l[0]&&(l=da(l.slice(1),f.cast));!E(l)&&f.cast&&(l=f.cast(l));d[f.name]?(d[f.name]=Array.isArray(d[f.name])?d[f.name]:[d[f.name]],d[f.name].push(l)):d[f.name]=l}return d}function da(a,b){var c=function(d){d=d[a];return b?b(d):d};c.__locatorRef=a;return c}function Ca(a){return Array.isArray(a)?a.filter(E):Object.keys(a).filter(function(b){return E(a[b])}).map(function(b){return a[b]})}
function ea(a,b,c){void 0===c&&(c={});var d,e,k,h,f,l;return t(this,void 0,void 0,function(){var g,n,q,w,D,J,R;return u(this,function(fa){switch(fa.label){case 0:return g=null===(d=c)||void 0===d?void 0:d.bails,n=null===(e=c)||void 0===e?void 0:e.skipIfEmpty,q={name:(null===(k=c)||void 0===k?void 0:k.name)||"{field}",rules:I(b),bails:null!==g&&void 0!==g?g:!0,skipIfEmpty:null!==n&&void 0!==n?n:!0,forceRequired:!1,crossTable:(null===(h=c)||void 0===h?void 0:h.values)||{},names:(null===(f=c)||void 0===
f?void 0:f.names)||{},customMessages:(null===(l=c)||void 0===l?void 0:l.customMessages)||{}},[4,Da(q,a,c)];case 1:return w=fa.sent(),D=[],J={},R={},w.errors.forEach(function(L){var ha=L.msg();D.push(ha);J[L.rule]=ha;R[L.rule]=L.msg}),[2,{valid:w.valid,errors:D,failedRules:J,regenerateMap:R}]}})})}function Da(a,b,c){c=(void 0===c?{}:c).isInitial;var d=void 0===c?!1:c;return t(this,void 0,void 0,function(){var e,k,h,f,l,g,n,q;return u(this,function(w){switch(w.label){case 0:return[4,Ea(a,b)];case 1:e=
w.sent();k=e.shouldSkip;h=e.errors;if(k)return[2,{valid:!h.length,errors:h}];f=Object.keys(a.rules).filter(function(D){return!x.isRequireRule(D)});l=f.length;g=0;w.label=2;case 2:if(!(g<l))return[3,5];if(d&&x.isLazy(f[g]))return[3,4];n=f[g];return[4,ia(a,b,{name:n,params:a.rules[n]})];case 3:q=w.sent();if(!q.valid&&q.error&&(h.push(q.error),a.bails))return[2,{valid:!1,errors:h}];w.label=4;case 4:return g++,[3,2];case 5:return[2,{valid:!h.length,errors:h}]}})})}function Ea(a,b){return t(this,void 0,
void 0,function(){var c,d,e,k,h,f,l,g,n;return u(this,function(q){switch(q.label){case 0:c=Object.keys(a.rules).filter(x.isRequireRule);d=c.length;e=[];var w;(w=z(b)||""===b)||(w=Array.isArray(b)&&0===b.length);h=(k=w)&&a.skipIfEmpty;f=!1;l=0;q.label=1;case 1:if(!(l<d))return[3,4];g=c[l];return[4,ia(a,b,{name:g,params:a.rules[g]})];case 2:n=q.sent();if(!B(n))throw Error("Require rules has to return an object (see docs)");n.required&&(f=!0);if(!n.valid&&n.error&&(e.push(n.error),a.bails))return[2,
{shouldSkip:!0,errors:e}];q.label=3;case 3:return l++,[3,1];case 4:return k&&!f&&!a.skipIfEmpty||!a.bails&&!h?[2,{shouldSkip:!1,errors:e}]:[2,{shouldSkip:!f&&k,errors:e}]}})})}function ia(a,b,c){return t(this,void 0,void 0,function(){var d,e,k,h,f;return u(this,function(l){switch(l.label){case 0:d=x.getRuleDefinition(c.name);if(!d||!d.validate)throw Error("No such validator '"+c.name+"' exists.");e=d.castValue?d.castValue(b):b;k=Fa(c.params,a.crossTable);return[4,d.validate(e,k)];case 1:h=l.sent();
if("string"===typeof h)return f=r(r({},k||{}),{_field_:a.name,_value_:b,_rule_:c.name}),[2,{valid:!1,error:{rule:c.name,msg:function(){return Q(h,f)}}}];B(h)||(h={valid:h});return[2,{valid:h.valid,required:h.required,error:h.valid?void 0:Ga(a,b,d,c.name,k)}]}})})}function Ga(a,b,c,d,e){var k,h=(k=a.customMessages[d],null!==k&&void 0!==k?k:c.message);k=Ha(a,c,d);c=Ia(a,c,d,h);h=c.userTargets;var f=c.userMessage,l=r(r(r(r({},e||{}),{_field_:a.name,_value_:b,_rule_:d}),k),h);return{msg:function(){var g=
f||C.defaultMessage;var n=a.name;g="function"===typeof g?g(n,l):Q(g,r(r({},l),{_field_:n}));return g},rule:d}}function Ha(a,b,c){b=b.params;if(!b||0>=b.filter(function(f){return f.isTarget}).length)return{};var d={},e=a.rules[c];!Array.isArray(e)&&B(e)&&(e=b.map(function(f){return e[f.name]}));for(c=0;c<b.length;c++){var k=b[c],h=e[c];E(h)&&(h=h.__locatorRef,d[k.name]=a.names[h]||h,d["_"+k.name+"_"]=a.crossTable[h])}return d}function Ia(a,b,c,d){var e={},k=a.rules[c],h=b.params||[];if(!k)return{};
Object.keys(k).forEach(function(f,l){var g=k[f];if(!E(g))return{};var n=h[l];if(!n)return{};g=g.__locatorRef;e[n.name]=a.names[g]||g;e["_"+n.name+"_"]=a.crossTable[g]});return{userTargets:e,userMessage:d}}function Fa(a,b){if(Array.isArray(a))return a;var c={};Object.keys(a).forEach(function(d){var e=a[d];e=E(e)?e(b):e;c[d]=e});return c}function ja(){S.$emit("change:locale")}function Ja(a){var b,c;if(!a||!("undefined"!==typeof Event&&y(Event)&&a instanceof Event||a&&a.srcElement))return a;a=a.target;
return"file"===a.type&&a.files?M(a.files):(null===(b=a._vModifiers)||void 0===b?0:b.number)?(b=parseFloat(a.value),b!==b?a.value:b):(null===(c=a._vModifiers)||void 0===c?0:c.trim)?"string"===typeof a.value?a.value.trim():a.value:a.value}function T(a){if(a.data){var b=a.data;if("model"in b)return b.model;if(a.data.directives)return za(a.data.directives,function(c){return"model"===c.name})}}function U(a){var b,c,d,e=T(a);if(e)return{value:e.value};e=(null===(b=V(a))||void 0===b?void 0:b.prop)||"value";
if((null===(c=a.componentOptions)||void 0===c?0:c.propsData)&&e in a.componentOptions.propsData)return{value:a.componentOptions.propsData[e]};if((null===(d=a.data)||void 0===d?0:d.domProps)&&"value"in a.data.domProps)return{value:a.data.domProps.value}}function Ka(a){return Array.isArray(a)?a:Array.isArray(a.children)?a.children:a.componentOptions&&Array.isArray(a.componentOptions.children)?a.componentOptions.children:[]}function ka(a){return Array.isArray(a)||void 0===U(a)?Ka(a).reduce(function(b,
c){var d=ka(c);d.length&&b.push.apply(b,d);return b},[]):[a]}function V(a){return a.componentOptions?a.componentOptions.Ctor.options.model:null}function K(a,b,c){z(a[b])?a[b]=[c]:y(a[b])&&a[b].fns?(a=a[b],a.fns=Array.isArray(a.fns)?a.fns:[a.fns],F(a.fns,c)||a.fns.push(c)):(y(a[b])&&(a[b]=[a[b]]),Array.isArray(a[b])&&!F(a[b],c)&&a[b].push(c))}function W(a,b,c){a.componentOptions?a.componentOptions&&(a.componentOptions.listeners||(a.componentOptions.listeners={}),K(a.componentOptions.listeners,b,c)):
(a.data||(a.data={}),z(a.data.on)&&(a.data.on={}),K(a.data.on,b,c))}function la(a,b){var c;return a.componentOptions?(V(a)||{event:"input"}).event:(null===(c=null===b||void 0===b?void 0:b.modifiers)||void 0===c?0:c.lazy)?"change":ma(a)?"input":"change"}function La(a,b){return Object.keys(a).reduce(function(c,d){a[d].forEach(function(e){e.context||(a[d].context=b,e.data||(e.data={}),e.data.slot=d)});return c.concat(a[d])},[])}function na(a,b){return a.$scopedSlots["default"]?a.$scopedSlots["default"](b)||
[]:a.$slots["default"]||[]}function oa(a){return r(r({},a.flags),{errors:a.errors,classes:a.classes,failedRules:a.failedRules,reset:function(){return a.reset()},validate:function(){for(var b=[],c=0;c<arguments.length;c++)b[c]=arguments[c];return a.validate.apply(a,b)},ariaInput:{"aria-invalid":a.flags.invalid?"true":"false","aria-required":a.isRequired?"true":"false","aria-errormessage":"vee_"+a.id},ariaMsg:{id:"vee_"+a.id,"aria-live":a.errors.length?"assertive":"off"}})}function pa(a,b){a.initialized||
(a.initialValue=b);var c=!a._ignoreImmediate&&a.immediate||a.value!==b&&a.normalizedEvents.length||a._needsValidation||!a.initialized&&void 0===b?!0:!1;a._needsValidation=!1;a.value=b;a._ignoreImmediate=!0;if(c){var d=function(){if(a.immediate||a.flags.validated)return X(a);a.validateSilent()};a.initialized?d():a.$once("hook:mounted",function(){return d()})}}function qa(a){return(y(a.mode)?a.mode:ra[a.mode])(a)}function X(a){var b=a.validateSilent();a._pendingValidation=b;return b.then(function(c){b===
a._pendingValidation&&(a.applyResult(c),a._pendingValidation=void 0);return c})}function sa(a){a.$veeOnInput||(a.$veeOnInput=function(k){a.syncValue(k);a.setFlags({dirty:!0,pristine:!1})});var b=a.$veeOnInput;a.$veeOnBlur||(a.$veeOnBlur=function(){a.setFlags({touched:!0,untouched:!1})});var c=a.$veeOnBlur,d=a.$veeHandler,e=qa(a);d&&a.$veeDebounce===a.debounce||(d=ba(function(){a.$nextTick(function(){a._pendingReset||X(a);a._pendingReset=!1})},e.debounce||a.debounce),a.$veeHandler=d,a.$veeDebounce=
a.debounce);return{onInput:b,onBlur:c,onValidate:d}}function Ma(a,b){var c=U(b);a._inputEventName=a._inputEventName||la(b,T(b));pa(a,null===c||void 0===c?void 0:c.value);c=sa(a);var d=c.onBlur,e=c.onValidate;W(b,a._inputEventName,c.onInput);W(b,"blur",d);a.normalizedEvents.forEach(function(k){W(b,k,e)});a.initialized=!0}function Na(a,b){for(var c={},d=Object.keys(b),e=d.length,k=function(f){f=d[f];var l=a&&a[f]||f,g=b[f];if(z(g)||("valid"===f||"invalid"===f)&&!b.validated)return"continue";"string"===
typeof l?c[l]=g:Array.isArray(l)&&l.forEach(function(n){c[n]=g})},h=0;h<e;h++)k(h);return c}function Oa(a){var b=a.$_veeObserver.refs;return a.fieldDeps.reduce(function(c,d){if(!b[d])return c;c.values[d]=b[d].value;c.names[d]=b[d].name;return c},{names:{},values:{}})}function Pa(a){if(a.vid)return a.vid;if(a.name)return a.name;if(a.id)return a.id;if(a.fieldName)return a.fieldName;ta++;return"_vee_"+ta}function Qa(){return{refs:{},observe:function(a){this.refs[a.id]=a},unobserve:function(a){delete this.refs[a]}}}
function ua(a,b,c){void 0===c&&(c=!0);var d=a.$_veeObserver.refs;a._veeWatchers||(a._veeWatchers={});if(!d[b]&&c)return a.$once("hook:mounted",function(){ua(a,b,!1)});!y(a._veeWatchers[b])&&d[b]&&(a._veeWatchers[b]=d[b].$watch("value",function(){a.flags.validated&&(a._needsValidation=!0,a.validate())}))}function va(a){a.$_veeObserver&&a.$_veeObserver.unobserve(a.id,"observer")}function wa(a){a.$_veeObserver&&a.$_veeObserver.observe(a,"observer")}function xa(){return r(r({},P()),{valid:!0,invalid:!1})}
function Ra(){for(var a=v(N(this.refs),this.observers),b={},c=xa(),d={},e=a.length,k=0;k<e;k++){var h=a[k];Array.isArray(h.errors)?(b[h.id]=h.errors,d[h.id]=r({id:h.id,name:h.name,failedRules:h.failedRules},h.flags)):(b=r(r({},b),h.errors),d=r(r({},d),h.fields))}Sa.forEach(function(f){var l=f[0];c[l]=a[f[1]](function(g){return g.flags[l]})});return{errors:b,flags:c,fields:d}}p=p&&p.hasOwnProperty("default")?p["default"]:p;var r=function(){r=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<
d;c++){b=arguments[c];for(var e in b)Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e])}return a};return r.apply(this,arguments)},B=function(a){return null!==a&&a&&"object"===typeof a&&!Array.isArray(a)},G={},x=function(){function a(){}a.extend=function(b,c){var d=Ba(c);G[b]=G[b]?O(G[b],c):r({lazy:!1,computesRequired:!1},d)};a.isLazy=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.lazy)};a.isRequireRule=function(b){var c;return!(null===(c=G[b])||void 0===c||!c.computesRequired)};a.getRuleDefinition=
function(b){return G[b]};return a}(),C=r({},{defaultMessage:"{_field_} is not valid.",skipOptional:!0,classes:{touched:"touched",untouched:"untouched",valid:"valid",invalid:"invalid",pristine:"pristine",dirty:"dirty"},bails:!0,mode:"aggressive",useConstraintAttrs:!0}),Y=function(a){C=r(r({},C),a)},ra={aggressive:function(){return{on:["input","blur"]}},eager:function(a){return a.errors.length?{on:["input","change"]}:{on:["change","blur"]}},passive:function(){return{on:[]}},lazy:function(){return{on:["change"]}}},
S=new p,Ta=function(){function a(b,c){this.container={};this.locale=b;this.merge(c)}a.prototype.resolve=function(b,c,d){return this.format(this.locale,b,c,d)};a.prototype.format=function(b,c,d,e){var k,h,f,l,g,n,q,w;(d=(null===(f=null===(h=null===(k=this.container[b])||void 0===k?void 0:k.fields)||void 0===h?void 0:h[c])||void 0===f?void 0:f[d])||(null===(g=null===(l=this.container[b])||void 0===l?void 0:l.messages)||void 0===g?void 0:g[d]))||(d="{field} is not valid");c=(w=null===(q=null===(n=this.container[b])||
void 0===n?void 0:n.names)||void 0===q?void 0:q[c],null!==w&&void 0!==w?w:c);return y(d)?d(c,e):Q(d,r(r({},e),{_field_:c}))};a.prototype.merge=function(b){O(this.container,b)};a.prototype.hasRule=function(b){var c,d;return!(null===(d=null===(c=this.container[this.locale])||void 0===c?void 0:c.messages)||void 0===d||!d[b])};return a}(),H,ma=function(a){var b,c=(null===(b=a.data)||void 0===b?void 0:b.attrs)||a.elm;return("input"!==a.tag||c&&c.type)&&"textarea"!==a.tag?F("text password search email tel url number".split(" "),
null===c||void 0===c?void 0:c.type):!0},ta=0,ya=p.extend({inject:{$_veeObserver:{from:"$_veeObserver","default":function(){this.$vnode.context.$_veeObserver||(this.$vnode.context.$_veeObserver=Qa());return this.$vnode.context.$_veeObserver}}},props:{vid:{type:String,"default":""},name:{type:String,"default":null},mode:{type:[String,Function],"default":function(){return C.mode}},rules:{type:[Object,String],"default":null},immediate:{type:Boolean,"default":!1},bails:{type:Boolean,"default":function(){return C.bails}},
skipIfEmpty:{type:Boolean,"default":function(){return C.skipOptional}},debounce:{type:Number,"default":0},tag:{type:String,"default":"span"},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1},customMessages:{type:Object,"default":function(){return{}}}},watch:{rules:{deep:!0,handler:function(a,b){this._needsValidation=!A(a,b)}}},data:function(){return{errors:[],value:void 0,initialized:!1,initialValue:void 0,flags:P(),failedRules:{},isActive:!0,fieldName:"",id:""}},computed:{fieldDeps:function(){var a=
this;return Object.keys(this.normalizedRules).reduce(function(b,c){var d=Ca(a.normalizedRules[c]).map(function(e){return e.__locatorRef});b.push.apply(b,d);d.forEach(function(e){ua(a,e)});return b},[])},normalizedEvents:function(){var a=this;return(qa(this).on||[]).map(function(b){return"input"===b?a._inputEventName:b})},isRequired:function(){var a=r(r({},this._resolvedRules),this.normalizedRules);a=Object.keys(a).some(x.isRequireRule);this.flags.required=!!a;return a},classes:function(){return Na(C.classes,
this.flags)},normalizedRules:function(){return I(this.rules)}},created:function(){var a=this,b=function(){if(a.flags.validated){var c=a._regenerateMap;if(c){var d=[],e={};Object.keys(c).forEach(function(k){var h=c[k]();d.push(h);e[k]=h});a.applyResult({errors:d,failedRules:e,regenerateMap:c})}else a.validate()}};S.$on("change:locale",b);this.$on("hook:beforeDestroy",function(){S.$off("change:locale",b)})},render:function(a){var b=this;this.registerField();var c=oa(this);c=na(this,c);ka(c).forEach(function(d){var e,
k,h,f,l;if(C.useConstraintAttrs){var g,n=null===(g=d.data)||void 0===g?void 0:g.attrs;if(F(["input","select","textarea"],d.tag)&&n)if(g={},"required"in n&&!1!==n.required&&x.getRuleDefinition("required")&&(g.required="checkbox"===n.type?[!0]:!0),ma(d)){n=r;g=r({},g);var q=null===(l=d.data)||void 0===l?void 0:l.attrs;l={};q&&("email"===q.type&&x.getRuleDefinition("email")&&(l.email=["multiple"in q]),q.pattern&&x.getRuleDefinition("regex")&&(l.regex=q.pattern),0<=q.maxlength&&x.getRuleDefinition("max")&&
(l.max=q.maxlength),0<=q.minlength&&x.getRuleDefinition("min")&&(l.min=q.minlength),"number"===q.type&&(Z(q.min)&&x.getRuleDefinition("min_value")&&(l.min_value=Number(q.min)),Z(q.max)&&x.getRuleDefinition("max_value")&&(l.max_value=Number(q.max))));l=I(n(g,l))}else l=I(g);else l={}}else l={};A(b._resolvedRules,l)||(b._needsValidation=!0);F(["input","select","textarea"],d.tag)&&(b.fieldName=(null===(k=null===(e=d.data)||void 0===e?void 0:e.attrs)||void 0===k?void 0:k.name)||(null===(f=null===(h=d.data)||
void 0===h?void 0:h.attrs)||void 0===f?void 0:f.id));b._resolvedRules=l;Ma(b,d)});return this.slim&&1>=c.length?c[0]:a(this.tag,c)},beforeDestroy:function(){this.$_veeObserver.unobserve(this.id)},activated:function(){this.isActive=!0},deactivated:function(){this.isActive=!1},methods:{setFlags:function(a){var b=this;Object.keys(a).forEach(function(c){b.flags[c]=a[c]})},syncValue:function(a){this.value=a=Ja(a);this.flags.changed=this.initialValue!==a},reset:function(){var a=this;this.errors=[];this.initialValue=
this.value;var b=P();b.required=this.isRequired;this.setFlags(b);this.failedRules={};this.validateSilent();this._pendingValidation=void 0;this._pendingReset=!0;setTimeout(function(){a._pendingReset=!1},this.debounce)},validate:function(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];return t(this,void 0,void 0,function(){return u(this,function(c){0<a.length&&this.syncValue(a[0]);return[2,X(this)]})})},validateSilent:function(){return t(this,void 0,void 0,function(){var a,b;return u(this,
function(c){switch(c.label){case 0:return this.setFlags({pending:!0}),a=r(r({},this._resolvedRules),this.normalizedRules),Object.defineProperty(a,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),[4,ea(this.value,a,r(r({name:this.name||this.fieldName},Oa(this)),{bails:this.bails,skipIfEmpty:this.skipIfEmpty,isInitial:!this.initialized,customMessages:this.customMessages}))];case 1:return b=c.sent(),this.setFlags({pending:!1,valid:b.valid,invalid:!b.valid}),[2,b]}})})},setErrors:function(a){this.applyResult({errors:a,
failedRules:{}})},applyResult:function(a){var b=a.errors,c=a.failedRules;a=a.regenerateMap;this.errors=b;this._regenerateMap=a;this.failedRules=r({},c||{});this.setFlags({valid:!b.length,passed:!b.length,invalid:!!b.length,failed:!!b.length,validated:!0,changed:this.value!==this.initialValue})},registerField:function(){var a=Pa(this),b=this.id;!this.isActive||b===a&&this.$_veeObserver.refs[b]||(b!==a&&this.$_veeObserver.refs[b]===this&&this.$_veeObserver.unobserve(b),this.id=a,this.$_veeObserver.observe(this))}}}),
Sa=[["pristine","every"],["dirty","some"],["touched","some"],["untouched","every"],["valid","every"],["invalid","some"],["pending","some"],["validated","every"],["changed","some"],["passed","every"],["failed","some"]],Ua=0,Va=p.extend({name:"ValidationObserver",provide:function(){return{$_veeObserver:this}},inject:{$_veeObserver:{from:"$_veeObserver","default":function(){return this.$vnode.context.$_veeObserver?this.$vnode.context.$_veeObserver:null}}},props:{tag:{type:String,"default":"span"},vid:{type:String,
"default":function(){return"obs_"+Ua++}},slim:{type:Boolean,"default":!1},disabled:{type:Boolean,"default":!1}},data:function(){return{id:"",refs:{},observers:[],errors:{},flags:xa(),fields:{}}},created:function(){var a=this;this.id=this.vid;wa(this);var b=ba(function(c){var d=c.flags,e=c.fields;a.errors=c.errors;a.flags=d;a.fields=e},16);this.$watch(Ra,b)},activated:function(){wa(this)},deactivated:function(){va(this)},beforeDestroy:function(){va(this)},render:function(a){var b=na(this,r(r({},this.flags),
{errors:this.errors,fields:this.fields,validate:this.validate,passes:this.handleSubmit,handleSubmit:this.handleSubmit,reset:this.reset}));return this.slim&&1>=b.length?b[0]:a(this.tag,{on:this.$listeners},b)},methods:{observe:function(a,b){var c;void 0===b&&(b="provider");"observer"===b?this.observers.push(a):this.refs=r(r({},this.refs),(c={},c[a.id]=a,c))},unobserve:function(a,b){void 0===b&&(b="provider");if("provider"===b)this.refs[a]&&this.$delete(this.refs,a);else{var c=aa(this.observers,function(d){return d.id===
a});-1!==c&&this.observers.splice(c,1)}},validate:function(a){a=(void 0===a?{}:a).silent;var b=void 0===a?!1:a;return t(this,void 0,void 0,function(){var c;return u(this,function(d){switch(d.label){case 0:return[4,Promise.all(v(N(this.refs).filter(function(e){return!e.disabled}).map(function(e){return e[b?"validateSilent":"validate"]().then(function(k){return k.valid})}),this.observers.filter(function(e){return!e.disabled}).map(function(e){return e.validate({silent:b})})))];case 1:return c=d.sent(),
[2,c.every(function(e){return e})]}})})},handleSubmit:function(a){return t(this,void 0,void 0,function(){var b;return u(this,function(c){switch(c.label){case 0:return[4,this.validate()];case 1:return(b=c.sent())&&a?[2,a()]:[2]}})})},reset:function(){return v(N(this.refs),this.observers).forEach(function(a){return a.reset()})},setErrors:function(a){var b=this;Object.keys(a).forEach(function(c){var d=b.refs[c];d&&(c=a[c]||[],c="string"===typeof c?[c]:c,d.setErrors(c))});this.observers.forEach(function(c){c.setErrors(a)})}}});
m.ValidationObserver=Va;m.ValidationProvider=ya;m.configure=function(a){Y(a)};m.extend=function(a,b){if(!y(b)&&!y(b.validate)&&!x.getRuleDefinition(a))throw Error("Extension Error: The validator '"+a+"' must be a function or have a 'validate' method.");"object"===typeof b?x.extend(a,b):x.extend(a,{validate:b})};m.localeChanged=ja;m.localize=function(a,b){var c;H||(H=new Ta("en",{}),Y({defaultMessage:function(d,e){return H.resolve(d,null===e||void 0===e?void 0:e._rule_,e||{})}}));"string"===typeof a?
(H.locale=a,b&&H.merge((c={},c[a]=b,c)),ja()):H.merge(a)};m.normalizeRules=I;m.setInteractionMode=function(a,b){Y({mode:a});if(b){if(!y(b))throw Error("A mode implementation must be a function");ra[a]=b}};m.validate=ea;m.version="3.2.3";m.withValidation=function(a,b){void 0===b&&(b=Aa);var c,d="options"in a?a.options:a,e=ya.options;e={name:(d.name||"AnonymousHoc")+"WithValidation",props:r({},e.props),data:e.data,computed:r({},e.computed),methods:r({},e.methods),beforeDestroy:e.beforeDestroy,inject:e.inject};
var k=(null===(c=null===d||void 0===d?void 0:d.model)||void 0===c?void 0:c.event)||"input";e.render=function(h){var f;this.registerField();var l=oa(this),g=r({},this.$listeners),n=T(this.$vnode);this._inputEventName=this._inputEventName||la(this.$vnode,n);var q=U(this.$vnode);pa(this,null===q||void 0===q?void 0:q.value);q=sa(this);var w=q.onBlur,D=q.onValidate;K(g,k,q.onInput);K(g,"blur",w);this.normalizedEvents.forEach(function(J){K(g,J,D)});q=(V(this.$vnode)||{prop:"value"}).prop;l=r(r(r({},this.$attrs),
(f={},f[q]=null===n||void 0===n?void 0:n.value,f)),b(l));return h(d,{attrs:this.$attrs,props:l,on:g},La(this.$slots,this.$vnode.context))};return e};Object.defineProperty(m,"__esModule",{value:!0})});



!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((r=r||self).VeeValidateRules={})}(this,function(r){"use strict";var i={en:/^[A-Z]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[A-ZÆØÅ]*$/i,de:/^[A-ZÄÖÜß]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[A-Z\xC0-\xFF]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ]*$/i,nl:/^[A-ZÉËÏÓÖÜ]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[А-ЯЁ]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[A-ZČĆŽŠĐ]*$/i,sv:/^[A-ZÅÄÖ]*$/i,tr:/^[A-ZÇĞİıÖŞÜ]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[A-ZÇƏĞİıÖŞÜ]*$/i},a={en:/^[A-Z\s]*$/i,cs:/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ\s]*$/i,da:/^[A-ZÆØÅ\s]*$/i,de:/^[A-ZÄÖÜß\s]*$/i,es:/^[A-ZÁÉÍÑÓÚÜ\s]*$/i,fr:/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ\s]*$/i,it:/^[A-Z\xC0-\xFF\s]*$/i,lt:/^[A-ZĄČĘĖĮŠŲŪŽ\s]*$/i,nl:/^[A-ZÉËÏÓÖÜ\s]*$/i,hu:/^[A-ZÁÉÍÓÖŐÚÜŰ\s]*$/i,pl:/^[A-ZĄĆĘŚŁŃÓŻŹ\s]*$/i,pt:/^[A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ\s]*$/i,ru:/^[А-ЯЁ\s]*$/i,sk:/^[A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ\s]*$/i,sr:/^[A-ZČĆŽŠĐ\s]*$/i,sv:/^[A-ZÅÄÖ\s]*$/i,tr:/^[A-ZÇĞİıÖŞÜ\s]*$/i,uk:/^[А-ЩЬЮЯЄІЇҐ\s]*$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ\s]*$/,az:/^[A-ZÇƏĞİıÖŞÜ\s]*$/i},u={en:/^[0-9A-Z]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]*$/i,da:/^[0-9A-ZÆØÅ]$/i,de:/^[0-9A-ZÄÖÜß]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]*$/i,it:/^[0-9A-Z\xC0-\xFF]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ]*$/i,ru:/^[0-9А-ЯЁ]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ]*$/i,sr:/^[0-9A-ZČĆŽŠĐ]*$/i,sv:/^[0-9A-ZÅÄÖ]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ]*$/i},s={en:/^[0-9A-Z_-]*$/i,cs:/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ_-]*$/i,da:/^[0-9A-ZÆØÅ_-]*$/i,de:/^[0-9A-ZÄÖÜß_-]*$/i,es:/^[0-9A-ZÁÉÍÑÓÚÜ_-]*$/i,fr:/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ_-]*$/i,it:/^[0-9A-Z\xC0-\xFF_-]*$/i,lt:/^[0-9A-ZĄČĘĖĮŠŲŪŽ_-]*$/i,nl:/^[0-9A-ZÉËÏÓÖÜ_-]*$/i,hu:/^[0-9A-ZÁÉÍÓÖŐÚÜŰ_-]*$/i,pl:/^[0-9A-ZĄĆĘŚŁŃÓŻŹ_-]*$/i,pt:/^[0-9A-ZÃÁÀÂÇÉÊÍÕÓÔÚÜ_-]*$/i,ru:/^[0-9А-ЯЁ_-]*$/i,sk:/^[0-9A-ZÁÄČĎÉÍĹĽŇÓŔŠŤÚÝŽ_-]*$/i,sr:/^[0-9A-ZČĆŽŠĐ_-]*$/i,sv:/^[0-9A-ZÅÄÖ_-]*$/i,tr:/^[0-9A-ZÇĞİıÖŞÜ_-]*$/i,uk:/^[0-9А-ЩЬЮЯЄІЇҐ_-]*$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ_-]*$/,az:/^[0-9A-ZÇƏĞİıÖŞÜ_-]*$/i},o=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return o(r,{locale:n})}):n?(i[n]||i.en).test(e):Object.keys(i).some(function(r){return i[r].test(e)})},e={validate:o,params:[{name:"locale"}]},l=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return l(r,{locale:n})}):n?(s[n]||s.en).test(e):Object.keys(s).some(function(r){return s[r].test(e)})},t={validate:l,params:[{name:"locale"}]},c=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return c(r,{locale:n})}):n?(u[n]||u.en).test(e):Object.keys(u).some(function(r){return u[r].test(e)})},n={validate:c,params:[{name:"locale"}]},A=function(e,r){var t=(void 0===r?{}:r).locale,n=void 0===t?"":t;return Array.isArray(e)?e.every(function(r){return A(r,{locale:n})}):n?(a[n]||a.en).test(e):Object.keys(a).some(function(r){return a[r].test(e)})},f={validate:A,params:[{name:"locale"}]},m=function(r,e){var t=void 0===e?{}:e,n=t.min,i=t.max;return Array.isArray(r)?r.every(function(r){return!!m(r,{min:n,max:i})}):Number(n)<=r&&Number(i)>=r},v={validate:m,params:[{name:"min"},{name:"max"}]},$={validate:function(r,e){var t=e.target;return String(r)===String(t)},params:[{name:"target",isTarget:!0}]},d=function(r,e){var t=e.length;if(Array.isArray(r))return r.every(function(r){return d(r,{length:t})});var n=String(r);return/^[0-9]*$/.test(n)&&n.length===t},y={validate:d,params:[{name:"length",cast:function(r){return Number(r)}}]},g={validate:function(r,e){var u=e.width,s=e.height,t=[];r=Array.isArray(r)?r:[r];for(var n=0;n<r.length;n++){if(!/\.(jpg|svg|jpeg|png|bmp|gif)$/i.test(r[n].name))return Promise.resolve(!1);t.push(r[n])}return Promise.all(t.map(function(r){return t=r,n=u,i=s,a=window.URL||window.webkitURL,new Promise(function(r){var e=new Image;e.onerror=function(){return r(!1)},e.onload=function(){return r(e.width===n&&e.height===i)},e.src=a.createObjectURL(t)});var t,n,i,a})).then(function(r){return r.every(function(r){return r})})},params:[{name:"width",cast:function(r){return Number(r)}},{name:"height",cast:function(r){return Number(r)}}]},Z={validate:function(r,e){var t=(void 0===e?{}:e).multiple,n=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return t&&!Array.isArray(r)&&(r=String(r).split(",").map(function(r){return r.trim()})),Array.isArray(r)?r.every(function(r){return n.test(String(r))}):n.test(String(r))},params:[{name:"multiple",default:!1}]};function p(r){return null==r}function h(r){return Array.isArray(r)&&0===r.length}function x(r){return"function"==typeof Array.from?Array.from(r):function(r){for(var e=[],t=r.length,n=0;n<t;n++)e.push(r[n]);return e}(r)}function _(r){return h(r)||-1!==[!1,null,void 0].indexOf(r)||!String(r).trim().length}var b=function(e,t){return Array.isArray(e)?e.every(function(r){return b(r,t)}):x(t).some(function(r){return r==e})},w={validate:b},S={validate:function(r,e){return!b(r,e)}},N={validate:function(r,e){var t=new RegExp(".("+e.join("|")+")$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.name)}):t.test(r.name)}},j={validate:function(r){var e=/\.(jpg|svg|jpeg|png|bmp|gif)$/i;return Array.isArray(r)?r.every(function(r){return e.test(r.name)}):e.test(r.name)}},k={validate:function(r){return Array.isArray(r)?r.every(function(r){return/^-?[0-9]+$/.test(String(r))}):/^-?[0-9]+$/.test(String(r))}},z={validate:function(r,e){return r===e.other},params:[{name:"other"}]},F={validate:function(r,e){return r!==e.other},params:[{name:"other"}]},R={validate:function(r,e){var t=e.length;return!p(r)&&("number"==typeof r&&(r=String(r)),r.length||(r=x(r)),r.length===t)},params:[{name:"length",cast:function(r){return Number(r)}}]},O=function(r,e){var t=e.length;return p(r)?0<=t:Array.isArray(r)?r.every(function(r){return O(r,{length:t})}):String(r).length<=t},q={validate:O,params:[{name:"length",cast:function(r){return Number(r)}}]},C=function(r,e){var t=e.max;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return C(r,{max:t})}):Number(r)<=t)},P={validate:C,params:[{name:"max",cast:function(r){return Number(r)}}]},E={validate:function(r,e){var t=new RegExp(e.join("|").replace("*",".+")+"$","i");return Array.isArray(r)?r.every(function(r){return t.test(r.type)}):t.test(r.type)}},L=function(r,e){var t=e.length;return!p(r)&&(Array.isArray(r)?r.every(function(r){return L(r,{length:t})}):String(r).length>=t)},U={validate:L,params:[{name:"length",cast:function(r){return Number(r)}}]},T=function(r,e){var t=e.min;return!p(r)&&""!==r&&(Array.isArray(r)?0<r.length&&r.every(function(r){return T(r,{min:t})}):Number(r)>=t)},V={validate:T,params:[{name:"min",cast:function(r){return Number(r)}}]},I=/^[٠١٢٣٤٥٦٧٨٩]+$/,M=/^[0-9]+$/,B={validate:function(r){function e(r){var e=String(r);return M.test(e)||I.test(e)}return Array.isArray(r)?r.every(e):e(r)}},D=function(r,e){var t=e.regex;return Array.isArray(r)?r.every(function(r){return D(r,{regex:t})}):t.test(String(r))},G={validate:D,params:[{name:"regex",cast:function(r){return"string"==typeof r?new RegExp(r):r}}]},H={validate:function(r,e){var t=(void 0===e?{allowFalse:!0}:e).allowFalse,n={valid:!1,required:!0};return p(r)||h(r)||!1===r&&!t||(n.valid=!!String(r).trim().length),n},params:[{name:"allowFalse",default:!0}],computesRequired:!0},J={validate:function(r,e){var t,n=e.target,i=e.values;return(t=i&&i.length?(Array.isArray(i)||"string"!=typeof i||(i=[i]),i.some(function(r){return r==String(n).trim()})):!_(n))?{valid:!_(r),required:t}:{valid:!0,required:t}},params:[{name:"target",isTarget:!0},{name:"values"}],computesRequired:!0},K={validate:function(r,e){var t=e.size;if(isNaN(t))return!1;var n=1024*t;if(!Array.isArray(r))return r.size<=n;for(var i=0;i<r.length;i++)if(r[i].size>n)return!1;return!0},params:[{name:"size",cast:function(r){return Number(r)}}]};r.alpha=e,r.alpha_dash=t,r.alpha_num=n,r.alpha_spaces=f,r.between=v,r.confirmed=$,r.digits=y,r.dimensions=g,r.email=Z,r.excluded=S,r.ext=N,r.image=j,r.integer=k,r.is=z,r.is_not=F,r.length=R,r.max=q,r.max_value=P,r.mimes=E,r.min=U,r.min_value=V,r.numeric=B,r.oneOf=w,r.regex=G,r.required=H,r.required_if=J,r.size=K,Object.defineProperty(r,"__esModule",{value:!0})});
Vue.component('yuno-page-grid', {
    props: {
        authorizedRoles: {
            type: Array,
            required: false,
            default: () => []
        },
        hasPageHeader: {
            type: Boolean,
            required: false,
            default: true
        },
        hasPageFooter: {
            type: Boolean,
            required: false,
            default: true
        },
        hasSearchBar: {
            type: Boolean,
            required: false,
            default: true
        },
        zohoMeta: {
            type: Object,
            required: false,
            default: null
        },
        hasLHSMenu: {
            type: Boolean,
            required: false,
            default: true
        }
    },
    template: `
        <div :class="[header.isMenuOpen ? 'menuOpen' : '']">
            <yuno-page-header v-if="loginStatus && hasPageHeader" :hasSearchBar="hasSearchBar"></yuno-page-header>
            <yuno-header-revamp v-else-if="!loginStatus && hasPageHeader" ref="yunoHeader" :options="{zohoMeta: zohoMeta}">></yuno-header-revamp>
            <div class="pageGrid" :class="[!hasLHSMenu ? 'noLHSMenu' : '']">
                <yuno-header-v2 
                    @userInfo="onUserInfo" 
                    @isMini="onMini" 
                    v-if="loginStatus && hasPageHeader"
                >
                </yuno-header-v2>
                <slot name="aboveMain"></slot>
                <main id="yunoMain" class="mainBody" :class="[isMiniSidebar ? 'miniSidebar' : '', loginStatus ? 'postLogin' : 'preLogin', loginStatus && !hasPageHeader && !hasPageFooter ? 'noHeaderFooter' : '']">
                    <template v-if="userInfo.loading">
                        <div class="container hasTopGap">
                            <figure class="infiniteSpinner">
                                <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                            </figure>
                        </div>
                    </template>
                    <template v-if="userInfo.success || !user.isLoggedin">
                        <template v-if="isUserAuthorized">
                            <slot name="main"></slot>     
                        </template>
                        <template v-else>
                            <div class="container">
                                <yuno-empty-states :options="emptyStates"></yuno-empty-states>
                            </div>
                        </template>
                    </template>
                </main>
            </div>
            <yuno-footer :isnav="false" :whatsapp="false" v-if="loginStatus && hasPageHeader"></yuno-footer> 
            <yuno-footer v-else-if="!loginStatus && hasPageFooter"></yuno-footer>
            <slot name="belowFooter"></slot>
        </div>
    `,
    data() {
        return {
            isMiniSidebar: false,
            loginStatus: isLoggedIn !== '0' ? true : false
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'userInfo',
            'user',
            'header',
            'footer'
        ]),
        isUserAuthorized : {
            get() {
                if (YUNOCommon.findInArray(this.$props.authorizedRoles, this.userRole.data)) { //
                    return true;
                } else if (this.$props.authorizedRoles.length === 0) {
                    return true;
                } else {
                    return false;
                }
            }
        },
        emptyStates() {
            return {
                state: "notAuthorized"
            }
        },
        isPageLoading() {
            return this.userInfo.loading || this.header.loading || this.footer.loading;
        },
        wpThemeURL() {
            return this.$store.state.themeURL
        },
    },
    async created() {

    },
    destroyed() {

    },
    mounted() {
        
    },
    methods: {
        onUserInfo(data) {
            this.$emit('onUserInfo', data);
            // if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
            //     this.fetchModules(data.role);
            // }
            
        },
        onMini(data) {
            this.isMiniSidebar = data;
        },
    }
});
Vue.component('yuno-page-header', {
    props: {
        hasSearchBar: {
            type: Boolean,
            required: false,
            default: true
        }
    },
    template: `
        <div class="yunoPageHeader">
            <figure class="logo">
                <img width="68" height="32" :src="wpThemeURL + '/assets/images/yunoLogo.svg'" alt="Yuno Learning">
            </figure>
            <yuno-course-search-bar v-if="hasSearchBar"></yuno-course-search-bar>
            <ul class="actions">
                <li v-if="manageOrgSwitchVisiblity()">
                    <b-skeleton width="200px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>
                    <b-dropdown 
                        v-model="selectedOrg" 
                        position="is-bottom-left"
                        v-if="header.success && userInfo.success"
                        aria-role="list"
                        :class="['orgSwitchWrapper']"
                    >
                        <template #trigger>
                            <div class="orgSwitch">
                                <img :src="selectedOrg.image" :alt="selectedOrg.name" width="24" height="24">
                                <span class="name">{{ selectedOrg.name }}</span>
                                <span class="icon"></span>
                            </div>
                        </template>
                        <b-dropdown-item 
                            aria-role="menuitem"
                            v-for="(org, i) in activeUser.org_id"
                            :key="i"
                            @click="manageOrg(org)"
                            :value="org"
                        >
                            
                            <img :src="org.image" :alt="org.name" width="24" height="24"> <span class="caption">{{ org.name }}</span>        
                            
                        </b-dropdown-item>
                    </b-dropdown>
                </li>
                <li>
                    <b-skeleton circle width="32px" height="32px" v-if="header.loading || userInfo.loading"></b-skeleton>
                    <b-dropdown
                        v-model="navigation"
                        position="is-bottom-left"
                        v-if="header.success && userInfo.success"
                        aria-role="menu"
                    >
                        <template #trigger>
                            <div class="userIcon">
                                <img width="32" height="32" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">
                            </div>
                        </template>
                        <b-dropdown-item custom aria-role="menuitem" :class="['normal']">
                            <figure class="userCard">
                                <div class="imgWrapper">
                                    <img width="64" height="64" :src="activeUser.profile_img" :alt="activeUser.yuno_display_name">
                                </div>
                                <figcaption>
                                    <h3>{{ activeUser.yuno_display_name }}</h3>
                                    <p>{{ activeUser.email }}</p>
                                    <p>{{ activeUser.role }}</p>
                                </figcaption>
                            </figure>
                        </b-dropdown-item>
                        <b-dropdown-item 
                            has-link 
                            aria-role="menuitem"
                            v-for="(menu, i) in accountMenu.items"
                            @click="manageMenuItem($event, menu)"
                            :key="i"
                        >
                            <a :href="menu.url">
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>        
                            </a>
                        </b-dropdown-item>
                        
                    </b-dropdown>
                </li>
            </ul>
        </div>
    `,
    data() {
        return {
            navigation: "",
            selectedOrg: null,
            isLoading: true
        }
    },
    computed: {
        ...Vuex.mapState([
            'header',
            'userInfo',
            'userRole',
            'subform3'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
        getHomeURL() {
            return this.$store.state.homeURL
        },
        accountMenu() {
            return YUNOCommon.findObjectByKey(this.header.data, "section", "Account")
        },
        activeUser() {
            return this.userInfo.data
        },
    },
    watch: {
        'userInfo.data': {
            handler(newValue, oldValue) {
                if (newValue !== oldValue) {
                    this.init();   
                }
            },
            deep: true
        }
    },
    async created() {

    },
    destroyed() {

    },
    mounted() {
        
    },
    methods: {
        manageMenuItem(e, data) {
            if (data.label === "Switch Account") {
                localStorage.setItem('userState', window.location.pathname);
                localStorage.setItem('isChooseAccountState', true);
                sessionStorage.clear();
            }
        },
        manageOrgSwitchVisiblity() {
            if (this.userRole.data === "org-admin" && this.userInfo.data.org_id.length > 1) {
                return true;
            } else  {
                return false;
            }
        },
        manageOrg(org) {
            this.updateActiveOrg(org.id);
        },
        orgUpdated(options) {
            const response = options?.response?.data;

            if (response?.code === 201) {
                sessionStorage.clear();
                window.location.reload(true);
            } else if (response?.message) { 
                console.log(response.message)
            }
        },
        updateActiveOrg(orgID) {
            this.$buefy.loading.open();

            const options = {
                apiURL: YUNOCommon.config.academy("activeOrg", false), 
                module: "gotData", 
                store: "subform3", 
                payload: {
                    "user_id": isLoggedIn,
                    "org_id": orgID
                }, 
                callback: true, 
                callbackFunc: (options) => this.orgUpdated(options)
            };

            this.dispatchData('postData', options);
        },
        dispatchData(action, options) {
            this.$store.dispatch(action, options);
        },
        init() {
            if (this.userInfo.data.role === "org-admin") {
                const activeOrg = YUNOCommon.findObjectByKey(this.userInfo.data.org_id, "id", Number(this.activeOrg()));
                this.selectedOrg = activeOrg;    
            }
        },
        searchBar() {
            if (this.userRole.data === "Learner") {
                return true;    
            } else {
                return false;
            }
            
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            }
        }
    }
});
Vue.component('yuno-header-v2', {
    props: ["data", "options"],
    template: `
        <div class="sidebarWrapper">
            <div class="sidebar-page yunoSidebar" :class="[isMobile ? 'isMobile' : 'isDesktop', reduce ? 'collapseView' : 'expandView']">
                <section class="sidebar-layout">
                    <b-sidebar
                        position="static"
                        :mobile="mobile"
                        :expand-on-hover="expandOnHover"
                        :reduce="reduce"
                        :delay="expandWithDelay ? 500 : null"
                        type="is-light"
                        open
                    >
                        <a href="#" @click.prevent="sidebarToggle(false)" class="sidebarToggle" :class="[isMobile ? 'isMobile' : 'isDesktop']">
                            <span class="material-icons">
                                <template v-if="isMobile">
                                    menu
                                </template>
                                <template v-else>
                                    expand_less
                                </template>
                            </span>
                        </a>
                        <figure class="logo" v-if="!isPageGrid">
                            <a href="#">
                                <img width="106" height="50" :src="wpThemeURL + '/assets/images/yunoLogo.svg'" alt="Yuno Learning">
                            </a>
                        </figure>
                        <yuno-main-nav
                            :options="{'isMini': reduce}"
                            :isPageGrid="isPageGrid"
                        >
                        </yuno-main-nav>
                    </b-sidebar>
                </section>
                <b-modal 
                    :active.sync="config.unauthorizedModal" 
                    :width="450" 
                    :can-cancel="['escape', 'x']" 
                    :on-cancel="unauthorizedModalClose"
                    class="yunoModal">
                        <div class="modalHeader">
                            <h2 class="modalTitle">Session Expired</h2>
                        </div>
                        <div class="modalBody">
                            <div class="wrapper">
                                <p>{{sessionExpired}}</p>
                            </div>
                        </div>
                        <div class="modalFooter">
                            <div class="unauthorizedLogin">
                                <a 
                                    @click.prevent="setState()"
                                    href="#">
                                    <span class="g_icon"></span>
                                    <span class="yuno-login-with-google-on-pages">Sign-in with Google</span>
                                </a>
                            </div>
                        </div>
                </b-modal>
            </div>
        </div>
    `,
    data() {
        return {
            isMobile: false,
            menuLoading: 3,
            expandOnHover: false,
            expandWithDelay: false,
            mobile: "reduce",
            reduce: false,
            tokenExpiry: {
                payload: {
                    "userID": isLoggedIn,
                    "token": this.$store.state.config.yunoAPIToken
                }
            },
            sessionExpired: YUNOCommon.config.errorMsg.sesstionExpired,
            storage: {
                name: "activeUser",
                version: 1
            },
            isPageGrid: true,
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'userInfo',
            'userRole',
            'userProfile',
            'config',
            'header',
            'apiTokenExpiryTime',
            'apiTokenRefresh',
            'referralCode'
        ]),
        wpThemeURL() {
            return this.$store.state.themeURL
        },
        getHomeURL() {
            return this.$store.state.homeURL
        },
    },
    async created() {
        window.addEventListener("resize", this.manageOnResize);
        this.emitEvents();
    },
    destroyed() {
        window.removeEventListener("resize", this.manageOnResize);
    },
    mounted() {
        this.checkMenuState();
        this.manageOnResize();
        this.fetchModule();
    },
    methods: {
        emitEvents() {
            Event.$on('fetchReferralCode', () => {
                this.referralCode.success = false;
                this.referralCode.error = null;
                this.referralCode.errorData = [];
                this.referralCode.data = [];
                this.fetchReferralCode();
            });
        },
        manageOnResize() {
            let windowWidth = window.outerWidth;

            if (windowWidth >= 768) {
                this.isMobile = false;
            } else {
                this.isMobile = true;
                this.reduce = true;
            }
        },
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        gotReferralCode(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;
            }
        },
        fetchReferralCode() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.referrerID(isLoggedIn, 0),
                module: "gotData",
                store: "referralCode",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        checkMenuState() {
            if (this.$parent && this.$parent.$options.name !== "yuno-page-grid") {
                this.isPageGrid = false;
            }

            const store = sessionStorage.getItem('isLHSMenu');

            if (store !== null && !this.isMobile) {
                this.reduce = store === 'true' ? true : false;
                this.sidebarToggle(true);
            } else {
                this.reduce = false
            }
        },
        sidebarToggle(noToggle) {
            if (!noToggle) {
                if (this.reduce) {
                    sessionStorage.setItem("isLHSMenu", false);
                    this.reduce = false
                } else {
                    sessionStorage.setItem("isLHSMenu", true);
                    this.reduce = true
                }    
            }

            this.$emit('isMini', this.reduce);
        },
        chooseAccountState() {
            localStorage.setItem('userState', window.location.pathname);
            localStorage.setItem('isChooseAccountState', true);
        },
        unauthorizedModalClose() {
            window.location.href = "/logout";
        },
        fetchModule() {
            this.getStorage();
        },
        initTokenTime(minutes) {
            let setMinutes = parseInt(minutes - 10),
                timeout = parseInt(60000 * setMinutes); // minutes to miliseconds;

            setTimeout(() => {
                this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
            }, timeout);
        },
        doneRefreshAPIToken(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const data = options.response.data.data;

                this.config.yunoAPIToken = "Bearer "+ data.token +"";
                this.tokenExpiry.payload.token = "Bearer "+ data.token +"";
                this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
            };
        },
        refreshAPIToken(payload) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.apiTokenRefresh(),
                module: "gotData",
                store: "apiTokenRefresh",
                payload: JSON.stringify(payload),
                callback: true,
                callbackFunc: function(options) {
                    return instance.doneRefreshAPIToken(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        gotAPITokenExpiryTime(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const data = options.response.data.data,
                    minTime = 10;

                if (data.minutes <= minTime) {
                    let payload = {
                        user_id: isLoggedIn,
                        id_token: this.config.yunoAPIToken
                    };

                    this.refreshAPIToken(payload);
                } else {
                    this.initTokenTime(data.minutes);
                };
            };
        },
        fetchAPITokenExpiryTime(payload) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.apiTokenExpiry(isLoggedIn),
                module: "gotData",
                store: "apiTokenExpiryTime",
                payload: JSON.stringify(payload),
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotAPITokenExpiryTime(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        extractSlugFromURL(str) {
            // Remove the trailing slash from URl if it exists
            const currentUrl = str.replace(/\/$/, ''),
                urlParts = currentUrl.split("/");

            // Remove empty string from the end if the URL ends with a slash
            if (urlParts[urlParts.length - 1] === "") {
                urlParts.pop();
            }

            // Get the value between the last two slashes
            
            const value = urlParts[urlParts.length - 1];

            return value;
        },
        manageCurrentPage(data) {
            const normalizeUrl = url => url.replace(/\/$/, '');
            const currentUrl = normalizeUrl(window.location.origin + window.location.pathname);
        
            // Iterate through the array and set is_active to true if url matches currentUrl
            data.forEach(section => {
                section.items.forEach(item => {
                    item.is_active = currentUrl === normalizeUrl(item.url);
        
                    let isAnySubItemActive = false;
                    item.sub_items.forEach(subItem => {
                        subItem.is_active = currentUrl === normalizeUrl(subItem.url);
        
                        // Determine if any subItem is active
                        if (subItem.is_active && subItem.parent_id === item.id) {
                            isAnySubItemActive = true;
                        } 
                    });
        
                    // Set item.is_expended based on subItem activity and item activity
                    if (isAnySubItemActive) {
                        item.is_expended = true;
                    } else {
                        item.is_expended = false;
                    }
                });
            });
        },
        activeOrg() {
            const activeOrg = this.userInfo.data.current_state.org_id;

            if (activeOrg) {
                return activeOrg;
            }
        },
        gotPostLoginMenu(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200 || !options) {
                let data = "";

                if (!options) {
                    data = this.header.data;
                    this.header.success = true;
                } else {
                    data = options.response.data.data;
                }

                this.manageCurrentPage(data);
                this.header.data = data;
                this.setStorage()
                this.$emit('menuLoaded');
            };
        },
        fetchPostLoginMenu(role) {
            const props = {
                userID: isLoggedIn,
                orgID: this.userInfo.data.role === "org-admin" ? this.activeOrg() : 0,
            };

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.header("menu", props),
                module: "gotData",
                store: "header",
                addToModule: false,
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotPostLoginMenu(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        manageOrgAdmin(userData) {
            const { host } = YUNOCommon.config;
            const { has_org, org_id } = userData;
            const activeOrg = sessionStorage.getItem("activeOrg");

            if (activeOrg === null) {
                if (!has_org) {
                    window.location.href = `${host()}/create-organization-account`;
                } else if (org_id.length > 1) {
                    window.location.href = `${host()}/select-an-organization`;
                    sessionStorage.setItem('redirectURL', window.location.pathname + window.location.search);
                } else {
                    sessionStorage.setItem('activeOrg', JSON.stringify(org_id[0].id));
                }    
            }
        },
        gotUserInfo(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200 || !options) {
                let data = "";

                if (!options) {
                    data = this.userInfo.data;
                    this.userInfo.success = true;
                } else {
                    data = options.response.data.data;
                };

                if (this.header.data.length !== 0) {
                    this.gotPostLoginMenu(false);
                } else {
                    this.fetchPostLoginMenu(data.role);
                }
                
                this.userRole.data = data.role;
                this.userProfile.data = data;
                this.userProfile.success = true;

                if (data.role === "Instructor") {
                    // this.fetchReferralCode();
                }

                if (data.role === "Learner") {
                    this.fetchReferralCode();
                }

                if (data.role === "Learner" && data.is_signup_completed === "pending") {
                    const userState = localStorage.getItem('userState'),
                        windowURL = window.location.pathname + window.location.search;

                    if (windowURL !== userState) {
                        window.location.href = YUNOCommon.config.host() + "/sign-up";
                        setTimeout(() => { 
                            localStorage.removeItem('skipSignUp');
                        }, 10);
                    };
                }

                if (data.role === "org-admin") {
                    // this.manageOrgAdmin(data);    
                }

                this.$emit('userInfo', data);
            }
        },
        fetchUserInfo() {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.userInfoAPI(isLoggedIn, false),
                module: "gotData",
                store: "userInfo",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotUserInfo(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        getStorage() {
            const storage = this.storage;
            let version = Number(JSON.parse(JSON.stringify(storage.version)));
                lastStorage = storage.name + "V" + (--version);

            sessionStorage.removeItem(lastStorage);
            const store = sessionStorage.getItem(storage.name + "V" + storage.version);

            if (store !== null) {
                const data = JSON.parse(store);
                this.header.data = data.menu;
            };

            this.loginStatus();
        },
        setStorage() {
            const storage = this.storage;
            const store = {
                menu: this.header.data
            };
            

            if (this.userInfo.data.is_signup_completed === "completed") {
                sessionStorage.setItem(storage.name + "V" + storage.version, JSON.stringify(store));
            };
        },
        loginStatus() {
            let userID = Number(isLoggedIn); // Logged-in user id
            
            if (userID !== 0) {
                this.user.isLoggedin = true;
                
                if (this.userInfo.data.length !== 0) {
                    this.gotUserInfo(false)
                } else {
                    this.fetchUserInfo();
                }
                
                // this.fetchAPITokenExpiryTime(this.tokenExpiry.payload);
                this.$emit('login', this.user.isLoggedin);
            } else {
                const storage = this.storage;

                sessionStorage.removeItem(storage.name + "V" + storage.version);
                this.user.isLoggedin = false;
                this.$emit('login', this.user.isLoggedin);
            }
        },
    }
});
Vue.component('yuno-main-nav', {
    props: ["data", "options", "isPageGrid"],
    template: `
        <b-menu class="is-custom-mobile">
            <nav class="menuWrapper">
                <template v-if="header.loading || userInfo.loading">
                    <b-skeleton v-for="i in menuLoading" :key="i" active></b-skeleton>
                </template>
                <template v-if="header.success">
                    <template v-if="header.error">
                        {{ header.errorData }}
                    </template>
                    <template v-else>
                        <template v-if="isPageGrid">
                            <b-menu-list 
                                :key="i"
                                :label="section.section"
                                v-for="(section, i) in header.data"
                                v-if="section.section !== 'Account'"
                            >       
                                <template v-for="(menu, j) in section.items">
                                    <b-menu-item 
                                        :key="'menu-' + j"
                                        :href="menu.url"
                                        :expanded="menu.is_expended"
                                        :active="menu.is_active"
                                        tag="a"
                                        :class="[menu.sub_items.length !== 0  ? 'hasSubmenu' : '', generateClass(menu), section.slug]"
                                        @click="manageNavItem($event, menu)"
                                    >
                                        <template #label="props">
                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">
                                                <template v-if="props.expanded">
                                                    arrow_drop_down
                                                </template>
                                                <template v-else>
                                                    arrow_drop_up
                                                </template>
                                            </span>
                                            <template v-if="menu.slug === 'generate-code'">
                                                <template v-if="referralCode.loading">
                                                    <b-skeleton active></b-skeleton>
                                                </template>
                                                <template v-if="referralCode.success">
                                                    <template v-if="referralCode.error">
                                                        <template v-if="generateCode.loading">
                                                            <b-skeleton active></b-skeleton>
                                                        </template>
                                                        <template v-else>
                                                            <template v-if="options.isMini">
                                                                <b-tooltip label="Generate Code"
                                                                    type="is-dark"
                                                                    position="is-right">
                                                                    <div class="referralField" @click="generateReferralCode()">
                                                                        <span class="referralIcon"></span>
                                                                    </div>
                                                                </b-tooltip>
                                                            </template>
                                                            <template v-else>
                                                                <div class="referralField">
                                                                    <span class="referralIcon"></span>
                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">
                                                                        Generate Code
                                                                    </a>
                                                                </div>
                                                            </template>
                                                        </template>
                                                    </template>    
                                                    <template v-else>
                                                        <template v-if="options.isMini">
                                                            <b-tooltip label="Referral Code"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <div class="referralField isMini">
                                                                    <b-field>
                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                    </b-field>
                                                                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                        <span>Copy</span>
                                                                    </a>
                                                                </div>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <div class="referralField">
                                                                <span class="referralIcon"></span>
                                                                <b-field>
                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                </b-field>
                                                                <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                    <span class="caption">Copy</span>
                                                                </a>
                                                            </div>
                                                        </template>
                                                    </template>    
                                                </template>
                                            </template>
                                            <template v-else>
                                                <template v-if="options.isMini">
                                                    <b-tooltip :label="menu.label"
                                                        type="is-dark"
                                                        position="is-right">
                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>
                                                    </b-tooltip>
                                                </template>
                                                <template v-else>
                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>
                                                </template>
                                            </template>
                                        </template>
                                        <template v-if="menu.sub_items !== undefined">
                                            <template v-for="(submenu, k) in menu.sub_items">
                                                <b-menu-item
                                                    :key="'submenu-' + k"
                                                    :active="submenu.is_active"
                                                    :href="submenu.url"
                                                    tag="a"
                                                >
                                                    <template #label="props">
                                                        <template v-if="options.isMini">
                                                            <b-tooltip :label="submenu.label"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>
                                                        </template>
                                                    </template>
                                                </b-menu-item>
                                            </template>
                                        </template>
                                    </b-menu-item>
                                </template>
                            </b-menu-list> 
                        </template>
                        <template v-else>
                            <b-menu-list 
                                :key="i"
                                :label="section.section"
                                v-for="(section, i) in header.data"
                            >       
                                <template v-if="section.section === 'Account'">
                                    <template v-if="header.loading">
                                        <figure class="menuFooter loading">
                                            <b-skeleton circle width="35px" height="35px"></b-skeleton>
                                            <figcaption>
                                                <p class="userName"><b-skeleton active></b-skeleton></p>
                                            </figcaption>
                                        </figure>
                                    </template>
                                    <template v-if="header.success">
                                        <figure class="menuFooter" :class="[options.isMini ? 'isMini' : '']">
                                            <img :src="userInfo.data.profile_img" :alt="userInfo.data.yuno_display_name">
                                            <figcaption>
                                                <p class="userName">{{ userInfo.data.yuno_display_name }}</p>
                                                <p class="userEmail">{{ userInfo.data.email }}</p>
                                            </figcaption>
                                        </figure>
                                    </template>
                                </template>
                                <template v-for="(menu, j) in section.items">
                                    <b-menu-item 
                                        :key="'menu-' + j"
                                        :href="menu.url"
                                        :expanded="menu.is_expended"
                                        :active="menu.is_active"
                                        tag="a"
                                        :class="[menu.sub_items.length !== 0  ? 'hasSubmenu' : '', generateClass(menu), section.slug]"
                                        @click="manageNavItem($event, menu)"
                                    >
                                        <template #label="props">
                                            <span class="material-icons-outlined iconWrapper" v-if="menu.sub_items.length !== 0">
                                                <template v-if="props.expanded">
                                                    arrow_drop_down
                                                </template>
                                                <template v-else>
                                                    arrow_drop_up
                                                </template>
                                            </span>
                                            <template v-if="menu.slug === 'generate-code'">
                                                <template v-if="referralCode.loading">
                                                    <b-skeleton active></b-skeleton>
                                                </template>
                                                <template v-if="referralCode.success">
                                                    <template v-if="referralCode.error">
                                                        <template v-if="generateCode.loading">
                                                            <b-skeleton active></b-skeleton>
                                                        </template>
                                                        <template v-else>
                                                            <template v-if="options.isMini">
                                                                <b-tooltip label="Generate Code"
                                                                    type="is-dark"
                                                                    position="is-right">
                                                                    <div class="referralField" @click="generateReferralCode()">
                                                                        <span class="referralIcon"></span>
                                                                    </div>
                                                                </b-tooltip>
                                                            </template>
                                                            <template v-else>
                                                                <div class="referralField">
                                                                    <span class="referralIcon"></span>
                                                                    <a href="#" @click.prevent="generateReferralCode()" class="noLeftGap">
                                                                        Generate Code
                                                                    </a>
                                                                </div>
                                                            </template>
                                                        </template>
                                                    </template>    
                                                    <template v-else>
                                                        <template v-if="options.isMini">
                                                            <b-tooltip label="Referral Code"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <div class="referralField isMini">
                                                                    <b-field>
                                                                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                    </b-field>
                                                                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                        <span>Copy</span>
                                                                    </a>
                                                                </div>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <div class="referralField">
                                                                <span class="referralIcon"></span>
                                                                <b-field>
                                                                    <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                                                                </b-field>
                                                                <a href="#" @click.prevent="copyToClipboard('referralCode')">
                                                                    <span class="caption">Copy</span>
                                                                </a>
                                                            </div>
                                                        </template>
                                                    </template>    
                                                </template>
                                            </template>
                                            <template v-else>
                                                <template v-if="options.isMini">
                                                    <b-tooltip :label="menu.label"
                                                        type="is-dark"
                                                        position="is-right">
                                                        <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span>
                                                    </b-tooltip>
                                                </template>
                                                <template v-else>
                                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon_url }}</span> <span class="caption">{{ menu.label }}</span>
                                                </template>
                                            </template>
                                        </template>
                                        <template v-if="menu.sub_items !== undefined">
                                            <template v-for="(submenu, k) in menu.sub_items">
                                                <b-menu-item
                                                    :key="'submenu-' + k"
                                                    :active="submenu.is_active"
                                                    :href="submenu.url"
                                                    tag="a"
                                                >
                                                    <template #label="props">
                                                        <template v-if="options.isMini">
                                                            <b-tooltip :label="submenu.label"
                                                                type="is-dark"
                                                                position="is-right">
                                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span>
                                                            </b-tooltip>
                                                        </template>
                                                        <template v-else>
                                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon_url }}</span> <span class="caption">{{ submenu.label }}</span>
                                                        </template>
                                                    </template>
                                                </b-menu-item>
                                            </template>
                                        </template>
                                    </b-menu-item>
                                </template>
                            </b-menu-list>  
                        </template>
                    </template>
                </template>
            </nav>
        </b-menu>
    `,
    data() {
        return {
            menuLoading: 3
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'userInfo',
            'header',
            'referralCode',
            'generateCode'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        gotReferralCode(options) {
            this.generateCode.loading = false;

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const response = options.response.data;
                Event.$emit('fetchReferralCode');
            } else {
                const response = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${response.message}`,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        generateReferralCode() {
            this.generateCode.loading = true;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generateRefferralCode(),
                module: "gotData",
                store: "generateCode",
                payload: {
                    user_id: Number(isLoggedIn),
                    role: this.userRole.data
                },
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        manageNavItem(e, data) {
            if (data.sub_items.length !== 0) {
                e.preventDefault();
            }

            if (data.slug === "generate-code") {
                e.preventDefault();
            };

            if (data.label === "Switch Account") {
                localStorage.setItem('userState', window.location.pathname);
                localStorage.setItem('isChooseAccountState', true);
                // sessionStorage.removeItem("activeUserV1");
                sessionStorage.clear();
            }
        },
        manageLabel(role) {
            if (role === "Learner") {
                return "Learn"
            } else {
                return "Insights"
            }
        },
        generateClass(data) {
            const itemClass = data.label.replace(/\s/g, '').toLowerCase();
            return itemClass;
        }
    }
});
Vue.component('yuno-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper">
            <b-menu-list :label="manageLabel(userRole.data)">
                <template v-for="(menu, i) in data">
                    <b-menu-item 
                        :key="'menu-' + i"
                        :active="menu.isActive"
                        :expanded="menu.isExpanded"
                        :class="[menu.submenu !== undefined ? 'hasSubmenu' : '', generateClass(menu)]"
                        :href="menu.url"
                        tag="a"
                    >
                        <template #label="props">
                            <span class="material-icons-outlined iconWrapper" v-if="menu.submenu !== undefined">
                                <template v-if="props.expanded">
                                    expand_more
                                </template>
                                <template v-else>
                                    expand_less
                                </template>
                            </span>
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                        <template v-if="menu.submenu !== undefined">
                            <template v-for="(submenu, j) in menu.submenu">
                                <b-menu-item
                                    :key="'submenu-' + j"
                                    :active="submenu.isActive"
                                    :href="submenu.url"
                                    tag="a"
                                >
                                    <template #label="props">
                                        <template v-if="options.isMini">
                                            <b-tooltip :label="submenu.label"
                                                type="is-dark"
                                                position="is-right">
                                                <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span>
                                            </b-tooltip>
                                        </template>
                                        <template v-else>
                                            <span class="material-icons-outlined yunoIcon">{{ submenu.icon }}</span> <span class="caption">{{ submenu.label }}</span>
                                        </template>
                                    </template>
                                </b-menu-item>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list>  
        </nav>
    `,
    data() {
        return {
            
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        manageLabel(role) {
            if (role === "Learner") {
                return "Learn"
            } else {
                return "Insights"
            }
        },
        generateClass(data) {
            const itemClass = data.label.replace(/\s/g, '').toLowerCase();
            return itemClass;
        }
    }
});
Vue.component('yuno-referral-code', {
    props: ["data", "options"],
    template: `
        <div>
            <template v-if="options.isMini">
                <b-tooltip label="Referral Code"
                    type="is-dark"
                    position="is-right">
                    <div class="referralField isMini">
                        <b-field>
                            <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                        </b-field>
                        <a href="#" @click.prevent="copyToClipboard('referralCode')">
                            <span>Copy</span>
                        </a>
                    </div>
                </b-tooltip>
            </template>
            <template v-else>
                <div class="referralField">
                    <span class="referralIcon"></span>
                    <b-field>
                        <b-input id="referralCode" readonly :value="referralCode.data.referral_code"></b-input>
                    </b-field>
                    <a href="#" @click.prevent="copyToClipboard('referralCode')">
                        <span class="caption">Copy</span>
                    </a>
                </div>
            </template>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        }
    }
});

Vue.component('yuno-referral-code-generate', {
    props: ["data", "options"],
    template: `
        <div class="fluid">
            <template v-if="referralCode.error">
                <template v-if="moduleWithoutTab.success">
                    <template v-if="moduleWithoutTab.loading">
                        <div class="referralField">
                            <span class="referralIcon"></span>
                            <b-skeleton active></b-skeleton>
                        </div>
                    </template>
                    <template v-if="moduleWithoutTab.success">
                        <yuno-referral-code :options="options"></yuno-referral-code>    
                    </template>
                </template>
                <template v-else>
                    <template v-if="options.isMini">
                        <b-tooltip label="Generate Code"
                            type="is-dark"
                            position="is-right">
                            <div class="referralField" @click="generateCode()">
                                <span class="referralIcon"></span>
                            </div>
                        </b-tooltip>
                    </template>
                    <template v-else>
                        <div class="referralField" v-if="!moduleWithoutTab.loading && !moduleWithoutTab.success">
                            <span class="referralIcon"></span>
                            <a href="#" @click.prevent="generateCode()" class="noLeftGap">
                                Generate Code
                            </a>
                        </div>
                        <template v-if="moduleWithoutTab.loading">
                            <div class="referralField">
                                <span class="referralIcon"></span>
                                <b-skeleton active></b-skeleton>
                            </div>
                        </template>
                    </template>
                </template>
            </template>
            <template v-else>
                <yuno-referral-code :options="options"></yuno-referral-code>
            </template>
        </div>
    `,
    data() {
        return {
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
        gotReferralCode(options) {
            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 200) {
                const data = options.response.data.data;

                this.referralCode.data = data;
            }
        },
        fetchReferralCode() {
            this.moduleWithoutTab.data = [];
            this.moduleWithoutTab.error = null;
            this.moduleWithoutTab.success = false;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.referrerID(isLoggedIn, 0),
                module: "gotData",
                store: "moduleWithoutTab",
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotReferralCode(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        gotCode(options) {
            this.moduleWithoutTab.loading = false;

            if (options.response !== undefined && options.response.data !== undefined && options.response.data.code === 201) {
                const response = options.response.data;

                // this.$buefy.toast.open({
                //     duration: 5000,
                //     message: `${response.message}`,
                //     position: 'is-bottom'
                // });

                this.fetchReferralCode();
            } else {
                const response = options.response.data;

                this.$buefy.toast.open({
                    duration: 5000,
                    message: `${response.message}`,
                    position: 'is-bottom',
                    type: 'is-danger'
                });
            }
        },
        generateCode() {
            this.moduleWithoutTab.loading = true;

            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generateRefferralCode(),
                module: "gotData",
                store: "moduleWithoutTab",
                payload: {
                    user_id: Number(isLoggedIn),
                    role: this.userRole.data
                },
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotCode(options)
                }
            };

            this.$store.dispatch('postData', options);
        },
    }
});

Vue.component('yuno-referral-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper referral">
            <b-menu-list :label="manageLabel(userRole.data)">
                <b-menu-item 
                    href="#"
                    tag="a"
                >
                    <template #label="props">
                        <template v-if="userRole.data === 'Instructor'">
                            <yuno-referral-code :options="options"></yuno-referral-code>
                        </template>
                        <template v-if="userRole.data === 'Learner'">
                            <yuno-referral-code-generate :options="options"></yuno-referral-code-generate>
                        </template>
                    </template>
                </b-menu-item>
                <template v-for="(menu, i) in otherItems">
                    <b-menu-item 
                        :key="'menu-static' + i"
                        :active="menu.isActive"
                        :href="menu.url"
                        v-if="isItemAvailable(menu.role)"
                        tag="a"
                    >
                        <template #label="props">
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list>
        </nav>
    `,
    data() {
        return {
            otherItems: [
                {
                    label: "Earnings",
                    slug: "earnings",
                    role: ["Instructor", "Learner"],
                    icon: "currency_rupee",
                    iconType: "material-icons-outlined",
                    url: YUNOCommon.config.pickHost() + "/earnings/",
                    isActive: false,
                    callbackFunc: false
                },
                {
                    label: "How it works",
                    slug: "howItWorks",
                    role: ["Instructor", "Learner"],
                    icon: "help_outline",
                    iconType: "material-icons-outlined",
                    url: YUNOCommon.config.pickHost() + "/how-it-works/",
                    isActive: false,
                    callbackFunc: false
                }
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole',
            'referralCode',
            'moduleWithoutTab'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        manageLabel(role) {
            if (role === "Learner") {
                return "Referral"
            } else {
                return "Referral Earnings"
            }
        },
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        },
        copyToClipboard(ele) {
            let copyText = document.getElementById(ele);

            copyText.select();
            copyText.setSelectionRange(0, 99999)
            document.execCommand("copy");

            this.$buefy.toast.open({
                duration: 1000,
                message: `Copy to clipboard`,
            });
        },
    }
});
Vue.component('yuno-static-menu', {
    props: ["data", "options"],
    template: `
        <nav class="menuWrapper">
            <b-menu-list label="Account">
                <template v-for="(menu, i) in data">
                    <b-menu-item 
                        :key="'menu-static' + i"
                        :active="menu.isActive"
                        :href="menu.url"
                        v-if="isItemAvailable(menu.role)"
                        tag="a"
                    >
                        <template #label="props">
                            <template v-if="options.isMini">
                                <b-tooltip :label="menu.label"
                                    type="is-dark"
                                    position="is-right">
                                    <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span>
                                </b-tooltip>
                            </template>
                            <template v-else>
                                <span class="material-icons-outlined yunoIcon">{{ menu.icon }}</span> <span class="caption">{{ menu.label }}</span>
                            </template>
                        </template>
                    </b-menu-item>
                </template>
            </b-menu-list> 
        </nav>
    `,
    data() {
        return {
            
            
        }
    },
    computed: {
        ...Vuex.mapState([
            'userRole'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        isItemAvailable(data) {
            if (YUNOCommon.findInArray(data, this.userRole.data)) {
                return true;
            } else {
                return false;
            }
        }
    }
});
const YUNOTable = (function($) {
    
    const table = function() {
        Vue.component('yuno-table', {
            props: ["data", "options", "tabindex"],
            template: `
                <div class="yunoTable" :class="{'container-fluid': options.isFluid, 'container': options.isFluid === undefined || !options.isFluid}">
                    <template v-if="data.loading">
                        <b-skeleton height="500px"></b-skeleton>
                    </template>
                    <template v-if="data.success">
                        <ul class="nestedFilters" v-if="data.nestedTabs !== undefined">
                            <li 
                                v-for="(nestedTab, nestedTabIndex) in data.nestedTabs"
                                :key="nestedTabIndex"
                                :class="{'active': nestedTab.isActive}">
                                <a href="#" @click="manageNestedTabs($event, nestedTabIndex, data, nestedTab, tabindex)">{{nestedTab.label}}</a>
                            </li>
                        </ul>
                        <div class="field clearFilters" v-if="data.appliedFilters !== undefined && data.appliedFilters.length !== 0">
                            <b-tag
                                v-for="(applied, appliedIndex) in data.appliedFilters"
                                :key="appliedIndex"
                                attached
                                closable
                                aria-close-label="Close tag"
                                @close="onFilterClear(applied, tabindex)">
                                <span v-html="applied.label"></span>
                            </b-tag>
                        </div>
                        <div class="filterWrap" v-if="data.filters !== undefined">
                            <template v-for="(filter, filterIndex) in data.filters">
                                <template v-if="filter.isActive && filter.module === 'dropdown'">
                                    <b-dropdown
                                        :key="filterIndex"
                                        v-model="filter.selected"
                                        :multiple="filter.canSelectMulti === undefined ? false : filter.canSelectMulti"
                                        aria-role="list"
                                        @change="onFilterChange($event, filter.type)"
                                        class="filterMenu"
                                        :class="filter.type === 'tableColumn' ? 'selectColumns' : ''">
                                        <button class="button is-primary" type="button" slot="trigger" slot-scope="{ active }">
                                            <template v-if="filter.type === 'tableColumn'">
                                                <span><i class="fa fa-cog" aria-hidden="true"></i></span>
                                            </template>
                                            <template v-else>
                                                <span>{{filter.selected}}</span>
                                            </template>
                                            <b-icon :icon="active ? 'menu-up' : 'menu-down'"></b-icon>
                                        </button>
                                        <b-dropdown-item v-if="filter.type === 'tableColumn'" class="dropdownTitle">
                                            Hide Columns
                                        </b-dropdown-item>
                                        <template v-for="(item, itemIndex) in filter.items">
                                            <b-dropdown-item 
                                                @click="onFilterItemSelect(item.val, filter.type, item.label, item.default, tabindex)"
                                                :value="filter.canSelectMulti === undefined ? item.label : item.val"
                                                :key="itemIndex"
                                                aria-role="listitem">
                                                <span>{{item.label}}</span>
                                            </b-dropdown-item>
                                        </template>
                                    </b-dropdown>
                                </template>
                                <template v-if="filter.isActive && filter.module === 'autocomplete'">
                                    <b-field :class="'filter'+filter.type">
                                        <b-autocomplete
                                            v-model="filter.selected"
                                            :data="filteredAutocomplete(filter, 'search')"
                                            :placeholder="filter.placeholder"
                                            field="search"
                                            @select="onAutocompleteSelect($event, tabindex, filter.type)"
                                            :clearable="true">
                                            <template slot="empty">No results for {{filter.selected}}</template>
                                            <template slot-scope="props">
                                                <template v-if="filter.type === 'course'">
                                                    <div class="courseList">
                                                        <figure class="img">
                                                            <img v-if="props.option.image !== ''" width="40" height="40" :src="props.option.image" :alt="props.option.label">
                                                            <i v-else class="fa fa-user-circle-o" aria-hidden="true"></i>
                                                        </figure>
                                                        <div class="courseContent">
                                                            <h4 class="courseTitle" v-html="props.option.label"></h4>
                                                            <div class="groupContent">
                                                                <span class="instructorName" v-if="props.option.instructor">{{props.option.instructor}},</span>
                                                                <span class="dateTime">{{props.option.date}}</span>
                                                                <span class="batchLabel" v-if="props.option.batchLabel">({{props.option.batchLabel}})</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </template>
                                                <template v-else> 
                                                    <div class="commonList">
                                                        <div class="courseContent">
                                                            <h4 class="courseTitle" v-html="props.option.label"></h4>
                                                        </div>
                                                    </div>
                                                </template>
                                            </template>
                                        </b-autocomplete>
                                    </b-field>
                                </template>
                                <template v-if="filter.isActive && filter.module === 'autocompleteQuerySearch'">
                                    <b-field :class="'filter'+filter.type">
                                        <b-autocomplete
                                            v-model="filter.selected"
                                            :data="filter.items"
                                            :loading="filter.isLoading"
                                            :placeholder="filter.placeholder"
                                            field="search"
                                            @typing="fetchQueryData($event, filter)"
                                            @select="onAutocompleteSelect($event, tabindex, filter.type)"
                                            :clearable="true">
                                            <template slot="empty">No results for {{filter.selected}}</template>
                                        </b-autocomplete>
                                    </b-field>
                                </template>
                            </template>
                        </div>
                        <template v-if="data.error">
                            <yuno-empty-states :options="{'state': 'dataNotFound', 'description': data.errorData}"></yuno-empty-states>
                        </template>
                            <b-table
                                :class="{'scrollable': options.scrollable, 'tableInvisible': data.error}"
                                :loading="options.pageLoading"
                                :paginated="true"
                                :detailed="options.hasDetailed !== undefined ? options.hasDetailed : false"
                                :backend-pagination="options.apiPaginated !== undefined ? options.apiPaginated : false"
                                :total="options.totalResult !== undefined ? options.totalResult : 0"
                                :sticky-header="options.isStickyHeader !== undefined ? options.isStickyHeader : false"
                                :height="options.height !== undefined ? options.height : ''"
                                @page-change="onPageChange($event, tabindex)"
                                :per-page="options.perPage"
                                :current-page="options.currentPage !== undefined ? options.currentPage : 1"
                                ref="table"
                                :data="data.data.rows"
                                :default-sort="options.defaultSort"
                                :default-sort-direction="options.sortDirection !== undefined ? options.sortDirection : 'asc'"
                                :striped="options.hasStriped !== undefined ? options.hasStriped : true">
                                <template slot-scope="props">
                                    <b-table-column 
                                        v-for="(col, colIndex) in data.data.columns" 
                                        :key="colIndex" 
                                        :field="col.field" 
                                        :visible="col.isActive === undefined ? true : col.isActive"
                                        :label="col.label" 
                                        :sortable="col.sortable">
                                            <template slot-scope="props" slot="header">
                                                <template v-if="col.info_icon !== undefined && col.info_icon !== ''">
                                                    {{props.column.label}}
                                                    <b-tooltip :label="col.info_icon"
                                                        type="is-dark"
                                                        position="is-bottom">
                                                        <i class="fa fa-info-circle" aria-hidden="true"></i>
                                                    </b-tooltip>
                                                </template>
                                                <template v-else>
                                                    {{props.column.label}}
                                                </template>
                                            </template>
                                            <template v-if="col.field === 'action' || col.field === 'actions'">
                                                <div class="fieldVal ctaGroup noOverflow">
                                                    <template v-for="(item, itemIndex) in props.row['actions']">
                                                        <b-tooltip :label="item.label"
                                                            type="is-light"
                                                            :key="itemIndex"
                                                            position="is-left">
                                                            <template v-if="item.isAnchor === undefined || !item.isAnchor">
                                                                <b-button 
                                                                    :loading="item.isLoading !== undefined && item.isLoading ? true : false" 
                                                                    :disabled="item.isLoading !== undefined && item.isLoading ? true : false" 
                                                                    @click="onActionTrigger(props.row, item, tabindex)"
                                                                    class="yunoPrimaryCTA small iconOnly">
                                                                        <i 
                                                                            v-if="item.iconType === 'fa'" 
                                                                            :class="'fa ' +  item.icon" 
                                                                            aria-hidden="true">
                                                                        </i>
                                                                        <span class="material-icons" v-else>{{item.icon}}</span>
                                                                </b-button>
                                                            </template>
                                                            <template v-else>
                                                                <b-button 
                                                                    tag="a"
                                                                    :href="item.href"
                                                                    :target="item.target"
                                                                    class="yunoPrimaryCTA small iconOnly anchor">
                                                                        <i 
                                                                            v-if="item.iconType === 'fa'" 
                                                                            :class="'fa ' +  item.icon" 
                                                                            aria-hidden="true">
                                                                        </i>
                                                                </b-button>
                                                            </template>
                                                        </b-tooltip>
                                                    </template>
                                                </div>
                                            </template>
                                            <template v-else-if="col.hasAction">
                                                
                                                    <template v-for="(item, itemIndex) in props.row[col.field]">
                                                        <template v-if="item.hasClickToView">
                                                            <div class="fieldVal ctaGroup noOverflow">
                                                                <b-tooltip :label="item.label"
                                                                    type="is-light"
                                                                    :key="itemIndex"
                                                                    position="is-left">
                                                                    <template v-if="item.isAnchor === undefined || !item.isAnchor">
                                                                        <b-button 
                                                                            :loading="item.isLoading !== undefined && item.isLoading ? true : false" 
                                                                            :disabled="item.isLoading !== undefined && item.isLoading ? true : false" 
                                                                            @click="onActionTrigger(props.row, item, tabindex)"
                                                                            class="yunoPrimaryCTA small iconOnly">
                                                                                <i 
                                                                                    v-if="item.iconType === 'fa'" 
                                                                                    :class="'fa ' +  item.icon" 
                                                                                    aria-hidden="true">
                                                                                </i>
                                                                        </b-button>
                                                                    </template>
                                                                    <template v-else>
                                                                        <b-button 
                                                                            tag="a"
                                                                            :href="item.href"
                                                                            :target="item.target"
                                                                            class="yunoPrimaryCTA small iconOnly anchor">
                                                                                <i 
                                                                                    v-if="item.iconType === 'fa'" 
                                                                                    :class="'fa ' +  item.icon" 
                                                                                    aria-hidden="true">
                                                                                </i>
                                                                        </b-button>
                                                                    </template>
                                                                </b-tooltip>
                                                            </div>
                                                        </template>
                                                        <template v-else>
                                                            <div class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']">{{ item.value }}</div>
                                                        </template>
                                                    </template>
                                            </template>
                                            <template v-else-if="col.field === 'learner'">
                                                <ul class="user">
                                                    <li class="userImg"><img :src="props.row.learner.image" :alt="props.row.learner.name"></li>
                                                    <li class="userName">
                                                        <a :href="props.row.learner.profile_url" target="_blank">{{props.row.learner.name}}</a>
                                                    </li>
                                                </ul>
                                            </template>
                                            <template v-else-if="col.field === 'referrer'">
                                                <template v-if="props.row[col.field].id !== 0">
                                                    <ul class="user">
                                                        <li class="userImg"><img :src="props.row[col.field].image" :alt="props.row[col.field].name"></li>
                                                        <li class="userName">
                                                            {{props.row[col.field].name}}
                                                        </li>
                                                    </ul>
                                                </template>
                                                <template v-else>
                                                    <div class="fieldVal" :class="[col.field]">NA</div>
                                                </template>
                                            </template>
                                            <template v-else-if="col.field === 'attendance' && props.row.attendance.percentage !== undefined">
                                                <div class="fieldVal percentageBlock" :class="[col.field !== undefined ? col.field : '']">
                                                    <b-progress 
                                                        :type="{
                                                            'is-red': props.row.attendance.percentage <= 30,
                                                            'is-orange': props.row.attendance.percentage > 30,
                                                            'is-yellow': props.row.attendance.percentage > 50,
                                                            'is-lightGreen': props.row.attendance.percentage > 70,
                                                            'is-darkGreen': props.row.attendance.percentage > 80
                                                        }"  
                                                        format="percent"    
                                                        :value="Number(props.row.attendance.percentage)">
                                                        {{props.row.attendance.percentage}}
                                                    </b-progress>
                                                    <div class="percentage">{{props.row.attendance.percentage}}% <a href="#" @click="nestedTableModal($event, props.row, props.row[col.field])" v-if="props.row.attendance.attendanceModal !== undefined"><i class="fa fa-external-link-square" aria-hidden="true"></i></a></div>
                                                </div>
                                            </template>
                                            <template v-else-if="col.field === 'batch' && props.row.batch.percentage !== undefined">
                                                <div class="fieldVal percentageBlock" :class="[col.field !== undefined ? col.field : '']">
                                                    <b-progress 
                                                        :type="{
                                                            'is-red': props.row.batch.percentage <= 30,
                                                            'is-orange': props.row.batch.percentage > 30,
                                                            'is-yellow': props.row.batch.percentage > 50,
                                                            'is-lightGreen': props.row.batch.percentage > 70,
                                                            'is-darkGreen': props.row.batch.percentage > 80
                                                        }"  
                                                        format="percent"    
                                                        :value="Number(props.row.batch.percentage)">
                                                        {{props.row.batch.percentage}}
                                                    </b-progress>
                                                    <div class="percentage">{{props.row.batch.percentage}}%</div>
                                                </div>
                                            </template>
                                            <template v-else-if="col.field === 'name' && props.row['image'] !== undefined">
                                                <ul class="user" :class="{'hasTags': props.row.fieldWithTags !== undefined}">
                                                    <li class="userImg"><img :src="props.row['image']" :alt="props.row[col.field]"></li>
                                                    <li class="userName">
                                                        <template v-if="col.customEvent !== undefined && col.customEvent">
                                                            <a href="#" @click="onCustomEvent($event, props.row, col)">{{props.row[col.field]}}</a>
                                                        </template>
                                                        <template v-else>
                                                            {{props.row[col.field]}}    
                                                        </template>
                                                        <template v-if="props.row.fieldWithTags !== undefined && props.row.fieldWithTags.field === 'name'">
                                                            <div class="tagsWrapper">
                                                                <b-tag rounded
                                                                    v-for="(tag, tagIndex) in props.row.fieldWithTags.tags" :key="tagIndex">
                                                                    {{ tag }}
                                                                </b-tag>
                                                            </div>
                                                        </template>
                                                    </li>
                                                </ul>
                                            </template>
                                            <template v-else-if="col.isLink !== undefined && col.isLink">
                                                <template v-if="col.hasTooltip !== undefined && col.hasTooltip">
                                                    <b-tooltip :label="props.row[col.field]"
                                                        type="is-dark"
                                                        :position="col.tooltipPosition === undefined ? 'is-left' : col.tooltipPosition">
                                                        <div class="fieldVal withLink" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']"><a :href="props.row['url']" :target="col.linkTarget">{{ props.row[col.field] }}</a></div>    
                                                    </b-tooltip>
                                                </template>
                                                <template v-else>
                                                    <div class="fieldVal withLink" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']"><a :href="props.row['url']" :target="col.linkTarget">{{ props.row[col.field] }}</a></div>    
                                                </template>
                                            </template>
                                            <template v-else-if="props.row[col.field] !== undefined && col.copyToClipboard !== undefined && col.copyToClipboard">
                                                <template v-if="props.row[col.field].isEmpty === undefined || !props.row[col.field].isEmpty">
                                                    <div class="clipboard noField">
                                                        <div class="fieldWrapper">
                                                            <b-input :id="props.row[col.field].id" :value="props.row[col.field].url" readonly></b-input>
                                                        </div>
                                                        <b-tooltip :label="props.row[col.field].label"
                                                            type="is-light"
                                                            :position="props.row[col.field].position">
                                                            <i @click="copyToClipboard(props.row[col.field].id)" class="fa trigger fa-clipboard" aria-hidden="true"></i>    
                                                        </b-tooltip>
                                                    </div>
                                                </template>
                                                <template v-else>
                                                    <template v-if="col.withTag !== undefined && col.withTag">
                                                        <div class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']">
                                                            <span class="tag" :class="[{'is-success': props.row[col.field].url === 'online'}, {'is-warning': props.row[col.field].url === 'offline'}]">{{ props.row[col.field].url }}</span>
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']">{{ props.row[col.field].url }}</div>
                                                    </template>
                                                </template>
                                            </template>
                                            <template v-else-if="props.row[col.field] !== undefined && col.customColor !== undefined && col.customColor">
                                                <template v-if="col.customEvent !== undefined && col.customEvent">
                                                    <div class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '', props.row[col.field].colorClass !== undefined ? props.row[col.field].colorClass : '']"><a href="#" @click="onCustomEvent($event, props.row, col)">{{ props.row[col.field].val }}</a></div>
                                                </template>
                                                <template v-else>
                                                    <div class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '', props.row[col.field].colorClass !== undefined ? props.row[col.field].colorClass : '']">{{ props.row[col.field].val }}</div>
                                                </template>
                                            </template>
                                            <template v-else-if="props.row[col.field] !== undefined && col.userWithPic !== undefined && col.userWithPic">
                                                <ul class="user">
                                                    <li class="userImg"><img :src="props.row[col.field].image" :alt="props.row[col.field].name"></li>
                                                    <li class="userName">{{props.row[col.field].name}}</li>
                                                </ul>
                                            </template>
                                            <template v-else-if="col.hasArray !== undefined && col.hasArray">
                                                <div class="fieldVal arrayList" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']">
                                                    <template v-for="(item, itemIndex) in props.row[col.field]">
                                                        <span class="item">{{item}}</span>
                                                    </template>
                                                </div>
                                            </template>
                                            <template v-else-if="col.hasTooltip !== undefined && col.hasTooltip">
                                                <b-tooltip :label="props.row[col.field]"
                                                    type="is-dark"
                                                    :position="col.tooltipPosition === undefined ? 'is-left' : col.tooltipPosition">
                                                    <div class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']">{{ props.row[col.field] }}</div>    
                                                </b-tooltip>
                                            </template>
                                            <template v-else-if="col.hasTag !== undefined && col.hasTag">
                                                <div :id="props.row.scrollID !== undefined ? props.row.scrollID : ''" class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']">
                                                    <span class="tag" 
                                                        :class="
                                                            [
                                                                {'is-success': props.row[col.field] === 'PAID'}, 
                                                                {'is-danger': props.row[col.field] === 'EXPIRED'},
                                                                {'is-warning': props.row[col.field] === 'FAILED'},
                                                                {'is-info': props.row[col.field] === 'ISSUED'},
                                                                {'is-info noBG': col.hasSlot === true},
                                                                {'is-success': props.row[col.field] === 'ACTIVE'},
                                                                {'is-success noText': props.row[col.field] === true},
                                                                {'is-light noText': props.row[col.field] === false},
                                                                {'is-warning': props.row[col.field] === 'INACTIVE'}
                                                            ]">
                                                        {{ props.row[col.field] }}
                                                    </span>
                                                </div>
                                            </template>
                                            <template v-else-if="col.isInstructor !== undefined && col.isInstructor">
                                                <ul class="user">
                                                    <li class="userImg"><img :src="props.row['instructor'] !== undefined ? props.row['instructor'].image : props.row['instructor_image']" :alt="props.row[col.field]"></li>
                                                    <li class="userName">
                                                        <template v-if="props.row['instructor'] !== undefined">
                                                            {{props.row['instructor'].name}}
                                                        </template>
                                                        <template v-else>
                                                            {{props.row[col.field]}}
                                                        </template>
                                                    </li>
                                                </ul>
                                            </template>
                                            <template v-else-if="col.isOrg !== undefined && col.isOrg">
                                                <template v-if="props.row['org_admin'].id !== 0 && props.row['org_admin'].id !== ''">
                                                    <ul class="user">
                                                        <li class="userImg"><img :src="props.row['org_admin'].image" :alt="props.row['org_admin'].name"></li>
                                                        <li class="userName">
                                                            {{props.row['org_admin'].name}}
                                                        </li>
                                                    </ul>
                                                </template>
                                                <template v-else>
                                                    <div class="fieldVal">NA</div>
                                                </template>
                                            </template>
                                            <template v-else-if="col.hasCombined !== undefined">
                                                <div class="fieldVal" :class="[col.field !== undefined ? col.field : '']">
                                                    <template v-if="props.row[col.field] === props.row[col.hasCombined.otherField]">
                                                        <b-tag class="colorRed" rounded>{{ props.row[col.field] }} of {{ props.row[col.hasCombined.otherField] }}</b-tag>
                                                    </template>
                                                    <template v-else>
                                                        <b-tag class="colorGreen" rounded>{{ props.row[col.field] }} of {{ props.row[col.hasCombined.otherField] }}</b-tag>
                                                    </template>
                                                </div>
                                            </template>
                                            <template v-else-if="col.isCounselor !== undefined && col.isCounselor">
                                                <template v-if="props.row[col.field] !== 'NA'"> 
                                                    <ul class="user">
                                                        <li class="userImg"><img :src="props.row['counselor_image']" :alt="props.row[col.field]"></li>
                                                        <li class="userName">{{props.row[col.field]}}</li>
                                                    </ul>
                                                </template>
                                                <template v-else>
                                                    {{props.row[col.field]}}
                                                </template>
                                            </template>
                                            <template v-else-if="col.hasCounselor !== undefined && col.hasCounselor">
                                                <template v-if="props.row.counselor.name !== 'NA'"> 
                                                    <ul class="user">
                                                        <li class="userImg"><img :src="props.row.counselor.image" :alt="props.row.counselor.name"></li>
                                                        <li class="userName">{{props.row.counselor.name}}</li>
                                                    </ul>
                                                </template>
                                                <template v-else> 
                                                    {{props.row.counselor.name}}
                                                </template>
                                            </template>
                                            <template v-else-if="col.hasHierarchy !== undefined && col.hasHierarchy">
                                                <div class="fieldVal hierarchyList" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']">
                                                    <template v-if="props.row[col.field].length > 1">
                                                        <template v-for="(item, itemIndex) in props.row[col.field]">
                                                            <template v-if="itemIndex === 0">
                                                                <span class="item">{{item}} <b-tooltip :label="props.row.isExpand ? 'Click to collapse' : 'Click to expand'"
                                                                    type="is-light"
                                                                    position="is-left">
                                                                    <i @click="toggleShowmore($event, props.row)" class="fa" :class="props.row.isExpand ? 'fa-minus-circle' : 'fa-plus-circle'" aria-hidden="true"></i>
                                                                </b-tooltip></span>
                                                            </template>
                                                            <template v-else>
                                                                <template v-if="props.row.isExpand">
                                                                    <span class="item">{{item}}</span>
                                                                </template>
                                                            </template>
                                                        </template>
                                                    </template>
                                                    <template v-else>
                                                        <template v-for="(item, itemIndex) in props.row[col.field]">
                                                            <template v-if="itemIndex === 0">
                                                                <span class="item">{{item}}</span>
                                                            </template>
                                                        </template>
                                                    </template>
                                                    
                                                </div>
                                            </template>
                                            <template v-else-if="col.customEvent !== undefined && col.customEvent">
                                                <div class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']"><a href="#" @click="onCustomEvent($event, props.row, col)">{{ props.row[col.field] }}</a></div>
                                            </template>
                                            <template v-else>
                                                <div class="fieldVal" :class="[col.allowWrap ? 'allowWrap' : '', col.field !== undefined ? col.field : '']">{{ props.row[col.field] }}</div>
                                            </template>    
                                    </b-table-column>
                                </template>
                                <template slot="detail" slot-scope="props" v-if="options.hasDetailed !== undefined && options.hasDetailed">
                                    <template v-if="props.row.detail.type === 'table-grid'">
                                        <b-table
                                            :data="props.row.detail.data.rows">
                                            <template slot-scope="propsrow">
                                                <b-table-column 
                                                    v-for="(nestedCol, nestedColIndex) in props.row.detail.data.columns" 
                                                    :key="nestedColIndex" 
                                                    :field="nestedCol.field" 
                                                    :label="nestedCol.label" 
                                                    :sortable="nestedCol.sortable">
                                                    <template v-if="nestedCol.field === 'batch' && propsrow.row[nestedCol.field].attendanceModal !== undefined">
                                                        <div class="fieldVal">
                                                            <a href="#" @click="nestedTableModal($event, propsrow.row, propsrow.row[nestedCol.field], true)"><i class="fa fa-external-link-square" aria-hidden="true"></i> {{propsrow.row[nestedCol.field].label}}</a>
                                                        </div>        
                                                    </template>
                                                    <template v-else-if="nestedCol.field === 'instructor'">
                                                        <ul class="user">
                                                            <li class="userImg"><img :src="propsrow.row[nestedCol.field].image" :alt="propsrow.row[nestedCol.field].name"></li>
                                                            <li class="userName">
                                                                {{propsrow.row[nestedCol.field].name}}
                                                            </li>
                                                        </ul>
                                                    </template>
                                                    <template v-else-if="nestedCol.field === 'attendance' && propsrow.row[nestedCol.field].percentage !== undefined">
                                                        <div class="fieldVal percentageBlock">
                                                            <b-progress 
                                                                :type="{
                                                                    'is-red': propsrow.row[nestedCol.field].percentage <= 30,
                                                                    'is-orange': propsrow.row[nestedCol.field].percentage > 30,
                                                                    'is-yellow': propsrow.row[nestedCol.field].percentage > 50,
                                                                    'is-lightGreen': propsrow.row[nestedCol.field].percentage > 70,
                                                                    'is-darkGreen': propsrow.row[nestedCol.field].percentage > 80
                                                                }"  
                                                                format="percent"    
                                                                :value="propsrow.row[nestedCol.field].percentage">
                                                            </b-progress>
                                                            <div class="percentage">{{propsrow.row[nestedCol.field].percentage}}%</div>
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div class="fieldVal" :class="[nestedCol.field !== undefined ? nestedCol.field : '']">{{propsrow.row[nestedCol.field]}}</div>
                                                    </template>
                                                </b-table-column>
                                            </template>
                                        </b-table>
                                    </template>
                                    <template v-if="props.row.detail.type === 'list'">
                                        <template v-if="props.row.detail.data.length !== 0">
                                            <ul class="detailList">
                                                <li v-for="(item, itemIndex) in props.row.detail.data">
                                                    <figure class="listImage">
                                                        <img :src="item.image" :alt="item.name">
                                                    </figure>
                                                    <div class="listInfo">
                                                        <p class="listTitle">{{item.name}}</p>
                                                        <small>{{item.email}}</small>
                                                    </div>
                                                </li>
                                            </ul> 
                                        </template>
                                        <template v-else>
                                            <p class="listMessage"><i class="fa fa-exclamation-circle" aria-hidden="true"></i> {{props.row.detail.message}}</p>
                                        </template>
                                    </template>
                                </template>
                            </b-table>
                        
                    </template>
                </div>
            `,
            data() {
                return {
                        
                }
            },
            computed: {
                
            },
            created() {
                
            },
            mounted() {
                
            },
            methods: {
                nestedTableModal($event, nestedTableRow, currentObj, isDetailModal) {
                    $event.preventDefault();

                    Event.$emit('nestedTableModal', $event, nestedTableRow, currentObj, isDetailModal);
                },
                toggleShowmore($event, row) {
                    $event.preventDefault();

                    if (row.isExpand) {
                        row.isExpand = false
                    } else {
                        row.isExpand = true
                    }
                },
                filteredAutocomplete(data, key) {
                    return data.items.filter(option => {
                        return (
                            option[key].toString().toLowerCase().indexOf(data.selected.toLowerCase()) >= 0
                        )
                    })
                },
                scrollToEle(tab) {
                    let totalHeight = (YUNOCommon.heightOfEle(document.querySelectorAll(".yunoHeader")[0]) + YUNOCommon.heightOfEle(document.querySelectorAll(".yunoTabNav")[0], true) + 10);

                    jQuery([document.documentElement, document.body]).animate({
                        scrollTop: jQuery("#"+tab.url+"").offset().top - totalHeight
                    }, 500);
                },
                copyToClipboard(ele) {
                    let copyText = document.getElementById(ele);

                    copyText.select();
                    copyText.setSelectionRange(0, 99999)
                    document.execCommand("copy");

                    this.$buefy.toast.open({
                        duration: 1000,
                        message: `Copy to clipboard`,
                        position: 'is-bottom'
                    });
                },
                onCustomEvent(e, row, col) {
                    Event.$emit('onCustomEvent', e, row, col);
                },
                onPageChange(page, tabIndex) {
                    Event.$emit('onTablePageChange', page, tabIndex);

                    if (this.$props.data.manageState) {
                        this.manageState(this.$props.data, tabIndex, page)    
                    }
                },
                onActionTrigger(row, action, tabindex) {
                    Event.$emit('onActionTrigger', row, action, tabindex);
                },
                onFilterChange(filterVal, filter) {
                    Event.$emit('onFilterChange', filter, filterVal);
                },
                manageState(tab, index, page) {
                    let existingTab =  JSON.parse(JSON.stringify(tab)),
                        filters = tab.filters,
                        tableColumn = YUNOCommon.findObjectByKey(filters, "type", "tableColumn");

                    const pushToArray = function(array, putTo) {
                        for (let i = 0; i < array.length; i++) {
                            const item = array[i];
                            putTo.push(item)
                        }
                    };

                    const state = {
                        tab: existingTab.tab,
                        index: index,
                        defaultFilters: [],
                        appliedFilters: [],
                        nestedTabs: "",
                        hideColumns: [],
                        page: page !== false ? page : 1
                    };

                    if (tableColumn !== null) {
                        pushToArray(tableColumn.selected, state.hideColumns);
                    }

                    if (tab.nestedTabs !== undefined) {
                        let getActive = YUNOCommon.findObjectByKey(tab.nestedTabs, "isActive", true);
                        state.nestedTabs = getActive.value;
                    }

                    pushToArray(existingTab.defaultFilters, state.defaultFilters);
                    pushToArray(existingTab.appliedFilters, state.appliedFilters);

                    const stateToString = encodeURI(JSON.stringify(state));
                    
                    if (history.pushState) {
                        var newurl = window.location.protocol + "//" + window.location.host + window.location.pathname + '?state='+ stateToString +'';
                        window.history.pushState({path:newurl},'',newurl);
                    }
                },
                onFilterItemSelect(itemVal, filterType, itemLabel, itemDefault, tabIndex) {
                    Event.$emit('onFilterItemSelect', itemVal, filterType, itemLabel, itemDefault, tabIndex);
                    if (this.$props.data.manageState) {
                        this.manageState(this.$props.data, tabIndex, false)
                    }
                },
                fetchQueryData: _.debounce(function (name, filter) {
                    if (name.length > 2) {
                        filter.isLoading = true;
                        Event.$emit('fetchQueryData', name, filter);    
                    } else {
                        filter.data = []
                        return
                    }

                }, 500),
                onAutocompleteSelect(data, tabIndex, filterType) {
                    Event.$emit('onAutocompleteSelect', data, tabIndex, filterType);
                    
                    if (this.$props.data.manageState) {
                        this.manageState(this.$props.data, tabIndex, false)
                    }
                },
                onFilterClear(filter, tabIndex) {
                    Event.$emit('onFilterClear', filter, tabIndex);
                    
                    if (this.$props.data.manageState) {
                        this.manageState(this.$props.data, tabIndex, false)
                    }
                },
                manageNestedTabs($event, tabIndex, tab, nestedTab, contentIndex) {
                    event.preventDefault();

                    let getTabs = tab.nestedTabs;

                    for (let i = 0; i < getTabs.length; i++) {
                        if (getTabs[i].isActive) {
                            getTabs[i].isActive = false;
                        }
                    }

                    getTabs[tabIndex].isActive = true;

                    Event.$emit('onNestedTabChange', tab, nestedTab, contentIndex);
                    if (this.$props.data.manageState) {
                        this.manageState(tab, contentIndex, false);
                    };
                },
            }
        });
    };

    return {
        table: table
    };
})(jQuery);


const YUNOCreateBatch = (function($) {
    const AUTHORIZED_ROLES = [
        "yuno-admin",
        "yuno-category-admin",
        "Instructor",
        "org-admin"
    ];
    const DAYS_OF_WEEK = [
        {
            label: "Monday",
            slug: "Mon"
        },
        {
            label: "Tuesday",
            slug: "Tue"
        },
        {
            label: "Wednesday",
            slug: "Wed"
        },
        {
            label: "Thursday",
            slug: "Thu"
        },
        {
            label: "Friday",
            slug: "Fri"
        },
        {
            label: "Saturday",
            slug: "Sat"
        },
        {
            label: "Sunday",
            slug: "Sun"
        }
    ];

    const createBatch = function() {
        const validationMsg = {
            "messages": {
                "required": "This field is required",
                "numeric": "Numbers only",
                "min": "Minimum 10 numbers required",
                "max": "Maximum 15 numbers required",
                "is": "This field is required",
                "is_not": "New batch shouldn't be same as current batch"
            }
        };

        YUNOCommon.assignVValidationObj(validationMsg);
        YUNOTable.table();

        Vue.component('yuno-create-batch', {
            template: `
                <yuno-page-grid
                    :authorizedRoles="authorizedRoles"
                    @onUserInfo="onUserInfo"
                    :hasSearchBar="false"
                >
                    <template v-slot:main>
                        <section id="createBatch" class="container-fluid formSection createBatch ">
                            <template v-if="isFormLoading">
                                <figure class="infiniteSpinner">
                                    <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                                </figure>
                            </template>
                            <template v-if="isFormReady">
                                <template v-if="isEditBatch">
                                    <h1 class="sectionTitle">Update Batch</h1>    
                                </template>
                                <template v-else>
                                    <h1 class="sectionTitle">Create New Batch</h1>    
                                </template>
                                <validation-observer 
                                    tag="div" 
                                    ref="createBatchObserver" 
                                    v-slot="{ handleSubmit, invalid }">
                                    <form id="createBatchForm" @submit.prevent="handleSubmit(initForm)">
                                        <div class="row isRelative">
                                            <div class="col-12 col-md-5 col-lg-5 noRelative">
                                                <div class="formWrapper">
                                                    <b-field label="Batch Label">
                                                        <validation-provider 
                                                            :rules="{required:true}" 
                                                            v-slot="{ errors, classes }">
                                                            <b-input :class="classes" 
                                                                placeholder="Add a batch title" 
                                                                v-model="payload.title">
                                                            </b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Course" id="courseList">
                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-taginput
                                                                :class="classes"
                                                                v-model="courseSelection.selected"
                                                                :data="courseSelection.filtered"
                                                                autocomplete
                                                                :disabled="isEditBatch ? true : false"
                                                                field="product_code"
                                                                placeholder="Add a course"
                                                                @add="onCourseSelect($event, 'add')"
                                                                @remove="onCourseSelect($event, 'remove')"
                                                                @keydown.native="onCourseKeypress($event)"
                                                                @typing="getFilteredCourse">
                                                                <template v-slot="props">
                                                                    <span class="wrapper" :class="props.option.enroll_type">
                                                                        <strong>{{props.option.product_code}} <small class="helper">({{props.option.enroll_type}})</small></strong>
                                                                        <span class="courseData">
                                                                            <span class="courseLabel">Duration:</span>
                                                                            <span class="courseVal">{{props.option.duration_weeks}} Weeks</span>
                                                                            <span class="courseLabel">Price:</span>
                                                                            <span class="courseVal"><template v-if="props.option.unit_price !== '0'">&#8377;</template>{{props.option.unit_price}}</span>
                                                                        </span>
                                                                    </span>
                                                                </template>
                                                                <template #empty>
                                                                    There are no items
                                                                </template>
                                                            </b-taginput>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Instructor" v-if="instructor.isActive">
                                                        <template v-if="instructor.isLoading">
                                                            <div class="smallLoader withField"></div>
                                                        </template>
                                                        <template v-if="mappedInstructor.success">
                                                            <validation-provider 
                                                                tag="div"
                                                                v-if="instructor.isField"
                                                                :customMessages="{ isNotBlank: errorMsg.instructor }"
                                                                :rules="{required:true, isNotBlank:instructor.selected}" 
                                                                v-slot="{ errors, classes }">
                                                                <b-autocomplete
                                                                    :class="classes"
                                                                    v-model="instructor.current"
                                                                    :data="filteredInstructor"
                                                                    placeholder="Add instructor"
                                                                    field="name"
                                                                    @input="onInstructorChange"
                                                                    @select="onInstructorSelect($event)"
                                                                    :clearable="true">
                                                                    <template slot="empty">No results for {{instructor.current}}</template>
                                                                </b-autocomplete>
                                                                <p class="error">{{errors[0]}}</p>
                                                            </validation-provider>
                                                        </template>
                                                    </b-field>
                                                    <b-field label="Start Date" v-if="courseDate">
                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-datepicker
                                                                @input="onDatepick"
                                                                :class="classes"
                                                                :date-formatter="formatDate"
                                                                v-model="payload.start_date"
                                                                placeholder="Pick date"
                                                                :mobile-native="false"
                                                                :min-date="startDate.minDate"
                                                                trap-focus>
                                                            </b-datepicker>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="End Date" v-if="courseDate">
                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-datepicker
                                                                :class="classes"
                                                                :date-formatter="formatDate"
                                                                v-model="payload.end_date"
                                                                :mobile-native="false"
                                                                :min-date="endDate.minDate"
                                                                :max-date="endDate.maxDate"
                                                                trap-focus>
                                                            </b-datepicker>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Days Of The Week">
                                                        <validation-provider tag="div" class="makeItGrid" :rules="{required:true, minLength:1}" v-slot="{ errors, classes }">
                                                            <template v-for="(day, dayIndex) in daysOfWeek">
                                                                <div class="field" :key="dayIndex">
                                                                    <b-checkbox
                                                                        :class="classes"
                                                                        :native-value="day.slug"
                                                                        v-model="payload.class_days">
                                                                        {{day.label}}
                                                                    </b-checkbox>
                                                                </div>
                                                            </template>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Class Duration" v-if="userRole.data !== 'Instructor'">
                                                        <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-select 
                                                                :class="classes"
                                                                v-model="payload.duration"
                                                                placeholder="Select">
                                                                <option value="">Select</option>
                                                                <option value="15">15 Minutes</option>
                                                                <option value="30">30 Minutes</option>
                                                                <option value="45">45 Minutes</option>
                                                                <option value="60">1 Hour</option>
                                                                <option value="75"> 1 Hour 15 Minutes</option>
                                                                <option value="90">1 Hour 30 Minutes</option>
                                                                <option value="105">1 Hour 45 Minutes</option>
                                                                <option value="120">2 Hours</option>
                                                            </b-select>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Class Time">
                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-timepicker
                                                                :class="classes"
                                                                v-model="payload.class_time"
                                                                placeholder="Pick time"
                                                                hour-format="12"
                                                                :mobile-native="false"
                                                                icon="clock">
                                                            </b-timepicker>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Max Seats">
                                                        <validation-provider tag="div" :rules="{required:false, numeric: true}" v-slot="{ errors, classes }">
                                                            <b-input 
                                                                :disabled="true"
                                                                v-model="payload.seats_max"
                                                                :class="classes">
                                                            </b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field>
                                                        <validation-provider tag="div" :rules="{required:false}" v-slot="{ errors, classes }">
                                                            <b-checkbox
                                                                :class="classes"
                                                                :native-value="payload.is_locked"
                                                                v-model="payload.is_locked">
                                                                <b-tooltip 
                                                                    type="is-dark"
                                                                    label="New enrollments cannot be made in a locked batch"
                                                                    :multilined="true"
                                                                    position="is-top">
                                                                    Lock this batch    
                                                                </b-tooltip>
                                                            </b-checkbox>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                </div>
                                                <div class="ctaWrapper">
                                                    <b-button
                                                        native-type="submit"
                                                        :loading="form.isLoading ? true : false"
                                                        :disabled="form.isLoading ? true : false"
                                                        class="yunoSecondaryCTA">
                                                        <template v-if="isEditBatch">
                                                            Update Batch
                                                        </template>
                                                        <template v-else>
                                                            Create Batch
                                                        </template>
                                                    </b-button>    
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </validation-observer>
                            </template>
                        </section>
                    </template>
                </yuno-page-grid>
            `,
            data() {
                return {
                    isMiniSidebar: false,
                    tableOptions: {
                        isFluid: false,
                        pageLoading: false,
                        apiPaginated: false,
                        totalResult: "",
                        perPage: 50,
                        limit: 20,
                        offset: 0,
                        hasStriped: false,
                        isStickyHeader: true,
                        height: "600"
                    },
                    startDate: {
                        minDate: new Date()
                    },
                    endDate: {
                        minDate: new Date(),
                        maxDate: new Date(),
                    },
                    disabledEnrollment: "",
                    courseDate: false,
                    daysOfWeek: DAYS_OF_WEEK,
                    courseSelection: {
                        isActive: false,
                        filtered: [],
                        selected: []
                    },
                    instructor: {
                        selected: null,
                        current: "",
                        isLoading: false,
                        isActive: false,
                        isField: false
                    },
                    errorMsg: {
                        instructor: "Please select the instructor from list"
                    },
                    form: {
                        isLoading: false
                    },
                    payload: {
                        title: "",
                        created_by: isLoggedIn,
                        course_id: [],
                        start_date: new Date(),
                        end_date: new Date(""),
                        class_time: new Date(),
                        instructor_id: "",
                        duration: "",
                        class_days: [],
                        personalisation: "",
                        seats_max: "",
                        is_locked: false,
                        teaching_mode: "online"
                    },
                    authorizedRoles: AUTHORIZED_ROLES
                }
            },
            computed: {
                ...Vuex.mapState([
                    'user',
                    'userInfo',
                    'header',
                    'userProfile',
                    'userRole',
                    'footer',
                    'loader',
                    'allCourses',
                    'mappedInstructor',
                    'batchCreateUpdate',
                    'batchDetail',
                    'instructorAvailabilityGrid',
                    'capabilities'
                ]),
                wpThemeURL() {
                    return this.$store.state.themeURL
                },
                isUserAuthorized : {
                    get() {
                        if (!YUNOCommon.findInArray(this.authorizedRoles, this.userRole.data)) {
                            return false;
                        }
                        const capabilities = this.capabilities.data;
                        const isValid = capabilities.isGovernGangs;
                        return this.userRole.data === "org-admin" || isValid;
                    }
                },
                isPageLoading: {
                    get() {
                        return this.userInfo.loading || this.capabilities.loading;
                    }
                },
                isPageReady: {
                    get() {
                        return !this.user.isLoggedin || (this.userInfo.success && this.capabilities.success);
                    }
                },
                isFormLoading: {
                    get() {
                        const isEditing = YUNOCommon.getQueryParameter("isEdit") !== false;
                        return this.allCourses.loading || this.capabilities.loading || (isEditing && this.batchDetail.loading);
                    }
                },
                isFormReady: {
                    get() {
                        const isEditing = YUNOCommon.getQueryParameter("isEdit") !== false;
                        const isReady = this.allCourses.success && this.capabilities.success;
                        return isEditing ? isReady && this.batchDetail.success : isReady;
                    }
                },
                emptyStates() {
                    return {
                        state: "notAuthorized"
                    }
                },
                isEditBatch() {
                    const getID = YUNOCommon.getQueryParameter("isEdit");

                    if (getID !== false) {
                        if (this.allCourses.success) {
                            this.initEdit(getID);    
                        }
                        return true;    
                    } else {
                        return false;
                    }
                },
                filteredInstructor: {
                    get() {
                        return this.mappedInstructor.data.filter(option => {
                            return (
                                option.name.toString().toLowerCase().indexOf(this.instructor.current.toLowerCase()) >= 0
                            )
                        })
                    }
                }
            },
            async created() {
                this.emitEvents();
            },
            mounted() {
                
            },
            methods: {
                onLogin(staus) {

                },
                onUserInfo(data) {
                    if ( YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
                        this.fetchCapabilities(false);

                        if (data.role === "Instructor") {
                            this.manageInstructorCase();    
                        }
                    }
                },
                onMini(data) {
                    this.isMiniSidebar = data;
                },
                onMenuLoaded() {
            
                },
                gotCapabilities(options, noAPICall) {
                    if (noAPICall) {
                        this.capabilities.loading = false;
                        this.capabilities.success = true;

                        const roleCapabilities = {
                            "yuno-admin": {
                                isClassesWisdom: true,
                                isEntranceWisdom: true,
                                isGangsWisdom: true,
                                isGovernClasses: true,
                                isGovernLearners: true,
                                isGovernGangs: true,
                                isSysopWisdom: true,
                                isPagoWisdom: true
                            },
                            "Counselor": {
                                isEntranceWisdom: true,
                                isClassesWisdom: true,
                                isGangsWisdom: true,
                                isGovernClasses: true,
                                isGovernLearners: true,
                                isSysopWisdom: true,
                                isPagoWisdom: true
                            }
                        };

                        if (roleCapabilities[this.userRole.data]) {
                            this.capabilities.data = roleCapabilities[this.userRole.data];
                        }

                        this.authorizedUser(this.userRole.data)

                    } else {
                        if (options.response?.data?.code === 200) {
                            this.capabilities.data = options.response.data.data;
                            this.authorizedUser(this.userRole.data)
                        };
                    };
                },
                fetchCapabilities(noAPICall) {
                    if (noAPICall) {
                        this.gotCapabilities(false, noAPICall);
                    } else {
                        const options = {
                            apiURL: YUNOCommon.config.capabilitiesAPI(isLoggedIn, false),
                            module: "gotData",
                            store: "capabilities",
                            addToModule: "false",
                            callback: true,
                            callbackFunc: (options) => this.gotCapabilities(options)
                        };

                        this.$store.dispatch('fetchData', options);
                    };
                },
                manageInstructorCase() {
                    this.payload.instructor_id = isLoggedIn;
                    this.payload.duration = 60;
                },
                emitEvents() {
                    Event.$on('gotUserMenu', () => {
                        if (this.user.isLoggedin) {
                            const role = this.userRole.data;
                            this.fetchCapabilities(false);
                            if (role === "Instructor") {
                                this.manageInstructorCase();
                            }
                        }
                    });
                },
                enrollmentType(type) {
                    this.disabledEnrollment = type === "rolling" ? "fixed" : "rolling";
                    this.courseSelection.selected = [];
                    this.courseSelection.filtered = [];
                    this.courseSelection.isActive = true;
                    this.payload.end_date = new Date("");
                    this.payload.start_date = new Date();
                    this.payload.course_id = [];
                },
                formatDate(d) {
                    return moment(d).format('MMMM DD, YYYY');
                },
                onDatepick(value) {
                    let payload = this.payload;
                    
                    payload.end_date = new Date(this.generateClassEndDate());
                    this.endDate.maxDate = new Date(this.generateClassEndDate());
                    this.endDate.minDate = new Date(value);

                    const currentDate = new Date();
                    this.startDate.minDate = new Date(currentDate.setDate(currentDate.getDate() - 1));

                },
                onInstructorChange(val) {
                    if (val === "") {
                        this.payload.instructor_id = "";
                    }
                },
                onInstructorSelect($event) {
                    if ($event !== null) {
                        this.instructor.selected = $event;
                        this.payload.instructor_id = $event.instructor_id;
                        // this.fetchResources(true, this.payload.instructor_id)
                    } else {
                        this.instructor.selected = null;
                        this.payload.instructor_id = "";
                    }
                },
                gotInstructors(options, instructorID) {
                    let module = this.mappedInstructor;

                    this.instructor.isLoading = false;

                    if (options.response?.data?.code === 201) {
                        this.instructor.isField = true;

                        const getID = YUNOCommon.getQueryParameter("isEdit");

                        if (getID !== false) {
                            this.preFillInstructor(instructorID)
                        }
                    } else {
                        module.data = [];
                        module.error = null;
                        module.errorData = [];
                        this.instructor.isActive = true;
                        this.instructor.isField = true;

                        this.$buefy.snackbar.open({
                            duration: 5000,
                            message: YUNOCommon.config.errorMsg.notMapped,
                            type: 'is-warning',
                            position: 'is-top',
                            actionText: 'Ok'
                        });
                    };
                },
                fetchMappedInstructor(courseID, instructorID) {
                    let payload = {
                        course_id: []
                    }

                    for (let i = 0; i < courseID.length; i++) {
                        const id = courseID[i];
                        payload.course_id.push(id)
                    };

                    const options = {
                        apiURL: YUNOCommon.config.listOfMappedInstructorAPI(),
                        module: "gotData",
                        store: "mappedInstructor",
                        payload: JSON.stringify(payload),
                        headers: {
                            'accept': 'application/json',
                            'content-type': 'application/json'
                        },
                        callback: true,
                        callbackFunc: (options) => this.gotInstructors(options, instructorID)
                    };

                    this.$store.dispatch('postData', options);
                },
                generateClassEndDate() {
                    let getAddedCourses = this.courseSelection.selected,
                        weeks = [],
                        startDate = this.payload.start_date;
                        

                    for (let i = 0; i < getAddedCourses.length; i++) {
                        const course = getAddedCourses[i];

                        weeks.push(course.duration_weeks)
                    }

                    let largestWeek = Math.max(...weeks),
                        classStartDate = new Date(startDate);
                    
                    classStartDate.setDate(classStartDate.getDate() + largestWeek * 7);
                    
                    return classStartDate;
                },
                manageMaxSeats() {
                    if (this.courseSelection.selected.length !== 0) {
                        const validSeats = this.courseSelection.selected
                            .map(o => o.max_seats)
                            .filter(seats => seats !== undefined);
                
                        this.payload.seats_max = validSeats.length > 0 ? Math.max(...validSeats) : "";
                    } else {
                        this.payload.seats_max = "";
                    }
                },
                _manageCourseElements() {
                    let getCourseID = this.payload.course_id;

                    if (this.userRole.data !== "Instructor") {
                        if (this.courseSelection.selected.length !== 0) {
                            this.instructor.isActive = true;
                            this.instructor.isField = false;
                            this.instructor.isLoading = true;
                            this.fetchMappedInstructor(getCourseID);
                        } else {

                            this.instructor.current = "";
                            this.instructor.selected = null;
                            this.payload.instructor_id = "";
                        };
                    };

                    this.payload.end_date = new Date(this.generateClassEndDate());
                    this.endDate.maxDate = new Date(this.generateClassEndDate());
                },
                addCourse($event) {
                    const { payload, courseSelection, allCourses } = this;
                    let initialEnrollType = courseSelection.selected[0].enroll_type;

                    this.disabledEnrollment = initialEnrollType === "rolling" ? "fixed" : "rolling";

                    if ($event.enroll_type === this.disabledEnrollment) {
                        YUNOCommon.removeObjInArr(courseSelection.selected, "post_id", $event.post_id);
                    } else {
                        payload.course_id.push($event.post_id)
                        YUNOCommon.removeObjInArr(allCourses.data, "post_id", $event.post_id);
                        this._manageCourseElements();
                        this.courseDate = true;
                    }
                },
                removeCourse($event) {
                    const { payload, courseSelection, allCourses } = this;

                    YUNOCommon.removeValInArr(payload.course_id, $event.post_id);
                    allCourses.data.push($event)
                    this._manageCourseElements();

                    if (courseSelection.selected.length !== 0) {
                        payload.end_date = new Date(this.generateClassEndDate());
                        this.endDate.maxDate = new Date(this.generateClassEndDate());
                    } else {
                        this.courseDate = false;
                        this.disabledEnrollment = "";
                    };
                },
                onCourseSelect($event, state) {
                    if (state === "add") {
                        this.addCourse($event);
                    } else {
                        this.removeCourse($event);
                    }
                    this.manageMaxSeats();
                },
                onCourseKeypress(data) {
                    if (data.key === "ArrowDown" || data.key === "ArrowUp") {
                        this.manageItems(this.courseSelection.filtered);
                    };
                },
                manageItems(data) {
                    // This direct DOM manipulation is necessary due to limitations in dynamically styling dropdown items of the b-taginput component.
                    let getItems = document.querySelectorAll("#courseList .dropdown-menu .dropdown-item");

                    for (let i = 0; i < getItems.length; i++) {
                        const item = getItems[i],
                            obj = data[i];

                        if (obj !== undefined && obj.enroll_type === this.disabledEnrollment) {
                            item.classList.add("disabled");    
                        } else {
                            item.classList.remove("disabled");    
                        }
                    }
                    
                },
                getFilteredCourse(text) {
                    this.courseSelection.filtered = this.allCourses.data.filter((option) => {
                        return option.product_code
                            .toString()
                            .toLowerCase()
                            .indexOf(text.toLowerCase()) >= 0
                    })

                    this.$nextTick(() => {
                        this.manageItems(this.courseSelection.filtered);
                    });
                },
                preFillInstructor(instructorID) {
                    let mappedInstructor = this.mappedInstructor.data,
                        payload = this.payload,
                        findInstructor = YUNOCommon.findObjectByKey(mappedInstructor, "instructor_id", String(instructorID));

                    if (findInstructor !== null) {
                        this.onInstructorSelect(findInstructor)
                        this.instructor.current = findInstructor.name;
                        this.instructor.selected = findInstructor;
                        payload.instructor_id = findInstructor.instructor_id;
                        // this.fetchResources(true, payload.instructor_id)
                    };
                },
                preFillCourses(courseID, instructorID) {
                    const coursesData = this.allCourses.data,
                        payload = this.payload,
                        courseSelected = this.courseSelection.selected,
                        instructorField = this.instructor;

                    for (let i = 0; i < courseID.length; i++) {
                        const course = Number(courseID[i]),
                            findCourse = YUNOCommon.findObjectByKey(coursesData, "post_id", course);
                        
                        courseSelected.push(findCourse);
                        payload.course_id.push(course)

                        if (this.userRole.data === "Instructor") {
                            this.payload.instructor_id = instructorID;
                        } else {
                            if (courseSelected.length !== 0) {
                                instructorField.isActive = true; 
                                instructorField.isField = false;
                                instructorField.isLoading = true;
                                this.fetchMappedInstructor(payload.course_id, instructorID);    
                            } else {
                                instructorField.current = "";
                                instructorField.selected = null;
                                this.payload.instructor_id = "";
                            };
                        }

                        
                    };
                },
                gotBatch(options) {
                    if (options.response?.data?.code === 200) {

                        const getData = options.response.data.data,
                            payload = this.payload,
                            courseID = getData.course.map(course => course.id),
                            instructorID = getData.instructor.id;

                        this.preFillCourses(courseID, instructorID);
                        payload.title = getData.title;
                        payload.start_date = new Date(getData.start_end.start_date.time);
                        payload.end_date = new Date(getData.start_end.end_date.time);
                        payload.class_time = new Date(getData.class_time.start_time.time);
                        payload.duration = this.userRole.data !== "Instructor" ? getData.class_time.duration : "60"; 
                        payload.class_days = getData.class_days.map(day => day.day);
                        payload.seats_max = getData.seats.max;
                        payload.is_locked = getData.is_locked;
                        this.courseSelection.isActive = true;
                        this.courseDate = true;
                        
                        const today = new Date();
                        let lastEnrollmentDate = new Date(getData.start_end.end_date.time);

                        this.endDate.minDate = new Date(lastEnrollmentDate.setDate(lastEnrollmentDate.getDate() - 1));
                        this.endDate.maxDate = new Date(today.getFullYear() + 18, today.getMonth(), today.getDate());
                    };
                },
                fetchBatch(batchID) {
                    const options = {
                        apiURL: YUNOCommon.config.batch("update", {batchID: batchID}),
                        module: "gotData",
                        store: "batchDetail",
                        callback: true,
                        callbackFunc: (options) => this.gotBatch(options)
                    };

                    this.$store.dispatch('fetchData', options);
                },
                initEdit(batchID) {
                    if (YUNOCommon.findInArray(this.authorizedRoles, this.userRole.data)) {
                        this.fetchBatch(batchID);
                    };
                },
                resetForm() {
                    let payload = this.payload;

                    this.$refs.createBatchObserver.reset();
                    this.courseSelection.filtered = [];
                    this.courseSelection.selected = [];
                    this.instructor.selected = null;
                    this.instructor.current = "";
                    this.instructor.isLoading = false;
                    this.instructor.isActive = false;
                    this.instructor.isField = false;
                    this.courseSelection.isActive = false;
                    this.courseDate = false;
                    
                    payload.title = "";
                    payload.created_by = isLoggedIn;
                    payload.course_id = [];
                    payload.start_date = new Date();
                    payload.end_date = new Date("");
                    payload.class_time = new Date();
                    payload.seats_max = "";
                    payload.is_locked = false;
                    payload.instructor_id = "";
                    payload.duration = "";
                    payload.class_days = [];
                    payload.batch_db_id = "";

                    const role = this.userInfo.data.role;

                    if (role === "org-admin") {
                        payload.created_by = this.activeOrg();
                    }
                },
                manageBatch(options) {
                    this.form.isLoading = false;

                    let title = "",
                        getID = YUNOCommon.getQueryParameter("isEdit");

                    if (getID !== false) {
                        title = "Update Batch"
                    } else {
                        title = "Create Batch"
                    }

                    if (options.response?.data?.code === 201) {
                        let response = options.response.data;

                        this.resetForm();
    
                        if (getID !== false) {
                            this.fetchBatch(getID);
                        } 

                        this.$buefy.toast.open({
                            duration: 5000,
                            message: `${response.message}`,
                            position: 'is-bottom'
                        });

                        this.fetchCourses();

                    } else {
                        this.$buefy.dialog.alert({
                            title: title,
                            message: this.batchCreateUpdate.errorData,
                            confirmText: 'Ok'
                        });
                    };
                },
                initForm() {
                    this.form.isLoading = true;
        
                    const getID = YUNOCommon.getQueryParameter("isEdit"),
                        getClassSize = YUNOCommon.getQueryParameter("classSize");
                
                    let payload = {
                        ...this.payload,
                        batch_db_id: getID !== false ? getID : undefined,
                        personalisation: getClassSize,
                    };

                    const role = this.userInfo.data.role;

                    if (role === "org-admin") {
                        payload.created_by = this.activeOrg();
                    }

                    if (getID) {
                        payload.batch_db_id = getID;
                    }
                
                    const options = {
                        apiURL: this.getFormURL(),
                        module: "gotData",
                        store: "batchCreateUpdate",
                        payload: JSON.stringify(payload),
                        headers: {
                            'accept': 'application/json',
                            'content-type': 'application/json'
                        },
                        callback: true,
                        callbackFunc: options => this.manageBatch(options)
                    };
                
                    this.$store.dispatch(getID !== false ? 'putData' : 'postData', options);
                    
                },
                getFormURL() {
                    const role = this.userInfo.data.role;
                    const getID = YUNOCommon.getQueryParameter("isEdit");
                
                    const allowedRoles = ["org-admin", "yuno-admin", "Instructor"];
                    if (!allowedRoles.includes(role)) {
                        return null;
                    }

                    const isUpdate = getID !== false;
                    const action = isUpdate ? "update" : "create";
                    const params = isUpdate ? { batchID: getID } : false;
                
                    return YUNOCommon.config.batch(action, params);
                },
                activeOrg() {
                    const activeOrg = this.userInfo.data.current_state.org_id;
        
                    if (activeOrg) {
                        return activeOrg;
                    }
                },
                getGroupType(personalization) {
                    if (!personalization) return null;
                    if (personalization.length === 2) return "both";
                    if (personalization.length === 1) {
                        return YUNOCommon.findInArray(personalization, "1-1") ? "one_to_one" : "one_to_many";
                    }
                    return null;
                },
                getGroupTypeFromElement(groupType) {
                    if (groupType.one_to_one && groupType.one_to_many) return "both";
                    if (groupType.one_to_many) return "one_to_many";
                    if (groupType.one_to_one) return "one_to_one";
                    return null;
                },
                /**
                 * Transforms raw course data into a standardized format.
                 * @param {Object} element - The course data object.
                 */
                transformCourseData(element) {
                    const role = this.userInfo.data.role;

                    if (role === "org-admin") {
                        element.enroll_type = element.enrollment_type;
                        element.post_id = element.course_id;
                        element.product_code = element.title;
                        element.group_type = this.getGroupType(element.personalization);
                    } else {
                        element.group_type = this.getGroupTypeFromElement(element.group_type);
                    }
                    return element;
                },
                /**
                 * Handles the response received from the API when fetching courses.
                 * Populates the course selection and performs additional actions based on the user's role.
                 *
                 * @param {Object} options - The options object containing the API response.
                 */
                gotCourses(options) {
                    if (options.response?.data?.code !== 200) return;
                
                    const data = options.response.data.data;
                    const courseID = Number(YUNOCommon.getQueryParameter("courseid"));
                    
                    const transformedData = data.map(this.transformCourseData);

                    if (courseID) {
                        const selectedCourse = transformedData.find(element => element.post_id === courseID);
                        if (selectedCourse) {
                            this.courseSelection.selected.push(selectedCourse);
                        }
                    }
                
                    this.allCourses.data = transformedData;
                    this.isEditBatch;
                },
                fetchCourses() {
                    const options = {
                        apiURL: this.getCoursesURL(),
                        module: "gotData",
                        store: "allCourses",
                        callback: true,
                        addToModule: false,
                        callbackFunc: (options) => this.gotCourses(options)
                    };

                    this.$store.dispatch('fetchData', options);
                },
                getCoursesURL() {
                    const role = this.userInfo.data.role;
                    const getClassSize = YUNOCommon.getQueryParameter("classSize");
                
                    const urlMapping = {
                        "org-admin": YUNOCommon.config.org("courses", this.activeOrg(), isLoggedIn, 0, "all", false, false, false, false, "all", "all", "list-view", 100, 0),
                        "yuno-admin": YUNOCommon.config.allCoursesAPI(getClassSize),
                        "Instructor": YUNOCommon.config.mappedCoursesAPI(isLoggedIn, getClassSize)
                    };
                
                    return urlMapping[role] || null;
                },
                authorizedUser(role) {
                    if (YUNOCommon.findInArray(this.authorizedRoles, role) || this.capabilities.data.isGovernGangs) {
                        this.fetchCourses();
                    }
                },
                additionalRow(rows) {
                    for (let i = 0; i < rows.length; i++) {
                        const row = rows[i];

                        if (row.sun || row.mon || row.tue || row.wed || row.thu || row.fri || row.sat) {
                            row.scrollID = "moveScrollTopHere";

                            break;
                        }
                    }
                },
                additionalCols(cols) {
                    cols.push({
                        field: "slot",
                        label: "",
                        sortable: true,
                        hasSlot: true
                    });

                    const tagFields = ["slot", "sun", "mon", "tue", "wed", "thu", "fri", "sat"];

                    for (const col of cols) {
                        if (tagFields.includes(col.field)) {
                            col.hasTag = true;
                        }
                    }
                },
                scrollToActiveRow() {
                    let table = document.querySelectorAll(".table-wrapper")[0],
                        firstActiveCol = document.getElementById('moveScrollTopHere').parentElement,
                        firstActiveRow = firstActiveCol.parentElement,
                        topPos = firstActiveRow.offsetTop;

                    table.scrollTop = topPos;
                },
                gotResources(options) {
                    const module = this.instructorAvailabilityGrid;

                    if (options.response?.data?.code === 200) {
                        let getData = options.response.data.data,
                            rows = getData.rows,
                            cols = getData.columns,
                            count = options.response.data.count;

                        this.additionalCols(cols);
                        this.additionalRow(rows)

                        this.tableOptions.pageLoading = false;
                        this.tableOptions.totalResult = count;
                        module.data = getData;

                        this.$nextTick(() => {
                            this.scrollToActiveRow();
                        });
                        
                    } else {
                        module.data = [];
                        this.tableOptions.totalResult = 0;
                    }
                },
                fetchResources(moduleLoading, instructorID) {
                    this.instructorAvailabilityGrid.data = [];
                    this.instructorAvailabilityGrid.success = false;

                    const options = {
                        apiURL: YUNOCommon.config.availabilityGridAPI(instructorID),
                        module: "gotData",
                        store: "instructorAvailabilityGrid",
                        moduleLoading: moduleLoading,
                        addToModule: false,
                        callback: true,
                        callbackFunc: (options) => this.gotResources(options)
                    };

                    this.$store.dispatch('fetchData', options);
                },
                /**
                 * Fetch the footer data from API
                 */
                fetchFooter() {
                    const options = {
                        apiURL: YUNOCommon.config.footerAPI(),
                        module: "gotData",
                        store: "footer",
                        callback: false
                    };

                    this.$store.dispatch('fetchData', options);
                },
            }
        });
    };

    return {
        createBatch: createBatch
    };
})(jQuery);



