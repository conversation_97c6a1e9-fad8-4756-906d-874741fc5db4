Vue.component('yuno-hero-banner-v2', {
    props: ["data", "options"],
    template: `
        <section class="heroBanner">
            <div class="container">
                <div class="wrapper">
                    <div class="innerWrapper">
                        <div class="starRating" v-if="data.rating.isActive">
                            <div class="iconsWrapper">
                                <span class="material-icons">star</span>
                                <span class="material-icons">star</span>
                                <span class="material-icons">star</span>
                                <span class="material-icons">star</span>
                                <span class="material-icons">star</span>
                            </div>
                            <p class="smallCaption">{{ data.rating.label }}</p>
                        </div>
                        <h1 class="largestTitle">{{ data.title }}</h1>
                        <validation-observer 
                            tag="div" 
                            class="searchBarWrapper"
                            ref="searchObserver" 
                            v-slot="{ handleSubmit, invalid }">
                            <form id="searchForm" @submit.prevent="handleSubmit(initForm)">
                                <b-field class="searchFieldWrapper">
                                    <validation-provider 
                                        tag="div"
                                        class="searchField"
                                        :customMessages="{ isNotBlank: errorMsg.subject }"
                                        :rules="{required:true, isNotBlank:categories.selected}" 
                                        v-slot="{ errors, classes }">
                                        <b-autocomplete
                                            :class="classes"
                                            v-model="categories.current"
                                            :data="categories.data"
                                            autocomplete="courseSearch"
                                            :loading="categories.isLoading"
                                            placeholder="Search for..."
                                            @typing="searchOnTyping"
                                            @select="onSelect($event)"
                                            :clearable="true"
                                        >
                                            <template slot-scope="props">
                                                <template v-if="props.option.course_url">
                                                    <div class="suggestion courseBlock">
                                                        <figure>
                                                            <div class="imageWrapper" v-if="false">
                                                                <img :src="props.option.imageurl" :alt="props.option.title">
                                                            </div>
                                                            <figcaption>
                                                                <p class="courseTitle">{{ props.option.title }}</p>
                                                                <p class="courseDetail">
                                                                    <span class="caption">Course</span>
                                                                    <span class="value">{{ props.option.duration_weeks > 0 ? props.option.duration_weeks + " " +  (props.option.duration_weeks > 1 ? "weeks" : "week") : props.option.duration_weeks }}</span>
                                                                </p>
                                                            </figcaption>
                                                        </figure>
                                                    </div>
                                                </template>
                                                <template v-if="props.option.course_count && props.option.parent_cat_slug === undefined">
                                                    <div class="suggestion categoryBlock">
                                                        <p class="courseTitle">{{ "See all courses of " + props.option.name + " category" }}</p>
                                                        <p class="courseDetail">
                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>
                                                        </p>
                                                    </div>
                                                </template>
                                                <template v-if="props.option.parent_cat_slug && props.option.course_count">
                                                    <div class="suggestion categoryBlock">
                                                        <p class="courseTitle">{{ "See all courses of " + props.option.parent_cat_name + ", " + props.option.name }}</p>
                                                        <p class="courseDetail">
                                                            <span class="caption">{{ props.option.course_count + " courses available" }}</span>
                                                        </p>
                                                    </div>
                                                </template>
                                            </template>
                                        </b-autocomplete>
                                    </validation-provider>
                                    <div class="ctaWrapper">
                                        <b-button
                                            native-type="submit"
                                            class="doSearch">
                                            <span class="material-icons-outlined">search</span>
                                        </b-button>  
                                    </div>
                                </b-field>
                            </form>
                        </validation-observer>
                        <div class="categoriesWrapper" :class="{ 'has-scroll': scrollState.isScrollable }">
                            <b-button class="prev" @click="handlePrevClick">
                                <span class="material-icons">chevron_left</span>
                            </b-button>
                            <ul class="categories">
                                <template v-if="categoryList.loading">
                                    <li v-for="(category, i) in 4" :key="i">
                                        <b-skeleton active width="220px" height="66px"></b-skeleton>
                                    </li>
                                </template>
                                <template v-else-if="categoryList.success && categoryList.error === null">
                                    <li v-for="(category, i) in categoryList.data" :key="i" >
                                        <a :href="category.url" class="inner">{{ category.name }} <sup v-if="false">®</sup></a>
                                    </li>
                                </template>
                            </ul>
                            <b-button class="next" @click="handleNextClick">
                                <span class="material-icons">chevron_right</span>
                            </b-button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    `,
    data() {
        return {
            heroStyle: {
                "background-image": "url(" + this.$props.data.bgImg + ")"
            },
            categories: {
                data: [],
                selected: null,
                current: "",
                isLoading: false
            },
            errorMsg: {
                subject: "Please select the subject from list"
            },
            payload: {
                search: ""
            },
            searchParams: {
                limit: 20,
                offset: 0,
                personalization: "all",
                category: [],
                category_level_1: [],
                category_level_2: [],
                class_days_time: [
                    {
                        selected: [],
                        slug: "class_days"
                    },
                    {
                        selected: [],
                        slug: "class_time"
                    }
                ],
                instructor_id: 0,
                price_per_hour: 10000,
                total_duration: 24
            },
            scrollState: {
                currentPosition: 0,
                scrollAmount: 200, // Default scroll amount
                visibleItems: 4,
                totalItems: 0,
                isScrollable: false
            },
            featuredCategories: [
                { 
                    "name": "MS Excel", 
                    "url": "/microsoft-excel/courses/", 
                    "slug": "microsoft-excel" 
                },
                { 
                    "name": "English Speaking", 
                    "url": "/english-speaking/courses/", 
                    "slug": "english-speaking" 
                },
                { 
                    "name": "Data Science", 
                    "url": "/data-science-and-analytics/courses/", 
                    "slug": "data-science" 
                },
                { 
                    "name": "AI", 
                    "url": "/artificial-intelligence/courses/", 
                    "slug": "ai" 
                },
                {
                    "name": "Python",
                    "url": "/python-programming/courses/",
                    "slug": "python"
                },
                {
                    "name": "Soft Skills",
                    "url": "/soft-skills/courses/",
                    "slug": "soft-skills"
                },
                {
                    "name": "German",
                    "url": "/german/courses/",
                    "slug": "german"
                },
                {
                    "name": "Spanish",
                    "url": "/spanish/courses/",
                    "slug": "spanish"
                },
                {
                    "name": "French",
                    "url": "/french/courses/",
                    "slug": "french"
                },
                {
                    "name": "IELTS",
                    "url": "/ielts/courses/",
                    "slug": "ielts"
                },
                {
                    "name": "TOEFL",
                    "url": "/toefl/courses/",
                    "slug": "toefl"
                },
                {
                    "name": "GMAT",
                    "url": "/gmat/courses/",
                    "slug": "gmat"
                },
                {
                    "name": "GRE",
                    "url": "/gre/courses/",
                    "slug": "gre"
                },
                {
                    "name": "SAT",
                    "url": "/sat/courses/",
                    "slug": "sat"
                }
            ]
        }
    },
    computed: {
        ...Vuex.mapState([
            'searchSuggestions',
            'categoryList'
        ]),
        isScrollable() {
            const categoriesList = this.$el?.querySelector('.categories');
            return categoriesList ? categoriesList.scrollWidth > categoriesList.clientWidth : false;
        }
    },
    async created() {

    },
    mounted() {
        this.fetchCategories();
        // Wait for categories to be loaded and rendered
        this.$watch('categoryList.data', () => {
            this.$nextTick(() => {
                this.initializeScroll();
                this.checkScrollable();
            });
        }, { immediate: true });

        // Add resize observer to check scrollable state on window resize
        window.addEventListener('resize', this.checkScrollable);
    },
    beforeDestroy() {
        // Clean up scroll event listener
        const categoriesList = this.$el.querySelector('.categories');
        if (categoriesList) {
            categoriesList.removeEventListener('scroll', this.updateButtonStates);
        }
        window.removeEventListener('resize', this.checkScrollable);
    },
    methods: {
        initForm() {
            const selected = this.categories.selected,
                data = {
                    filter: "category",
                    id: selected.category_id,
                    label: selected.category,
                    parent_id: 0,
                    slug: selected.categorySlug
                },
                filter = {
                    filter: "category"
                };

            if (this.$props.hassearchbar === undefined) {
                // this.searchParams.category = [selected.category_id];
                window.location.href = YUNOCommon.config.host() + "/search/?state=" + encodeURI(JSON.stringify(this.searchParams)) + "";
            } else {
                Event.$emit('initHeaderSearch', data, filter);
            }
        },
        gotCourseSuggestions(options) {
            this.categories.isLoading = false;
            if (options.response?.data?.code === 200) {
                const { course, category, sub_category } = options.response.data.data;

                if (category) {
                    this.categories.data.push(...category);
                }

                if (sub_category) {
                    this.categories.data.push(...sub_category);
                }

                if (course) {
                    this.categories.data.push(...course);
                }
            }
        },
        fetchCourseSuggestions(query) {
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.generic("courseSuggestions", query),
                module: "gotData",
                store: "searchSuggestions",
                callback: true,
                callbackFunc: function (options) {
                    return instance.gotCourseSuggestions(options)
                }
            };

            this.$store.dispatch('fetchData', options);
        },
        searchOnTyping: _.debounce(function (name) {
            if (name.length > 2) {
                this.categories.isLoading = true;
                this.categories.data = [];
                this.fetchCourseSuggestions(name);
            } else {
                this.categories.data = []
                return
            }

        }, 700),
        onSelect(e) {
            if (!e) return;

            if (e.course_url) {
                window.location.href = e.course_url;
                return;
            }

            if (e.course_count) {
                this.categories.selected = e;
                this.payload.search = e.id;
                this.searchParams.category = [e.id];
                this.searchParams.category_level_1 = [];
                this.searchParams.category_level_2 = [];

                if (e.parent_cat_slug) {
                    this.searchParams.category = [e.parent_cat_id];
                    this.searchParams.category_level_1 = [e.category_level_1];
                    this.searchParams.category_level_2 = [e.id];
                }

                this.initForm();
            }
        },
        gotCategories(options) {
            const { code, data } = options.response?.data || {};

            if (code === 200) {

            };
        },
        fetchCategories() {
            this.categoryList.data = this.featuredCategories;
            this.categoryList.loading = false;
            this.categoryList.success = true;
            this.categoryList.error = null;

            // const options = {
            //     apiURL: YUNOCommon.config.categoriesAPi("?isAll=true"),
            //     module: "gotData",
            //     store: "categoryList",
            //     callback: true,
            //     callbackFunc: (options) => this.gotCategories(options)
            // };

            // this.$store.dispatch('fetchData', options);
        },
        initializeScroll() {
            const categoriesList = this.$el.querySelector('.categories');
            if (categoriesList) {
                this.scrollState.totalItems = this.categoryList.data.length;
                // Add scroll event listener
                categoriesList.addEventListener('scroll', this.updateButtonStates);
                // Initial button state update
                this.updateButtonStates();
            }
        },
        updateButtonStates() {
            const prevButton = this.$el.querySelector('.prev');
            const nextButton = this.$el.querySelector('.next');
            const categoriesList = this.$el.querySelector('.categories');

            if (prevButton && nextButton && categoriesList) {
                const hasOverflow = categoriesList.scrollWidth > categoriesList.clientWidth;

                // Enable/disable prev button
                prevButton.disabled = !hasOverflow || categoriesList.scrollLeft <= 0;

                // Enable/disable next button
                const maxScroll = categoriesList.scrollWidth - categoriesList.clientWidth;
                nextButton.disabled = !hasOverflow || Math.ceil(categoriesList.scrollLeft) >= maxScroll;
            }
        },
        handlePrevClick() {
            const categoriesList = this.$el.querySelector('.categories');
            if (categoriesList) {
                const scrollAmount = Math.min(this.scrollState.scrollAmount, categoriesList.scrollLeft);
                categoriesList.scrollBy({
                    left: -scrollAmount,
                    behavior: 'smooth'
                });
            }
        },
        handleNextClick() {
            const categoriesList = this.$el.querySelector('.categories');
            if (categoriesList) {
                const maxScroll = categoriesList.scrollWidth - categoriesList.clientWidth;
                const remainingScroll = maxScroll - categoriesList.scrollLeft;
                const scrollAmount = Math.min(this.scrollState.scrollAmount, remainingScroll);

                categoriesList.scrollBy({
                    left: scrollAmount,
                    behavior: 'smooth'
                });
            }
        },
        checkScrollable() {
            const categoriesList = this.$el?.querySelector('.categories');
            if (categoriesList) {
                this.scrollState.isScrollable = categoriesList.scrollWidth > categoriesList.clientWidth;
            }
        },
    }
});