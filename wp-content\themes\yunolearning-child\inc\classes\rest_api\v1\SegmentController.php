<?php
/*********require get_theme_file_path('inc/segment/vendor/autoload.php');***********/
use CdnSteve\Segment\Segment;
use CdnSteve\Segment\Analytics;

use V1\PayloadController;
//include_once('PayloadController.php');
Segment::config([
	'api_key' => SEGMENT_API_KEY,
	'timeout' => 2.0, // HTTP request in seconds.
]);
/**
 * This controller is using for segment for web 
 */
class SegmentController
{
	function __construct()
	{
		$this->namespace       = 'yuno/v1';
		$this->resource_name1  = '/segment/user';
		$this->resource_name2  = '/segment/org';
		$this->resource_name3  = '/segment/add/enrollment';
		$this->resource_name4  = '/segment/update/enrollment';
		$this->resource_name5  = '/segment/unenroll/learner';
		$this->resource_name6  = '/segment/create/examresult';
		$this->resource_name7  = '/segment/update/examresult';
		$this->resource_name8  = '/segment/delete/examresult';
		$this->resource_name9  = '/segment/create/class/reviews';
		$this->resource_name10 = '/segment/update/class/reviews';
		$this->resource_name11 = '/segment/update/class/attended';
	}

	public function register_routes()
	{

		register_rest_route($this->namespace, $this->resource_name1, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_user_identify'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name2, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_org_group'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name3, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_add_enrollment_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name4, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_update_enrollment_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name5, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_unenroll_learner_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name6, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_create_examresult_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name7, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_update_examresult_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name8, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_delete_examresult_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name9, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_create_class_reviews_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name10, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_update_class_reviews_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);

		register_rest_route($this->namespace, $this->resource_name11, array(
			array(
				'methods'             => WP_REST_Server::CREATABLE,
				'callback'            => array($this, 'post_update_class_attended_track'),
				'permission_callback' => array($this, 'segment_access_permissions_check'),
				'args'                => array(
				),
			),
		)
		);
	}

	/**
	 * Token authorization check
	 * This function common for all post login apis  
	 */
	public function check_access_permissions_check(WP_REST_Request $request)
  {
    $authToken = $request->get_header('authorization');
    if (empty($authToken)) { return false; }
        list($bearer, $token) = explode(" ", $authToken);
        if (!empty($token) && !empty(CURRENT_LOGGED_IN_USER_ID)) {
            return true;
        } else {
            $return = jwt_token_validation_check($token);  // this is for postman
            if ($return) { return true; } else { return false; }

        }
		$codes = error_code_setting();
		$authToken            = $request->get_header('authorization');
		list($bearer, $token) = explode(" ", $authToken);
		$result               = token_validation_check($token);
		$newData              = [];
		if ($result === true) {
			return true;
		}
		else if ($result != '' && strlen($result) > 10) {
			$newData = [
				"status"     => $codes["TOKEN_FAIL"]["code"],
				"reValidate" => true,
				"token"      => $result
			];
			return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
		}
		else {
			$newData = [
				"status"     => $codes["TOKEN_FAIL"]["code"],
				"reValidate" => false
			];
			return new WP_Error($codes["TOKEN_FAIL"]["code"], $codes["TOKEN_FAIL"]["message"], $newData);
		}
	}
	/**
	 * remove authorization check
	 * This function common for all segment apis  
	 */
	public function segment_access_permissions_check($request)
	{
		return true;
	}	
	/**
	 * post user segment identify
	 */
	public static function post_user_identify($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data   = json_decode($request->get_body(), true);
		$request_data   = json_decode($request, true);
		$user_id        = (int) $request_data['user_id'];
		$req['user_id'] = $user_id;
		$details        = PayloadController::get_user_identify($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid user id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data      = $details->data['data'];
			$user_id   = (int) $data['userId'];
			$type      = $data['type'];
			$timestamp = $data['timestamp'];
			$userdata  = get_userdata($user_id);
			if ($user_id > 0) {
			}
			else {
				return new WP_Error($codes["USER_ID_FAIL"]["code"], $codes["USER_ID_FAIL"]["message"], array('status' => $codes["USER_ID_FAIL"]["status"]));
			}
			if (empty($userdata)) {
				return new WP_Error($codes["USER_FAIL"]["code"], $codes["USER_FAIL"]["message"], array('status' => $codes["USER_FAIL"]["status"]));
			}

			if ($userdata->roles) {
				if (empty(SEGMENT_API_KEY)) {
					return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
				}
				else {
					$response = Analytics::identify($data);
					if ($response->getStatusCode() == 200) {
						$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
						return new WP_REST_Response($response, 200);
					}
					else {
						return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
					}
				}
			}
			else {
				return new WP_Error($codes["ROLE_FAIL"]["code"], $codes["ROLE_FAIL"]["message"], array('status' => $codes["ROLE_FAIL"]["status"]));
			}
		}
	}
	/**
	 * post org segment group
	 */
	public static function post_org_group($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		$request_data  = json_decode($request->get_body(), true);
		$org_id        = (int) $request_data['org_id'];
		$req['org_id'] = $org_id;
		$details       = PayloadController::get_org_group($req);
		if ($details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid org id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data                = $details->data['data'];
			$org_id              = (int) $data['groupId'];
			$type                = $data['type'];
			$timestamp           = $data['timestamp'];
			$current_post_status = get_post_status($org_id);
			if ($org_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid org id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}
			if ($current_post_status != "publish") {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Invalid org id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}

			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::group($data);
				if ($response->getStatusCode() == 200) {
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post add enrollment segment track
	 */
	public static function post_add_enrollment_track($request)
	{
		error_log("post_add_enrollment_track  - request" . date('Y-m-d H:i:s') . json_encode($request) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data         = json_decode($request->get_body(), true);
		$request_data         = json_decode($request, true);
		$enrollment_id        = (int) $request_data['enrollment_id'];
		$record_id            = (int) $request_data['record_id'];
		$req['enrollment_id'] = $enrollment_id;
		error_log("post_add_enrollment_track  - req " . date('Y-m-d H:i:s') . json_encode($req) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');

		$details              = PayloadController::get_add_enrollment_track($req);
		error_log("post_add_enrollment_track  - details " . date('Y-m-d H:i:s') . json_encode($details) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data      = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type      = $data['type'];
			$timestamp = $data['timestamp'];
			if ($enrollment_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid enrollment id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}
			// $is_enrollment_exist = $wpdb->get_row("SELECT id, org_id FROM wp_enrollment WHERE id='" . $enrollment_id . "'", ARRAY_A);

			// if (empty($is_enrollment_exist)) {
			// 	return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"]));
			// }
			error_log("post_add_enrollment_track  - data" . date('Y-m-d H:i:s') . json_encode($data) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
			$org_id = $data['groupId'] ?? null;
			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {

				error_log("post_add_enrollment_track  - org_id else " . date('Y-m-d H:i:s') . json_encode($org_id) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
				error_log("post_add_enrollment_track  - data else " . date('Y-m-d H:i:s') . json_encode($data) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
				//$response = Analytics::track($data);
				try {
					$response = Analytics::track($data);
					error_log("post_add_enrollment_track  - response else " . date('Y-m-d H:i:s') . json_encode($response) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
					if (is_object($response)) {
						error_log("STATUS CODE: " . $response->getStatusCode(), 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
					} else {
						error_log("Analytics::track returned non-object: " . var_export($response, true), 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
					}					
				} catch (Exception $e) {
					error_log("Exception in Analytics::track: " . $e->getMessage(), 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
				}
				
				error_log("post_add_enrollment_track  - response else " . date('Y-m-d H:i:s') . json_encode($response) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
				if ($response->getStatusCode() == 200) {
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					error_log("post_add_enrollment_track  - request else " . date('Y-m-d H:i:s') . json_encode($request) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
					$get_event = getExistanceOfEvent($request);
					error_log("post_add_enrollment_track  - get_event else " . date('Y-m-d H:i:s') . json_encode($get_event) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
					if (!$get_event) {
						$curlPost     = [
							"data" => [
							  "details"    => [
								"record_id"         => $record_id,
								"org_id"            => (int) $org_id,
								"status"            => "completed",
								"number_of_attempt" => 1,
								"webhook_data"      => $data['properties'],
								"event_type"        => "webhooklogs",
								"event_label"       => "Webhook Add Enrollment Logs"
							  ],
							  "@timestamp" => date("Y-m-d H:i:s")
							]
						  ];
					error_log("post_add_enrollment_track  - curlPost else " . date('Y-m-d H:i:s') . json_encode($curlPost) . "\n\n", 3, ABSPATH . 'error-logs/post_add_enrollment_track.log');
						post_elastic_event($curlPost);
					} 
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post update enrollment segment track
	 */
	public static function post_update_enrollment_track($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data         = json_decode($request->get_body(), true);
		$request_data         = json_decode($request, true);
		$enrollment_id        = (int) $request_data['enrollment_id'];
		$record_id            = (int) $request_data['record_id'];
		$req['enrollment_id'] = $enrollment_id;
		$details              = PayloadController::get_update_enrollment_track($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data      = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type      = $data['type'];
			$timestamp = $data['timestamp'];
			if ($enrollment_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid enrollment id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}
			// $is_enrollment_exist = $wpdb->get_row("SELECT id, org_id FROM wp_enrollment WHERE id='" . $enrollment_id . "'", ARRAY_A);

			// if (empty($is_enrollment_exist)) {
			// 	return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"]));
			// }
			$org_id = $data['groupId'] ?? null;
			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::track($data);
				if ($response->getStatusCode() == 200) {
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					$get_event = getExistanceOfEvent($request);
					if (!$get_event) {
					$curlPost     = [
						"data" => [
						  "details"    => [
							"record_id"         => $record_id,
							"org_id"            => (int) $org_id,
							"status"            => "completed",
							"number_of_attempt" => 1,
							"webhook_data"      => $data['properties'],
							"event_type"        => "webhooklogs",
							"event_label"       => "Webhook Update Enrollment Logs"
						  ],
						  "@timestamp" => date("Y-m-d H:i:s")
						]
					  ];
					post_elastic_event($curlPost);
					}
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post unenroll learner segment track
	 */
	public static function post_unenroll_learner_track($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		$request_data         = json_decode($request, true);
		$enrollment_id        = (int) $request_data['enrollment_id'];
		$record_id            = (int) $request_data['record_id'];
		$req['enrollment_id'] = $enrollment_id;
		$details              = PayloadController::get_unenroll_learner_track($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data      = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type      = $data['type'];
			$timestamp = $data['timestamp'];
			if ($enrollment_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid enrollment id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}
			// $is_enrollment_exist = $wpdb->get_row("SELECT id, org_id FROM wp_enrollment WHERE id='" . $enrollment_id . "'", ARRAY_A);

			// if (empty($is_enrollment_exist)) {
			// 	return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"]));
			// }
			$org_id = $data['groupId'] ?? null;
			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::track($data);
				if ($response->getStatusCode() == 200) {
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					$get_event = getExistanceOfEvent($request);
					if (!$get_event) {
					$curlPost     = [
						"data" => [
						  "details"    => [
							"record_id"         => $record_id,
							"org_id"            => (int) $org_id,
							"status"            => "completed",
							"number_of_attempt" => 1,
							"webhook_data"      => $data['properties'],
							"event_type"        => "webhooklogs",
							"event_label"       => "Webhook Unenroll Learner Logs"
						  ],
						  "@timestamp" => date("Y-m-d H:i:s")
						]
					  ];
					post_elastic_event($curlPost);
					}
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post create examresult segment track
	 */
	public static function post_create_examresult_track($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data         = json_decode($request->get_body(), true);
		$request_data         = json_decode($request, true);
		$examresult_id        = (int) $request_data['examresult_id'];
		$record_id            = (int) $request_data['record_id'];
		$req['examresult_id'] = $examresult_id;
		$details              = PayloadController::get_create_examresult_track($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid examresult id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data                = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type                = $data['type'];
			$timestamp           = $data['timestamp'];
			$current_post_status = get_post_status($examresult_id);
			if ($examresult_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid examresult id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}

			if ($current_post_status != "publish") {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid examresult id", array('status' => $codes["GET_FAIL"]["status"]));
			}

			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::track($data);
				if ($response->getStatusCode() == 200) {
					// $is_enrollment_exist = $wpdb->get_row("SELECT id, org_id FROM wp_enrollment WHERE id='" . $enrollment_id . "'", ARRAY_A);

					// if (empty($is_enrollment_exist)) {
					// 	return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"]));
					// }
					$org_id = $data['groupId'] ?? null;
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					$get_event = getExistanceOfEvent($request);
					if (!$get_event) {
					$curlPost     = [
						"data" => [
						  "details"    => [
							"record_id"         => $record_id,
							"org_id"            => (int) $org_id,
							"status"            => "completed",
							"number_of_attempt" => 1,
							"webhook_data"      => $data['properties'],
							"event_type"        => "webhooklogs",
							"event_label"       => "Webhook Create Examresult Logs"
						  ],
						  "@timestamp" => date("Y-m-d H:i:s")
						]
					  ];
					post_elastic_event($curlPost);
					}
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post update examresult segment track
	 */
	public static function post_update_examresult_track($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data         = json_decode($request->get_body(), true);
		$request_data         = json_decode($request, true);
		$examresult_id        = (int) $request_data['examresult_id'];
		$record_id            = (int) $request_data['record_id'];
		$req['examresult_id'] = $examresult_id;
		$details              = PayloadController::get_update_examresult_track($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid examresult id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data                = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type                = $data['type'];
			$timestamp           = $data['timestamp'];
			$current_post_status = get_post_status($examresult_id);
			if ($examresult_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid examresult id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}

			if ($current_post_status != "publish") {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid examresult id", array('status' => $codes["GET_FAIL"]["status"]));
			}

			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::track($data);
				if ($response->getStatusCode() == 200) {
					// $is_enrollment_exist = $wpdb->get_row("SELECT org_id FROM wp_enrollment WHERE id='" . $data['properties']['enrollment_id']. "'", ARRAY_A);

					// $org_id = isset($is_enrollment_exist['org_id']) ? $is_enrollment_exist['org_id'] : null;
					$org_id = $data['groupId'] ?? null;
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					$get_event = getExistanceOfEvent($request);
					if (!$get_event) {
					$curlPost     = [
						"data" => [
						  "details"    => [
							"record_id"         => $record_id,
							"org_id"            => (int) $org_id,
							"status"            => "completed",
							"number_of_attempt" => 1,
							"webhook_data"      => $data['properties'],
							"event_type"        => "webhooklogs",
							"event_label"       => "Webhook Update Examresult Logs"
						  ],
						  "@timestamp" => date("Y-m-d H:i:s")
						]
					  ];
					post_elastic_event($curlPost);
					}
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post delete examresult segment track
	 */
	public static function post_delete_examresult_track($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data         = json_decode($request->get_body(), true);
		$request_data         = json_decode($request, true);
		$examresult_id        = (int) $request_data['examresult_id'];
		$record_id            = (int) $request_data['record_id'];
		$req['examresult_id'] = $examresult_id;
		$details              = PayloadController::get_delete_examresult_track($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid examresult id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data                = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type                = $data['type'];
			$timestamp           = $data['timestamp'];
			$current_post_status = get_post_status($examresult_id);
			if ($examresult_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid examresult id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}

			if ($current_post_status != "publish") {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid examresult id", array('status' => $codes["GET_FAIL"]["status"]));
			}

			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::track($data);
				if ($response->getStatusCode() == 200) {
					// $is_enrollment_exist = $wpdb->get_row("SELECT id, org_id FROM wp_enrollment WHERE id='" . $enrollment_id . "'", ARRAY_A);

					// if (empty($is_enrollment_exist)) {
					// 	return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"]));
					// }
					$org_id = $data['groupId'] ?? null;
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					$get_event = getExistanceOfEvent($request);
					if (!$get_event) {
					$curlPost     = [
						"data" => [
						  "details"    => [
							"record_id"         => $record_id,
							"org_id"            => (int) $org_id,
							"status"            => "completed",
							"number_of_attempt" => 1,
							"webhook_data"      => $data['properties'],
							"event_type"        => "webhooklogs",
							"event_label"       => "Webhook Delete Examresult Logs"
						  ],
						  "@timestamp" => date("Y-m-d H:i:s")
						]
					  ];
					post_elastic_event($curlPost);
					}
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post create class reviews segment track
	 */
	public static function post_create_class_reviews_track($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data    = json_decode($request->get_body(), true);
		$request_data    = json_decode($request, true);
		$class_id        = (int) $request_data['class_id'];
		$user_id         = (int) $request_data['user_id'];
		$record_id       = (int) $request_data['record_id'];
		$req['class_id'] = $class_id;
		$req['user_id']  = $user_id;
		$details         = PayloadController::get_create_class_reviews_track($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid class id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data                = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type                = $data['type'];
			$timestamp           = $data['timestamp'];
			$current_post_status = get_post_status($class_id);
			if ($class_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid class id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}

			if ($current_post_status != "publish") {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid class id", array('status' => $codes["GET_FAIL"]["status"]));
			}

			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::track($data);
				if ($response->getStatusCode() == 200) {
					// $is_enrollment_exist = $wpdb->get_row("SELECT id, org_id FROM wp_enrollment WHERE id='" . $enrollment_id . "'", ARRAY_A);

					// if (empty($is_enrollment_exist)) {
					// 	return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"]));
					// }
					$org_id = $data['groupId'] ?? null;
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					$get_event = getExistanceOfEvent($request);
					if (!$get_event) {
					$curlPost     = [
						"data" => [
						  "details"    => [
							"record_id"         => $record_id,
							"org_id"            => (int) $org_id,
							"status"            => "completed",
							"number_of_attempt" => 1,
							"webhook_data"      => $data['properties'],
							"event_type"        => "webhooklogs",
							"event_label"       => "Webhook Create Class Reviews Logs"
						  ],
						  "@timestamp" => date("Y-m-d H:i:s")
						]
					  ];
					post_elastic_event($curlPost);
					}
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post update class reviews segment track
	 */
	public static function post_update_class_reviews_track($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data    = json_decode($request->get_body(), true);
		$request_data    = json_decode($request, true);
		$class_id        = (int) $request_data['class_id'];
		$user_id         = (int) $request_data['user_id'];
		$record_id       = (int) $request_data['record_id'];
		$req['class_id'] = $class_id;
		$req['user_id']  = $user_id;
		$details         = PayloadController::get_update_class_reviews_track($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid class id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data                = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type                = $data['type'];
			$timestamp           = $data['timestamp'];
			$current_post_status = get_post_status($class_id);
			if ($class_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid class id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}

			if ($current_post_status != "publish") {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid class id", array('status' => $codes["GET_FAIL"]["status"]));
			}

			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::track($data);
				if ($response->getStatusCode() == 200) {
					// $is_enrollment_exist = $wpdb->get_row("SELECT id, org_id FROM wp_enrollment WHERE id='" . $enrollment_id . "'", ARRAY_A);

					// if (empty($is_enrollment_exist)) {
					// 	return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"]));
					// }
					$org_id = $data['groupId'] ?? null;
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					$get_event = getExistanceOfEvent($request);
					if (!$get_event) {
					$curlPost     = [
						"data" => [
						  "details"    => [
							"record_id"         => $record_id,
							"org_id"            => (int) $org_id,
							"status"            => "completed",
							"number_of_attempt" => 1,
							"webhook_data"      => $data['properties'],
							"event_type"        => "webhooklogs",
							"event_label"       => "Webhook Update Class Reviews Logs"
						  ],
						  "@timestamp" => date("Y-m-d H:i:s")
						]
					  ];
					post_elastic_event($curlPost);
					}
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
	/**
	 * post update class attended segment track
	 */
	public static function post_update_class_attended_track($request)
	{
		global $wpdb;
		$codes = error_code_setting();
		date_default_timezone_set('Asia/Kolkata');
		//$request_data    = json_decode($request->get_body(), true);
		$request_data    = json_decode($request, true);
		$class_id        = (int) $request_data['class_id'];
		$user_id         = (int) $request_data['user_id'];
		$record_id       = (int) $request_data['record_id'];
		$req['class_id'] = $class_id;
		$req['user_id']  = $user_id;
		$details         = PayloadController::get_update_class_attended_track($req);
		if (isset($details->error_data['204']['status']) && $details->error_data['204']['status'] == "FAIL") {
			return new WP_Error($codes["GET_FAIL"]["code"], "Invalid class id", array('status' => $codes["GET_FAIL"]["status"], 'response' => $details));
		}
		else {
			$data                = $details->data['data'];
			$data['properties']['record_id'] = $record_id;
			$type                = $data['type'];
			$timestamp           = $data['timestamp'];
			$current_post_status = get_post_status($class_id);
			if ($class_id > 0) {
			}
			else {
				return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "invalid class id", array('status' => $codes["POST_INSERT_FAIL"]["status"]));
			}

			if ($current_post_status != "publish") {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid class id", array('status' => $codes["GET_FAIL"]["status"]));
			}

			if (empty(SEGMENT_API_KEY)) {
				return new WP_Error($codes["GET_FAIL"]["code"], "Invalid SEGMENT API KEY", array('status' => $codes["GET_FAIL"]["status"]));
			}
			else {
				$response = Analytics::track($data);
				if ($response->getStatusCode() == 200) {
					// $is_enrollment_exist = $wpdb->get_row("SELECT id, org_id FROM wp_enrollment WHERE id='" . $enrollment_id . "'", ARRAY_A);

					// if (empty($is_enrollment_exist)) {
					// 	return new WP_Error($codes["GET_FAIL"]["code"], "Invalid enrollment id", array('status' => $codes["GET_FAIL"]["status"]));
					// }
					$org_id = $data['groupId'] ?? null;
					$fields = ["record_id" => $record_id];
					$request = ["document_type" => "webhooklogs", "fields" => $fields];
					$get_event = getExistanceOfEvent($request);
					if (!$get_event) {
					$curlPost     = [
						"data" => [
						  "details"    => [
							"record_id"         => $record_id,
							"org_id"            => (int) $org_id,
							"status"            => "completed",
							"number_of_attempt" => 1,
							"webhook_data"      => $data['properties'],
							"event_type"        => "webhooklogs",
							"event_label"       => "Webhook Update Class Attendance Logs"
						  ],
						  "@timestamp" => date("Y-m-d H:i:s")
						]
					  ];
					post_elastic_event($curlPost);
					}
					$response = array('code' => $codes["POST_INSERT"]["code"], 'message' => "Event posted successfully", 'status' => $codes["POST_INSERT"]["status"]);
					return new WP_REST_Response($response, 200);
				}
				else {
					return new WP_Error($codes["POST_INSERT_FAIL"]["code"], "Event not posted successfully", array('status' => $codes["POST_INSERT_FAIL"]["status"], "response" => $response));
				}
			}
		}
	}
}
?>