Vue.component("yuno-classCard-drawer", {
  props: {
    data: {
      type: Object,
      required: true,
    },
	hasNewWindow: {
		type: Boolean,
		required: false,
		default: true
	}
  },
  template: `
    <div class="cardContent">
  		<div v-if="false" class="openNewWindow">
			<a class="overline noBold" :href="data.private_url" target="_blank">
  				Open in new window
				<span class="material-icons-outlined">open_in_new</span>
			</a>
		</div>
        <template v-if="userRole.data == 'Learner'">
			<yuno-learner-drawer
				:data="data"
			>
			</yuno-learner-drawer>
		</template>
		<template v-else-if="userRole.data == 'Instructor'">
			<yuno-instructor-drawer
				:data="data"
			>
			</yuno-instructor-drawer>
		</template>
    </div>
    `,
  data() {
    return {};
  },
  computed: {
    ...Vuex.mapState(["userRole"]),
  },
});
