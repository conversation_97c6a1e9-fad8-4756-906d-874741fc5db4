window.Event = new Vue();
const validationMsg = {
  messages: {
    required: "This field is required",
    is_not: "Value should be defferent then previous one",
  },
};

YUNOCommon.assignVValidationObj(validationMsg);
Vue.component("star-rating", VueStarRating.default);
Vue.component("yuno-learner-v2", {
  template: `
        <yuno-page-grid
            @onUserInfo="onUserInfo"
			:authorizedRoles="authorizedRoles"
            :hasSearchBar="hasSearchBar"
        >
          	<template v-slot:main>
              	<div class="container-fluid">
					<div class="pageHeader" :class="{'d-flex': isRoleInstructor}">
						<div class="mainHeader">
							<h1 class="headline5">{{ pageHeader.title }}</h1>
						</div>
						<div class="cta" v-if="isRoleInstructor">
							<b-button
								tag="a"
								class="yunoPrimaryCTA"
								href="/class-schedule"
							>
								Schedule New Class
							</b-button>
						</div>
					</div>
                  	<yuno-tabs-v2
						:destroyOnHide="true"
						@tabChange="tabChange"
                  	>
                      	<template v-slot:upcomingClasses>
							<yuno-filter-panel
								@filterChange="onFilterChange"
								@toggleFilters="toggleFilters"
							> 
							</yuno-filter-panel> 
							<template v-if="!hideCards">
								<yuno-card-wrapper
									@handlePageChange="handlePageChange('upcomingClasses', $event)"
								>
								</yuno-card-wrapper>
							</template>
                      	</template>
                      	<template v-slot:pastClassesRecordings>
							<yuno-filter-panel
								@filterChange="onFilterChange"
								@toggleFilters="toggleFilters"
							> 
							</yuno-filter-panel>
							<template v-if="!hideCards">
								<yuno-card-wrapper
									@handlePageChange="handlePageChange('pastClassesRecordings', $event)"
								>
								</yuno-card-wrapper>
							</template>
                      	</template>
                  	</yuno-tabs-v2>
              	</div>
          	</template>
          	<template v-slot:belowFooter>
				<yuno-snackbar 
					:data="subjectsList"
					:options="snackbarOptions"
					v-if="user.isLoggedin && userRole.data === 'Learner' && updateLink.success && updateLink.error === null">
				</yuno-snackbar>
          	</template>
        </yuno-page-grid>
    `,
  data() {
    return {
      pageHeader: {
        title: "My Schedule",
      },
      authorizedRoles: ["Instructor", "Learner"],
      storage: {
        name: "myClasses",
        version: 1,
      },
      resetPayload: {
        course: 0,
        batch: 0,
        academy: 0,
      },
      hideCards: false,
    };
  },
  computed: {
    ...Vuex.mapState([
      "user",
      "userInfo",
      "header",
      "userProfile",
      "userRole",
      "footer",
      "loader",
      "filterResult",
      "filters",
      "subjectsList",
      "updateLink",
	  "module",
    ]),
    hasSearchBar() {
      return this.userRole.data != "Instructor";
    },
    isRoleInstructor() {
      return this.userRole.data === "Instructor";
    },
  },
  async created() {},
  mounted() {},
  methods: {
    /**
     * Loads filter state from session storage if available, otherwise fetches default filters
     * and upcoming classes data
     */
    loadFiltersFromStorage() {
      const storageKey = `${this.storage.name}V${this.storage.version}`;
      const storedFilters = sessionStorage.getItem(storageKey);
      if (storedFilters) {
        const parsedFilters = JSON.parse(storedFilters);
        this.filterResult.payload = parsedFilters.filters;
        const activeTabSlug =
          this.filterResult.tabs.items[parsedFilters.currentTab]?.slug;
        if (activeTabSlug) {
          this.onTabChanged(activeTabSlug);
        } else {
          console.error("Invalid tab index:", parsedFilters.currentTab);
        }
      } else {
        this.fetchFilters(this.filterResult.payload);
        this.fetchUpcomingClasses(this.filterResult.payload);
      }
    },

    /**
     * Saves the current filter state and active tab to session storage
     */
    saveFiltersToStorage() {
      const storageKey = `${this.storage.name}V${this.storage.version}`;
      sessionStorage.setItem(
        storageKey,
        JSON.stringify({
          filters: this.filterResult.payload,
          currentTab: this.filterResult.tabs.activeTab,
        })
      );
    },

    /**
     * Handles tab change event, updates active tab state and triggers data fetch
     */
    tabChange(e) {
      // Update isActive state for all tabs
      this.filterResult.tabs.items.forEach((tab, index) => {
        tab.isActive = index === e;
      });
      this.filterResult.tabs.activeTab = e;
      this.filterResult.payload = { ...this.resetPayload };
      const activeTabSlug = this.filterResult.tabs.items[e]?.slug;
      if (activeTabSlug) {
        this.onTabChanged(activeTabSlug);
        this.saveFiltersToStorage();
      } else {
        console.error("Invalid tab index:", e);
      }
    },

    /**
     * Handles the tab change by resetting filter state and fetching appropriate data
     */
    onTabChanged(activeTab) {
      this.filterResult.data = [];
      this.filterResult.error = null;
      this.filterResult.offset = 0;

      if (activeTab === "upcomingClasses") {
        this.fetchFilters(this.filterResult.payload);
        this.fetchUpcomingClasses(this.filterResult.payload);
      } else if (activeTab === "pastClassesRecordings") {
        this.fetchFilters(this.filterResult.payload);
        this.fetchPastClasses(this.filterResult.payload);
      }
      this.saveFiltersToStorage();
    },
    /**
     * Handles filter changes for a specific tab and triggers data refresh
     */
    onFilterChange(tab) {
      this.filterResult.count = 1; // Reset to first page on filter change
      this.filterResult.offset = 0;
      this.filterResult.error = null;
      if (tab === "upcomingClasses") {
        this.fetchUpcomingClasses(this.filterResult.payload);
      } else if (tab === "pastClassesRecordings") {
        this.fetchPastClasses(this.filterResult.payload);
      }

      this.saveFiltersToStorage();
    },
    toggleFilters(data) {
      this.hideCards = data;
    },
    /**
     * Sets up the tab structure with proper labels and visibility
     */
    setupTabs(wrapperClass, role) {
      function createTab(label, slug, eleClass, isVisible) {
        return {
          label,
          slug,
          isActive: false,
          isVisible: isVisible,
          class: eleClass,
        };
      }

      const storage = this.storage;
      const store = sessionStorage.getItem(
        storage.name + "V" + storage.version
      );
      let activeTab = "";

      if (store) {
        activeTab = JSON.parse(store).currentTab;
      } else {
        activeTab = 0;
      }

      this.filterResult.tabs = {
        activeTab: activeTab,
        wrapperClass: wrapperClass,
        items: [
          createTab(
            "Upcoming Classes",
            "upcomingClasses",
            "upcomingClasses",
            true
          ),
          createTab(
            "Past Classes & Recordings",
            "pastClassesRecordings",
            "pastClassesRecordings",
            true
          ),
        ],
      };
    },

    /**
     * Handles user info update and initializes the page accordingly
     */
    onUserInfo(data) {
      if (YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
        this.setupTabs("tabsAvailable", data.role);
        this.filterResult.payload = this.resetPayload;
        this.loadFiltersFromStorage();
        if (data.role === "Learner") {
          this.fetchLearnerActivity();
        }
		this.fetchUserRegion();
        this.filters.hideCards = false;
      }
    },
	gotUserRegion(options) {
		const response = options?.response?.data;
		if(response?.code === 200){
			const getData = response?.data;
			this.module.data = getData;
		}
	},
	fetchUserRegion(){
		const props = { loggedinUserID: this.user.userID };
		const options = {
			apiURL: YUNOCommon.config.user("region", props),
			module: "gotData",
			store: "module",
			addToModule: false,
			callback: true,
			callbackFunc: (options) => this.gotUserRegion(options),
		};
		this.$store.dispatch("fetchData", options);
	},

    /**
     * Processes the response from recent class fetch
     */
    gotRecentClass(options) {
      if (
        options.response !== undefined &&
        options.response.data !== undefined &&
        options.response.data.code === 200
      ) {
        const data = options.response.data.data;
      }
    },

    /**
     * Fetches recent class data for a specific item
     */
    fetchRecentClass(itemID) {
      const options = {
        apiURL: YUNOCommon.config.recentLearnerClass(isLoggedIn, itemID),
        module: "gotData",
        store: "subjectsList",
        callback: true,
        callbackFunc: (options) => this.gotRecentClass(options),
      };
      this.$store.dispatch("fetchData", options);
    },

    /**
     * Processes the response from learner activity fetch
     */
    gotLearnerActivity(options) {
      if (
        options.response !== undefined &&
        options.response.data !== undefined &&
        options.response.data.code === 200
      ) {
        const data = options.response.data.data;
        this.snackbarOptions = data;
        this.fetchRecentClass(data.id);
      }
    },

    /**
     * Fetches learner activity data from the API
     */
    fetchLearnerActivity() {
      const options = {
        apiURL: YUNOCommon.config.learnerActivity(isLoggedIn),
        module: "gotData",
        store: "updateLink",
        callback: true,
        callbackFunc: (options) => this.gotLearnerActivity(options),
      };

      this.$store.dispatch("fetchData", options);
    },
    /**
     * Fetches filter options from the API
     */
    fetchFilters(payload) {
      this.filters.loading = true;
      const options = {
        apiURL: YUNOCommon.config.learner(
          "filters",
          this.user.userID,
          false,
          this.userRole.data.toLowerCase()
        ),
        module: "gotData",
        store: "filters",
        payload: payload,
        callback: true,
        callbackFunc: (options) => this.gotFilters(options),
      };
      this.$store.dispatch("postData", options);
    },

    /**
     * Processes the response from filters fetch
     */
    gotFilters(options) {
      this.filters.loading = false;
      const response = options?.response?.data;

      if (response?.code === 200) {
		   this.filters.data = this.filters.data.filter((item) => item.filter != "batch");
      } else if (response?.message) {
        // this.showToastMessage(response.message);
      }
    },

    /**
     * Fetches upcoming classes data based on current filters
     */
    fetchUpcomingClasses(payload) {
      this.filterResult.loading = true;
      const prop = {
        academy: payload.academy,
        course: payload.course,
        batch: payload.batch,
        limit: this.filterResult.limit,
        offset: this.filterResult.offset,
      };
      this.filterResult.data = [];
      const options = {
        apiURL: YUNOCommon.config.learner(
          "classes",
          this.user.userID,
          "ongoing-upcoming",
          this.userRole.data.toLowerCase(),
          prop
        ),
        module: "gotData",
        store: "filterResult",
        headers: {
          accept: "application/json",
          "content-type": "application/json",
        },
        callback: true,
        callbackFunc: (options) => this.gotClasses(options),
      };
      this.$store.dispatch("fetchData", options);
    },

    /**
     * Processes the response from classes fetch
     */
    gotClasses(options) {
      this.filterResult.loading = false;
      const response = options?.response?.data;
      if (response?.code === 200) {
        this.filterResult.count = response.count;
      }
    },

    /**
     * Fetches past classes data based on current filters
     */
    fetchPastClasses(payload) {
      this.filterResult.loading = true;
      const prop = {
        academy: payload.academy,
        course: payload.course,
        batch: payload.batch,
        limit: this.filterResult.limit,
        offset: this.filterResult.offset,
      };
      this.filterResult.data = [];
      const options = {
        apiURL: YUNOCommon.config.learner(
          "classes",
          this.user.userID,
          "past",
          this.userRole.data.toLowerCase(),
          prop
        ),
        module: "gotData",
        store: "filterResult",
        headers: {
          accept: "application/json",
          "content-type": "application/json",
        },
        callback: true,
        callbackFunc: (options) => this.gotClasses(options),
      };
      this.$store.dispatch("fetchData", options);
    },

    /**
     * Displays a toast message to the user
     */
    showToastMessage(message) {
      this.$buefy.toast.open({
        duration: 5000,
        message: `${message}`,
        position: "is-bottom",
      });
    },

    /**
     * Handles pagination changes for different tabs
     */
    handlePageChange(tab, page) {
      if (tab === "upcomingClasses") {
        this.filterResult.currentPage = page;
        this.filterResult.offset = this.filterResult.limit * (page - 1);
        this.fetchUpcomingClasses(this.filterResult.payload);
      } else if (tab === "pastClassesRecordings") {
        this.filterResult.currentPage = page;
        this.filterResult.offset = this.filterResult.limit * (page - 1);
        this.fetchPastClasses(this.filterResult.payload);
      }
    },
  },
});
