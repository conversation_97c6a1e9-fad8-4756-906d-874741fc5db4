@import "../../assets/scss/variables";
@import "../../assets/scss/mixins";

@font-face {
	font-family: "Material Icons Outlined";
	font-style: normal;
	font-weight: 400;
	src: url("../../../dist/fonts/material-Icons.woff2?6qrc5l") format("woff2");
}

@font-face {
	font-family: "Material Icons";
	font-style: normal;
	font-weight: 400;
	src: url("../../../dist/fonts/material-Icons-filled.woff2?8qrc5l") format("woff2");
}

.material-icons-outlined {
	font-family: "Material Icons Outlined";
	font-weight: normal;
	font-style: normal;
	font-size: 24px;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-feature-settings: "liga";
	-webkit-font-smoothing: antialiased;
}

.material-icons {
	font-family: "Material Icons";
	font-weight: normal;
	font-style: normal;
	font-size: 24px;
	line-height: 1;
	letter-spacing: normal;
	text-transform: none;
	display: inline-block;
	white-space: nowrap;
	word-wrap: normal;
	direction: ltr;
	-webkit-font-feature-settings: "liga";
	-webkit-font-smoothing: antialiased;
}

.dark38 {
	@include setFontColor($primaryCopyColor, 0.38);
}

.dark87 {
	color: $onSurface;
}

.dark60 {
	color: $onSurfaceVariant;
}

.tertiary {
	@include setBGColor($tertiary, 1);
}

#app {
	@extend .dark87;

	p {
		margin-bottom: 0;
	}

	.tertiaryBg {
		@extend .tertiary;
		padding: 5px;
		border-radius: 2px;
	}

	.largestTitle {
		@include setFont($headline3, 42px, 700, $gapSmall);

		@media (min-width: 768px) {
			@include setFont($headline1, 62px, 700, $gapSmall);
		}
	}

	.largerTitle {
		@include setFont($headline5, 28px, 500, $gapSmall);
		@extend .dark87;
	}

	.largeTitle {
		@include setFont($headline6, 24px, 500, 0);
	}

	.smallCaption {
		@include setFont($subtitle1, 24px, 500, 0);
	}

	.smallerCaption {
		@include setFont($body1, 25px, 500, $gapSmall);
	}

	.smallestCaption {
		@include setFont($caption1, 16px, 400, 0);
	}

	.body1 {
		@include setFont($body2, 20px, 400, 0);
		@extend .dark60;
	}

	.body2 {
		@include setFont($caption2, 16px, 400, 0);
		@extend .dark60;
	}

	.body3 {
		@include setFont($caption2, 16px, 400, $gap15);
		@extend .dark87;
	}

	.body4 {
		@include setFont($caption2 + 1, 16px, 400, 0);
		@extend .dark60;
	}

	.underline {
		text-decoration: underline;
		@extend .dark87;
	}

	.overline {
		@include setFont($overline, normal, 400, 0);
		text-transform: uppercase;
		letter-spacing: 1px;
		@extend .dark60;
	}

	.emptyStateV2 {
		display: flex;
		justify-content: center;
		margin-top: $gapLargest;

		figure {
			display: flex;
			flex-direction: column;
			align-items: center;

			img {
				width: 158px;
				height: auto;
			}

			figcaption {
				margin-top: $gap15;
				@include setFont($subtitle1, 24px, 500, 0);
			}

			.button {
				margin-top: $gap15;
			}
		}
	}

	.pageRoot {
		&.headerIsVisible {
			.filtersWrapper {
				&.isStickyEle {
					top: 72px;
				}
			}
		}
	}

	.hideHeader {
		z-index: 8;
	}

	.headerTop {
		height: 1px;
		background: $whiteBG;
	}

	#yunoMain {
		&.isStickyEle {
			@media (min-width: 1024px) {
				padding-bottom: 56px;
			}
		}
	}

	.filterActive {
		overflow: hidden;
		height: 100%;

		.isSticky {
			display: none;
		}
	}

	.filtersWrapper {
		position: sticky;
		top: 0;
		z-index: 9;
		background-color: #fff;

		.filterHeader {
			display: none;
		}

		&.mobileView {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			margin: 0;
			padding: $gap15;

			.filterHeader {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
		}
	}

	.filters {
		display: flex;
		flex-wrap: wrap;
		margin: 0 (-$gap15);
		padding: $gap15 0 $gap15;
		background-color: #ffffff;

		&.categoryFilter {
			margin-bottom: $gap15;

			@media (min-width: 768px) {
				margin-bottom: 0;
			}

			&.mobileView {
				margin-bottom: 0;
			}
		}

		&.otherFilters {
			display: none;

			@media (min-width: 768px) {
				display: flex;
			}

			&.mobileView {
				display: block;
			}
		}

		&.noBtmGap {
			padding-bottom: 0;
		}

		&.noTopGap {
			padding-top: 0;
		}

		.yunoDropdown {
			flex: 0 0 25%;
			margin: 0 0 0;
			padding: 0 $gap15;
			display: none;

			&:first-child {
				display: block;
				flex: 0 0 calc(100% - 43px);

				@media (min-width: 768px) {
					flex: 0 0 25%;
				}

				&.category {
					@media (min-width: 768px) {
						flex: 0 0 auto;
					}
				}
			}

			@media (min-width: 768px) {
				display: block;
				flex: 0 0 25%;
				margin: $gap15 0 0;

				// &.category {
				//     flex: 0 0 auto;
				// }
			}
		}

		&.mobileView {
			.yunoDropdown {
				flex: 0 0 100%;
				display: block;
				margin-bottom: $gap15;

				&:first-child {
					display: block;
					flex: 0 0 100%;
				}
			}

			.filterTrigger {
				display: none;
			}
		}

		.filterTrigger {
			display: block;
			align-self: center;
			padding-right: $gap15;

			.material-icons {
				font-size: 28px;
			}

			@media (min-width: 768px) {
				display: none;
			}
		}
	}

	.yunoDropdown {
		min-width: 0;

		&.slider {
			.dropdown-menu {
				padding: 0 20px 0 $gap15;
			}

			.b-slider.is-primary .b-slider-fill {
				background: $primary !important;
			}

			.b-tooltip.is-primary .tooltip-content {
				background: $primary !important;
			}
		}

		&.availability {
			.selectedItem {}
		}

		.timesDays {
			padding-bottom: 0;

			.wrapper {
				padding: $gapSmall 0 0;
			}

			small {
				@extend .smallestCaption;
				padding: 0 $gap15;
				display: block;
			}

			ul {
				display: flex;
				flex-direction: column;
				margin: $gapSmall 0 0;

				li {
					padding: 0;

					.inner {
						@include setFont($subtitle2, normal, 400, 0);
						cursor: pointer;
						display: flex;
						align-items: center;
						color: $onSurface;
						padding: $gapSmall $gap15;

						&:hover {
							background-color: $hover;
						}

						&:active {
							background-color: $active;
						}
					}

					.itemLabel {
						font-size: $caption1;
						margin-left: 3px;
					}

					.material-icons {
						font-size: 22px;
						color: $onSurfaceVariant;
						margin-right: $gap15;
					}

					&.active {
						.inner {
							background-color: $active;
						}
					}
				}
			}
		}

		.dropdown {
			width: 100%;

			&.is-active {
				.labelWrapper {
					border-color: $onSurface;
				}
			}
		}

		.dropdown-trigger {
			width: 100%;
		}

		&.availability {
			.dropdown-menu {
				width: 100%;
				left: auto;
				right: 0;
			}
		}

		.dropdown-menu {
			width: 100%;
			padding: 0;
			border-radius: 4px;
			border: 1px solid #e6e6e6;
			box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04),
				0px 4px 8px 0px rgba(0, 0, 0, 0.06);

			.dropdown-content {
				box-shadow: none;
				padding: 0;
			}

			a {
				color: $onSurface;
				padding: $gapSmall $gap15;

				&:hover {
					background-color: $hover;
				}

				&:active {
					background-color: $active;
				}

				&.dropdown-item {
					&.is-active {
						background-color: $active;
					}
				}
			}
		}

		.labelWrapper {
			padding: $gapSmaller - 1 $gapLargest + 5 $gapSmaller - 1 $gap15;
			border: 1px solid $grey;
			border-radius: 4px;
			position: relative;
			cursor: pointer;

			&:hover {
				border-color: $primary;
			}

			&:active {
				border-color: $onSurface;
			}

			.icon {
				position: absolute;
				right: 10px;
				top: calc(50% - 12px);
			}

			.placeHolder {
				@include setFont($caption2, normal, 400, 0);
				display: block;
				@extend .dark60;
			}

			.clearFilter {
				position: absolute;
				right: 35px;
				bottom: 0;
			}

			.selectedItem {
				font-weight: 400;
				padding-top: 0;
				overflow: hidden;
				text-overflow: ellipsis;
				text-wrap: nowrap;
				display: block;
				padding-right: 27px;

				span {
					flex-grow: 0;

					&:last-child {
						&::after {
							display: none;
						}
					}

					&::after {
						content: ",";
						position: relative;
						left: -2px;
					}
				}

				&.hasGrid {
					display: flex;
					padding-right: 0;

					.item {
						flex-grow: 0;
						display: flex;
						position: relative;
						padding-right: 27px;
						margin-right: 5px;

						&:last-child {
							margin-right: 0;
						}

						span {
							&::after {
								display: none;
							}
						}
					}

					.clearFilter {
						position: absolute;
						right: 0;
						top: 0;
					}
				}
			}
		}

		&.category {
			@media (min-width: 768px) {
				font-size: $subtitle1;
				margin-top: 0;

				.labelWrapper {
					border-color: transparent;
					@include setBGColor($primary, 0.05);
					padding: $gapSmaller + 2 $gapLargest + 5 $gapSmaller + 2 $gap15;
					font-size: $headline6;

					.selectedItem {
						font-weight: 500;
					}
				}

				.placeHolder {
					display: none;
				}

				.dropdown-menu {
					width: auto;
					
					.dropdown-content {
						height: 300px;
						overflow-y: auto;
					}
				}
			}
		}

		&.category_level_1 {
			@media (min-width: 768px) {
				flex: 0 0 auto;
				padding-right: 0;

				.labelWrapper {
					padding: $gapSmaller + 2 $gapLargest + 5 $gapSmaller + 2 $gap15;
				}

				.dropdown-menu {
					width: max-content;
				}
			}

			.dropdown-menu {
				.listCaption {
					@include setFont($subtitle2, 24px, 500, 0);
					@extend .dark87;
					padding: 0 $gap15;
				}

				.dropdown-item {
					position: relative;
					padding-left: 35px;

					&::before {
						content: "\e835";
						@extend .material-icons-outlined;
						position: absolute;
						left: 13px;
						top: 11px;
						font-size: 18px;
					}

					&.is-active {
						background-color: #fff;
						@extend .dark60;

						&::before {
							content: "\e834";
							@extend .material-icons;
							font-size: 18px;
							color: $primary;
						}
					}
				}
			}
		}

		&.personalization {
			.dropdown-item {
				display: flex;
				align-items: flex-start;

				.material-icons {
					margin-right: $gap15;
					font-size: 22px;
					color: $onSurfaceVariant;
				}

				.itemSubtitle {
					display: block;
					white-space: normal;
					font-size: $caption1;
					color: $onSurfaceVariant;
				}
			}
		}
	}

	.pagination {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0 (-$gap15);

		li {
			padding: 0 $gapSmall;

			&.noRightGap {
				padding-right: 0;
			}

			&.page {
				padding-left: 0;
			}

			&.gapRight {
				padding-right: $gap15;
			}

			&.firstLast {
				display: none;

				@media (min-width: 768px) {
					display: block;
				}
			}
		}

		button {
			background: #ffffff;
			border: 1px solid rgba(0, 0, 0, 0.12);
			border-radius: 4px;
			padding: 5px 15px;
			height: 36px;
			@include setFont($body1, 24px, 400, 0);

			&.nextPrev {
				padding: 0 15px;
				display: flex;
				align-items: center;

				.material-icons {
					font-size: 24px;
				}
			}

			&.disabled {
				cursor: not-allowed;

				&.active {
					background: $primary;
					border-color: transparent;
					color: #ffffff;
				}
			}
		}
	}

	.mainBody {
		.courses {
			margin-bottom: $gapLargest;

			.count {
				margin-bottom: $gap15;
				font-weight: 500;
			}
		}
	}

	.course {
		border: 1px solid $grey;
		border-radius: 4px;
		background: #ffffff;
		padding: 8px;
		margin-bottom: $gapLargest;

		.greyBG {
			background-color: $bg;
			padding: $gap15 $gap15 6px $gap15;
		}

		.categoryInfo {
			display: flex;
			align-items: center;
			justify-content: space-between;

			@media (max-width: 768px) {
				flex-direction: column;
				align-items: baseline;
				gap: 5px;
			}

			.hasFlex {
				display: flex;
				gap: 20px;
			}

			.academy {
				.tooltip-content {
					width: 165px;
				}
			}

			.courseType {
				display: flex;
				gap: 7px;
				align-items: center;

				.tooltip-trigger {
					height: 24px;
				}

				.material-icons-outlined {
					color: $primary;
				}
			}
		}

		.noBg {
			padding: 0 $gap15 $gapSmaller;

			&.hasFlex {
				@media (min-width: 768px) {
					display: flex;
					justify-content: space-between;
					align-items: end;
					flex-wrap: wrap;
					margin-top: 0;
					padding: 0 $gap15 $gapSmaller;
				}
			}

			.overline {
				margin: $gap15 0 0;
			}
		}

		.noPad {
			padding: 0;
			margin: 0;
			border-radius: 0;
			border: 0;
		}

		&:hover {
			border-color: $primary;
		}

		.demoClass {}

		.ctaWrapper {
			display: flex;
			justify-content: flex-start;
			margin-top: $gap15;

			@media (min-width: 768px) {
				margin-top: 0;
				justify-content: flex-end;
				width: 100%;
			}

			.button {
				&:first-child {
					margin-right: $gap15;
					width: 100%;
				}

				&:last-child {
					// display: none;
					margin-top: $gap15;

					@media (min-width: 768px) {
						margin-top: 0;
						display: flex;
					}
				}
			}
		}

		.courseHeader {
			display: flex;
			margin: $gapSmaller 0 0;
			flex-direction: column;

			.largerTitle {
				a {
					text-decoration: none;
					border-bottom: 2px solid transparent;
					transition: border-color 0.3s ease;
				}

				a:focus {
					border-bottom: 2px solid black;
				}
			}

			.price {
				margin-top: $gapSmaller;

				.hasFlex {
					display: flex;
					align-items: center;

					.smallestCaption {
						margin-left: $gapSmaller;
					}
				}

				.largerTitle {
					margin: 0;
				}

				@media (min-width: 768px) {
					margin-top: 0;
					text-align: right;
				}
			}

			@media (min-width: 768px) {
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
			}
		}

		h2.largerTitle {
			margin: 0;

			a {
				color: $onSurface;

				&:hover {
					color: $primary;
					text-decoration: none;
				}
			}
		}

		.features {
			display: flex;
			flex-wrap: wrap;
			margin: $gap15 (
				-$gap15) $gapSmall;

			@media (min-width: 768px) {
				margin-top: 0;
			}

			&.hasFlexColumn {
				flex-direction: column;
				gap: 6px;
			}

			&.hasActiveBg {
				.b-tooltip {
					display: flex;
					font-size: $fontSizeLarge;
					font-weight: 500;
					color: $onSurface;
					background: $active;
					padding: 6px 10px;

					@media (max-width: 768px) {
						font-size: $body2;
						padding: 10px 8px;
					}
				}
			}

			&.hasCheckList {
				li {
					opacity: 0.3;
					position: relative;

					.material-icons {
						font-size: 20px;
						color: rgba(0, 0, 0, 0.5
						);
					margin-right: $gapSmaller;
				}

				&.active {
					opacity: 1;

					.material-icons {
						color: #25d366;
					}
				}

				&:last-child {
					&::after {
						display: none;
					}
				}

				&::after {
					content: "";
					width: 1px;
					height: 100%;
					background-color: rgba(0, 0, 0, 0.08);
					position: absolute;
					top: 0;
					right: 0;
				}

				.maxCount {
					display: flex;
					@extend .smallestCaption;
					margin-left: $gapSmaller;

					.value {
						border-radius: 4px 0px 0px 4px;
						border: 1.5px solid $onSurfaceVariant;
						background: #fff;
						padding: 1px 8px;
					}

					.key {
						border-radius: 0px 4px 4px 0px;
						border: 1.5px solid $onSurfaceVariant;
						background: $onSurfaceVariant;
						color: white;
						padding: 1px 8px;
					}
				}
			}
		}

		&.noborder {
			border: 0;
			margin-top: $gap15;
			margin-bottom: $gap15;
			padding-bottom: 0;

			@media (min-width: 768px) {
				margin-bottom: 0;
			}
		}

		li {
			flex: 0 0 100%;
			padding: 0 $gap15;
			display: flex;
			align-items: center;
			font-size: $body2;
			margin-bottom: $gapSmaller;

			@media (max-width: 768px) {
				flex: 0 0 auto;
				padding: 0 0 0 15px;
			}

			.tooltip-trigger {
				display: flex;
				align-items: center;
			}

			.material-icons-outlined {
				margin-right: $gapSmaller;
				font-size: 20px;
			}

			@media (min-width: 768px) {
				flex: 0 0 auto;
				margin-bottom: 0;
			}
		}
	}

	.studentsInfo {
		display: flex;
		flex-wrap: wrap;
		margin: 0 (-$gap15) $gap15;

		li {
			flex: 0 0 50%;
			padding: 0 $gap15;
			display: flex;
			align-items: center;
			font-size: $body2;
			position: relative;

			@media (min-width: 768px) {
				flex: 0 0 auto;
			}

			&:last-child {
				&::after {
					display: none;
				}
			}

			&::after {
				content: "";
				width: 1px;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.08);
				position: absolute;
				top: 0;
				right: 0;
			}

			.label {
				margin: 0 $gapSmaller 0 0;
				padding: 0;
			}

			.value {
				@extend .dark60;
			}
		}
	}

	.availability {
		display: flex;
		flex-wrap: wrap;
		margin: $gapSmall (
			-$gap15
		);

	@media (min-width: 768px) {
		margin: $gap15 (
			-$gap15
		);
}

li {
	padding: 0 $gap15;
	display: flex;
	align-items: top;
	font-size: $caption1;
	flex: 0 0 100%;
	margin-bottom: $gapSmaller;

	@media (min-width: 768px) {
		flex: 0 0 auto;
		margin-bottom: 0;
	}

	.label {
		margin: 0 $gapSmaller 0;
		font-weight: 400;
		font-size: $caption1;
		display: flex;
		align-items: center;
		opacity: 0.3;

		small {
			font-size: $caption1;
			margin-left: 3px;
		}
	}

	.material-icons {
		font-size: 20px;
		@include setFontColor($primaryCopyColor, 0.5);
		opacity: 0.3;
	}

	&.active {
		.label {
			opacity: 1;
		}

		.material-icons {
			color: #25d366;
			opacity: 1;
		}
	}
}

&.resources {
	li {
		flex: 0 0 100%;
		opacity: 1;

		.material-icons {
			@extend .dark87;
		}

		@media (min-width: 768px) {
			flex: 0 0 auto;
		}
	}
}
}

.instructorsWrapper {
	font-size: $caption1;
	@extend .dark87;
	display: flex;
	align-items: flex-start;
	flex-direction: column;
	gap: 4px;

	.instructorsList {
		display: flex;
		gap: 10px;

		.b-tooltip {
			.tooltip-content {
				width: 150px;
			}
		}

		$size: 40px;

		.instructorMapped {

			img {
				width: $size;
				height: $size;
				border-radius: 4px;
			}
		}

		.material-icons {
			background-color: $whiteBG;
		}

		li {
			&.noImage {
				position: relative;
				width: $size;
				height: $size;

				.material-icons {
					position: absolute;
					top: -19px;
					left: -6px;
					font-size: 52px
				}
			}
		}
	}
}

.description {
	@include setFont($body2, 20px, 400, 0);
	@extend .dark60;
	min-height: 40px;

	&.hasShowMore {
		height: 50px;
		overflow: hidden;
		-webkit-mask-image: linear-gradient(to bottom,
				black 20%,
				transparent 100%);
		mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
	}

	.media {
		div[data-oembed-url] {
			width: 100%;
			margin-bottom: $gap15;
		}
	}

	p {
		margin-bottom: $gap15;
	}

	h2 {
		@include setFont($headline4, auto, 500, $gap15);
		@extend .dark87;
	}

	h3 {
		@include setFont($headline5, auto, 500, $gap15);
		@extend .dark87;
	}

	h4 {
		@include setFont($headline6, auto, 500, $gap15);
		@extend .dark87;
	}

	ul,
	ol {
		padding: 0;
		margin: 0 0 $gap15 18px;

		li {
			list-style: disc outside;
			margin-bottom: $gapSmaller;
			font-size: $fontSizeLarge;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	ol {
		li {
			list-style: decimal outside;
		}
	}
}

.showmore {
	display: inline-flex;
	align-items: center;
	@include setFont($caption1, 20px, 500, 0);
	text-decoration: none;
	margin-bottom: $gap15;

	@media (min-width: 768px) {
		margin-bottom: 0;
	}

	&:hover {
		text-decoration: none;
	}

	.anchorLabel {
		text-decoration: underline;
	}

	.material-icons {
		font-size: 18px;
	}
}
}
}

.b-sidebar {
	&.yunoSidebar {
		$font-sizes: (
			headline1: $headline1,
			headline2: $headline2,
			headline3: $headline3,
			headline4: $headline4,
			headline5: $headline5,
			headline6: $headline6,
			subtitle1: $subtitle1,
			subtitle2: $subtitle2,
			caption1: $caption1,
			overline: $overline,
		);

	$gaps: (
		largest: $gapLargest,
		larger: $gap15,
		large: $gapSmall,
		small: $gapSmaller,
	);

$multipliers: (
	1,
	2,
	3,
	4,
	5
);

.container {
	width: 1140px;
	padding: $gapLargest $gap15;

	@media (max-width: 767px) {
		width: 100%;
	}

	.close {
		position: absolute;
		right: 10px;
		top: 10px;
		cursor: pointer;
	}
}

.batchesWrapper {
	margin-bottom: $gapLargest * 2;

	.sectionTitle {
		@include setFont($headline6, 24px, 500, $gap15);
	}

	.showAll {
		height: 40px;
		width: 100%;
	}

	.batchesCount {
		@include setFont($caption2, normal, 500, $gap15);
		@extend .dark60;
	}
}

/**
        * Styles for the .sectionWrapper class.
        * 
        * This class is used to define the styling for a section wrapper element.
        * It sets the padding and border properties, and also provides additional styles
        * for elements with the .hasBorder, .hasTopGap, and .ctaWrapper classes.
        * 
    */
.sectionWrapper {
	padding: $gapLarger;

	&.gap15 {
		padding: $gap15;
	}

	&.hasBorder {
		border: 1px solid #e6e6e6;
		border-radius: 4px;
	}

	.radiusBorder {
		border: 1px solid #e6e6e6;
		border-radius: 4px;
	}

	&.hasTopGap,
	.ctaWrapper.hasTopGap {
		margin-top: $gapLarger;
	}

	&.hasBtmGap {
		margin-bottom: $gapLarger;
	}

	&.noPadding {
		padding: 0;
	}

	.ctaWrapper {
		&.isFlexbox {
			display: flex;
			gap: $gap15;

			&.alignC {
				justify-content: center;
			}
		}

		&.stack {
			flex-direction: column;
		}
	}

	.sectionMedia {
		img {
			width: 100%;
			height: auto;
			border-radius: 4px;
		}

		iframe {
			width: 100%;
			height: auto;
		}
	}
}

.batches {
	display: flex;
	flex-wrap: wrap;
	margin: 0 (-$gap15);

	.batchCard {
		flex: 0 0 100%;
		padding: 0 $gap15;
		margin-bottom: $gapLargest;
		@extend .dark87;

		@media (min-width: 768px) {
			flex: 0 0 50%;
		}

		.calendar {
			border-radius: 4px;
			text-align: center;
			border: 1px solid $grey;
			overflow: hidden;
			width: 75px;
			margin-right: $gap15;

			.day {
				background-color: #201a19;
				color: #fff;
				font-size: $subtitle2;
				font-weight: bold;
				padding: 6px 0;
				margin: 0;
			}

			.date {
				color: $onSurface;
				font-size: $headline4;
				font-weight: bold;
				line-height: 36px;
				padding-top: 10px;
				margin: 0;
			}

			.month {
				color: $onSurfaceVariant;
				font-size: $caption1;
				font-weight: bold;
				padding-bottom: 10px;
				margin: 0;
			}
		}

		.wrapper {
			background: #ffffff;
			box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
			border-radius: 4px;
			padding: $gap15;
			border: 1px solid transparent;

			&:hover {
				border: 1px solid rgba(0, 0, 0, 0.12);

				.ctaWrapper,
				.full {
					visibility: visible;
				}
			}
		}

		.batchdate {
			@include setFont($subtitle1, 24px, 500, $gapSmall);
			display: flex;
			align-items: center;

			.material-icons {
				margin-right: $gapSmaller;
			}
		}

		.days {
			display: flex;
			justify-content: space-between;
			margin: 0 (-$gapSmaller) $gapSmall;

			li {
				@include setFont($overline, 16px, 500, 0);
				letter-spacing: 1.5px;
				@include setFontColor($primaryCopyColor, 0.38);
				text-transform: uppercase;
				padding: 0 $gapSmaller;

				&.isActive {
					color: $onSurface;
				}
			}
		}

		.scheduleInfo {
			// border-bottom: 1px solid rgba(0, 0, 0, 0.08);
			margin-bottom: $gapSmall;
			padding-bottom: $gapSmall;

			&.noBtmMargin {
				margin-bottom: 0;
			}

			li {
				display: flex;
				@extend .dark60;
				align-items: center;
				margin-top: $gapSmaller;

				.material-icons-outlined {
					font-size: 16px;
				}

				.itemCaption {
					@include setFont($caption2, 16px, 500, 0);
					margin-left: $gapSmaller;

					&.noMargin {
						margin: 0;
					}
				}

				.hasBG {
					@include setFont($caption2, 16px, 500, 0);
					background-color: rgba(168, 30, 34, 0.04);
					border-radius: 100px;
					display: flex;
					align-items: center;
					padding: 4px 10px;

					.material-icons-outlined {
						margin-right: $gapSmaller;
					}
				}
			}
		}

		.mappedInstructor {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			border-bottom: 1px solid rgba(0, 0, 0, 0.08);
			margin-bottom: $gapSmall;
			padding-bottom: $gapSmall;

			.imgWrapper {
				flex: 0 0 64px;
				margin-right: $gapSmall;

				img {
					width: 64px;
					height: 64px;
					border-radius: 50%;
					font-size: 0;
				}
			}

			figcaption {
				flex: 0 0 calc(100% - 79px);
			}

			.insName {
				@include setFont($subtitle1, 20px, 500, 0);
			}

			.insRating {
				display: flex;
				align-items: center;
				position: relative;
				left: -3px;

				.material-icons {
					color: #f9b600;
					margin-right: $gapSmaller;
					font-size: 18px;
				}

				.caption {
					@include setFont($caption2, normal, 500, 0);
					@extend .dark60;
				}
			}

			.studentCount {
				flex: 0 0 100%;
				@include setFont($caption1, 16px, 400, 0);
				margin-top: $gapSmall;
				@extend .dark60;
			}
		}

		.cardFooter {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			align-items: center;
			margin: 0;

			.price {
				@include setFont($subtitle1, 20px, 500, 0);
			}

			.ctaWrapper {
				@media (min-width: 768px) {
					visibility: hidden;
				}
			}

			.full {
				flex: 0 0 100%;
				margin-top: $gapSmaller;
				@include setFont($caption1, 16px, 400, 0);
				@extend .dark60;
				visibility: hidden;
			}
		}
	}
}

.yunoTabsV3 {
	margin-top: $gap15;

	.tabs {
		margin-bottom: $gap15;

		ul {
			li {
				a {
					@extend .dark60;
					font-size: $body2;
				}

				&.is-active {
					a {
						@extend .dark87;
						border-bottom-color: $onSurface;
					}
				}
			}
		}
	}

	.tab-content {
		padding: 0;
	}
}

.filtersWrapper {
	padding: 0;
	margin: 0 0 $gap15;

	@media (min-width: 768px) {
		padding: 0;
	}

	>ul {
		display: flex;
		flex-wrap: wrap;
		margin: 0 (-$gap15);

		li {
			padding: 0 $gap15;

			@media (min-width: 768px) {
				padding: 0 0 0 $gap15;
			}

			&.course_search,
			&.search_instructor {
				flex: 0 0 100%;
				margin-bottom: $gapSmall;

				@media (min-width: 768px) {
					flex: 0 0 400px;
					margin-bottom: 0;
				}
			}

			&.isFocus {
				&.search {
					.field .control input[type="text"] {
						@include setBorderColor($primaryCopyColor, 1);
					}
				}
			}

			&.search {
				.field .control input[type="text"] {
					height: 40px;
					@include setBorderColor($primaryCopyColor, 0.12);
				}

				.dropdown-menu {
					.dropdown-item {
						.subtitle {
							@include setFont($overline, auto, 500, 0);
						}
					}
				}
			}

			&.singleCheck {
				display: flex;
				align-items: center;

				@media (min-width: 1280px) {
					margin-top: $gap15;
				}

				@media (min-width: 1800px) {
					margin-top: 0;
				}

				.b-checkbox {
					margin-bottom: 0;

					&.is-disabled {
						cursor: not-allowed;
					}
				}
			}
		}
	}

	&.loading {
		.b-skeleton {
			width: auto;
			margin: 0 0 0 $gap15;
		}
	}

	.fa-filter {
		font-size: $fontSizeLarger + 6;
		padding-right: $gap15;
	}

	.button {
		&.is-primary {
			background-color: $whiteBG;
			color: $primaryCopyColor;
			font-size: $body2;
			@extend .dark87;
			@include setBorderColor($primaryCopyColor, 0.12);
			font-size: $fontSizeSmall;
			border-radius: 4px;
			padding: 8px $gap15 9px;

			&:active,
			&:focus {
				box-shadow: none;
			}
		}

		.icon {
			margin: 0 0 0 $gapSmaller;
			position: relative;
			top: 1px;

			.mdi {

				&.mdi-menu-down,
				&.mdi-menu-up {
					&:after {
						content: "\e5c5";
						@extend .material-icons;
					}
				}

				&.mdi-menu-up {
					&:after {
						content: "\e5c7";
					}
				}
			}
		}
	}

	.filterMenu {
		flex: 0 0 100%;
		margin-bottom: $gap15;
		margin-left: 0;

		@media (min-width: 768px) {
			flex: 0 0 auto;
			margin-bottom: 0;
			margin: 0;
		}

		&.active {
			button.filter {
				@include setBorderColor($primary, 1);
				@include setBGColor($primary, 0.04);
				color: $primary;

				.icon {
					color: $primary;
				}
			}
		}

		&.is-active {
			button {
				@include setBorderColor($primaryCopyColor, 0.87);
			}
		}

		&.class_days_time {
			.ctaWrapper {
				display: flex;
				justify-content: space-between;
				margin: $gapSmall $gapSmall 0;
				padding-top: $gapSmall;
				border-top: 1px solid;
				@include setBorderColor($primaryCopyColor, 0.12);

				.button {
					border-color: $primary;
				}
			}
		}

		.filterSet {
			.listCaption {
				@include setFont($subtitle2, 24px, 500, 0);
				@extend .dark87;
				padding: 0 $gapSmall;
			}

			&.class_days {
				.innerWrapper {
					display: flex;
					padding: $gapSmaller $gap15 $gapSmall $gapSmall;
					flex-wrap: wrap;

					@media (min-width: 768px) {
						width: 440px;
					}
				}

				.dropdown-item {
					flex: 0 0 59px;
					padding: $gapSmall $gap15 0 0;
					box-sizing: border-box;

					&:hover {
						background: none;
					}

					&:before {
						display: none;
					}

					.itemLabel {
						display: block;
						background: rgba(0, 0, 0, 0.02);
						border-radius: 100px;
						@include setFont($overline, 16px, 500, 0);
						text-transform: uppercase;
						text-align: center;
						border: 1px solid transparent;
						padding: 6px $gapSmaller;

						&:hover {
							@include setBorderColor($primaryCopyColor, 0.6);
						}
					}

					&.is-active {
						.itemLabel {
							@include setBorderColor($primary, 1);
							@include setBGColor($primary, 0.04);
							color: $primary;
						}
					}
				}
			}

			&.class_time {
				.innerWrapper {
					display: flex;
					padding: $gapSmall $gap15 $gapSmall $gapSmall;
					flex-wrap: wrap;

					@media (min-width: 768px) {
						width: 440px;
					}

					.dropdown-item {
						flex: 0 0 25%;
						display: flex;
						flex-direction: column;
						padding: $gapSmall $gap15;
						align-items: center;
						@extend .dark87;
						border: 1px solid transparent;

						&.is-active {
							@include setBorderColor($primary, 1);
							@include setBGColor($primary, 0.04);
						}

						&:before {
							font-family: "Material Icons" !important;
							@extend .material-icons;
							position: static;
						}

						&.morning {
							&:before {
								content: "\e1c6";
							}
						}

						&.afternoon {
							&:before {
								content: "\e518";
							}
						}

						&.evening {
							&:before {
								content: "\e1c6";
							}
						}

						&.night {
							&:before {
								content: "\e51c";
							}
						}

						.itemCaption {
							padding: $gapSmaller 0;
							font-size: $subtitle1;
							font-weight: 500;
						}

						.itemLabel {
							@include setFont($overline, normal, 400, 0);
							letter-spacing: 1.5px;
							@extend .dark60;
							text-transform: uppercase;
						}
					}
				}

				.ctaWrapper {
					border: 0;
					margin-bottom: 0;
					padding-bottom: 0;
				}
			}
		}

		.ctaWrapper {
			display: flex;
			justify-content: space-between;
			margin: $gapSmall $gapSmall 0;
			padding-top: $gapSmall;
			border-top: 1px solid;
			@include setBorderColor($primaryCopyColor, 0.12);

			.button {
				border-color: $primary;
			}
		}

		.dropdown-trigger {
			width: 100%;
		}

		button {
			width: 100%;
			justify-content: space-between;

			@media (min-width: 768px) {
				width: auto;
				justify-content: center;
			}

			>span {
				text-transform: capitalize;
			}
		}

		.dropdown-content {
			box-shadow: none;
			border: 0;
			max-height: 300px;
			overflow: hidden;
			overflow-y: auto;

			a {
				@include setFontColor($primaryCopyColor, 0.5);

				&.is-active {
					background: none;
					@include setFontColor($primaryCopyColor, 1);
				}

				&:active,
				&:focus {
					background: none;
					outline: none;
				}
			}
		}
	}
}

.onSurface {
	color: $onSurface;
}

.onSurfaceVariant {
	color: $onSurfaceVariant;
}

.greenColor {
	color: #25d366;
}

.primaryColor {
	color: $primary;
}

.sidebar-content {
	width: 100%;
	max-width: 1140px;
	background-color: #fff;
}

@each $tag, $lineheight in $font-sizes {
	.#{$tag} {
		// Directly use the font size from the map, line height from the loop, and a fixed font weight
		@include setFont(map-get($font-sizes, $tag), $lineheight + 4px, 500);

		&.bolder {
			font-weight: 700;
		}

		&.noBold {
			font-weight: 400;
		}

		&.alignC {
			text-align: center;
		}

		&.uppercase {
			text-transform: uppercase;
		}

		&.capitalize {
			text-transform: capitalize;
		}

		&.ellipsis {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}

.makeGrid {
	display: flex;
	align-items: flex-start;
	justify-content: flex-start;

	&.hasPadding {
		padding: $gap15;
	}

	&.isWrap {
		flex-wrap: wrap;
	}

	&.vAlignCenter {
		align-items: center;
	}

	&.spaceBetween {
		justify-content: space-between;
	}

	&.skeletonWidthAuto {
		.b-skeleton {
			width: auto;
			margin-left: 3px;
		}
	}

	.roundImg16 {
		img {
			width: 16px;
			height: 16px;
			border-radius: 50%;
			font-size: 0;
		}
	}

	.roundImg48 {
		img {
			width: 48px;
			height: 48px;
			border-radius: 50%;
			font-size: 0;
		}
	}

	.width50 {
		flex: 0 0 50%;
	}

	.width70 {
		flex: 0 0 70%;
	}

	.width100 {
		flex: 0 0 100%;
	}

	.width120 {
		flex: 0 0 100%;

		@media (min-width: 768px) {
			flex: 0 0 120px;
		}
	}

	.calc140 {
		flex: 0 0 100%;

		@media (min-width: 768px) {
			flex: 0 0 calc(100% - 140px);
		}
	}

	&.withPipe {
		li {
			&:not(:last-child) {
				&:after {
					content: "|";
					margin: 0 $gap15;
					color: $grey;
				}
			}
		}
	}

	.mediaImg {
		img {
			width: 100%;
			height: auto;
		}
	}
}

// Iterates over each gap size and multiplier to generate margin utility classes
// based on the provided $gaps and $multipliers maps.
//
// Parameters:
// $gaps - A map of gap names to their corresponding sizes.
// $multipliers - A list of multipliers to apply to each gap size.
//
// Example:
// If $gaps: (small: 8px, medium: 16px) and $multipliers: (1, 2),
// the following classes will be generated:
// .m-top-small-times-1, .m-top-small-times-2, .m-top-large-times-1, .m-top-large-times-2,
@each $name, $size in $gaps {
	@each $multiplier in $multipliers {
		.m-top-#{$name}-times-#{$multiplier} {
			margin-top: $size * $multiplier;
		}

		.m-right-#{$name}-times-#{$multiplier} {
			margin-right: $size * $multiplier;
		}

		.m-bottom-#{$name}-times-#{$multiplier} {
			margin-bottom: $size * $multiplier;
		}

		.m-left-#{$name}-times-#{$multiplier} {
			margin-left: $size * $multiplier;
		}
	}
}
}

.yunoTable {
	margin-top: $gapLargest;

	.smallLoader {
		font-size: 8px;
	}

	.table-mobile-sort {
		display: none;
	}

	.progress-wrapper {
		.progress-value {
			@include setFontColor($primaryCopyColor, 0.7);
		}

		.progress {
			border: 1px solid #fff;

			&.is-orange::-webkit-progress-value {
				background: #fc9927;
			}

			&.is-red::-webkit-progress-value {
				background: #ca0813;
			}

			&.is-yellow::-webkit-progress-value {
				background: #f0c042;
			}

			&.is-lightGreen::-webkit-progress-value {
				background: #669d4f;
			}

			&.is-darkGreen::-webkit-progress-value {
				background: #356b21;
			}
		}
	}

	.ctaGroup {
		.button {
			&.yunoPrimaryCTA {
				border: 1px solid;
				background-color: #fff;
				@include setBorderColor($primaryCopyColor, 0.2);
				color: $primaryCopyColor;
			}
		}
	}

	.tag {
		&.is-success {
			background-color: #54ac75;
		}

		&.is-info {
			background-color: #136bcd;
		}

		&.noText {
			text-indent: -9999px;
			width: 100%;
		}

		&.is-light {
			@include setBGColor($primaryCopyColor, 0.2);
		}

		&.noBG {
			@include setFontColor($primaryCopyColor, 0.4);
			background: none;
		}
	}

	&.container-fluid {
		padding-left: 0;
		padding-right: 0;
	}

	.clearFilters {
		display: flex;
		align-items: flex-start;
		margin-bottom: 0;
		margin-top: $gap15;

		.tags:not(:last-child) {
			margin: 0 $gapSmall 0 0;
		}
	}

	.b-table {
		&.tableInvisible {
			visibility: hidden;
		}

		.pagination {
			margin-top: 0;

			.pagination-ellipsis {
				text-indent: -9999px;

				&:before {
					content: "...";
					text-indent: 0;
				}
			}
		}

		.listHeading {
			font-weight: 700;
			font-size: $fontSizeLarger;
			margin-bottom: $gap15;
		}

		.detailList {
			display: flex;
			flex-direction: column;

			@media (min-width: 768px) {
				flex-wrap: wrap;
				flex-direction: row;
				margin: 0 (-$gap15);
			}

			li {
				display: flex;
				align-items: center;
				margin-bottom: $gap15;

				&:last-child {
					margin-bottom: 0;
				}

				@media (min-width: 768px) {
					padding: 0 $gap15;

					&:last-child {
						margin-bottom: $gap15;
					}
				}

				.listImage {
					flex: 0 0 40px;
					height: 40px;
					overflow: hidden;
					@include setBGColor($primaryCopyColor, 0.1);
					border: 2px solid;
					@include setBorderColor($primaryCopyColor, 0.3);
					border-radius: 50%;
					margin-right: 10px;

					img {
						width: 36px;
						height: 36px;
						transform: scale(1, 1);
					}
				}

				.listTitle {
					font-size: $fontSizeSmall;
				}

				.listInfo {
					small {
						font-size: $fontSizeSmallest;
					}
				}
			}
		}

		&.scrollable {
			.table-wrapper {
				&.has-mobile-cards {
					tr {
						&:not(.detail):not(.is-empty):not(.table-footer) {
							td {
								&.chevron-cell {
									.mdi-chevron-right {
										&:before {
											@extend .fa;
											content: "\f054";
										}
									}
								}

								.fieldVal {}
							}
						}
					}
				}
			}
		}

		&.is-loading {
			tbody {
				tr {
					&.is-empty {
						td {
							padding: 10% 0;

							&:before {
								display: none;
							}
						}
					}
				}
			}
		}

		.table {
			&.is-striped {
				tbody {
					tr {
						&:not(.is-selected) {
							&:nth-child(even) {
								@include setBGColor($primaryColor, 0.03);
							}
						}
					}
				}
			}
		}

		.has-addons {
			button.button {
				height: 2.25em;
				background-color: $primaryColor;

				.icon {
					.mdi-arrow-up {
						&:after {
							content: "\f0de";
							@extend .fa;
							position: relative;
							top: 2px;
						}
					}
				}
			}
		}

		.table-wrapper {
			overflow-y: auto;

			&.has-sticky-header {
				@media screen and (max-width: 2048px) {
					height: auto !important;
					max-height: 950px;
				}

				@media screen and (max-width: 1792px) {
					height: auto !important;
					max-height: 800px;
				}

				@media screen and (max-width: 1680px) {
					height: auto !important;
					max-height: 750px;
				}

				@media screen and (max-width: 1600px) {
					height: auto !important;
					max-height: 600px;
				}

				@media screen and (max-width: 1440px) {
					height: auto !important;
					max-height: 575px;
				}

				@media screen and (max-width: 1920px) {
					height: auto !important;
					max-height: 750px;
				}

				@media (max-width: 1366px) {
					height: auto !important;
					max-height: 450px;
				}

				@media (max-width: 767px) {
					height: auto !important;
					max-height: 542px !important;
				}
			}

			&.has-mobile-cards {
				tr {
					&:not(.detail):not(.is-empty):not(.table-footer) {
						td {
							justify-content: flex-start;
							flex-direction: column;
							text-align: left;
							font-size: $fontSizeSmall;

							.clipboard {
								display: flex;
								justify-content: space-between;
								align-items: center;

								@media (min-width: 768px) {
									width: 140px;
								}

								.fieldWrapper {
									flex: 0 0 85%;
								}

								&.noField {
									position: relative;

									.fieldWrapper {
										@media (min-width: 992px) {
											position: absolute;
											overflow: hidden;

											.control {
												position: absolute;
												left: -200px;
												top: 0;
											}
										}
									}
								}

								.trigger {
									cursor: pointer;
									width: 36px;
									height: 36px;
									display: flex;
									justify-content: center;
									align-items: center;
								}
							}

							// .user {
							// 	margin-top: $gapSmaller;
							// }

							.fieldVal {
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
								width: 100%;

								@media (min-width: 768px) {
									width: 256px;
								}

								.tag {
									&.colorGreen {
										background: #669d4f;
										color: $secondaryCopyColor;
									}

									&.colorRed {
										background: #ca0813;
										color: $secondaryCopyColor;
									}
								}

								&.percentageBlock {
									display: flex;
									align-items: center;
									width: 130px;
									justify-content: space-between;

									.progress-wrapper {
										width: 50%;
										margin: 0;
									}
								}

								&.allowWrap {
									overflow: visible;
									white-space: normal;
								}

								&.action,
								&.ctaGroup {
									visibility: hidden;
								}

								&.noOverflow {
									overflow: visible;
								}

								&.arrayList,
								&.hierarchyList {
									.item {
										display: inline-block;

										&:after {
											content: ",";
											padding-right: $gapSmaller;
										}

										&:last-child {
											&:after {
												display: none;
											}
										}
									}
								}

								&.hierarchyList {
									.item {
										display: block;
										position: relative;
										padding-left: $gapSmall;

										.fa {
											position: relative;
											right: -5px;
											top: 2px;
											font-size: $fontSizeLarger;
											cursor: pointer;
										}

										&:before {
											@extend .fa;
											content: "\f105";
											position: absolute;
											left: 0;
											top: 4px;
										}

										&:after {
											display: none;
										}
									}
								}

								&.slot {
									overflow: visible;
									position: relative;

									.tag {
										width: 100%;
									}

									&:before {
										content: "";
										width: calc(100% + 18px);
										height: 1px;
										background-color: #fff;
										position: absolute;
										left: -8px;
										top: -8px;
									}

									&:after {
										content: "";
										width: calc(100% + 18px);
										height: 1px;
										background-color: #fff;
										position: absolute;
										left: -8px;
										bottom: -8px;
									}
								}

								&.paymentLink {
									@media (min-width: 768px) {
										width: 110px;
										text-transform: uppercase;
									}
								}

								&.sun,
								&.mon,
								&.tue,
								&.wed,
								&.thu,
								&.fri,
								&.sat {
									@media (min-width: 768px) {
										width: 52px;
									}
								}

								&.start {
									@media (min-width: 768px) {
										width: 108px;
									}
								}

								&.class_attended {
									@media (min-width: 768px) {
										width: 121px;
									}
								}

								&.class_time_v2 {
									@media (min-width: 768px) {
										width: auto;
									}
								}

								&.class_time {
									@media (min-width: 768px) {
										width: 94px;
									}
								}

								&.group_type {
									@media (min-width: 768px) {
										width: 100px;
									}
								}

								&.email {
									@media (min-width: 768px) {
										width: 200px;
									}
								}

								&.enrollment_type {
									@media (min-width: 768px) {
										width: 142px;
									}
								}

								&.ends_in {
									@media (min-width: 768px) {
										width: 132px;
									}
								}

								&.class_duration {
									@media (min-width: 768px) {
										width: 130px;
									}
								}

								&.category {
									@media (min-width: 768px) {
										width: 170px;
										overflow: visible;
									}
								}

								&.end_date {
									@media (min-width: 768px) {
										width: 82px;
									}
								}

								&.duration {
									@media (min-width: 768px) {
										width: 110px;
									}
								}

								&.enrolled_end {
									@media (min-width: 768px) {
										width: 128px;
									}
								}

								&.enrolled_days_left {
									@media (min-width: 768px) {
										width: 85px;
									}
								}

								&.crm_contact_id {
									@media (min-width: 768px) {
										width: 154px;
									}
								}

								&.product_code {
									@media (min-width: 768px) {
										width: 328px;
									}
								}

								&.days {
									@media (min-width: 768px) {
										width: 210px;
									}
								}

								&.title {
									@media (min-width: 768px) {
										width: 300px;
										font-size: $fontSizeSmall;
										font-weight: 400;
									}
								}

								&.days,
								&.category {
									@media (min-width: 768px) {
										white-space: normal;
									}
								}

								&.grid_date {
									@media (min-width: 768px) {
										width: 166px;
									}
								}

								&.start_date {
									@media (min-width: 768px) {
										width: 105px;
									}
								}

								&.active_enrollments {
									@media (min-width: 768px) {
										width: 161px;
									}
								}

								&.attendance_count {
									@media (min-width: 768px) {
										width: 155px;
									}
								}

								&.time {
									@media (min-width: 768px) {
										width: 104px;
									}
								}

								&.related_courses {
									@media (min-width: 768px) {
										width: 328px;
									}
								}

								&.enrollment_count {
									@media (min-width: 768px) {
										width: 150px;
									}
								}

								&.link_status {
									@media (min-width: 768px) {
										width: 106px;
									}
								}

								&.link_status {
									@media (min-width: 768px) {
										width: 106px;
									}
								}

								&.link_amount {
									@media (min-width: 768px) {
										width: 105px;
									}
								}

								&.payment_link {
									@media (min-width: 768px) {
										width: 124px;
									}
								}

								&.created_on {
									@media (min-width: 768px) {
										width: 126px;
									}
								}

								&.course_name {
									@media (min-width: 768px) {
										width: auto;
									}
								}

								&.total_amount {
									@media (min-width: 768px) {
										width: 110px;
									}
								}

								&.instructor_name {
									@media (min-width: 768px) {
										width: 142px;
										white-space: normal;
									}
								}

								&.enrollment_status {
									@media (min-width: 768px) {
										width: 138px;
									}
								}

								&.payment_method {
									@media (min-width: 768px) {
										width: 142px;
									}
								}

								&.amount_received {
									@media (min-width: 768px) {
										width: 136px;
									}
								}

								&.amount_pending {
									@media (min-width: 768px) {
										width: 131px;
									}
								}

								&.batch_name {
									@media (min-width: 768px) {
										width: 79px;
									}
								}

								&.enrolled_on {
									@media (min-width: 768px) {
										width: 126px;
									}
								}

								&.counselor_name {
									@media (min-width: 768px) {
										width: 145px;
										white-space: normal;
									}
								}

								&.payment_status {
									&.green {
										color: green;

										a {
											color: green;
										}
									}

									@media (min-width: 768px) {
										width: 163px;
									}
								}

								&.commonCol {
									@media (min-width: 768px) {
										width: 150px;
									}
								}

								&.commonCol-2 {
									@media (min-width: 768px) {
										width: 160px;
									}
								}

								&.commonCol-3 {
									@media (min-width: 768px) {
										width: 153px;
									}
								}

								&.fixedWidth {
									@media (min-width: 768px) {
										width: 256px;
									}
								}

								&.noOverflow {
									overflow: visible;
								}

								&.ctaGroup {
									display: flex;
									align-items: center;

									.button,
									.b-tooltip {
										margin-right: $gapSmall;

										&:last-child {
											margin-right: 0;
										}
									}
								}

								@media (min-width: 768px) {
									width: auto;
								}
							}
						}

						&:hover {
							@include setBGColor($primaryCopyColor, 0.1);

							td {
								.fieldVal {

									&.action,
									&.ctaGroup {
										visibility: visible;
									}
								}
							}
						}
					}
				}

				thead {
					tr {
						&:not(.detail):not(.is-empty):not(.table-footer) {
							&:hover {
								background-color: #fff;
							}
						}
					}
				}
			}
		}

		.top.level {
			display: none;
		}

		thead {
			tr {
				th {
					border: 0;
					font-size: $fontSizeSmall;

					&.is-current-sort {
						.icon {
							.mdi-arrow-up {
								&:after {
									content: "\f077";
									@extend .fa;
									position: relative;
									top: -1px;
								}
							}
						}
					}
				}
			}
		}

		tbody {
			tr {
				.user {
					display: flex;
					align-items: center;
					width: 218px;

					&.hasTags {
						width: auto;

						.tagsWrapper {
							padding-top: $gapSmaller;

							.tag {
								margin-right: $gapSmaller;
								background-color: $yellowBG;
								color: $primaryCopyColor;

								&:last-child {
									margin-right: 0;
								}
							}
						}
					}

					.userImg {
						flex: 0 0 auto;
						width: 32px;
						height: 32px;
						overflow: hidden;
						background-color: rgba(255, 255, 255, 0.1);
						border-radius: 50%;
						margin-right: 10px;

						img {
							width: 32px;
							height: 32px;
							transform: scale(1, 1);
						}
					}

					.userName {
						text-transform: capitalize;
					}
				}

				&.is-empty {
					td {
						position: relative;
						text-align: center;

						&:before {
							content: "No record found";
							display: block;
							padding: 10% 0;
						}
					}
				}

				// &:not(.is-selected) {
				// 	&:nth-child(even) {
				// 		@include setBGColor($primaryColor, 0.03);
				// 	}
				// }

				&:last-child {
					td {
						border-bottom: 1px solid $grey;
					}
				}

				td {
					border-right: 1px solid $grey;
					vertical-align: middle;
					font-size: $fontSizeSmall;

					@media (min-width: 768px) {
						border: 0;
						border-top: 1px solid $grey;
						border-right: 0;
					}
				}
			}
		}

		.pagination-link {
			color: $primaryCopyColor;

			&:hover {
				text-decoration: none;
			}

			&.is-current {
				background-color: $primaryColor;
				border-color: $primaryColor;
				color: $secondaryCopyColor;
			}

			&.pagination-previous,
			&.pagination-next {
				.icon {

					.mdi-chevron-left,
					.mdi-chevron-right {
						&:after {
							content: "\f060";
							@extend .fa;
						}
					}

					.mdi-chevron-right {
						&:after {
							content: "\f061";
						}
					}
				}
			}
		}
	}
}

.emptyStateV2 {
	display: flex;
	justify-content: center;
	margin-top: 5%;

	figure {
		display: flex;
		flex-direction: column;
		align-items: center;

		img {
			width: 158px;
			height: auto;
		}

		figcaption {
			margin-top: $gap15;
			@include setFont($subtitle1, 24px, 500, 0);
		}

		.button {
			margin-top: $gap15;
		}
	}
}
}