const YUNOCreateBatch = (function($) {
    const AUTHORIZED_ROLES = [
        "yuno-admin",
        "yuno-category-admin",
        "Instructor",
        "org-admin"
    ];
    const DAYS_OF_WEEK = [
        {
            label: "Monday",
            slug: "Mon"
        },
        {
            label: "Tuesday",
            slug: "Tue"
        },
        {
            label: "Wednesday",
            slug: "Wed"
        },
        {
            label: "Thursday",
            slug: "Thu"
        },
        {
            label: "Friday",
            slug: "Fri"
        },
        {
            label: "Saturday",
            slug: "Sat"
        },
        {
            label: "Sunday",
            slug: "Sun"
        }
    ];

    const createBatch = function() {
        const validationMsg = {
            "messages": {
                "required": "This field is required",
                "numeric": "Numbers only",
                "min": "Minimum 10 numbers required",
                "max": "Maximum 15 numbers required",
                "is": "This field is required",
                "is_not": "New batch shouldn't be same as current batch"
            }
        };

        YUNOCommon.assignVValidationObj(validationMsg);
        YUNOTable.table();

        Vue.component('yuno-create-batch', {
            template: `
                <yuno-page-grid
                    :authorizedRoles="authorizedRoles"
                    @onUserInfo="onUserInfo"
                    :hasSearchBar="false"
                >
                    <template v-slot:main>
                        <section id="createBatch" class="container-fluid formSection createBatch ">
                            <template v-if="isFormLoading">
                                <figure class="infiniteSpinner">
                                    <img width="150" height="75" :src="wpThemeURL + '/assets/images/infinite-spinner.svg'" alt="Yuno Learning">
                                </figure>
                            </template>
                            <template v-if="isFormReady">
                                <template v-if="isEditBatch">
                                    <h1 class="sectionTitle">Update Batch</h1>    
                                </template>
                                <template v-else>
                                    <h1 class="sectionTitle">Create New Batch</h1>    
                                </template>
                                <validation-observer 
                                    tag="div" 
                                    ref="createBatchObserver" 
                                    v-slot="{ handleSubmit, invalid }">
                                    <form id="createBatchForm" @submit.prevent="handleSubmit(initForm)">
                                        <div class="row isRelative">
                                            <div class="col-12 col-md-5 col-lg-5 noRelative">
                                                <div class="formWrapper">
                                                    <b-field label="Batch Label">
                                                        <validation-provider 
                                                            :rules="{required:true}" 
                                                            v-slot="{ errors, classes }">
                                                            <b-input :class="classes" 
                                                                placeholder="Add a batch title" 
                                                                v-model="payload.title">
                                                            </b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Course" id="courseList">
                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-taginput
                                                                :class="classes"
                                                                v-model="courseSelection.selected"
                                                                :data="courseSelection.filtered"
                                                                autocomplete
                                                                :disabled="isEditBatch ? true : false"
                                                                field="product_code"
                                                                placeholder="Add a course"
                                                                @add="onCourseSelect($event, 'add')"
                                                                @remove="onCourseSelect($event, 'remove')"
                                                                @keydown.native="onCourseKeypress($event)"
                                                                @typing="getFilteredCourse">
                                                                <template v-slot="props">
                                                                    <span class="wrapper" :class="props.option.enroll_type">
                                                                        <strong>{{props.option.product_code}} <small class="helper">({{props.option.enroll_type}})</small></strong>
                                                                        <span class="courseData">
                                                                            <span class="courseLabel">Duration:</span>
                                                                            <span class="courseVal">{{props.option.duration_weeks}} Weeks</span>
                                                                            <span class="courseLabel">Price:</span>
                                                                            <span class="courseVal"><template v-if="props.option.unit_price !== '0'">&#8377;</template>{{props.option.unit_price}}</span>
                                                                        </span>
                                                                    </span>
                                                                </template>
                                                                <template #empty>
                                                                    There are no items
                                                                </template>
                                                            </b-taginput>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Instructor" v-if="instructor.isActive">
                                                        <template v-if="instructor.isLoading">
                                                            <div class="smallLoader withField"></div>
                                                        </template>
                                                        <template v-if="mappedInstructor.success">
                                                            <validation-provider 
                                                                tag="div"
                                                                v-if="instructor.isField"
                                                                :customMessages="{ isNotBlank: errorMsg.instructor }"
                                                                :rules="{required:true, isNotBlank:instructor.selected}" 
                                                                v-slot="{ errors, classes }">
                                                                <b-autocomplete
                                                                    :class="classes"
                                                                    v-model="instructor.current"
                                                                    :data="filteredInstructor"
                                                                    placeholder="Add instructor"
                                                                    field="name"
                                                                    @input="onInstructorChange"
                                                                    @select="onInstructorSelect($event)"
                                                                    :clearable="true">
                                                                    <template slot="empty">No results for {{instructor.current}}</template>
                                                                </b-autocomplete>
                                                                <p class="error">{{errors[0]}}</p>
                                                            </validation-provider>
                                                        </template>
                                                    </b-field>
                                                    <b-field label="Start Date" v-if="courseDate">
                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-datepicker
                                                                @input="onDatepick"
                                                                :class="classes"
                                                                :date-formatter="formatDate"
                                                                v-model="payload.start_date"
                                                                placeholder="Pick date"
                                                                :mobile-native="false"
                                                                :min-date="startDate.minDate"
                                                                trap-focus>
                                                            </b-datepicker>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="End Date" v-if="courseDate">
                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-datepicker
                                                                :class="classes"
                                                                :date-formatter="formatDate"
                                                                v-model="payload.end_date"
                                                                :mobile-native="false"
                                                                :min-date="endDate.minDate"
                                                                :max-date="endDate.maxDate"
                                                                trap-focus>
                                                            </b-datepicker>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Days Of The Week">
                                                        <validation-provider tag="div" class="makeItGrid" :rules="{required:true, minLength:1}" v-slot="{ errors, classes }">
                                                            <template v-for="(day, dayIndex) in daysOfWeek">
                                                                <div class="field" :key="dayIndex">
                                                                    <b-checkbox
                                                                        :class="classes"
                                                                        :native-value="day.slug"
                                                                        v-model="payload.class_days">
                                                                        {{day.label}}
                                                                    </b-checkbox>
                                                                </div>
                                                            </template>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Class Duration" v-if="userRole.data !== 'Instructor'">
                                                        <validation-provider :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-select 
                                                                :class="classes"
                                                                v-model="payload.duration"
                                                                placeholder="Select">
                                                                <option value="">Select</option>
                                                                <option value="15">15 Minutes</option>
                                                                <option value="30">30 Minutes</option>
                                                                <option value="45">45 Minutes</option>
                                                                <option value="60">1 Hour</option>
                                                                <option value="75"> 1 Hour 15 Minutes</option>
                                                                <option value="90">1 Hour 30 Minutes</option>
                                                                <option value="105">1 Hour 45 Minutes</option>
                                                                <option value="120">2 Hours</option>
                                                            </b-select>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Class Time">
                                                        <validation-provider tag="div" :rules="{required:true}" v-slot="{ errors, classes }">
                                                            <b-timepicker
                                                                :class="classes"
                                                                v-model="payload.class_time"
                                                                placeholder="Pick time"
                                                                hour-format="12"
                                                                :mobile-native="false"
                                                                icon="clock">
                                                            </b-timepicker>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field label="Max Seats">
                                                        <validation-provider tag="div" :rules="{required:false, numeric: true}" v-slot="{ errors, classes }">
                                                            <b-input 
                                                                :disabled="true"
                                                                v-model="payload.seats_max"
                                                                :class="classes">
                                                            </b-input>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                    <b-field>
                                                        <validation-provider tag="div" :rules="{required:false}" v-slot="{ errors, classes }">
                                                            <b-checkbox
                                                                :class="classes"
                                                                :native-value="payload.is_locked"
                                                                v-model="payload.is_locked">
                                                                <b-tooltip 
                                                                    type="is-dark"
                                                                    label="New enrollments cannot be made in a locked batch"
                                                                    :multilined="true"
                                                                    position="is-top">
                                                                    Lock this batch    
                                                                </b-tooltip>
                                                            </b-checkbox>
                                                            <p class="error">{{errors[0]}}</p>
                                                        </validation-provider>
                                                    </b-field>
                                                </div>
                                                <div class="ctaWrapper">
                                                    <b-button
                                                        native-type="submit"
                                                        :loading="form.isLoading ? true : false"
                                                        :disabled="form.isLoading ? true : false"
                                                        class="yunoSecondaryCTA">
                                                        <template v-if="isEditBatch">
                                                            Update Batch
                                                        </template>
                                                        <template v-else>
                                                            Create Batch
                                                        </template>
                                                    </b-button>    
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </validation-observer>
                            </template>
                        </section>
                    </template>
                </yuno-page-grid>
            `,
            data() {
                return {
                    isMiniSidebar: false,
                    tableOptions: {
                        isFluid: false,
                        pageLoading: false,
                        apiPaginated: false,
                        totalResult: "",
                        perPage: 50,
                        limit: 20,
                        offset: 0,
                        hasStriped: false,
                        isStickyHeader: true,
                        height: "600"
                    },
                    startDate: {
                        minDate: new Date()
                    },
                    endDate: {
                        minDate: new Date(),
                        maxDate: new Date(),
                    },
                    disabledEnrollment: "",
                    courseDate: false,
                    daysOfWeek: DAYS_OF_WEEK,
                    courseSelection: {
                        isActive: false,
                        filtered: [],
                        selected: []
                    },
                    instructor: {
                        selected: null,
                        current: "",
                        isLoading: false,
                        isActive: false,
                        isField: false
                    },
                    errorMsg: {
                        instructor: "Please select the instructor from list"
                    },
                    form: {
                        isLoading: false
                    },
                    payload: {
                        title: "",
                        created_by: isLoggedIn,
                        course_id: [],
                        start_date: new Date(),
                        end_date: new Date(""),
                        class_time: new Date(),
                        instructor_id: "",
                        duration: "",
                        class_days: [],
                        personalisation: "",
                        seats_max: "",
                        is_locked: false,
                        teaching_mode: "online"
                    },
                    authorizedRoles: AUTHORIZED_ROLES
                }
            },
            computed: {
                ...Vuex.mapState([
                    'user',
                    'userInfo',
                    'header',
                    'userProfile',
                    'userRole',
                    'footer',
                    'loader',
                    'allCourses',
                    'mappedInstructor',
                    'batchCreateUpdate',
                    'batchDetail',
                    'instructorAvailabilityGrid',
                    'capabilities'
                ]),
                wpThemeURL() {
                    return this.$store.state.themeURL
                },
                isUserAuthorized : {
                    get() {
                        if (!YUNOCommon.findInArray(this.authorizedRoles, this.userRole.data)) {
                            return false;
                        }
                        const capabilities = this.capabilities.data;
                        const isValid = capabilities.isGovernGangs;
                        return this.userRole.data === "org-admin" || isValid;
                    }
                },
                isPageLoading: {
                    get() {
                        return this.userInfo.loading || this.capabilities.loading;
                    }
                },
                isPageReady: {
                    get() {
                        return !this.user.isLoggedin || (this.userInfo.success && this.capabilities.success);
                    }
                },
                isFormLoading: {
                    get() {
                        const isEditing = YUNOCommon.getQueryParameter("isEdit") !== false;
                        return this.allCourses.loading || this.capabilities.loading || (isEditing && this.batchDetail.loading);
                    }
                },
                isFormReady: {
                    get() {
                        const isEditing = YUNOCommon.getQueryParameter("isEdit") !== false;
                        const isReady = this.allCourses.success && this.capabilities.success;
                        return isEditing ? isReady && this.batchDetail.success : isReady;
                    }
                },
                emptyStates() {
                    return {
                        state: "notAuthorized"
                    }
                },
                isEditBatch() {
                    const getID = YUNOCommon.getQueryParameter("isEdit");

                    if (getID !== false) {
                        if (this.allCourses.success) {
                            this.initEdit(getID);    
                        }
                        return true;    
                    } else {
                        return false;
                    }
                },
                filteredInstructor: {
                    get() {
                        return this.mappedInstructor.data.filter(option => {
                            return (
                                option.name.toString().toLowerCase().indexOf(this.instructor.current.toLowerCase()) >= 0
                            )
                        })
                    }
                }
            },
            async created() {
                this.emitEvents();
            },
            mounted() {
                
            },
            methods: {
                onLogin(staus) {

                },
                onUserInfo(data) {
                    if ( YUNOCommon.findInArray(this.authorizedRoles, data.role)) {
                        this.fetchCapabilities(false);

                        if (data.role === "Instructor") {
                            this.manageInstructorCase();    
                        }
                    }
                },
                onMini(data) {
                    this.isMiniSidebar = data;
                },
                onMenuLoaded() {
            
                },
                gotCapabilities(options, noAPICall) {
                    if (noAPICall) {
                        this.capabilities.loading = false;
                        this.capabilities.success = true;

                        const roleCapabilities = {
                            "yuno-admin": {
                                isClassesWisdom: true,
                                isEntranceWisdom: true,
                                isGangsWisdom: true,
                                isGovernClasses: true,
                                isGovernLearners: true,
                                isGovernGangs: true,
                                isSysopWisdom: true,
                                isPagoWisdom: true
                            },
                            "Counselor": {
                                isEntranceWisdom: true,
                                isClassesWisdom: true,
                                isGangsWisdom: true,
                                isGovernClasses: true,
                                isGovernLearners: true,
                                isSysopWisdom: true,
                                isPagoWisdom: true
                            }
                        };

                        if (roleCapabilities[this.userRole.data]) {
                            this.capabilities.data = roleCapabilities[this.userRole.data];
                        }

                        this.authorizedUser(this.userRole.data)

                    } else {
                        if (options.response?.data?.code === 200) {
                            this.capabilities.data = options.response.data.data;
                            this.authorizedUser(this.userRole.data)
                        };
                    };
                },
                fetchCapabilities(noAPICall) {
                    if (noAPICall) {
                        this.gotCapabilities(false, noAPICall);
                    } else {
                        const options = {
                            apiURL: YUNOCommon.config.capabilitiesAPI(isLoggedIn, false),
                            module: "gotData",
                            store: "capabilities",
                            addToModule: "false",
                            callback: true,
                            callbackFunc: (options) => this.gotCapabilities(options)
                        };

                        this.$store.dispatch('fetchData', options);
                    };
                },
                manageInstructorCase() {
                    this.payload.instructor_id = isLoggedIn;
                    this.payload.duration = 60;
                },
                emitEvents() {
                    Event.$on('gotUserMenu', () => {
                        if (this.user.isLoggedin) {
                            const role = this.userRole.data;
                            this.fetchCapabilities(false);
                            if (role === "Instructor") {
                                this.manageInstructorCase();
                            }
                        }
                    });
                },
                enrollmentType(type) {
                    this.disabledEnrollment = type === "rolling" ? "fixed" : "rolling";
                    this.courseSelection.selected = [];
                    this.courseSelection.filtered = [];
                    this.courseSelection.isActive = true;
                    this.payload.end_date = new Date("");
                    this.payload.start_date = new Date();
                    this.payload.course_id = [];
                },
                formatDate(d) {
                    return moment(d).format('MMMM DD, YYYY');
                },
                onDatepick(value) {
                    let payload = this.payload;
                    
                    payload.end_date = new Date(this.generateClassEndDate());
                    this.endDate.maxDate = new Date(this.generateClassEndDate());
                    this.endDate.minDate = new Date(value);

                    const currentDate = new Date();
                    this.startDate.minDate = new Date(currentDate.setDate(currentDate.getDate() - 1));

                },
                onInstructorChange(val) {
                    if (val === "") {
                        this.payload.instructor_id = "";
                    }
                },
                onInstructorSelect($event) {
                    if ($event !== null) {
                        this.instructor.selected = $event;
                        this.payload.instructor_id = $event.instructor_id;
                        // this.fetchResources(true, this.payload.instructor_id)
                    } else {
                        this.instructor.selected = null;
                        this.payload.instructor_id = "";
                    }
                },
                gotInstructors(options, instructorID) {
                    let module = this.mappedInstructor;

                    this.instructor.isLoading = false;

                    if (options.response?.data?.code === 201) {
                        this.instructor.isField = true;

                        const getID = YUNOCommon.getQueryParameter("isEdit");

                        if (getID !== false) {
                            this.preFillInstructor(instructorID)
                        }
                    } else {
                        module.data = [];
                        module.error = null;
                        module.errorData = [];
                        this.instructor.isActive = true;
                        this.instructor.isField = true;

                        this.$buefy.snackbar.open({
                            duration: 5000,
                            message: YUNOCommon.config.errorMsg.notMapped,
                            type: 'is-warning',
                            position: 'is-top',
                            actionText: 'Ok'
                        });
                    };
                },
                fetchMappedInstructor(courseID, instructorID) {
                    let payload = {
                        course_id: []
                    }

                    for (let i = 0; i < courseID.length; i++) {
                        const id = courseID[i];
                        payload.course_id.push(id)
                    };

                    const options = {
                        apiURL: YUNOCommon.config.listOfMappedInstructorAPI(),
                        module: "gotData",
                        store: "mappedInstructor",
                        payload: JSON.stringify(payload),
                        headers: {
                            'accept': 'application/json',
                            'content-type': 'application/json'
                        },
                        callback: true,
                        callbackFunc: (options) => this.gotInstructors(options, instructorID)
                    };

                    this.$store.dispatch('postData', options);
                },
                generateClassEndDate() {
                    let getAddedCourses = this.courseSelection.selected,
                        weeks = [],
                        startDate = this.payload.start_date;
                        

                    for (let i = 0; i < getAddedCourses.length; i++) {
                        const course = getAddedCourses[i];

                        weeks.push(course.duration_weeks)
                    }

                    let largestWeek = Math.max(...weeks),
                        classStartDate = new Date(startDate);
                    
                    classStartDate.setDate(classStartDate.getDate() + largestWeek * 7);
                    
                    return classStartDate;
                },
                manageMaxSeats() {
                    if (this.courseSelection.selected.length !== 0) {
                        const validSeats = this.courseSelection.selected
                            .map(o => o.max_seats)
                            .filter(seats => seats !== undefined);
                
                        this.payload.seats_max = validSeats.length > 0 ? Math.max(...validSeats) : "";
                    } else {
                        this.payload.seats_max = "";
                    }
                },
                _manageCourseElements() {
                    let getCourseID = this.payload.course_id;

                    if (this.userRole.data !== "Instructor") {
                        if (this.courseSelection.selected.length !== 0) {
                            this.instructor.isActive = true;
                            this.instructor.isField = false;
                            this.instructor.isLoading = true;
                            this.fetchMappedInstructor(getCourseID);
                        } else {

                            this.instructor.current = "";
                            this.instructor.selected = null;
                            this.payload.instructor_id = "";
                        };
                    };

                    this.payload.end_date = new Date(this.generateClassEndDate());
                    this.endDate.maxDate = new Date(this.generateClassEndDate());
                },
                addCourse($event) {
                    const { payload, courseSelection, allCourses } = this;
                    let initialEnrollType = courseSelection.selected[0].enroll_type;

                    this.disabledEnrollment = initialEnrollType === "rolling" ? "fixed" : "rolling";

                    if ($event.enroll_type === this.disabledEnrollment) {
                        YUNOCommon.removeObjInArr(courseSelection.selected, "post_id", $event.post_id);
                    } else {
                        payload.course_id.push($event.post_id)
                        YUNOCommon.removeObjInArr(allCourses.data, "post_id", $event.post_id);
                        this._manageCourseElements();
                        this.courseDate = true;
                    }
                },
                removeCourse($event) {
                    const { payload, courseSelection, allCourses } = this;

                    YUNOCommon.removeValInArr(payload.course_id, $event.post_id);
                    allCourses.data.push($event)
                    this._manageCourseElements();

                    if (courseSelection.selected.length !== 0) {
                        payload.end_date = new Date(this.generateClassEndDate());
                        this.endDate.maxDate = new Date(this.generateClassEndDate());
                    } else {
                        this.courseDate = false;
                        this.disabledEnrollment = "";
                    };
                },
                onCourseSelect($event, state) {
                    if (state === "add") {
                        this.addCourse($event);
                    } else {
                        this.removeCourse($event);
                    }
                    this.manageMaxSeats();
                },
                onCourseKeypress(data) {
                    if (data.key === "ArrowDown" || data.key === "ArrowUp") {
                        this.manageItems(this.courseSelection.filtered);
                    };
                },
                manageItems(data) {
                    // This direct DOM manipulation is necessary due to limitations in dynamically styling dropdown items of the b-taginput component.
                    let getItems = document.querySelectorAll("#courseList .dropdown-menu .dropdown-item");

                    for (let i = 0; i < getItems.length; i++) {
                        const item = getItems[i],
                            obj = data[i];

                        if (obj !== undefined && obj.enroll_type === this.disabledEnrollment) {
                            item.classList.add("disabled");    
                        } else {
                            item.classList.remove("disabled");    
                        }
                    }
                    
                },
                getFilteredCourse(text) {
                    this.courseSelection.filtered = this.allCourses.data.filter((option) => {
                        return option.product_code
                            .toString()
                            .toLowerCase()
                            .indexOf(text.toLowerCase()) >= 0
                    })

                    this.$nextTick(() => {
                        this.manageItems(this.courseSelection.filtered);
                    });
                },
                preFillInstructor(instructorID) {
                    let mappedInstructor = this.mappedInstructor.data,
                        payload = this.payload,
                        findInstructor = YUNOCommon.findObjectByKey(mappedInstructor, "instructor_id", String(instructorID));

                    if (findInstructor !== null) {
                        this.onInstructorSelect(findInstructor)
                        this.instructor.current = findInstructor.name;
                        this.instructor.selected = findInstructor;
                        payload.instructor_id = findInstructor.instructor_id;
                        // this.fetchResources(true, payload.instructor_id)
                    };
                },
                preFillCourses(courseID, instructorID) {
                    const coursesData = this.allCourses.data,
                        payload = this.payload,
                        courseSelected = this.courseSelection.selected,
                        instructorField = this.instructor;

                    for (let i = 0; i < courseID.length; i++) {
                        const course = Number(courseID[i]),
                            findCourse = YUNOCommon.findObjectByKey(coursesData, "post_id", course);
                        
                        courseSelected.push(findCourse);
                        payload.course_id.push(course)

                        if (this.userRole.data === "Instructor") {
                            this.payload.instructor_id = instructorID;
                        } else {
                            if (courseSelected.length !== 0) {
                                instructorField.isActive = true; 
                                instructorField.isField = false;
                                instructorField.isLoading = true;
                                this.fetchMappedInstructor(payload.course_id, instructorID);    
                            } else {
                                instructorField.current = "";
                                instructorField.selected = null;
                                this.payload.instructor_id = "";
                            };
                        }

                        
                    };
                },
                gotBatch(options) {
                    if (options.response?.data?.code === 200) {

                        const getData = options.response.data.data,
                            payload = this.payload,
                            courseID = getData.course.map(course => course.id),
                            instructorID = getData.instructor.id;

                        this.preFillCourses(courseID, instructorID);
                        payload.title = getData.title;
                        payload.start_date = new Date(getData.start_end.start_date.time);
                        payload.end_date = new Date(getData.start_end.end_date.time);
                        payload.class_time = new Date(getData.class_time.start_time.time);
                        payload.duration = this.userRole.data !== "Instructor" ? getData.class_time.duration : "60"; 
                        payload.class_days = getData.class_days.map(day => day.day);
                        payload.seats_max = getData.seats.max;
                        payload.is_locked = getData.is_locked;
                        this.courseSelection.isActive = true;
                        this.courseDate = true;
                        
                        const today = new Date();
                        let lastEnrollmentDate = new Date(getData.start_end.end_date.time);

                        this.endDate.minDate = new Date(lastEnrollmentDate.setDate(lastEnrollmentDate.getDate() - 1));
                        this.endDate.maxDate = new Date(today.getFullYear() + 18, today.getMonth(), today.getDate());
                    };
                },
                fetchBatch(batchID) {
                    const options = {
                        apiURL: YUNOCommon.config.batch("update", {batchID: batchID}),
                        module: "gotData",
                        store: "batchDetail",
                        callback: true,
                        callbackFunc: (options) => this.gotBatch(options)
                    };

                    this.$store.dispatch('fetchData', options);
                },
                initEdit(batchID) {
                    if (YUNOCommon.findInArray(this.authorizedRoles, this.userRole.data)) {
                        this.fetchBatch(batchID);
                    };
                },
                resetForm() {
                    let payload = this.payload;

                    this.$refs.createBatchObserver.reset();
                    this.courseSelection.filtered = [];
                    this.courseSelection.selected = [];
                    this.instructor.selected = null;
                    this.instructor.current = "";
                    this.instructor.isLoading = false;
                    this.instructor.isActive = false;
                    this.instructor.isField = false;
                    this.courseSelection.isActive = false;
                    this.courseDate = false;
                    
                    payload.title = "";
                    payload.created_by = isLoggedIn;
                    payload.course_id = [];
                    payload.start_date = new Date();
                    payload.end_date = new Date("");
                    payload.class_time = new Date();
                    payload.seats_max = "";
                    payload.is_locked = false;
                    payload.instructor_id = "";
                    payload.duration = "";
                    payload.class_days = [];
                    payload.batch_db_id = "";

                    const role = this.userInfo.data.role;

                    if (role === "org-admin") {
                        payload.created_by = this.activeOrg();
                    }
                },
                manageBatch(options) {
                    this.form.isLoading = false;

                    let title = "",
                        getID = YUNOCommon.getQueryParameter("isEdit");

                    if (getID !== false) {
                        title = "Update Batch"
                    } else {
                        title = "Create Batch"
                    }

                    if (options.response?.data?.code === 201) {
                        let response = options.response.data;

                        this.resetForm();
    
                        if (getID !== false) {
                            this.fetchBatch(getID);
                        } 

                        this.$buefy.toast.open({
                            duration: 5000,
                            message: `${response.message}`,
                            position: 'is-bottom'
                        });

                        this.fetchCourses();

                    } else {
                        this.$buefy.dialog.alert({
                            title: title,
                            message: this.batchCreateUpdate.errorData,
                            confirmText: 'Ok'
                        });
                    };
                },
                initForm() {
                    this.form.isLoading = true;
        
                    const getID = YUNOCommon.getQueryParameter("isEdit"),
                        getClassSize = YUNOCommon.getQueryParameter("classSize");
                
                    let payload = {
                        ...this.payload,
                        batch_db_id: getID !== false ? getID : undefined,
                        personalisation: getClassSize,
                    };

                    const role = this.userInfo.data.role;

                    if (role === "org-admin") {
                        payload.created_by = this.activeOrg();
                    }

                    if (getID) {
                        payload.batch_db_id = getID;
                    }
                
                    const options = {
                        apiURL: this.getFormURL(),
                        module: "gotData",
                        store: "batchCreateUpdate",
                        payload: JSON.stringify(payload),
                        headers: {
                            'accept': 'application/json',
                            'content-type': 'application/json'
                        },
                        callback: true,
                        callbackFunc: options => this.manageBatch(options)
                    };
                
                    this.$store.dispatch(getID !== false ? 'putData' : 'postData', options);
                    
                },
                getFormURL() {
                    const role = this.userInfo.data.role;
                    const getID = YUNOCommon.getQueryParameter("isEdit");
                
                    const allowedRoles = ["org-admin", "yuno-admin", "Instructor"];
                    if (!allowedRoles.includes(role)) {
                        return null;
                    }

                    const isUpdate = getID !== false;
                    const action = isUpdate ? "update" : "create";
                    const params = isUpdate ? { batchID: getID } : false;
                
                    return YUNOCommon.config.batch(action, params);
                },
                activeOrg() {
                    const activeOrg = this.userInfo.data.current_state.org_id;
        
                    if (activeOrg) {
                        return activeOrg;
                    }
                },
                getGroupType(personalization) {
                    if (!personalization) return null;
                    if (personalization.length === 2) return "both";
                    if (personalization.length === 1) {
                        return YUNOCommon.findInArray(personalization, "1-1") ? "one_to_one" : "one_to_many";
                    }
                    return null;
                },
                getGroupTypeFromElement(groupType) {
                    if (groupType.one_to_one && groupType.one_to_many) return "both";
                    if (groupType.one_to_many) return "one_to_many";
                    if (groupType.one_to_one) return "one_to_one";
                    return null;
                },
                /**
                 * Transforms raw course data into a standardized format.
                 * @param {Object} element - The course data object.
                 */
                transformCourseData(element) {
                    const role = this.userInfo.data.role;

                    if (role === "org-admin") {
                        element.enroll_type = element.enrollment_type;
                        element.post_id = element.course_id;
                        element.product_code = element.title;
                        element.group_type = this.getGroupType(element.personalization);
                    } else {
                        element.group_type = this.getGroupTypeFromElement(element.group_type);
                    }
                    return element;
                },
                /**
                 * Handles the response received from the API when fetching courses.
                 * Populates the course selection and performs additional actions based on the user's role.
                 *
                 * @param {Object} options - The options object containing the API response.
                 */
                gotCourses(options) {
                    if (options.response?.data?.code !== 200) return;
                
                    const data = options.response.data.data;
                    const courseID = Number(YUNOCommon.getQueryParameter("courseid"));
                    
                    const transformedData = data.map(this.transformCourseData);

                    if (courseID) {
                        const selectedCourse = transformedData.find(element => element.post_id === courseID);
                        if (selectedCourse) {
                            this.courseSelection.selected.push(selectedCourse);
                        }
                    }
                
                    this.allCourses.data = transformedData;
                    this.isEditBatch;
                },
                fetchCourses() {
                    const options = {
                        apiURL: this.getCoursesURL(),
                        module: "gotData",
                        store: "allCourses",
                        callback: true,
                        addToModule: false,
                        callbackFunc: (options) => this.gotCourses(options)
                    };

                    this.$store.dispatch('fetchData', options);
                },
                getCoursesURL() {
                    const role = this.userInfo.data.role;
                    const getClassSize = YUNOCommon.getQueryParameter("classSize");
                
                    const urlMapping = {
                        "org-admin": YUNOCommon.config.org("courses", this.activeOrg(), isLoggedIn, 0, "all", false, false, false, false, "all", "all", "list-view", 100, 0),
                        "yuno-admin": YUNOCommon.config.allCoursesAPI(getClassSize),
                        "Instructor": YUNOCommon.config.mappedCoursesAPI(isLoggedIn, getClassSize)
                    };
                
                    return urlMapping[role] || null;
                },
                authorizedUser(role) {
                    if (YUNOCommon.findInArray(this.authorizedRoles, role) || this.capabilities.data.isGovernGangs) {
                        this.fetchCourses();
                    }
                },
                additionalRow(rows) {
                    for (let i = 0; i < rows.length; i++) {
                        const row = rows[i];

                        if (row.sun || row.mon || row.tue || row.wed || row.thu || row.fri || row.sat) {
                            row.scrollID = "moveScrollTopHere";

                            break;
                        }
                    }
                },
                additionalCols(cols) {
                    cols.push({
                        field: "slot",
                        label: "",
                        sortable: true,
                        hasSlot: true
                    });

                    const tagFields = ["slot", "sun", "mon", "tue", "wed", "thu", "fri", "sat"];

                    for (const col of cols) {
                        if (tagFields.includes(col.field)) {
                            col.hasTag = true;
                        }
                    }
                },
                scrollToActiveRow() {
                    let table = document.querySelectorAll(".table-wrapper")[0],
                        firstActiveCol = document.getElementById('moveScrollTopHere').parentElement,
                        firstActiveRow = firstActiveCol.parentElement,
                        topPos = firstActiveRow.offsetTop;

                    table.scrollTop = topPos;
                },
                gotResources(options) {
                    const module = this.instructorAvailabilityGrid;

                    if (options.response?.data?.code === 200) {
                        let getData = options.response.data.data,
                            rows = getData.rows,
                            cols = getData.columns,
                            count = options.response.data.count;

                        this.additionalCols(cols);
                        this.additionalRow(rows)

                        this.tableOptions.pageLoading = false;
                        this.tableOptions.totalResult = count;
                        module.data = getData;

                        this.$nextTick(() => {
                            this.scrollToActiveRow();
                        });
                        
                    } else {
                        module.data = [];
                        this.tableOptions.totalResult = 0;
                    }
                },
                fetchResources(moduleLoading, instructorID) {
                    this.instructorAvailabilityGrid.data = [];
                    this.instructorAvailabilityGrid.success = false;

                    const options = {
                        apiURL: YUNOCommon.config.availabilityGridAPI(instructorID),
                        module: "gotData",
                        store: "instructorAvailabilityGrid",
                        moduleLoading: moduleLoading,
                        addToModule: false,
                        callback: true,
                        callbackFunc: (options) => this.gotResources(options)
                    };

                    this.$store.dispatch('fetchData', options);
                },
                /**
                 * Fetch the footer data from API
                 */
                fetchFooter() {
                    const options = {
                        apiURL: YUNOCommon.config.footerAPI(),
                        module: "gotData",
                        store: "footer",
                        callback: false
                    };

                    this.$store.dispatch('fetchData', options);
                },
            }
        });
    };

    return {
        createBatch: createBatch
    };
})(jQuery);



