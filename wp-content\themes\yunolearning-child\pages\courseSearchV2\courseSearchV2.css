@font-face {
  font-family: "Material Icons Outlined";
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/material-Icons.woff2?6qrc5l") format("woff2");
}

@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src: url("../../../dist/fonts/material-Icons-filled.woff2?8qrc5l") format("woff2");
}

.material-icons-outlined, #app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item::before {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.dark38 {
  color: rgba(0, 0, 0, 0.38);
}

.dark87, #app, #app .largerTitle, #app .body3, #app .underline, #app .yunoDropdown.category_level_1 .dropdown-menu .listCaption, #app .course .availability.resources li .material-icons, #app .course .availability.resources li .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .availability.resources li .dropdown-item.is-active::before, #app .course .availability.resources li .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .availability.resources li .mdi.mdi-menu-down:after, #app .course .availability.resources li .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .availability.resources li .mdi.mdi-menu-up:after, #app .course .availability.resources li .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .course .availability.resources li .dropdown-item:before, #app .course .instructorsWrapper, #app .course .description h2, #app .course .description h3, #app .course .description h4, .b-sidebar.yunoSidebar .batches .batchCard, .b-sidebar.yunoSidebar .yunoTabsV3 .tabs ul li.is-active a, .b-sidebar.yunoSidebar .filtersWrapper .button.is-primary, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet .listCaption, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item {
  color: #201A19;
}

.dark60, #app .body1, #app .body2, #app .body4, #app .overline, #app .yunoDropdown .labelWrapper .placeHolder, #app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active, #app .course .studentsInfo li .value, #app .course .description, .b-sidebar.yunoSidebar .batchesWrapper .batchesCount, .b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li, .b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating .caption, .b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .studentCount, .b-sidebar.yunoSidebar .batches .batchCard .cardFooter .full, .b-sidebar.yunoSidebar .yunoTabsV3 .tabs ul li a, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item .itemLabel {
  color: #534342;
}

.tertiary, #app .tertiaryBg {
  background-color: #ffdea6;
}

#app p {
  margin-bottom: 0;
}

#app .tertiaryBg {
  padding: 5px;
  border-radius: 2px;
}

#app .largestTitle {
  font-size: 32px;
  line-height: 42px;
  font-weight: 700;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  #app .largestTitle {
    font-size: 48px;
    line-height: 62px;
    font-weight: 700;
    margin-bottom: 10px;
  }
}

#app .largerTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 10px;
}

#app .largeTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .smallCaption {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .smallerCaption {
  font-size: 16px;
  line-height: 25px;
  font-weight: 500;
  margin-bottom: 10px;
}

#app .smallestCaption, #app .yunoDropdown .timesDays small, #app .course .features.hasCheckList li .maxCount {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .body1 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .body2 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .body3 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 15px;
}

#app .body4 {
  font-size: 13px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .underline {
  text-decoration: underline;
}

#app .overline {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

#app .emptyStateV2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 30px;
}

#app .emptyStateV2 figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .emptyStateV2 figure img {
  width: 158px;
  height: auto;
}

#app .emptyStateV2 figure figcaption {
  margin-top: 15px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

#app .emptyStateV2 figure .button {
  margin-top: 15px;
}

#app .pageRoot.headerIsVisible .filtersWrapper.isStickyEle {
  top: 72px;
}

#app .hideHeader {
  z-index: 8;
}

#app .headerTop {
  height: 1px;
  background: #FFF;
}

@media (min-width: 1024px) {
  #app #yunoMain.isStickyEle {
    padding-bottom: 56px;
  }
}

#app .filterActive {
  overflow: hidden;
  height: 100%;
}

#app .filterActive .isSticky {
  display: none;
}

#app .filtersWrapper {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 9;
  background-color: #fff;
}

#app .filtersWrapper .filterHeader {
  display: none;
}

#app .filtersWrapper.mobileView {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 15px;
}

#app .filtersWrapper.mobileView .filterHeader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .filters {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px;
  padding: 15px 0 15px;
  background-color: #ffffff;
}

#app .filters.categoryFilter {
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  #app .filters.categoryFilter {
    margin-bottom: 0;
  }
}

#app .filters.categoryFilter.mobileView {
  margin-bottom: 0;
}

#app .filters.otherFilters {
  display: none;
}

@media (min-width: 768px) {
  #app .filters.otherFilters {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

#app .filters.otherFilters.mobileView {
  display: block;
}

#app .filters.noBtmGap {
  padding-bottom: 0;
}

#app .filters.noTopGap {
  padding-top: 0;
}

#app .filters .yunoDropdown {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  margin: 0 0 0;
  padding: 0 15px;
  display: none;
}

#app .filters .yunoDropdown:first-child {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 43px);
          flex: 0 0 calc(100% - 43px);
}

@media (min-width: 768px) {
  #app .filters .yunoDropdown:first-child {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
  }
}

@media (min-width: 768px) {
  #app .filters .yunoDropdown:first-child.category {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
}

@media (min-width: 768px) {
  #app .filters .yunoDropdown {
    display: block;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
            flex: 0 0 25%;
    margin: 15px 0 0;
  }
}

#app .filters.mobileView .yunoDropdown {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  display: block;
  margin-bottom: 15px;
}

#app .filters.mobileView .yunoDropdown:first-child {
  display: block;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

#app .filters.mobileView .filterTrigger {
  display: none;
}

#app .filters .filterTrigger {
  display: block;
  -ms-flex-item-align: center;
      -ms-grid-row-align: center;
      align-self: center;
  padding-right: 15px;
}

#app .filters .filterTrigger .material-icons, #app .filters .filterTrigger .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .filters .filterTrigger .dropdown-item.is-active::before, #app .filters .filterTrigger .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .filters .filterTrigger .mdi.mdi-menu-down:after, #app .filters .filterTrigger .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .filters .filterTrigger .mdi.mdi-menu-up:after, #app .filters .filterTrigger .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .filters .filterTrigger .dropdown-item:before {
  font-size: 28px;
}

@media (min-width: 768px) {
  #app .filters .filterTrigger {
    display: none;
  }
}

#app .yunoDropdown {
  min-width: 0;
}

#app .yunoDropdown.slider .dropdown-menu {
  padding: 0 20px 0 15px;
}

#app .yunoDropdown.slider .b-slider.is-primary .b-slider-fill {
  background: #A81E22 !important;
}

#app .yunoDropdown.slider .b-tooltip.is-primary .tooltip-content {
  background: #A81E22 !important;
}

#app .yunoDropdown .timesDays {
  padding-bottom: 0;
}

#app .yunoDropdown .timesDays .wrapper {
  padding: 10px 0 0;
}

#app .yunoDropdown .timesDays small {
  padding: 0 15px;
  display: block;
}

#app .yunoDropdown .timesDays ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin: 10px 0 0;
}

#app .yunoDropdown .timesDays ul li {
  padding: 0;
}

#app .yunoDropdown .timesDays ul li .inner {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #201A19;
  padding: 10px 15px;
}

#app .yunoDropdown .timesDays ul li .inner:hover {
  background-color: #FFF8F7;
}

#app .yunoDropdown .timesDays ul li .inner:active {
  background-color: #FBEEEC;
}

#app .yunoDropdown .timesDays ul li .itemLabel {
  font-size: 12px;
  margin-left: 3px;
}

#app .yunoDropdown .timesDays ul li .material-icons, #app .yunoDropdown.category_level_1 .timesDays ul li .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .timesDays ul li .dropdown-item.is-active::before, #app .yunoDropdown .timesDays ul li .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .yunoDropdown .timesDays ul li .mdi.mdi-menu-down:after, #app .yunoDropdown .timesDays ul li .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .yunoDropdown .timesDays ul li .mdi.mdi-menu-up:after, #app .yunoDropdown .timesDays ul li .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .yunoDropdown .timesDays ul li .dropdown-item:before {
  font-size: 22px;
  color: #534342;
  margin-right: 15px;
}

#app .yunoDropdown .timesDays ul li.active .inner {
  background-color: #FBEEEC;
}

#app .yunoDropdown .dropdown {
  width: 100%;
}

#app .yunoDropdown .dropdown.is-active .labelWrapper {
  border-color: #201A19;
}

#app .yunoDropdown .dropdown-trigger {
  width: 100%;
}

#app .yunoDropdown.availability .dropdown-menu {
  width: 100%;
  left: auto;
  right: 0;
}

#app .yunoDropdown .dropdown-menu {
  width: 100%;
  padding: 0;
  border-radius: 4px;
  border: 1px solid #e6e6e6;
  -webkit-box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04), 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
          box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04), 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
}

#app .yunoDropdown .dropdown-menu .dropdown-content {
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 0;
}

#app .yunoDropdown .dropdown-menu a {
  color: #201A19;
  padding: 10px 15px;
}

#app .yunoDropdown .dropdown-menu a:hover {
  background-color: #FFF8F7;
}

#app .yunoDropdown .dropdown-menu a:active {
  background-color: #FBEEEC;
}

#app .yunoDropdown .dropdown-menu a.dropdown-item.is-active {
  background-color: #FBEEEC;
}

#app .yunoDropdown .labelWrapper {
  padding: 4px 35px 4px 15px;
  border: 1px solid #E6E6E6;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
}

#app .yunoDropdown .labelWrapper:hover {
  border-color: #A81E22;
}

#app .yunoDropdown .labelWrapper:active {
  border-color: #201A19;
}

#app .yunoDropdown .labelWrapper .icon {
  position: absolute;
  right: 10px;
  top: calc(50% - 12px);
}

#app .yunoDropdown .labelWrapper .placeHolder {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  display: block;
}

#app .yunoDropdown .labelWrapper .clearFilter {
  position: absolute;
  right: 35px;
  bottom: 0;
}

#app .yunoDropdown .labelWrapper .selectedItem {
  font-weight: 400;
  padding-top: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  text-wrap: nowrap;
  display: block;
  padding-right: 27px;
}

#app .yunoDropdown .labelWrapper .selectedItem span {
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
}

#app .yunoDropdown .labelWrapper .selectedItem span:last-child::after {
  display: none;
}

#app .yunoDropdown .labelWrapper .selectedItem span::after {
  content: ",";
  position: relative;
  left: -2px;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-right: 0;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item {
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  padding-right: 27px;
  margin-right: 5px;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item:last-child {
  margin-right: 0;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item span::after {
  display: none;
}

#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .clearFilter {
  position: absolute;
  right: 0;
  top: 0;
}

@media (min-width: 768px) {
  #app .yunoDropdown.category {
    font-size: 16px;
    margin-top: 0;
  }
  #app .yunoDropdown.category .labelWrapper {
    border-color: transparent;
    background-color: rgba(168, 30, 34, 0.05);
    padding: 7px 35px 7px 15px;
    font-size: 20px;
  }
  #app .yunoDropdown.category .labelWrapper .selectedItem {
    font-weight: 500;
  }
  #app .yunoDropdown.category .placeHolder {
    display: none;
  }
  #app .yunoDropdown.category .dropdown-menu {
    width: auto;
  }
  #app .yunoDropdown.category .dropdown-menu .dropdown-content {
    height: 300px;
    overflow-y: auto;
  }
}

@media (min-width: 768px) {
  #app .yunoDropdown.category_level_1 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    padding-right: 0;
  }
  #app .yunoDropdown.category_level_1 .labelWrapper {
    padding: 7px 35px 7px 15px;
  }
  #app .yunoDropdown.category_level_1 .dropdown-menu {
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
  }
}

#app .yunoDropdown.category_level_1 .dropdown-menu .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 15px;
}

#app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item {
  position: relative;
  padding-left: 35px;
}

#app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item::before {
  content: "\e835";
  position: absolute;
  left: 13px;
  top: 11px;
  font-size: 18px;
}

#app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active {
  background-color: #fff;
}

#app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before {
  content: "\e834";
  font-size: 18px;
  color: #A81E22;
}

#app .yunoDropdown.personalization .dropdown-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

#app .yunoDropdown.personalization .dropdown-item .material-icons, #app .yunoDropdown.personalization .dropdown-item .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .yunoDropdown.personalization .dropdown-item .dropdown-item.is-active::before, #app .yunoDropdown.personalization .dropdown-item .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .yunoDropdown.personalization .dropdown-item .mdi.mdi-menu-down:after, #app .yunoDropdown.personalization .dropdown-item .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .yunoDropdown.personalization .dropdown-item .mdi.mdi-menu-up:after, #app .yunoDropdown.personalization .dropdown-item .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .yunoDropdown.personalization .dropdown-item .dropdown-item:before {
  margin-right: 15px;
  font-size: 22px;
  color: #534342;
}

#app .yunoDropdown.personalization .dropdown-item .itemSubtitle {
  display: block;
  white-space: normal;
  font-size: 12px;
  color: #534342;
}

#app .pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0 -15px;
}

#app .pagination li {
  padding: 0 10px;
}

#app .pagination li.noRightGap {
  padding-right: 0;
}

#app .pagination li.page {
  padding-left: 0;
}

#app .pagination li.gapRight {
  padding-right: 15px;
}

#app .pagination li.firstLast {
  display: none;
}

@media (min-width: 768px) {
  #app .pagination li.firstLast {
    display: block;
  }
}

#app .pagination button {
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 5px 15px;
  height: 36px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
}

#app .pagination button.nextPrev {
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .pagination button.nextPrev .material-icons, #app .pagination button.nextPrev .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .pagination button.nextPrev .dropdown-item.is-active::before, #app .pagination button.nextPrev .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .pagination button.nextPrev .mdi.mdi-menu-down:after, #app .pagination button.nextPrev .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .pagination button.nextPrev .mdi.mdi-menu-up:after, #app .pagination button.nextPrev .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .pagination button.nextPrev .dropdown-item:before {
  font-size: 24px;
}

#app .pagination button.disabled {
  cursor: not-allowed;
}

#app .pagination button.disabled.active {
  background: #A81E22;
  border-color: transparent;
  color: #ffffff;
}

#app .mainBody .courses {
  margin-bottom: 30px;
}

#app .mainBody .courses .count {
  margin-bottom: 15px;
  font-weight: 500;
}

#app .course {
  border: 1px solid #E6E6E6;
  border-radius: 4px;
  background: #ffffff;
  padding: 8px;
  margin-bottom: 30px;
}

#app .course .greyBG {
  background-color: #FFFBFF;
  padding: 15px 15px 6px 15px;
}

#app .course .categoryInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (max-width: 768px) {
  #app .course .categoryInfo {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
    gap: 5px;
  }
}

#app .course .categoryInfo .hasFlex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
}

#app .course .categoryInfo .academy .tooltip-content {
  width: 165px;
}

#app .course .categoryInfo .courseType {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 7px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .course .categoryInfo .courseType .tooltip-trigger {
  height: 24px;
}

#app .course .categoryInfo .courseType .material-icons-outlined, #app .course .categoryInfo .courseType .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .categoryInfo .courseType .dropdown-item::before {
  color: #A81E22;
}

#app .course .noBg {
  padding: 0 15px 5px;
}

@media (min-width: 768px) {
  #app .course .noBg.hasFlex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: end;
        -ms-flex-align: end;
            align-items: end;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    margin-top: 0;
    padding: 0 15px 5px;
  }
}

#app .course .noBg .overline {
  margin: 15px 0 0;
}

#app .course .noPad {
  padding: 0;
  margin: 0;
  border-radius: 0;
  border: 0;
}

#app .course:hover {
  border-color: #A81E22;
}

#app .course .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  margin-top: 15px;
}

@media (min-width: 768px) {
  #app .course .ctaWrapper {
    margin-top: 0;
    -webkit-box-pack: end;
        -ms-flex-pack: end;
            justify-content: flex-end;
    width: 100%;
  }
}

#app .course .ctaWrapper .button:first-child {
  margin-right: 15px;
  width: 100%;
}

#app .course .ctaWrapper .button:last-child {
  margin-top: 15px;
}

@media (min-width: 768px) {
  #app .course .ctaWrapper .button:last-child {
    margin-top: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

#app .course .courseHeader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 5px 0 0;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

#app .course .courseHeader .largerTitle a {
  text-decoration: none;
  border-bottom: 2px solid transparent;
  -webkit-transition: border-color 0.3s ease;
  transition: border-color 0.3s ease;
}

#app .course .courseHeader .largerTitle a:focus {
  border-bottom: 2px solid black;
}

#app .course .courseHeader .price {
  margin-top: 5px;
}

#app .course .courseHeader .price .hasFlex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .course .courseHeader .price .hasFlex .smallestCaption, #app .course .courseHeader .price .hasFlex .yunoDropdown .timesDays small, #app .yunoDropdown .timesDays .course .courseHeader .price .hasFlex small, #app .course .courseHeader .price .hasFlex .features.hasCheckList li .maxCount, #app .course .features.hasCheckList li .courseHeader .price .hasFlex .maxCount {
  margin-left: 5px;
}

#app .course .courseHeader .price .largerTitle {
  margin: 0;
}

@media (min-width: 768px) {
  #app .course .courseHeader .price {
    margin-top: 0;
    text-align: right;
  }
}

@media (min-width: 768px) {
  #app .course .courseHeader {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

#app .course h2.largerTitle {
  margin: 0;
}

#app .course h2.largerTitle a {
  color: #201A19;
}

#app .course h2.largerTitle a:hover {
  color: #A81E22;
  text-decoration: none;
}

#app .course .features {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 15px -15px 10px;
}

@media (min-width: 768px) {
  #app .course .features {
    margin-top: 0;
  }
}

#app .course .features.hasFlexColumn {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 6px;
}

#app .course .features.hasActiveBg .b-tooltip {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
  font-weight: 500;
  color: #201A19;
  background: #FBEEEC;
  padding: 6px 10px;
}

@media (max-width: 768px) {
  #app .course .features.hasActiveBg .b-tooltip {
    font-size: 14px;
    padding: 10px 8px;
  }
}

#app .course .features.hasCheckList li {
  opacity: 0.3;
  position: relative;
}

#app .course .features.hasCheckList li .material-icons, #app .course .features.hasCheckList li .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .features.hasCheckList li .dropdown-item.is-active::before, #app .course .features.hasCheckList li .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .features.hasCheckList li .mdi.mdi-menu-down:after, #app .course .features.hasCheckList li .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .features.hasCheckList li .mdi.mdi-menu-up:after, #app .course .features.hasCheckList li .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .course .features.hasCheckList li .dropdown-item:before {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  margin-right: 5px;
}

#app .course .features.hasCheckList li.active {
  opacity: 1;
}

#app .course .features.hasCheckList li.active .material-icons, #app .course .features.hasCheckList li.active .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .features.hasCheckList li.active .dropdown-item.is-active::before, #app .course .features.hasCheckList li.active .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .features.hasCheckList li.active .mdi.mdi-menu-down:after, #app .course .features.hasCheckList li.active .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .features.hasCheckList li.active .mdi.mdi-menu-up:after, #app .course .features.hasCheckList li.active .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .course .features.hasCheckList li.active .dropdown-item:before {
  color: #25d366;
}

#app .course .features.hasCheckList li:last-child::after {
  display: none;
}

#app .course .features.hasCheckList li::after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.08);
  position: absolute;
  top: 0;
  right: 0;
}

#app .course .features.hasCheckList li .maxCount {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: 5px;
}

#app .course .features.hasCheckList li .maxCount .value {
  border-radius: 4px 0px 0px 4px;
  border: 1.5px solid #534342;
  background: #fff;
  padding: 1px 8px;
}

#app .course .features.hasCheckList li .maxCount .key {
  border-radius: 0px 4px 4px 0px;
  border: 1.5px solid #534342;
  background: #534342;
  color: white;
  padding: 1px 8px;
}

#app .course .features.noborder {
  border: 0;
  margin-top: 15px;
  margin-bottom: 15px;
  padding-bottom: 0;
}

@media (min-width: 768px) {
  #app .course .features.noborder {
    margin-bottom: 0;
  }
}

#app .course .features li {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14px;
  margin-bottom: 5px;
}

@media (max-width: 768px) {
  #app .course .features li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    padding: 0 0 0 15px;
  }
}

#app .course .features li .tooltip-trigger {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

#app .course .features li .material-icons-outlined, #app .course .features li .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .features li .dropdown-item::before {
  margin-right: 5px;
  font-size: 20px;
}

@media (min-width: 768px) {
  #app .course .features li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-bottom: 0;
  }
}

#app .course .studentsInfo {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px 15px;
}

#app .course .studentsInfo li {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14px;
  position: relative;
}

@media (min-width: 768px) {
  #app .course .studentsInfo li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
}

#app .course .studentsInfo li:last-child::after {
  display: none;
}

#app .course .studentsInfo li::after {
  content: "";
  width: 1px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.08);
  position: absolute;
  top: 0;
  right: 0;
}

#app .course .studentsInfo li .label {
  margin: 0 5px 0 0;
  padding: 0;
}

#app .course .availability {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 10px -15px;
}

@media (min-width: 768px) {
  #app .course .availability {
    margin: 15px -15px;
  }
}

#app .course .availability li {
  padding: 0 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: top;
      -ms-flex-align: top;
          align-items: top;
  font-size: 12px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 5px;
}

@media (min-width: 768px) {
  #app .course .availability li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-bottom: 0;
  }
}

#app .course .availability li .label {
  margin: 0 5px 0;
  font-weight: 400;
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  opacity: 0.3;
}

#app .course .availability li .label small {
  font-size: 12px;
  margin-left: 3px;
}

#app .course .availability li .material-icons, #app .course .availability li .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .availability li .dropdown-item.is-active::before, #app .course .availability li .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .availability li .mdi.mdi-menu-down:after, #app .course .availability li .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .availability li .mdi.mdi-menu-up:after, #app .course .availability li .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .course .availability li .dropdown-item:before {
  font-size: 20px;
  color: rgba(0, 0, 0, 0.5);
  opacity: 0.3;
}

#app .course .availability li.active .label {
  opacity: 1;
}

#app .course .availability li.active .material-icons, #app .course .availability li.active .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .availability li.active .dropdown-item.is-active::before, #app .course .availability li.active .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .availability li.active .mdi.mdi-menu-down:after, #app .course .availability li.active .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .availability li.active .mdi.mdi-menu-up:after, #app .course .availability li.active .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .course .availability li.active .dropdown-item:before {
  color: #25d366;
  opacity: 1;
}

#app .course .availability.resources li {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  opacity: 1;
}

@media (min-width: 768px) {
  #app .course .availability.resources li {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
  }
}

#app .course .instructorsWrapper {
  font-size: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 4px;
}

#app .course .instructorsWrapper .instructorsList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}

#app .course .instructorsWrapper .instructorsList .b-tooltip .tooltip-content {
  width: 150px;
}

#app .course .instructorsWrapper .instructorsList .instructorMapped img {
  width: 40px;
  height: 40px;
  border-radius: 4px;
}

#app .course .instructorsWrapper .instructorsList .material-icons, #app .course .instructorsWrapper .instructorsList .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .instructorsWrapper .instructorsList .dropdown-item.is-active::before, #app .course .instructorsWrapper .instructorsList .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .instructorsWrapper .instructorsList .mdi.mdi-menu-down:after, #app .course .instructorsWrapper .instructorsList .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .instructorsWrapper .instructorsList .mdi.mdi-menu-up:after, #app .course .instructorsWrapper .instructorsList .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .course .instructorsWrapper .instructorsList .dropdown-item:before {
  background-color: #FFF;
}

#app .course .instructorsWrapper .instructorsList li.noImage {
  position: relative;
  width: 40px;
  height: 40px;
}

#app .course .instructorsWrapper .instructorsList li.noImage .material-icons, #app .course .instructorsWrapper .instructorsList li.noImage .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .instructorsWrapper .instructorsList li.noImage .dropdown-item.is-active::before, #app .course .instructorsWrapper .instructorsList li.noImage .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .instructorsWrapper .instructorsList li.noImage .mdi.mdi-menu-down:after, #app .course .instructorsWrapper .instructorsList li.noImage .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .instructorsWrapper .instructorsList li.noImage .mdi.mdi-menu-up:after, #app .course .instructorsWrapper .instructorsList li.noImage .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .course .instructorsWrapper .instructorsList li.noImage .dropdown-item:before {
  position: absolute;
  top: -19px;
  left: -6px;
  font-size: 52px;
}

#app .course .description {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
  min-height: 40px;
}

#app .course .description.hasShowMore {
  height: 50px;
  overflow: hidden;
  -webkit-mask-image: -webkit-gradient(linear, left top, left bottom, color-stop(20%, black), to(transparent));
  -webkit-mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
  mask-image: -webkit-gradient(linear, left top, left bottom, color-stop(20%, black), to(transparent));
  mask-image: linear-gradient(to bottom, black 20%, transparent 100%);
}

#app .course .description .media div[data-oembed-url] {
  width: 100%;
  margin-bottom: 15px;
}

#app .course .description p {
  margin-bottom: 15px;
}

#app .course .description h2 {
  font-size: 28px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .course .description h3 {
  font-size: 24px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .course .description h4 {
  font-size: 20px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 15px;
}

#app .course .description ul,
#app .course .description ol {
  padding: 0;
  margin: 0 0 15px 18px;
}

#app .course .description ul li,
#app .course .description ol li {
  list-style: disc outside;
  margin-bottom: 5px;
  font-size: 16px;
}

#app .course .description ul li:last-child,
#app .course .description ol li:last-child {
  margin-bottom: 0;
}

#app .course .description ol li {
  list-style: decimal outside;
}

#app .course .showmore {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 12px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
  text-decoration: none;
  margin-bottom: 15px;
}

@media (min-width: 768px) {
  #app .course .showmore {
    margin-bottom: 0;
  }
}

#app .course .showmore:hover {
  text-decoration: none;
}

#app .course .showmore .anchorLabel {
  text-decoration: underline;
}

#app .course .showmore .material-icons, #app .course .showmore .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .course .showmore .dropdown-item.is-active::before, #app .course .showmore .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .showmore .mdi.mdi-menu-down:after, #app .course .showmore .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon #app .course .showmore .mdi.mdi-menu-up:after, #app .course .showmore .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper #app .course .showmore .dropdown-item:before {
  font-size: 18px;
}

.b-sidebar.yunoSidebar {
  /**
        * Styles for the .sectionWrapper class.
        * 
        * This class is used to define the styling for a section wrapper element.
        * It sets the padding and border properties, and also provides additional styles
        * for elements with the .hasBorder, .hasTopGap, and .ctaWrapper classes.
        * 
    */
}

.b-sidebar.yunoSidebar .container {
  width: 1140px;
  padding: 30px 15px;
}

@media (max-width: 767px) {
  .b-sidebar.yunoSidebar .container {
    width: 100%;
  }
}

.b-sidebar.yunoSidebar .container .close {
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
}

.b-sidebar.yunoSidebar .batchesWrapper {
  margin-bottom: 60px;
}

.b-sidebar.yunoSidebar .batchesWrapper .sectionTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 15px;
}

.b-sidebar.yunoSidebar .batchesWrapper .showAll {
  height: 40px;
  width: 100%;
}

.b-sidebar.yunoSidebar .batchesWrapper .batchesCount {
  font-size: 12px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 15px;
}

.b-sidebar.yunoSidebar .sectionWrapper {
  padding: 24px;
}

.b-sidebar.yunoSidebar .sectionWrapper.gap15 {
  padding: 15px;
}

.b-sidebar.yunoSidebar .sectionWrapper.hasBorder {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.b-sidebar.yunoSidebar .sectionWrapper .radiusBorder {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.b-sidebar.yunoSidebar .sectionWrapper.hasTopGap,
.b-sidebar.yunoSidebar .sectionWrapper .ctaWrapper.hasTopGap {
  margin-top: 24px;
}

.b-sidebar.yunoSidebar .sectionWrapper.hasBtmGap {
  margin-bottom: 24px;
}

.b-sidebar.yunoSidebar .sectionWrapper.noPadding {
  padding: 0;
}

.b-sidebar.yunoSidebar .sectionWrapper .ctaWrapper.isFlexbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
}

.b-sidebar.yunoSidebar .sectionWrapper .ctaWrapper.isFlexbox.alignC {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.b-sidebar.yunoSidebar .sectionWrapper .ctaWrapper.stack {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.b-sidebar.yunoSidebar .sectionWrapper .sectionMedia img {
  width: 100%;
  height: auto;
  border-radius: 4px;
}

.b-sidebar.yunoSidebar .sectionWrapper .sectionMedia iframe {
  width: 100%;
  height: auto;
}

.b-sidebar.yunoSidebar .batches {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px;
}

.b-sidebar.yunoSidebar .batches .batchCard {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  padding: 0 15px;
  margin-bottom: 30px;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .batches .batchCard {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
            flex: 0 0 50%;
  }
}

.b-sidebar.yunoSidebar .batches .batchCard .calendar {
  border-radius: 4px;
  text-align: center;
  border: 1px solid #E6E6E6;
  overflow: hidden;
  width: 75px;
  margin-right: 15px;
}

.b-sidebar.yunoSidebar .batches .batchCard .calendar .day {
  background-color: #201a19;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  padding: 6px 0;
  margin: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .calendar .date {
  color: #201A19;
  font-size: 28px;
  font-weight: bold;
  line-height: 36px;
  padding-top: 10px;
  margin: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .calendar .month {
  color: #534342;
  font-size: 12px;
  font-weight: bold;
  padding-bottom: 10px;
  margin: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .wrapper {
  background: #ffffff;
  -webkit-box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  padding: 15px;
  border: 1px solid transparent;
}

.b-sidebar.yunoSidebar .batches .batchCard .wrapper:hover {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.b-sidebar.yunoSidebar .batches .batchCard .wrapper:hover .ctaWrapper,
.b-sidebar.yunoSidebar .batches .batchCard .wrapper:hover .full {
  visibility: visible;
}

.b-sidebar.yunoSidebar .batches .batchCard .batchdate {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.b-sidebar.yunoSidebar .batches .batchCard .batchdate .material-icons, .b-sidebar.yunoSidebar .batches .batchCard .batchdate #app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .b-sidebar.yunoSidebar .batches .batchCard .batchdate .dropdown-item.is-active::before, .b-sidebar.yunoSidebar .batches .batchCard .batchdate .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon .batches .batchCard .batchdate .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .batches .batchCard .batchdate .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon .batches .batchCard .batchdate .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .batches .batchCard .batchdate .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .batches .batchCard .batchdate .dropdown-item:before {
  margin-right: 5px;
}

.b-sidebar.yunoSidebar .batches .batchCard .days {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 0 -5px 10px;
}

.b-sidebar.yunoSidebar .batches .batchCard .days li {
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  color: rgba(0, 0, 0, 0.38);
  text-transform: uppercase;
  padding: 0 5px;
}

.b-sidebar.yunoSidebar .batches .batchCard .days li.isActive {
  color: #201A19;
}

.b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo {
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo.noBtmMargin {
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 5px;
}

.b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li .material-icons-outlined, .b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li #app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item::before, #app .yunoDropdown.category_level_1 .dropdown-menu .b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li .dropdown-item::before {
  font-size: 16px;
}

.b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li .itemCaption {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  margin-left: 5px;
}

.b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li .itemCaption.noMargin {
  margin: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li .hasBG {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  background-color: rgba(168, 30, 34, 0.04);
  border-radius: 100px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 4px 10px;
}

.b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li .hasBG .material-icons-outlined, .b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li .hasBG #app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item::before, #app .yunoDropdown.category_level_1 .dropdown-menu .b-sidebar.yunoSidebar .batches .batchCard .scheduleInfo li .hasBG .dropdown-item::before {
  margin-right: 5px;
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .imgWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 64px;
          flex: 0 0 64px;
  margin-right: 10px;
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .imgWrapper img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  font-size: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor figcaption {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 calc(100% - 79px);
          flex: 0 0 calc(100% - 79px);
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insName {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  left: -3px;
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating .material-icons, .b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating #app .yunoDropdown.category_level_1 .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.category_level_1 .dropdown-menu .b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating .dropdown-item.is-active::before, .b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon .batches .batchCard .mappedInstructor .insRating .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating .filtersWrapper .button .icon .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon .batches .batchCard .mappedInstructor .insRating .mdi.mdi-menu-up:after, .b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .batches .batchCard .mappedInstructor .insRating .dropdown-item:before {
  color: #f9b600;
  margin-right: 5px;
  font-size: 18px;
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .insRating .caption {
  font-size: 12px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .mappedInstructor .studentCount {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 10px;
}

.b-sidebar.yunoSidebar .batches .batchCard .cardFooter {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: 0;
}

.b-sidebar.yunoSidebar .batches .batchCard .cardFooter .price {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .batches .batchCard .cardFooter .ctaWrapper {
    visibility: hidden;
  }
}

.b-sidebar.yunoSidebar .batches .batchCard .cardFooter .full {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-top: 5px;
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
  visibility: hidden;
}

.b-sidebar.yunoSidebar .yunoTabsV3 {
  margin-top: 15px;
}

.b-sidebar.yunoSidebar .yunoTabsV3 .tabs {
  margin-bottom: 15px;
}

.b-sidebar.yunoSidebar .yunoTabsV3 .tabs ul li a {
  font-size: 14px;
}

.b-sidebar.yunoSidebar .yunoTabsV3 .tabs ul li.is-active a {
  border-bottom-color: #201A19;
}

.b-sidebar.yunoSidebar .yunoTabsV3 .tab-content {
  padding: 0;
}

.b-sidebar.yunoSidebar .filtersWrapper {
  padding: 0;
  margin: 0 0 15px;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .filtersWrapper {
    padding: 0;
  }
}

.b-sidebar.yunoSidebar .filtersWrapper > ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: 0 -15px;
}

.b-sidebar.yunoSidebar .filtersWrapper > ul li {
  padding: 0 15px;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .filtersWrapper > ul li {
    padding: 0 0 0 15px;
  }
}

.b-sidebar.yunoSidebar .filtersWrapper > ul li.course_search, .b-sidebar.yunoSidebar .filtersWrapper > ul li.search_instructor {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 10px;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .filtersWrapper > ul li.course_search, .b-sidebar.yunoSidebar .filtersWrapper > ul li.search_instructor {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 400px;
            flex: 0 0 400px;
    margin-bottom: 0;
  }
}

.b-sidebar.yunoSidebar .filtersWrapper > ul li.isFocus.search .field .control input[type="text"] {
  border-color: black;
}

.b-sidebar.yunoSidebar .filtersWrapper > ul li.search .field .control input[type="text"] {
  height: 40px;
  border-color: rgba(0, 0, 0, 0.12);
}

.b-sidebar.yunoSidebar .filtersWrapper > ul li.search .dropdown-menu .dropdown-item .subtitle {
  font-size: 10px;
  line-height: auto;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .filtersWrapper > ul li.singleCheck {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (min-width: 1280px) {
  .b-sidebar.yunoSidebar .filtersWrapper > ul li.singleCheck {
    margin-top: 15px;
  }
}

@media (min-width: 1800px) {
  .b-sidebar.yunoSidebar .filtersWrapper > ul li.singleCheck {
    margin-top: 0;
  }
}

.b-sidebar.yunoSidebar .filtersWrapper > ul li.singleCheck .b-checkbox {
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .filtersWrapper > ul li.singleCheck .b-checkbox.is-disabled {
  cursor: not-allowed;
}

.b-sidebar.yunoSidebar .filtersWrapper.loading .b-skeleton {
  width: auto;
  margin: 0 0 0 15px;
}

.b-sidebar.yunoSidebar .filtersWrapper .fa-filter {
  font-size: 24px;
  padding-right: 15px;
}

.b-sidebar.yunoSidebar .filtersWrapper .button.is-primary {
  background-color: #FFF;
  color: #000;
  font-size: 14px;
  border-color: rgba(0, 0, 0, 0.12);
  font-size: 14px;
  border-radius: 4px;
  padding: 8px 15px 9px;
}

.b-sidebar.yunoSidebar .filtersWrapper .button.is-primary:active, .b-sidebar.yunoSidebar .filtersWrapper .button.is-primary:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.b-sidebar.yunoSidebar .filtersWrapper .button .icon {
  margin: 0 0 0 5px;
  position: relative;
  top: 1px;
}

.b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-down:after, .b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after {
  content: "\e5c5";
}

.b-sidebar.yunoSidebar .filtersWrapper .button .icon .mdi.mdi-menu-up:after {
  content: "\e5c7";
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
  margin-bottom: 15px;
  margin-left: 0;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .filtersWrapper .filterMenu {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
            flex: 0 0 auto;
    margin-bottom: 0;
    margin: 0;
  }
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu.active button.filter {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu.active button.filter .icon {
  color: #A81E22;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu.is-active button {
  border-color: rgba(0, 0, 0, 0.87);
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu.class_days_time .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 10px 0;
  padding-top: 10px;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu.class_days_time .ctaWrapper .button {
  border-color: #A81E22;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 10px;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_days .innerWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 5px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_days .innerWrapper {
    width: 440px;
  }
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 59px;
          flex: 0 0 59px;
  padding: 10px 15px 0 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item:hover {
  background: none;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item:before {
  display: none;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item .itemLabel {
  display: block;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 100px;
  font-size: 10px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
  text-transform: uppercase;
  text-align: center;
  border: 1px solid transparent;
  padding: 6px 5px;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item .itemLabel:hover {
  border-color: rgba(0, 0, 0, 0.6);
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_days .dropdown-item.is-active .itemLabel {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
  color: #A81E22;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px 15px 10px 10px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper {
    width: 440px;
  }
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 25%;
          flex: 0 0 25%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  padding: 10px 15px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 1px solid transparent;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.is-active {
  border-color: #a81e22;
  background-color: rgba(168, 30, 34, 0.04);
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item:before {
  font-family: "Material Icons" !important;
  position: static;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.morning:before {
  content: "\e1c6";
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.afternoon:before {
  content: "\e518";
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.evening:before {
  content: "\e1c6";
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item.night:before {
  content: "\e51c";
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item .itemCaption {
  padding: 5px 0;
  font-size: 16px;
  font-weight: 500;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .innerWrapper .dropdown-item .itemLabel {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .filterSet.class_time .ctaWrapper {
  border: 0;
  margin-bottom: 0;
  padding-bottom: 0;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .ctaWrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin: 10px 10px 0;
  padding-top: 10px;
  border-top: 1px solid;
  border-color: rgba(0, 0, 0, 0.12);
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .ctaWrapper .button {
  border-color: #A81E22;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .dropdown-trigger {
  width: 100%;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu button {
  width: 100%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .filtersWrapper .filterMenu button {
    width: auto;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu button > span {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .dropdown-content {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 0;
  max-height: 300px;
  overflow: hidden;
  overflow-y: auto;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .dropdown-content a {
  color: rgba(0, 0, 0, 0.5);
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .dropdown-content a.is-active {
  background: none;
  color: black;
}

.b-sidebar.yunoSidebar .filtersWrapper .filterMenu .dropdown-content a:active, .b-sidebar.yunoSidebar .filtersWrapper .filterMenu .dropdown-content a:focus {
  background: none;
  outline: none;
}

.b-sidebar.yunoSidebar .onSurface {
  color: #201A19;
}

.b-sidebar.yunoSidebar .onSurfaceVariant {
  color: #534342;
}

.b-sidebar.yunoSidebar .greenColor {
  color: #25d366;
}

.b-sidebar.yunoSidebar .primaryColor {
  color: #A81E22;
}

.b-sidebar.yunoSidebar .sidebar-content {
  width: 100%;
  max-width: 1140px;
  background-color: #fff;
}

.b-sidebar.yunoSidebar .headline1 {
  font-size: 48px;
  line-height: 52px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .headline1.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .headline1.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .headline1.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .headline1.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .headline1.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .headline1.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .headline2 {
  font-size: 40px;
  line-height: 44px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .headline2.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .headline2.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .headline2.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .headline2.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .headline2.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .headline2.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .headline3 {
  font-size: 32px;
  line-height: 36px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .headline3.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .headline3.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .headline3.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .headline3.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .headline3.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .headline3.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .headline4 {
  font-size: 28px;
  line-height: 32px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .headline4.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .headline4.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .headline4.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .headline4.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .headline4.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .headline4.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .headline5 {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .headline5.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .headline5.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .headline5.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .headline5.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .headline5.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .headline5.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .headline6 {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .headline6.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .headline6.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .headline6.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .headline6.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .headline6.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .headline6.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .subtitle1 {
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .subtitle1.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .subtitle1.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .subtitle1.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .subtitle1.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .subtitle1.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .subtitle1.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .subtitle2 {
  font-size: 14px;
  line-height: 18px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .subtitle2.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .subtitle2.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .subtitle2.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .subtitle2.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .subtitle2.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .subtitle2.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .caption1 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .caption1.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .caption1.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .caption1.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .caption1.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .caption1.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .caption1.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .overline {
  font-size: 10px;
  line-height: 14px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar.yunoSidebar .overline.bolder {
  font-weight: 700;
}

.b-sidebar.yunoSidebar .overline.noBold {
  font-weight: 400;
}

.b-sidebar.yunoSidebar .overline.alignC {
  text-align: center;
}

.b-sidebar.yunoSidebar .overline.uppercase {
  text-transform: uppercase;
}

.b-sidebar.yunoSidebar .overline.capitalize {
  text-transform: capitalize;
}

.b-sidebar.yunoSidebar .overline.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.b-sidebar.yunoSidebar .makeGrid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.b-sidebar.yunoSidebar .makeGrid.hasPadding {
  padding: 15px;
}

.b-sidebar.yunoSidebar .makeGrid.isWrap {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.b-sidebar.yunoSidebar .makeGrid.vAlignCenter {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.b-sidebar.yunoSidebar .makeGrid.spaceBetween {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.b-sidebar.yunoSidebar .makeGrid.skeletonWidthAuto .b-skeleton {
  width: auto;
  margin-left: 3px;
}

.b-sidebar.yunoSidebar .makeGrid .roundImg16 img {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 0;
}

.b-sidebar.yunoSidebar .makeGrid .roundImg48 img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: 0;
}

.b-sidebar.yunoSidebar .makeGrid .width50 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50%;
          flex: 0 0 50%;
}

.b-sidebar.yunoSidebar .makeGrid .width70 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 70%;
          flex: 0 0 70%;
}

.b-sidebar.yunoSidebar .makeGrid .width100 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

.b-sidebar.yunoSidebar .makeGrid .width120 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .makeGrid .width120 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 120px;
            flex: 0 0 120px;
  }
}

.b-sidebar.yunoSidebar .makeGrid .calc140 {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 100%;
          flex: 0 0 100%;
}

@media (min-width: 768px) {
  .b-sidebar.yunoSidebar .makeGrid .calc140 {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 calc(100% - 140px);
            flex: 0 0 calc(100% - 140px);
  }
}

.b-sidebar.yunoSidebar .makeGrid.withPipe li:not(:last-child):after {
  content: "|";
  margin: 0 15px;
  color: #E6E6E6;
}

.b-sidebar.yunoSidebar .makeGrid .mediaImg img {
  width: 100%;
  height: auto;
}

.b-sidebar.yunoSidebar .m-top-largest-times-1 {
  margin-top: 30px;
}

.b-sidebar.yunoSidebar .m-right-largest-times-1 {
  margin-right: 30px;
}

.b-sidebar.yunoSidebar .m-bottom-largest-times-1 {
  margin-bottom: 30px;
}

.b-sidebar.yunoSidebar .m-left-largest-times-1 {
  margin-left: 30px;
}

.b-sidebar.yunoSidebar .m-top-largest-times-2 {
  margin-top: 60px;
}

.b-sidebar.yunoSidebar .m-right-largest-times-2 {
  margin-right: 60px;
}

.b-sidebar.yunoSidebar .m-bottom-largest-times-2 {
  margin-bottom: 60px;
}

.b-sidebar.yunoSidebar .m-left-largest-times-2 {
  margin-left: 60px;
}

.b-sidebar.yunoSidebar .m-top-largest-times-3 {
  margin-top: 90px;
}

.b-sidebar.yunoSidebar .m-right-largest-times-3 {
  margin-right: 90px;
}

.b-sidebar.yunoSidebar .m-bottom-largest-times-3 {
  margin-bottom: 90px;
}

.b-sidebar.yunoSidebar .m-left-largest-times-3 {
  margin-left: 90px;
}

.b-sidebar.yunoSidebar .m-top-largest-times-4 {
  margin-top: 120px;
}

.b-sidebar.yunoSidebar .m-right-largest-times-4 {
  margin-right: 120px;
}

.b-sidebar.yunoSidebar .m-bottom-largest-times-4 {
  margin-bottom: 120px;
}

.b-sidebar.yunoSidebar .m-left-largest-times-4 {
  margin-left: 120px;
}

.b-sidebar.yunoSidebar .m-top-largest-times-5 {
  margin-top: 150px;
}

.b-sidebar.yunoSidebar .m-right-largest-times-5 {
  margin-right: 150px;
}

.b-sidebar.yunoSidebar .m-bottom-largest-times-5 {
  margin-bottom: 150px;
}

.b-sidebar.yunoSidebar .m-left-largest-times-5 {
  margin-left: 150px;
}

.b-sidebar.yunoSidebar .m-top-larger-times-1 {
  margin-top: 15px;
}

.b-sidebar.yunoSidebar .m-right-larger-times-1 {
  margin-right: 15px;
}

.b-sidebar.yunoSidebar .m-bottom-larger-times-1 {
  margin-bottom: 15px;
}

.b-sidebar.yunoSidebar .m-left-larger-times-1 {
  margin-left: 15px;
}

.b-sidebar.yunoSidebar .m-top-larger-times-2 {
  margin-top: 30px;
}

.b-sidebar.yunoSidebar .m-right-larger-times-2 {
  margin-right: 30px;
}

.b-sidebar.yunoSidebar .m-bottom-larger-times-2 {
  margin-bottom: 30px;
}

.b-sidebar.yunoSidebar .m-left-larger-times-2 {
  margin-left: 30px;
}

.b-sidebar.yunoSidebar .m-top-larger-times-3 {
  margin-top: 45px;
}

.b-sidebar.yunoSidebar .m-right-larger-times-3 {
  margin-right: 45px;
}

.b-sidebar.yunoSidebar .m-bottom-larger-times-3 {
  margin-bottom: 45px;
}

.b-sidebar.yunoSidebar .m-left-larger-times-3 {
  margin-left: 45px;
}

.b-sidebar.yunoSidebar .m-top-larger-times-4 {
  margin-top: 60px;
}

.b-sidebar.yunoSidebar .m-right-larger-times-4 {
  margin-right: 60px;
}

.b-sidebar.yunoSidebar .m-bottom-larger-times-4 {
  margin-bottom: 60px;
}

.b-sidebar.yunoSidebar .m-left-larger-times-4 {
  margin-left: 60px;
}

.b-sidebar.yunoSidebar .m-top-larger-times-5 {
  margin-top: 75px;
}

.b-sidebar.yunoSidebar .m-right-larger-times-5 {
  margin-right: 75px;
}

.b-sidebar.yunoSidebar .m-bottom-larger-times-5 {
  margin-bottom: 75px;
}

.b-sidebar.yunoSidebar .m-left-larger-times-5 {
  margin-left: 75px;
}

.b-sidebar.yunoSidebar .m-top-large-times-1 {
  margin-top: 10px;
}

.b-sidebar.yunoSidebar .m-right-large-times-1 {
  margin-right: 10px;
}

.b-sidebar.yunoSidebar .m-bottom-large-times-1 {
  margin-bottom: 10px;
}

.b-sidebar.yunoSidebar .m-left-large-times-1 {
  margin-left: 10px;
}

.b-sidebar.yunoSidebar .m-top-large-times-2 {
  margin-top: 20px;
}

.b-sidebar.yunoSidebar .m-right-large-times-2 {
  margin-right: 20px;
}

.b-sidebar.yunoSidebar .m-bottom-large-times-2 {
  margin-bottom: 20px;
}

.b-sidebar.yunoSidebar .m-left-large-times-2 {
  margin-left: 20px;
}

.b-sidebar.yunoSidebar .m-top-large-times-3 {
  margin-top: 30px;
}

.b-sidebar.yunoSidebar .m-right-large-times-3 {
  margin-right: 30px;
}

.b-sidebar.yunoSidebar .m-bottom-large-times-3 {
  margin-bottom: 30px;
}

.b-sidebar.yunoSidebar .m-left-large-times-3 {
  margin-left: 30px;
}

.b-sidebar.yunoSidebar .m-top-large-times-4 {
  margin-top: 40px;
}

.b-sidebar.yunoSidebar .m-right-large-times-4 {
  margin-right: 40px;
}

.b-sidebar.yunoSidebar .m-bottom-large-times-4 {
  margin-bottom: 40px;
}

.b-sidebar.yunoSidebar .m-left-large-times-4 {
  margin-left: 40px;
}

.b-sidebar.yunoSidebar .m-top-large-times-5 {
  margin-top: 50px;
}

.b-sidebar.yunoSidebar .m-right-large-times-5 {
  margin-right: 50px;
}

.b-sidebar.yunoSidebar .m-bottom-large-times-5 {
  margin-bottom: 50px;
}

.b-sidebar.yunoSidebar .m-left-large-times-5 {
  margin-left: 50px;
}

.b-sidebar.yunoSidebar .m-top-small-times-1 {
  margin-top: 5px;
}

.b-sidebar.yunoSidebar .m-right-small-times-1 {
  margin-right: 5px;
}

.b-sidebar.yunoSidebar .m-bottom-small-times-1 {
  margin-bottom: 5px;
}

.b-sidebar.yunoSidebar .m-left-small-times-1 {
  margin-left: 5px;
}

.b-sidebar.yunoSidebar .m-top-small-times-2 {
  margin-top: 10px;
}

.b-sidebar.yunoSidebar .m-right-small-times-2 {
  margin-right: 10px;
}

.b-sidebar.yunoSidebar .m-bottom-small-times-2 {
  margin-bottom: 10px;
}

.b-sidebar.yunoSidebar .m-left-small-times-2 {
  margin-left: 10px;
}

.b-sidebar.yunoSidebar .m-top-small-times-3 {
  margin-top: 15px;
}

.b-sidebar.yunoSidebar .m-right-small-times-3 {
  margin-right: 15px;
}

.b-sidebar.yunoSidebar .m-bottom-small-times-3 {
  margin-bottom: 15px;
}

.b-sidebar.yunoSidebar .m-left-small-times-3 {
  margin-left: 15px;
}

.b-sidebar.yunoSidebar .m-top-small-times-4 {
  margin-top: 20px;
}

.b-sidebar.yunoSidebar .m-right-small-times-4 {
  margin-right: 20px;
}

.b-sidebar.yunoSidebar .m-bottom-small-times-4 {
  margin-bottom: 20px;
}

.b-sidebar.yunoSidebar .m-left-small-times-4 {
  margin-left: 20px;
}

.b-sidebar.yunoSidebar .m-top-small-times-5 {
  margin-top: 25px;
}

.b-sidebar.yunoSidebar .m-right-small-times-5 {
  margin-right: 25px;
}

.b-sidebar.yunoSidebar .m-bottom-small-times-5 {
  margin-bottom: 25px;
}

.b-sidebar.yunoSidebar .m-left-small-times-5 {
  margin-left: 25px;
}

.b-sidebar .yunoTable {
  margin-top: 30px;
}

.b-sidebar .yunoTable .smallLoader {
  font-size: 8px;
}

.b-sidebar .yunoTable .table-mobile-sort {
  display: none;
}

.b-sidebar .yunoTable .progress-wrapper .progress-value {
  color: rgba(0, 0, 0, 0.7);
}

.b-sidebar .yunoTable .progress-wrapper .progress {
  border: 1px solid #fff;
}

.b-sidebar .yunoTable .progress-wrapper .progress.is-orange::-webkit-progress-value {
  background: #fc9927;
}

.b-sidebar .yunoTable .progress-wrapper .progress.is-red::-webkit-progress-value {
  background: #ca0813;
}

.b-sidebar .yunoTable .progress-wrapper .progress.is-yellow::-webkit-progress-value {
  background: #f0c042;
}

.b-sidebar .yunoTable .progress-wrapper .progress.is-lightGreen::-webkit-progress-value {
  background: #669d4f;
}

.b-sidebar .yunoTable .progress-wrapper .progress.is-darkGreen::-webkit-progress-value {
  background: #356b21;
}

.b-sidebar .yunoTable .ctaGroup .button.yunoPrimaryCTA {
  border: 1px solid;
  background-color: #fff;
  border-color: rgba(0, 0, 0, 0.2);
  color: #000;
}

.b-sidebar .yunoTable .tag.is-success {
  background-color: #54ac75;
}

.b-sidebar .yunoTable .tag.is-info {
  background-color: #136bcd;
}

.b-sidebar .yunoTable .tag.noText {
  text-indent: -9999px;
  width: 100%;
}

.b-sidebar .yunoTable .tag.is-light {
  background-color: rgba(0, 0, 0, 0.2);
}

.b-sidebar .yunoTable .tag.noBG {
  color: rgba(0, 0, 0, 0.4);
  background: none;
}

.b-sidebar .yunoTable.container-fluid {
  padding-left: 0;
  padding-right: 0;
}

.b-sidebar .yunoTable .clearFilters {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  margin-bottom: 0;
  margin-top: 15px;
}

.b-sidebar .yunoTable .clearFilters .tags:not(:last-child) {
  margin: 0 10px 0 0;
}

.b-sidebar .yunoTable .b-table.tableInvisible {
  visibility: hidden;
}

.b-sidebar .yunoTable .b-table .pagination {
  margin-top: 0;
}

.b-sidebar .yunoTable .b-table .pagination .pagination-ellipsis {
  text-indent: -9999px;
}

.b-sidebar .yunoTable .b-table .pagination .pagination-ellipsis:before {
  content: "...";
  text-indent: 0;
}

.b-sidebar .yunoTable .b-table .listHeading {
  font-weight: 700;
  font-size: 18px;
  margin-bottom: 15px;
}

.b-sidebar .yunoTable .b-table .detailList {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .detailList {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    margin: 0 -15px;
  }
}

.b-sidebar .yunoTable .b-table .detailList li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 15px;
}

.b-sidebar .yunoTable .b-table .detailList li:last-child {
  margin-bottom: 0;
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .detailList li {
    padding: 0 15px;
  }
  .b-sidebar .yunoTable .b-table .detailList li:last-child {
    margin-bottom: 15px;
  }
}

.b-sidebar .yunoTable .b-table .detailList li .listImage {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  height: 40px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.1);
  border: 2px solid;
  border-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  margin-right: 10px;
}

.b-sidebar .yunoTable .b-table .detailList li .listImage img {
  width: 36px;
  height: 36px;
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
}

.b-sidebar .yunoTable .b-table .detailList li .listTitle {
  font-size: 14px;
}

.b-sidebar .yunoTable .b-table .detailList li .listInfo small {
  font-size: 11px;
}

.b-sidebar .yunoTable .b-table.scrollable .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td.chevron-cell .mdi-chevron-right:before {
  content: "\f054";
}

.b-sidebar .yunoTable .b-table.is-loading tbody tr.is-empty td {
  padding: 10% 0;
}

.b-sidebar .yunoTable .b-table.is-loading tbody tr.is-empty td:before {
  display: none;
}

.b-sidebar .yunoTable .b-table .table.is-striped tbody tr:not(.is-selected):nth-child(even) {
  background-color: rgba(0, 47, 90, 0.03);
}

.b-sidebar .yunoTable .b-table .has-addons button.button {
  height: 2.25em;
  background-color: #002F5A;
}

.b-sidebar .yunoTable .b-table .has-addons button.button .icon .mdi-arrow-up:after {
  content: "\f0de";
  position: relative;
  top: 2px;
}

.b-sidebar .yunoTable .b-table .table-wrapper {
  overflow-y: auto;
}

@media screen and (max-width: 2048px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-sticky-header {
    height: auto !important;
    max-height: 950px;
  }
}

@media screen and (max-width: 1792px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-sticky-header {
    height: auto !important;
    max-height: 800px;
  }
}

@media screen and (max-width: 1680px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-sticky-header {
    height: auto !important;
    max-height: 750px;
  }
}

@media screen and (max-width: 1600px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-sticky-header {
    height: auto !important;
    max-height: 600px;
  }
}

@media screen and (max-width: 1440px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-sticky-header {
    height: auto !important;
    max-height: 575px;
  }
}

@media screen and (max-width: 1920px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-sticky-header {
    height: auto !important;
    max-height: 750px;
  }
}

@media (max-width: 1366px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-sticky-header {
    height: auto !important;
    max-height: 450px;
  }
}

@media (max-width: 767px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-sticky-header {
    height: auto !important;
    max-height: 542px !important;
  }
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: left;
  font-size: 14px;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .clipboard {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .clipboard {
    width: 140px;
  }
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .clipboard .fieldWrapper {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 85%;
          flex: 0 0 85%;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .clipboard.noField {
  position: relative;
}

@media (min-width: 992px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .clipboard.noField .fieldWrapper {
    position: absolute;
    overflow: hidden;
  }
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .clipboard.noField .fieldWrapper .control {
    position: absolute;
    left: -200px;
    top: 0;
  }
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .clipboard .trigger {
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal {
    width: 256px;
  }
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal .tag.colorGreen {
  background: #669d4f;
  color: #FFF;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal .tag.colorRed {
  background: #ca0813;
  color: #FFF;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.percentageBlock {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 130px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.percentageBlock .progress-wrapper {
  width: 50%;
  margin: 0;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.allowWrap {
  overflow: visible;
  white-space: normal;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.action, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.ctaGroup {
  visibility: hidden;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.noOverflow {
  overflow: visible;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.arrayList .item, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item {
  display: inline-block;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.arrayList .item:after, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item:after {
  content: ",";
  padding-right: 5px;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.arrayList .item:last-child:after, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item:last-child:after {
  display: none;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item {
  display: block;
  position: relative;
  padding-left: 10px;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .fa, .b-sidebar .yunoTable .b-table.scrollable .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td.chevron-cell .fieldVal.hierarchyList .item .mdi-chevron-right:before, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .has-addons button.button .icon .mdi-arrow-up:after, .b-sidebar .yunoTable .b-table .has-addons button.button .icon .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .mdi-arrow-up:after, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .item:before, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards thead tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item th.is-current-sort .icon .mdi-arrow-up:after, .b-sidebar .yunoTable .b-table thead .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item th.is-current-sort .icon .mdi-arrow-up:after, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards thead tr:not(.detail):not(.is-empty):not(.table-footer) th.is-current-sort .icon td .fieldVal.hierarchyList .item .mdi-arrow-up:after, .b-sidebar .yunoTable .b-table thead .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) th.is-current-sort .icon td .fieldVal.hierarchyList .item .mdi-arrow-up:after, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .pagination-link.pagination-previous .icon .mdi-chevron-left:after, .b-sidebar .yunoTable .b-table .pagination-link.pagination-previous .icon .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .mdi-chevron-left:after,
.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .pagination-link.pagination-previous .icon .mdi-chevron-right:after, .b-sidebar .yunoTable .b-table .pagination-link.pagination-previous .icon .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .mdi-chevron-right:after, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .pagination-link.pagination-next .icon .mdi-chevron-left:after, .b-sidebar .yunoTable .b-table .pagination-link.pagination-next .icon .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .mdi-chevron-left:after,
.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .pagination-link.pagination-next .icon .mdi-chevron-right:after, .b-sidebar .yunoTable .b-table .pagination-link.pagination-next .icon .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item .mdi-chevron-right:after {
  position: relative;
  right: -5px;
  top: 2px;
  font-size: 18px;
  cursor: pointer;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item:before {
  content: "\f105";
  position: absolute;
  left: 0;
  top: 4px;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.hierarchyList .item:after {
  display: none;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.slot {
  overflow: visible;
  position: relative;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.slot .tag {
  width: 100%;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.slot:before {
  content: "";
  width: calc(100% + 18px);
  height: 1px;
  background-color: #fff;
  position: absolute;
  left: -8px;
  top: -8px;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.slot:after {
  content: "";
  width: calc(100% + 18px);
  height: 1px;
  background-color: #fff;
  position: absolute;
  left: -8px;
  bottom: -8px;
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.paymentLink {
    width: 110px;
    text-transform: uppercase;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.sun, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.mon, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.tue, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.wed, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.thu, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.fri, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.sat {
    width: 52px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.start {
    width: 108px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.class_attended {
    width: 121px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.class_time_v2 {
    width: auto;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.class_time {
    width: 94px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.group_type {
    width: 100px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.email {
    width: 200px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.enrollment_type {
    width: 142px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.ends_in {
    width: 132px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.class_duration {
    width: 130px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.category {
    width: 170px;
    overflow: visible;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.end_date {
    width: 82px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.duration {
    width: 110px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.enrolled_end {
    width: 128px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.enrolled_days_left {
    width: 85px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.crm_contact_id {
    width: 154px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.product_code {
    width: 328px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.days {
    width: 210px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.title {
    width: 300px;
    font-size: 14px;
    font-weight: 400;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.days, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.category {
    white-space: normal;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.grid_date {
    width: 166px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.start_date {
    width: 105px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.active_enrollments {
    width: 161px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.attendance_count {
    width: 155px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.time {
    width: 104px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.related_courses {
    width: 328px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.enrollment_count {
    width: 150px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.link_status {
    width: 106px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.link_status {
    width: 106px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.link_amount {
    width: 105px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.payment_link {
    width: 124px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.created_on {
    width: 126px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.course_name {
    width: auto;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.total_amount {
    width: 110px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.instructor_name {
    width: 142px;
    white-space: normal;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.enrollment_status {
    width: 138px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.payment_method {
    width: 142px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.amount_received {
    width: 136px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.amount_pending {
    width: 131px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.batch_name {
    width: 79px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.enrolled_on {
    width: 126px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.counselor_name {
    width: 145px;
    white-space: normal;
  }
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.payment_status.green {
  color: green;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.payment_status.green a {
  color: green;
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.payment_status {
    width: 163px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.commonCol {
    width: 150px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.commonCol-2 {
    width: 160px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.commonCol-3 {
    width: 153px;
  }
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.fixedWidth {
    width: 256px;
  }
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.noOverflow {
  overflow: visible;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.ctaGroup {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.ctaGroup .button,
.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.ctaGroup .b-tooltip {
  margin-right: 10px;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.ctaGroup .button:last-child,
.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal.ctaGroup .b-tooltip:last-child {
  margin-right: 0;
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer) td .fieldVal {
    width: auto;
  }
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer):hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer):hover td .fieldVal.action, .b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer):hover td .fieldVal.ctaGroup {
  visibility: visible;
}

.b-sidebar .yunoTable .b-table .table-wrapper.has-mobile-cards thead tr:not(.detail):not(.is-empty):not(.table-footer):hover {
  background-color: #fff;
}

.b-sidebar .yunoTable .b-table .top.level {
  display: none;
}

.b-sidebar .yunoTable .b-table thead tr th {
  border: 0;
  font-size: 14px;
}

.b-sidebar .yunoTable .b-table thead tr th.is-current-sort .icon .mdi-arrow-up:after {
  content: "\f077";
  position: relative;
  top: -1px;
}

.b-sidebar .yunoTable .b-table tbody tr .user {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 218px;
}

.b-sidebar .yunoTable .b-table tbody tr .user.hasTags {
  width: auto;
}

.b-sidebar .yunoTable .b-table tbody tr .user.hasTags .tagsWrapper {
  padding-top: 5px;
}

.b-sidebar .yunoTable .b-table tbody tr .user.hasTags .tagsWrapper .tag {
  margin-right: 5px;
  background-color: #edc264;
  color: #000;
}

.b-sidebar .yunoTable .b-table tbody tr .user.hasTags .tagsWrapper .tag:last-child {
  margin-right: 0;
}

.b-sidebar .yunoTable .b-table tbody tr .user .userImg {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
  width: 32px;
  height: 32px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin-right: 10px;
}

.b-sidebar .yunoTable .b-table tbody tr .user .userImg img {
  width: 32px;
  height: 32px;
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
}

.b-sidebar .yunoTable .b-table tbody tr .user .userName {
  text-transform: capitalize;
}

.b-sidebar .yunoTable .b-table tbody tr.is-empty td {
  position: relative;
  text-align: center;
}

.b-sidebar .yunoTable .b-table tbody tr.is-empty td:before {
  content: "No record found";
  display: block;
  padding: 10% 0;
}

.b-sidebar .yunoTable .b-table tbody tr:last-child td {
  border-bottom: 1px solid #E6E6E6;
}

.b-sidebar .yunoTable .b-table tbody tr td {
  border-right: 1px solid #E6E6E6;
  vertical-align: middle;
  font-size: 14px;
}

@media (min-width: 768px) {
  .b-sidebar .yunoTable .b-table tbody tr td {
    border: 0;
    border-top: 1px solid #E6E6E6;
    border-right: 0;
  }
}

.b-sidebar .yunoTable .b-table .pagination-link {
  color: #000;
}

.b-sidebar .yunoTable .b-table .pagination-link:hover {
  text-decoration: none;
}

.b-sidebar .yunoTable .b-table .pagination-link.is-current {
  background-color: #002F5A;
  border-color: #002F5A;
  color: #FFF;
}

.b-sidebar .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-left:after,
.b-sidebar .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, .b-sidebar .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-left:after,
.b-sidebar .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  content: "\f060";
}

.b-sidebar .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, .b-sidebar .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  content: "\f061";
}

.b-sidebar .emptyStateV2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 5%;
}

.b-sidebar .emptyStateV2 figure {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.b-sidebar .emptyStateV2 figure img {
  width: 158px;
  height: auto;
}

.b-sidebar .emptyStateV2 figure figcaption {
  margin-top: 15px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.b-sidebar .emptyStateV2 figure .button {
  margin-top: 15px;
}
/*# sourceMappingURL=courseSearchV2.css.map */