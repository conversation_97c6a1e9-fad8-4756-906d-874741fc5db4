<?php
class JwtAuthenticationActivities
{
    /**
     * Get JWT token of user
     */
    function get_jwt_token($user_id)
    {
        date_default_timezone_set('Asia/Calcutta');
        $user_data = get_userdata($user_id);
        $username = $user_data->user_login;
        $password = $user_data->user_email . '###987654';
        $user = wp_authenticate($username, $password);

        if (is_wp_error($user)) {
            return new WP_Error('invalid_credentials', 'The username or password is incorrect', array('status' => 401));
        }
        // $curl = curl_init();
        // curl_setopt_array($curl, array(
        //     CURLOPT_URL => site_url('/wp-json/jwt-auth/v1/token'),
        //     CURLOPT_RETURNTRANSFER => true,
        //     CURLOPT_ENCODING => '',
        //     CURLOPT_MAXREDIRS => 10,
        //     CURLOPT_TIMEOUT => 0,
        //     CURLOPT_FOLLOWLOCATION => true,
        //     CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        //     CURLOPT_CUSTOMREQUEST => 'POST',
        //     CURLOPT_POSTFIELDS => '{
        //     "username": "' . $username . '",
        //     "password": "' . $password . '"
        //     }',
        //     CURLOPT_HTTPHEADER => array(
        //         'Content-Type: application/json'
        //     ),
        // )
        // );
        // $response = curl_exec($curl);
        // curl_close($curl);
        // echo $response;
        $issuedAt = time();
        //$expirationTime = $issuedAt + (60 * 60);  // Token expires in 7 days
        $expirationTime = $issuedAt + (7 * 24 * 60 * 60);
        $payload = array(
            'iss' => get_bloginfo('url'),  // Issuer
            'iat' => $issuedAt,            // Issued at
            'exp' => $expirationTime,      // Expiration time
            'userId' => $user_id,
        );
      
        $secretKey = JWT_AUTH_SECRET_KEY;
        $jwt = \Firebase\JWT\JWT::encode($payload, $secretKey, 'HS256');
        $response = ["token" => $jwt, "user_email" => $user_data->user_email, "user_nicename" => $user_data->user_nicename,"user_display_name" => $user_data->display_name];
        echo $response;
    }

    /**
     * Validate JWT Token
     */
    function validate_jwt_token($authToken)
    {
        date_default_timezone_set('Asia/Calcutta');
        try {
            $secretKey = JWT_AUTH_SECRET_KEY; // The same secret key as used in token generation
            $decoded = \Firebase\JWT\JWT::decode($authToken, new \Firebase\JWT\Key($secretKey, 'HS256'));
            $response = ["code" => "jwt_auth_valid_token","data" => ["status" => 200]];
            return $response;
        } catch (\Firebase\JWT\ExpiredException $e) {
            $response = ["code" => "jwt_auth_invalid_token","message" => "Expired token", "data" => ["status" => 403]];
            return $response;
            //jwt_auth_token_expired
        } catch (\Exception $e) {
            // Catches other exceptions, such as invalid tokens
            //return new WP_Error('jwt_auth_invalid_token', 'Signature verification failed', array('status' => 403));
            $response = ["code" => "jwt_auth_invalid_token","message" => "Signature verification failed", "data" => ["status" => 403]];
            return $response;
        }
        // if ($result['code'] == "jwt_auth_invalid_token") {
        //     //return new WP_Error($result); 
        //     return false;
        // } else {
        //     return true;
        // }
    }

    /**
     * Generate or regenerate JWT token for user
     */
    function create_jwt_token($user_id)
    {
        date_default_timezone_set('Asia/Calcutta');
        $user_data = get_userdata($user_id);
        $username = $user_data->user_login;
        $password = $user_data->user_email . '###987654';
        $user = wp_authenticate($username, $password);

        if (is_wp_error($user)) {
            return new WP_Error('invalid_credentials', 'The username or password is incorrect', array('status' => 401));
        }
        //     $curl = curl_init();
        //     error_log("create jwt token step 1: " . date('Y-m-d H:i:s') . " === " . $username . " === " . $password . "\n\n", 3, ABSPATH . 'error-logs/jwt.log');
        //     curl_setopt_array($curl, array(
        //         CURLOPT_URL => site_url('wp-json/jwt-auth/v1/token'),
        //         CURLOPT_RETURNTRANSFER => true,
        //         CURLOPT_ENCODING => '',
        //         CURLOPT_MAXREDIRS => 10,
        //         CURLOPT_TIMEOUT => 0,
        //         CURLOPT_FOLLOWLOCATION => true,
        //         CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        //         CURLOPT_CUSTOMREQUEST => 'POST',
        //         CURLOPT_POSTFIELDS => '{
        //     "username": "' . $username . '",
        //     "password": "' . $password . '"
        // }',
        //         CURLOPT_HTTPHEADER => array(
        //             'Content-Type: application/json'
        //         ),
        //     )
        //     );

        //     $response = curl_exec($curl);
        //     error_log("create jwt token: " . date('Y-m-d H:i:s') . " === " . json_encode($response) . "\n\n", 3, ABSPATH . 'error-logs/jwt.log');
        //     curl_close($curl);
        //     $response_data = json_decode($response, true);

        $issuedAt = time();
        //$expirationTime = $issuedAt + (60 * 60);  // Token expires in 7 days
        $expirationTime = $issuedAt + (7 * 24 * 60 * 60); 
        $payload = array(
            'iss' => get_bloginfo('url'),  // Issuer
            'iat' => $issuedAt,            // Issued at
            'exp' => $expirationTime,      // Expiration time
            'userId' => $user_id,
        );
      
        $secretKey = JWT_AUTH_SECRET_KEY;
        $jwt = \Firebase\JWT\JWT::encode($payload, $secretKey, 'HS256');
        $response_data = ["token" => $jwt, "user_email" => $user_data->user_email, "user_nicename" => $user_data->user_nicename,"user_display_name" => $user_data->display_name];
        
        if (!empty($response_data['token'])) {
            // todo: add token into user meta
            update_user_meta($user_id, 'CURRENT_USER_JWT_TOKEN', $jwt);
            date_default_timezone_set('Asia/Calcutta');
            //setcookie("CURRENT_USER_TOKEN", $jwt, time() + 3600, "/");
            setcookie("CURRENT_USER_TOKEN", $jwt, time() + (86400 * 30), "/");
        } else {
            return false;
        }
    }

    /**
     * Change the token's expire value.
     *
     * @param int $expire The default "exp" value in timestamp.
     * @param int $issued_at The "iat" value in timestamp.
     *
     * @return int The "nbf" value.
     */
    function jwt_auth_expire_time_set ($expire, $issued_at) {
            date_default_timezone_set('Asia/Calcutta');
            // Modify the "expire" here.
            $expire = strtotime("+7 days");
            //$expire = strtotime("180 seconds");
            return $expire;
    }

}