Vue.component("yuno-classCard-v2", {
    props: {
        data: {
            type: Object,
            required: true,
        },
    },
    template: `
		<div :id="data.id" class="cardSection p-bottom-larger-times-1" @click="openDrawer(data)">
			<template v-if="!data.hasNoClass">
				<template v-if="userRole.data == 'Learner'">
					<yuno-learner-card
						:data="data"
					>
					</yuno-learner-card>
				</template>
				<template v-else-if="userRole.data == 'Instructor'">
					<yuno-instructor-card
						:data="data"
					>
					</yuno-instructor-card>
				</template>
			</template>
			<template v-else>
				<span class="subtitle2 noBold hasNoClass onSurfaceVariant">No Class</span>
			</template>
			<b-sidebar
				type="is-light"
				:fullheight="fullheight"
				:fullwidth="fullwidth"
				:overlay="overlay"
				:right="right"
				v-model="open"
				class="card-slidebar"
				:class="{ extraWide: data?.temporal_status == 'past' }"
			>
				<yuno-classCard-drawer
					v-if="drawerData && drawerData.type"
					:data="drawerData"
					@closeSidebar="closeSidebar"
				>
				</yuno-classCard-drawer>
			</b-sidebar>
		</div>
    `,
    computed: {
        ...Vuex.mapState(["userInfo", "userRole", "drawer", "videoList"]),
    },
    data() {
        return {
            open: false,
            drawerData: null,
            snackbarData: null,
            fullheight: true,
            fullwidth: true,
            overlay: true,
            right: true,
        };
    },

    mounted() {},

    methods: {
        openDrawer(data) {
            if (data.hasNoClass) return;
            this.open = true;
            this.drawerData = data;
            if (data?.temporal_status === "past" && data?.recording?.url != "") {
                this.getVideoEmbed(data.recording?.id);
            }
            //   this.fetchClassDetail(data);
        },
        getVideoEmbed(data) {
            this.videoList.data = [];
            this.videoList.loading = false;
            this.videoList.error = null;
            this.fetchVideo(data);
        },
        gotVideo(options) {
            this.videoList.loading = false;
            if (
                options.response !== undefined &&
                options.response.data !== undefined &&
                options.response.data.code === 200
            ) {
                const response = options.response.data.data;

                response.default = 0;
                this.videoList.data = response;
                if (!this.videoList.data.videoEmbed) {
                    this.videoList.data.videoEmbed = "";
                }
                this.videoList.data.videoEmbed = ""
                this.videoList.data.videoEmbed = response.videos[response.default].embed;
            }
        },
        fetchVideo(data) {
            // Create the object structure you want
            const videoObj = {
                id: [data], // Wrap the ID in an array
            };
            this.videoList.loading = true;
            const videoID = "?videosid=" + encodeURI(JSON.stringify(videoObj));
            const instance = this;
            const options = {
                apiURL: YUNOCommon.config.videoListAPI(videoID),
                module: "gotData",
                store: "videoList",
                addToModule: false,
                callback: true,
                callbackFunc: function(options) {
                    return instance.gotVideo(options);
                },
            };

            this.$store.dispatch("fetchData", options);
        },
        gotClassDetail(options) {
            this.drawer.loading = false;
            const response = options?.response?.data;

            if (response?.code === 201) {
                // this.showToastMessage(response.message);
                this.drawer.data = response.data;
            } else if (response?.message) {
                // this.showToastMessage(response.message);
            }
        },
        fetchClassDetail(data) {
            this.drawer.loading = true;
            this.drawer.data = [];
            const prop = {
                classID: data.id,
            };
            const options = {
                apiURL: YUNOCommon.config.learner(
                    "getClassDetail",
                    false,
                    false,
                    false,
                    prop
                ),
                module: "gotData",
                store: "drawer",
                callback: true,
                callbackFunc: (options) => this.gotClassDetail(options),
            };
            this.$store.dispatch("fetchData", options);
        },
        openScheduleModal(data) {
            this.drawer.data = [];
            this.drawer.data = data;
            this.drawer.isActive = true;
        },

        closeSidebar() {
            this.open = false;
        },
        redirectToPage(targetUrl) {
            window.location.href = targetUrl;
        },
        formattedAcademyName(academy) {
            return academy
                .split("-") // Convert into array by splitting at '-'
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize each word
                .join(" "); // Join back as a string with spaces
        },
        formatedSchedule(data) {
            if (data?.temporal_status == "past") {
                return this.formatDuration(
                    data.actual.start.time,
                    data.actual.end.time
                );
            } else {
                return this.formatDuration(
                    data.scheduled.start.time,
                    data.scheduled.end.time
                );
            }
        },
        formatDuration(start, end) {
            const startTime = new Date(start);
            const endTime = new Date(end);
            return `${startTime.toLocaleTimeString([], {
				hour: "2-digit",
				minute: "2-digit",
				})} - ${endTime.toLocaleTimeString([], {
				hour: "2-digit",
				minute: "2-digit",
			})}`;
        },
        updateClassCardRating(newRating) {
            if (this.snackbarData) {
                this.snackbarData.rating = newRating; // Update the rating in the class card data
            }
        },
        openSnackbar(data) {
            this.snackbarData = data;
            this.$refs.snackbar.syncRating(this.data.rating); // Sync the current rating
            this.setRating();
        },

        setRating() {
            let rating = this.data.rating;
            if (rating === 5) {
                this.$refs.snackbar.setRating(rating);
            } else {
                this.$refs.snackbar.syncRating(rating);
                this.$refs.snackbar.setRating(rating);
                this.$refs.snackbar.openSnackbar();
                this.$refs.snackbar.fetchIssues();
            }
        },
    },
});