<?php
//Batch Controller API's
return [
    "/batches" => [
        "controller" => "BatchController",
        "methods" => [
            "POST" => ["callback" => "addBatch", "args" => [], "auth" => false]
        ]
    ],
    "/batches/(?P<batchId>\d+)" => [
        "controller" => "BatchController",
        "methods" => [
            "PUT" => ["callback" => "updBatch", "args" => [], "auth" => false],
            "GET" => ["callback" => "getBatch", "args" => [], "auth" => false]
        ]
    ],
    "/batches/(?P<viewType>list|grid)" => [
        "controller" => "BatchController",
        "methods" => [
            "GET" => ["callback" => "getBatches", "args" => [], "auth" => false]
        ]
    ],
    "/batches/filters" => [
        "controller" => "BatchController",
        "methods" => [
            "GET" => ["callback" => "getBatchesFilters", "args" => [], "auth" => false]
        ]
    ],
    "/batches/end/(?P<batchId>\d+)" => [
        "controller" => "BatchController",
        "methods" => [
            "PUT" => ["callback" => "endBatch", "args" => [], "auth" => false]
        ]
    ]
];
