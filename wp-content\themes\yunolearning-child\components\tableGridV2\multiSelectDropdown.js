Vue.component('yuno-multi-select-dropdown', {
    props: ["data", "options", "defaultFilters"],
    template: `
        <div class="yunoDropdown multiSelect" :class="[data.filter]">
            <b-dropdown 
                v-model="selectedOption" 
                aria-role="list"
                :mobile-modal="false"
                :disabled="data.is_disabled"
                :multiple="true"
                @change="onChange"
            >
                <template #trigger>
                    <div class="labelWrapper">
                        <span class="placeHolder" v-if="false">{{ data.placeholder }}</span>
                        <span class="selectedItem hasGrid">
                            <template v-if="!selectedOption.length || (selectedOption.length === 1 && selectedOption[0].slug === 'all')">
                                {{ data.placeholder }}
                            </template>
                            <template v-else>
                                <template v-for="(selected, i) in selectedOption.filter(s => s.slug !== 'all')">
                                    <div class="item" :key="i">
                                        <span>
                                            {{ selected.label }}
                                        </span>
                                        <a 
                                            href="#" 
                                            class="clearFilter" 
                                            @click.stop.prevent="clearFilter(selected)"
                                        >
                                            <span class="material-icons">cancel</span>
                                        </a>
                                    </div>
                                </template>
                            </template>
                        </span>
                        <span class="material-icons icon">expand_more</span>
                    </div>
                </template>
                <template v-for="(option, i) in data.items.filter(item => item.slug !== 'all')">
                    <b-dropdown-item 
                        :value="option" 
                        aria-role="listitem"
                        :class="{ 'is-active': option.is_checked }"
                    >
                        {{ option.label }}
                    </b-dropdown-item>
                </template>
            </b-dropdown>
        </div>
    `,
    data() {
        return {
            selectedOption: [],
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
        ]),
    },
    created() {
        const payload = this.$props.options.payload;
        const filterKey = this.data.filter;
        let selectedSlugs = payload[filterKey] || [];
        
        if (selectedSlugs.length === 0 || (selectedSlugs.length > 1 && selectedSlugs.includes('all'))) {
            const hasAllOption = this.data.items.some(i => i.slug === 'all');
            if (hasAllOption && !(selectedSlugs.length === 1 && selectedSlugs[0] !== 'all')) {
                payload[filterKey] = ['all'];
                selectedSlugs = ['all'];
            }
        }
        
        this.selectedOption = this.data.items.filter(i => selectedSlugs.includes(i.slug));
        this.updateItemsCheckedState();
        this.data.selected = this.selectedOption;
    },
    methods: {
        updateItemsCheckedState() {
            this.data.items.forEach(item => {
                item.is_checked = this.selectedOption.some(s => s.slug === item.slug);
            });
        },
        clearFilter(selected) {
            const newSelection = this.selectedOption.filter(s => s.slug !== selected.slug);
            this.onChange(newSelection);
        },
        onChange(selection) {
            const wasAllSelected = this.selectedOption.some(o => o.slug === 'all');
            let newSelection = [...selection];
            const isAllSelectedNow = newSelection.some(o => o.slug === 'all');

            if (isAllSelectedNow && !wasAllSelected) {
                newSelection = newSelection.filter(o => o.slug === 'all');
            } else if (isAllSelectedNow && newSelection.length > 1) {
                newSelection = newSelection.filter(o => o.slug !== 'all');
            }
            
            if (newSelection.length === 0) {
                const allOption = this.data.items.find(o => o.slug === 'all');
                if (allOption) newSelection = [allOption];
            }

            this.selectedOption = newSelection;
            const payload = this.$props.options.payload;
            const filterKey = this.data.filter;
            payload[filterKey] = this.selectedOption.map(o => o.slug);
            this.data.selected = this.selectedOption;
            this.updateItemsCheckedState();
            this.$emit("onDropdownChange", this.selectedOption, this.data);
        }
    }
});