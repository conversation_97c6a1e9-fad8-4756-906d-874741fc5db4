<?php
// Map Controller API's for Google Maps integration
return [
    "/maps/geocode" => [
        "controller" => "MapController",
        "methods" => [
            "GET" => [
                "callback" => "getLocationFromCoordinates",
                "args" => ["lat", "lng"],
                "auth" => false
            ]
        ]
    ],
    "/maps/places/autocomplete" => [
        "controller" => "MapController",
        "methods" => [
            "GET" => [
                "callback" => "getPlaceSuggestions",
                "args" => ["input"],
                "auth" => false
            ]
        ]
    ],
    "/maps/places/details" => [
        "controller" => "MapController",
        "methods" => [
            "GET" => [
                "callback" => "getPlaceDetails",
                "args" => ["place_id"],
                "auth" => false
            ]
        ]
    ]
]; 