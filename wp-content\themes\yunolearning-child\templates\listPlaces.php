
<?php 
	/**
		* Template Name: Yuno List Places
		* Template Post Type: page
		*
		* @package WordPress
		* @subpackage Yuno
	*/

    $page = "listPlaces";

	add_action('wp_enqueue_scripts', 'my_custom_template_dequeue', 100);
    
    function my_custom_template_dequeue() {
        wp_dequeue_script('yunoJquery');
        wp_dequeue_script('yunoCommonJS');
    };

	function enqueue_styles() {
        global $page;
		wp_enqueue_style('buefy', 'https://cdn.jsdelivr.net/npm/buefy@0.9.25/dist/buefy.min.css', array(), false, 'all');
        wp_enqueue_style('yunoPage', get_stylesheet_directory_uri() . '/pages/'. $page .'/dist/'. $page .'.min.css', array(), false, 'all');		
	}

    function enqueue_scripts() {
        global $page;
		wp_enqueue_script('jquerys', 'https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js', array(), 3.7, true);
        wp_enqueue_script('axios', get_stylesheet_directory_uri() . '/assets/js/lib/axios.min.js', array(), 2.6, true);

        if (site_url() == YUNO_PROD_ENV_HTTPS) {
            wp_enqueue_script('vueDev', get_stylesheet_directory_uri() . '/assets/js/lib/vue.js', array(), 2.6, true);
            
        } else {
            wp_enqueue_script('vueDev', get_stylesheet_directory_uri() . '/assets/js/lib/vue.dev.min.js', array(), 2.6, true);
        }
        wp_enqueue_script('vuex', get_stylesheet_directory_uri() . '/assets/js/lib/vuex.js', array(), 2.6, true);
        wp_enqueue_script('buefy', 'https://cdn.jsdelivr.net/npm/buefy@0.9.25/dist/buefy.min.js', array(), 2.14, true);
        wp_enqueue_script('veeValidate', get_stylesheet_directory_uri() . '/assets/js/lib/vee-validate.min.js#deferload', array(), 3.0, true);
        wp_enqueue_script('yunoPage', get_stylesheet_directory_uri() . '/pages/'. $page .'/dist/'. $page .'.min.js#deferload', array(), 1.0, true);
	}

	add_action( 'wp_enqueue_scripts', 'enqueue_styles' ); // default priority: 10
	add_action( 'wp_enqueue_scripts', 'enqueue_scripts', 9); // default priority: 10

	get_header();
?>

    <yuno-list-places></yuno-list-places>
	
<?php get_footer();?>
