Vue.component('yuno-checkbox', {
    props: {
        data: {
            type: Object,
            required: true
        },
        defaultFilters: {
            type: Object,
            required: true
        },
        payload: {
            type: Object,
            required: true
        }
    },
    template: `
        <div class="yunoCheckbox" :class="[data.filter]">
            <b-field>
                <b-checkbox
                    :disabled="data.is_disabled"
                    v-model="data.selected"
                    @input="onChange">
                    {{ data.placeholder }}
                </b-checkbox>
            </b-field>
        </div>
    `,
    data() {
        return {
            selectedOption: ""
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'filterResult'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onChange(value) {
            this.payload[this.data.filter] = value;
            this.$emit('onCheckboxChange', value, this.data);
        }
    }
});