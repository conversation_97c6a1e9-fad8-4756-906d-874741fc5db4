.material-icons, #app .yunoPageHeader .orgSwitch .icon::after {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

#app .pageGrid .mainBody.noHeaderFooter {
  width: 100%;
  max-width: 100%;
}
#app .pageGrid.noLHSMenu .sidebarWrapper {
  display: none;
}
#app .pageGrid.noLHSMenu .mainBody {
  width: 100%;
  max-width: 100%;
}
#app .yunoPageHeader {
  border-bottom: 1px solid #E6E6E6;
  padding: 8px 8px;
  min-height: auto;
  display: flex;
  justify-content: space-between;
}
#app .yunoPageHeader .logo {
  margin-left: 32px;
}
#app .yunoPageHeader .logo img {
  width: auto;
  height: 31px;
}
@media (min-width: 768px) {
  #app .yunoPageHeader .logo {
    margin-left: 0;
  }
}
#app .yunoPageHeader .orgSwitchWrapper.is-active .orgSwitch .icon::after {
  content: "\e5c7";
}
#app .yunoPageHeader .orgSwitch {
  border: 1px solid #E6E6E6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #201A19;
  cursor: pointer;
  height: 36px;
  padding: 0 6px;
}
#app .yunoPageHeader .orgSwitch img {
  width: 24px;
  height: auto;
  margin-right: 10px;
  font-size: 0;
}
#app .yunoPageHeader .orgSwitch .icon::after {
  content: "\e5c5";
}
#app .yunoPageHeader .orgSwitch .name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 151px;
}
#app .yunoPageHeader .hasSearchBar {
  flex: 0 0 60%;
}
@media (min-width: 768px) {
  #app .yunoPageHeader .hasSearchBar {
    flex: 0 0 40%;
  }
}
#app .yunoPageHeader .userIcon {
  cursor: pointer;
}
#app .yunoPageHeader .userIcon img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-size: 0;
}
#app .yunoPageHeader .userCard {
  display: flex;
}
#app .yunoPageHeader .userCard .imgWrapper {
  flex: 0 0 64px;
  margin-right: 15px;
}
#app .yunoPageHeader .userCard .imgWrapper img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  font-size: 0;
}
#app .yunoPageHeader .userCard figcaption {
  flex: 0 0 calc(100% - 79px);
  overflow: hidden;
}
#app .yunoPageHeader .userCard h3 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  color: #201A19;
}
#app .yunoPageHeader .userCard p {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
  color: #534342;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
#app .yunoPageHeader .actions {
  display: flex;
  align-items: center;
}
#app .yunoPageHeader .actions li {
  margin-left: 15px;
}
#app .yunoPageHeader .actions .dropdown-menu {
  width: max-content;
}
#app .yunoPageHeader .dropdown-menu {
  box-shadow: rgba(0, 0, 0, 0.117647) 0 0 10px;
  padding-bottom: 0;
  margin-bottom: 0;
  padding-top: 0;
  margin-top: 0;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content {
  box-shadow: none;
  padding-bottom: 0;
  margin-bottom: 0;
  padding-top: 0;
  margin-top: 0;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item {
  color: rgba(0, 0, 0, 0.5);
  padding: 10px 15px;
  border-bottom: 1px solid #FFF;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item.normal {
  border-bottom: 1px solid #E6E6E6;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item.normal:hover {
  background: none;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item.normal:active {
  border-bottom: 1px solid #E6E6E6;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item:hover {
  text-decoration: none;
  background-color: #FFF8F7;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item.is-active {
  background-color: #EDE0DE;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item.disabled {
  background-color: rgba(0, 0, 0, 0.2);
  opacity: 0.5;
  cursor: not-allowed;
  border-bottom: 1px solid #FFF;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item.is-hovered {
  background-color: rgba(0, 0, 0, 0.8);
  color: #FFF;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item:active, #app .yunoPageHeader .dropdown-menu .dropdown-content .dropdown-item:focus {
  border: 0;
  background: none;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .has-link a {
  display: flex;
  align-items: center;
  color: #201A19;
  padding-top: 10px;
  padding-bottom: 10px;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .has-link a .yunoIcon {
  text-decoration: none;
  margin-right: 15px;
  color: #201A19;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .has-link a:hover {
  text-decoration: none;
  background-color: #FFF8F7;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .has-link .caption {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}
#app .yunoPageHeader .dropdown-menu .dropdown-content .has-link:last-child {
  border-top: 1px solid #E6E6E6;
}
#app .yunoPageHeader .dropdown-menu .dropdown-item {
  padding: 0;
}
#app .yunoPageHeader .orgSwitchWrapper .dropdown-menu .dropdown-content .dropdown-item {
  display: flex;
  align-items: center;
  color: #201A19;
  font-size: 14px;
}
#app .yunoPageHeader .orgSwitchWrapper .dropdown-menu .dropdown-content .dropdown-item img {
  width: 24px;
  height: auto;
  margin-right: 10px;
  font-size: 0;
}
#app .container.hasTopGap {
  margin-top: 15px;
}
#app .infiniteSpinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 10%;
}
#app .emptyStates {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}
#app .emptyStates.noMaxHeight {
  min-height: 100px;
}
#app .emptyStates .tag.is-info a {
  color: white;
}
#app .emptyStates figure {
  text-align: center;
}
#app .emptyStates figure .stateTitle {
  font-size: 24px;
  font-weight: 400;
  margin-top: 30px;
}
#app .emptyStates figure .stateDescription {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 10px;
}
#app .emptyStates figure .stateDescription a {
  color: #002F5A;
}
#app .emptyStates figure .stateDescription .darkColor {
  color: rgb(0, 0, 0);
}
#app .emptyStates figure .stateDescription .tag:not(body) {
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  color: #4a4a4a;
  display: inline-flex;
  font-size: 0.75rem;
  height: 2em;
  justify-content: center;
  line-height: 1.5;
  padding-left: 0.75em;
  padding-right: 0.75em;
  white-space: nowrap;
}
#app .emptyStates figure .stateDescription .tag:not(body).is-rounded {
  border-radius: 290486px;
}
#app .emptyStates figure .stateDescription .tag:not(body).is-info {
  background-color: #167df0;
  color: #fff;
}
#app .emptyStates .googleSignIn {
  background-color: #FFF;
  border: 1px solid #949494;
  border-radius: 4px;
  font-weight: 500;
  padding-right: 30px;
  box-shadow: rgba(0, 0, 0, 0.2) 0 0 16px;
  margin-top: 30px;
}
#app .emptyStates .googleSignIn > span {
  display: flex;
  align-items: center;
}
#app .emptyStates .googleSignIn > span .icnGoogle {
  width: 28px;
  height: 28px;
  background-size: 28px 28px;
  margin-right: 15px;
  background-image: url("../../assets/images/google.svg");
  background-repeat: no-repeat;
}
@media (min-width: 768px) {
  #app .emptyStates {
    min-height: 600px;
  }
}
#app .emptyStates.statenotSelected {
  min-height: auto;
}
#app .emptyStates.statenotSelected .material-icons {
  border-radius: 50%;
  width: 80px;
  height: 80px;
  background-color: #E6E6E6;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 34px;
}
.dark87, #app .yunoSidebar .menu .menu-list li > ul li a:hover, #app .yunoSidebar .menu .menu-list li a.is-active .material-icons-outlined, #app .yunoSidebar .menu .menu-list li a.is-active, #app .yunoSidebar .menu .menu-list li a, #app .yunoSidebar .menu .referrals .referralField a.noLeftGap {
  color: rgba(32, 26, 25, 0.87);
}

.dark60, #app .yunoSidebar .menu .menu-list li > ul li a {
  color: rgba(83, 67, 66, 0.6);
}

.dark38 {
  color: rgba(0, 0, 0, 0.38);
}

:root {
  --ds-border: #E6E6E6;
}

.rotate-90-cw, #app .yunoSidebar .b-sidebar .sidebar-content.is-mini .sidebarToggle.isDesktop {
  -webkit-animation: rotate-90-cw 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: rotate-90-cw 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.rotate-90-ccw, #app .yunoSidebar .sidebarToggle.isDesktop {
  -webkit-animation: rotate-90-ccw 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: rotate-90-ccw 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

/* ----------------------------------------------
 * Generated by Animista on 2022-11-28 12:47:30
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */
/**
 * ----------------------------------------
 * animation rotate-90-cw
 * ----------------------------------------
 */
@-webkit-keyframes rotate-90-cw {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}
@keyframes rotate-90-cw {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}
/* ----------------------------------------------
* Generated by Animista on 2022-11-28 12:49:27
* Licensed under FreeBSD License.
* See http://animista.net/license for more info. 
* w: http://animista.net, t: @cssanimista
* ---------------------------------------------- */
/**
 * ----------------------------------------
 * animation rotate-90-ccw
 * ----------------------------------------
 */
@-webkit-keyframes rotate-90-ccw {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
  }
}
@keyframes rotate-90-ccw {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
  }
}
#app .pageGrid {
  display: flex;
}
@media (max-width: 767px) {
  #app .pageGrid {
    flex-direction: column;
    position: relative;
  }
  #app .pageGrid .yunoSidebar {
    position: absolute;
    left: -80px;
    top: 0;
    height: 100%;
  }
  #app .pageGrid .yunoSidebar.isMobile.expandView {
    left: 0;
  }
}
#app .pageGrid .mainBody {
  width: calc(100% - 240px);
  max-width: calc(100% - 240px);
}
#app .pageGrid .mainBody.preLogin {
  width: 100%;
  max-width: 100%;
}
@media (max-width: 767px) {
  #app .pageGrid .mainBody {
    width: 100%;
    max-width: 100%;
  }
}
#app .pageGrid .mainBody.miniSidebar {
  width: calc(100% - 80px);
  max-width: calc(100% - 80px);
}
@media (max-width: 767px) {
  #app .pageGrid .mainBody.miniSidebar {
    width: 100%;
    max-width: 100%;
  }
}
#app .additionalItems {
  padding: 0 15px 0;
}
#app .additionalItems .item {
  display: flex;
  align-items: flex-end;
}
#app .additionalItems .item .field {
  flex: 0 0 calc(50% - 15px);
  margin: 0 15px 0 0;
}
#app .additionalItems .item a {
  flex: 0 0 50%;
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
#app .additionalItems .item a .caption {
  font-size: 12px;
}
#app .sidebarWrapper {
  position: relative;
  position: relative;
  background-color: #fff;
  z-index: 6;
}
#app .sidebarWrapper::after {
  content: "";
  width: 2px;
  background: var(--ds-border, linear-gradient(to left, rgba(0, 0, 0, 0.2) 0px, rgba(0, 0, 0, 0.2) 1px, rgba(0, 0, 0, 0.1) 1px, rgba(0, 0, 0, 0) 100%));
  opacity: 0.5;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
}
#app .yunoSidebar .sidebar-layout {
  position: sticky;
  top: 0;
  z-index: 5;
}
#app .yunoSidebar .sidebarToggle {
  position: absolute;
  right: -12px;
  top: 18px;
  background-color: #fff;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #E6E6E6;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 6;
}
#app .yunoSidebar .sidebarToggle.isMobile {
  border-radius: 0;
  border: 0;
  right: -30px;
  top: -36px;
  color: rgba(0, 0, 0, 0.87);
}
#app .yunoSidebar .sidebarToggle.isMobile .material-icons {
  font-size: 24px;
}
#app .yunoSidebar .sidebarToggle.isMobile:hover {
  text-decoration: none;
  color: #A81E22;
}
#app .yunoSidebar .sidebarToggle.isDesktop .material-icons {
  font-size: 18px;
}
#app .yunoSidebar .sidebarToggle.isDesktop:hover {
  text-decoration: none;
  background-color: #A81E22;
  color: white;
}
#app .yunoSidebar .logo {
  padding: 10px 10px;
}
#app .yunoSidebar .logo img {
  width: auto;
  height: 40px;
}
#app .yunoSidebar .menuFooter {
  display: flex;
  align-items: center;
  padding: 0 0 0 5px;
  width: 100%;
  margin-bottom: 15px;
}
#app .yunoSidebar .menuFooter img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 0;
  background-color: #E6E6E6;
}
#app .yunoSidebar .menuFooter figcaption {
  flex: 0 0 calc(100% - 37px);
  margin-left: 7px;
  overflow: hidden;
}
#app .yunoSidebar .menuFooter figcaption .userName {
  font-size: 14px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  line-height: normal;
  text-transform: capitalize;
  margin: 0 0 3px;
}
#app .yunoSidebar .menuFooter figcaption .userEmail {
  font-size: 10px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.6);
  line-height: normal;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin: 0;
}
#app .yunoSidebar .menuFooter.isMini figcaption {
  display: none;
}
#app .yunoSidebar .b-sidebar .menu-label {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 10px;
  letter-spacing: 0.4px;
  color: #534342;
}
#app .yunoSidebar .b-sidebar .sidebar-content {
  width: 240px;
  box-shadow: none;
  position: relative;
  padding: 0;
  z-index: 5;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
#app .yunoSidebar .b-sidebar .sidebar-content.is-mini {
  width: 80px;
}
#app .yunoSidebar .b-sidebar .sidebar-content.is-mini .menuWrapper .menu-list li .caption {
  display: none;
}
#app .yunoSidebar .b-sidebar .sidebar-content.is-mini .menu-list li.hasSubmenu > a .iconWrapper {
  position: absolute;
  right: -1px;
  top: 11px;
  font-size: 17px;
}
#app .yunoSidebar .b-sidebar .sidebar-content.is-mini .additionalItems .item {
  flex-direction: column;
}
#app .yunoSidebar .b-sidebar .sidebar-content.is-mini .additionalItems .field {
  margin-right: 0;
  position: relative;
  overflow: hidden;
}
#app .yunoSidebar .b-sidebar .sidebar-content.is-mini .additionalItems .field .control {
  position: absolute;
  left: -99999px;
  top: 0;
}
#app .yunoSidebar .b-sidebar .sidebar-content.is-light {
  background-color: transparent;
}
#app .yunoSidebar .menu {
  padding: 15px 10px;
}
#app .yunoSidebar .menu .menuWrapper {
  margin-bottom: 15px;
}
#app .yunoSidebar .menu .referrals .referralField {
  display: flex;
  align-items: center;
  width: 100%;
}
#app .yunoSidebar .menu .referrals .referralField .b-skeleton > .b-skeleton-item {
  background-color: #181818;
}
#app .yunoSidebar .menu .referrals .referralField .referralIcon {
  background-image: url("../../../assets/images/union.svg");
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
  background-size: 16px 16px;
  margin-right: 11px;
}
#app .yunoSidebar .menu .referrals .referralField a {
  color: #A81E22;
  text-decoration: underline;
  background: none;
  padding: 0;
}
#app .yunoSidebar .menu .referrals .referralField a.noLeftGap {
  padding: 0;
  position: relative;
  left: -3px;
  text-decoration: none;
}
#app .yunoSidebar .menu .referrals .referralField a:hover {
  background: none;
}
#app .yunoSidebar .menu .referrals .referralField a .caption {
  font-size: 12px;
}
#app .yunoSidebar .menu .referrals .referralField a .caption:hover {
  background: none;
}
#app .yunoSidebar .menu .referrals .referralField.isMini {
  position: relative;
  overflow: hidden;
}
#app .yunoSidebar .menu .referrals .referralField.isMini .field {
  position: absolute;
  left: -99999px;
}
#app .yunoSidebar .menu .referrals .referralField.isMini a {
  padding: 0;
  position: relative;
  left: -1px;
}
#app .yunoSidebar .menu .referrals .field {
  margin-bottom: 0;
}
#app .yunoSidebar .menu .referrals .field .control input[type=text] {
  background: none;
  border: 0;
  width: 85px;
  padding-left: 0;
  margin: 0;
  font-size: 14px;
  height: auto;
}
#app .yunoSidebar .menu .referrals .menu-list li > ul li a:hover {
  background: none;
}
#app .yunoSidebar .menu .referrals .menu-list li > ul li a.is-active {
  background: none;
}
#app .yunoSidebar .menu .menu-list {
  border-bottom: 1px solid #E6E6E6;
}
#app .yunoSidebar .menu .menu-list li a {
  background: #fff;
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  border-radius: 4px;
  padding: 10px 24px 10px 10px;
}
#app .yunoSidebar .menu .menu-list li a .fluid {
  width: 100%;
}
#app .yunoSidebar .menu .menu-list li a .yunoIcon {
  margin-right: 10px;
  font-size: 16px;
}
#app .yunoSidebar .menu .menu-list li a:hover {
  text-decoration: none;
  background-color: rgb(255, 248, 247);
}
#app .yunoSidebar .menu .menu-list li a.is-active {
  background-color: rgb(237, 224, 222);
}
#app .yunoSidebar .menu .menu-list li.referrals a.is-active {
  background-color: transparent;
}
#app .yunoSidebar .menu .menu-list li.referrals.myearnings a.is-active {
  background-color: rgba(0, 0, 0, 0.1);
}
#app .yunoSidebar .menu .menu-list li.hasSubmenu > a {
  position: relative;
}
#app .yunoSidebar .menu .menu-list li.hasSubmenu > a .iconWrapper {
  position: absolute;
  right: 6px;
  top: 10px;
  font-size: 18px;
}
#app .yunoSidebar .menu .menu-list li.hasSubmenu .referralIcon {
  background-image: url("../../../assets/images/union.svg");
  width: 24px;
  height: 24px;
  background-repeat: no-repeat;
  background-size: 24px 24px;
  margin-right: 15px;
}
#app .yunoSidebar .menu .menu-list li > ul {
  margin: 10px 0 10px 10px;
}
#app .yunoSidebar .menu .menu-list li > ul li {
  margin-left: 0;
  border-color: #E6E6E6;
}
#app .yunoSidebar .menu .menu-list li > ul li a {
  background-color: transparent;
  font-size: 12px;
  padding-left: 14px;
}
#app .yunoSidebar .menu .menu-list li > ul li a.is-active {
  border-color: rgba(0, 0, 0, 0.87);
  background-color: rgba(0, 0, 0, 0.05);
}
#app .yunoSidebar .menu .menu-list li > ul li a .yunoIcon {
  font-size: 14px;
}
#app .yunoSidebar .menu .menu-list li > ul li a:hover {
  background-color: #FFF;
  outline: none;
}
#app .yunoSidebar .menu .menu-list li.search_item {
  display: none;
}

.fontAwesomeIcon, #app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-left:after, #app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-left:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after, #app .yunoTable .b-table .table thead th.is-current-sort .icon .mdi-arrow-up:after {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.material-icons-outlined, #app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item::before, #app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item::before, #app .yunoModal.drawerModal .modal-close::after {
  font-family: "Material Icons Outlined";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.material-icons, #app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .filters .yunoAutocompleteSearch .field .control .icon .mdi::after {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.ylIcon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "yuno-icon" !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Enable Ligatures ================ */
  letter-spacing: 0;
  -webkit-font-feature-settings: "liga";
  -moz-font-feature-settings: "liga=1";
  -moz-font-feature-settings: "liga";
  -ms-font-feature-settings: "liga" 1;
  font-feature-settings: "liga";
  -webkit-font-variant-ligatures: discretionary-ligatures;
  font-variant-ligatures: discretionary-ligatures;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.slide-in-right, #app .yunoModal.drawerModal .modal-content {
  -webkit-animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  animation: slide-in-right 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@-webkit-keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(1000px);
    transform: translateX(1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slide-in-right {
  0% {
    -webkit-transform: translateX(1000px);
    transform: translateX(1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    opacity: 1;
  }
}
.dark87, #app .yunoDropdown.hideColumn .dropdown-menu .listCaption, #app .yunoDropdown.multiSelect .dropdown-menu .listCaption, #app .yunoModal.drawerModal .filterNav > li > a, #app .yunoModal.drawerModal .learners .userImg .inlineList li .listLabel, #app .yunoModal.drawerModal .learners .userImg figcaption .primaryTitle, #app .yunoModal.drawerModal .modal-content, #app .yunoTable .b-table .table thead, .body3, .largerTitle {
  color: rgba(0, 0, 0, 0.87);
}

.dark60, #app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active, #app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active, #app .yunoDropdown .labelWrapper .placeHolder, #app .yunoDropdown .dropdown-menu a, #app .yunoModal.drawerModal .classDetail .barWrapper .barValue, #app .yunoModal.drawerModal .stats li .itemLabel, #app .yunoModal.drawerModal .classDate, #app .yunoModal.drawerModal .learners .userImg .inlineList li .value, #app .yunoModal.drawerModal .learners .userImg figcaption .secondaryCaption, #app .yunoTable .b-table .table tbody, #app .yunoTable .note, .overline, .body2, .body1, #app .yunoDropdown .timesDays small {
  color: rgba(0, 0, 0, 0.6);
}

.dark38, #app .yunoModal.drawerModal .classes .days li .disc, #app .yunoModal.drawerModal .classes .classesLabel, #app .yunoModal.drawerModal .classDetail .classInfo .itemLabel, #app .yunoModal.drawerModal .classDetail .classDateV2, #app .yunoModal.drawerModal .intro .introLabel {
  color: rgba(0, 0, 0, 0.38);
}

.fontColorDark {
  color: #201A19;
}

.fontColorDarkVariant {
  color: #534342;
}

.largestTitle {
  font-size: 32px;
  line-height: 42px;
  font-weight: 700;
  margin-bottom: 10px;
}
@media (min-width: 768px) {
  .largestTitle {
    font-size: 48px;
    line-height: 62px;
    font-weight: 700;
    margin-bottom: 10px;
  }
}

.largerTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 10px;
}

.largeTitle {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.smallCaption {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}

.smallerCaption {
  font-size: 16px;
  line-height: 25px;
  font-weight: 500;
  margin-bottom: 10px;
}

.smallestCaption {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

.body1, #app .yunoDropdown .timesDays small {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-bottom: 0;
}

.body2 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 0;
}

.body3 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  margin-bottom: 15px;
}

.overline {
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.progress-wrapper .progress-value {
  color: rgba(0, 0, 0, 0.7);
}
.progress-wrapper .progress {
  border: 1px solid #FFF;
  height: 8px;
}
.progress-wrapper .progress.is-orange::-webkit-progress-value {
  background: #FC9927;
}
.progress-wrapper .progress.is-red::-webkit-progress-value {
  background: #CA0813;
}
.progress-wrapper .progress.is-yellow::-webkit-progress-value {
  background: #F0C042;
}
.progress-wrapper .progress.is-lightGreen::-webkit-progress-value {
  background: #669D4F;
}
.progress-wrapper .progress.is-darkGreen::-webkit-progress-value {
  background: #356B21;
}

#app .yunoTable {
  margin-top: 10px;
}
#app .yunoTable .note {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
}
#app .yunoTable .table-mobile-sort .field.has-addons .control:not(.is-expanded) {
  display: none;
}
#app .yunoTable .table-mobile-sort .field.has-addons .control:first-child:not(:only-child) .select select {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}
#app .yunoTable .b-table .table-wrapper {
  overflow-x: auto;
  overflow-y: hidden;
}
#app .yunoTable .b-table .field.table-mobile-sort {
  display: none;
}
#app .yunoTable .b-table .table thead {
  font-size: 14px;
  line-height: normal;
  font-weight: 500;
  margin-bottom: 0;
}
#app .yunoTable .b-table .table thead th {
  padding: 10px 10px 10px 0;
}
#app .yunoTable .b-table .table thead th.is-current-sort .icon .mdi-arrow-up:after {
  content: "\f077";
  position: relative;
  top: -1px;
}
#app .yunoTable .b-table .table thead th .sort-icon.is-invisible {
  display: none;
}
#app .yunoTable .b-table .table thead th .th-wrap .is-relative {
  width: max-content;
}
#app .yunoTable .b-table .table tbody {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}
#app .yunoTable .b-table .table tbody tr:hover .actions {
  visibility: visible;
}
#app .yunoTable .b-table .table tbody td {
  vertical-align: middle;
  padding: 5px 10px 5px 0;
}
#app .yunoTable .b-table .table tbody td div {
  width: max-content;
}
#app .yunoTable .b-table .table tbody .userWithPhoto {
  display: flex;
  align-items: center;
  min-width: 60px;
  width: max-content;
}
#app .yunoTable .b-table .table tbody .userWithPhoto img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 5px;
  font-size: 0;
}
#app .yunoTable .b-table .table tbody .hasActiveInactive {
  display: flex;
  align-items: center;
  width: max-content;
}
#app .yunoTable .b-table .table tbody .hasActiveInactive .material-icons, #app .yunoTable .b-table .table tbody .hasActiveInactive .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .hasActiveInactive .dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .hasActiveInactive .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .hasActiveInactive .dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .hasActiveInactive .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .hasActiveInactive .mdi::after {
  font-size: 20px;
  padding-right: 5px;
}
#app .yunoTable .b-table .table tbody .hasActiveInactive.active {
  color: #356B21;
  font-weight: 500;
}
#app .yunoTable .b-table .table tbody .hasActiveInactive.inactive {
  color: #FC9927;
  font-weight: 500;
}
#app .yunoTable .b-table .table tbody .title {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}
#app .yunoTable .b-table .table tbody .classDays {
  display: flex;
  flex-wrap: wrap;
}
#app .yunoTable .b-table .table tbody .classDays li {
  text-transform: uppercase;
  font-size: 10px;
  margin-right: 5px;
  background-color: rgba(168, 30, 34, 0.04);
  border: 1px solid #A81E22;
  padding: 3px 8px;
  border-radius: 100px;
  flex: 0 0 35px;
  text-align: center;
  color: #A81E22;
  margin-top: 5px;
}
@media (min-width: 1214px) {
  #app .yunoTable .b-table .table tbody .classDays li {
    margin-top: 0;
  }
}
#app .yunoTable .b-table .table tbody .classDays li:last-child {
  margin-right: 0;
}
#app .yunoTable .b-table .table tbody .classDays li.disabled {
  background-color: rgba(0, 0, 0, 0.02);
  color: rgba(0, 0, 0, 0.38);
  border-color: transparent;
}
#app .yunoTable .b-table .table tbody .grid {
  display: flex;
  align-items: center;
  margin: 0 -3px;
}
#app .yunoTable .b-table .table tbody .grid .gridItem {
  padding: 0 3px;
}
#app .yunoTable .b-table .table tbody .grid .itemIcon.material-icons, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.hideColumn .dropdown-menu .itemIcon.dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .itemIcon.dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.multiSelect .dropdown-menu .itemIcon.dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .grid .itemIcon.dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .grid .filters .yunoAutocompleteSearch .field .control .icon .itemIcon.mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid .itemIcon.mdi::after {
  font-size: 17px;
  cursor: pointer;
}
#app .yunoTable .b-table .table tbody .grid .itemIcon.material-icons.active, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.hideColumn .dropdown-menu .itemIcon.active.dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .itemIcon.active.dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.multiSelect .dropdown-menu .itemIcon.active.dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .grid .itemIcon.active.dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .grid .filters .yunoAutocompleteSearch .field .control .icon .itemIcon.active.mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid .itemIcon.active.mdi::after {
  color: #A81E22;
}
#app .yunoTable .b-table .table tbody .grid .itemIcon.material-icons.inactive, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.hideColumn .dropdown-menu .itemIcon.inactive.dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .itemIcon.inactive.dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.multiSelect .dropdown-menu .itemIcon.inactive.dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .grid .itemIcon.inactive.dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .grid .filters .yunoAutocompleteSearch .field .control .icon .itemIcon.inactive.mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid .itemIcon.inactive.mdi::after {
  opacity: 0.5;
}
#app .yunoTable .b-table .table tbody .grid .material-icons-outlined, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.hideColumn .dropdown-menu .dropdown-item::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .grid .dropdown-item::before, #app .yunoTable .b-table .table tbody .grid .yunoDropdown.multiSelect .dropdown-menu .dropdown-item::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .grid .dropdown-item::before, #app .yunoTable .b-table .table tbody .grid .yunoModal.drawerModal .modal-close::after, #app .yunoModal.drawerModal .yunoTable .b-table .table tbody .grid .modal-close::after, #app .yunoTable .b-table .table tbody .grid .material-icons, #app .yunoTable .b-table .table tbody .grid .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .grid .mdi::after {
  font-size: 12px;
}
#app .yunoTable .b-table .table tbody .percentageBlock {
  display: flex;
  align-items: center;
  width: 150px;
  justify-content: space-between;
}
#app .yunoTable .b-table .table tbody .percentageBlock .progress-wrapper {
  width: 60%;
  margin: 0;
}
#app .yunoTable .b-table .table tbody .percentageBlock .percentage a {
  padding-left: 5px;
}
#app .yunoTable .b-table .table tbody .percentageBlock .material-icons, #app .yunoTable .b-table .table tbody .percentageBlock .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .yunoTable .b-table .table tbody .percentageBlock .dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .percentageBlock .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .yunoTable .b-table .table tbody .percentageBlock .dropdown-item.is-active::before, #app .yunoTable .b-table .table tbody .percentageBlock .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoTable .b-table .table tbody .percentageBlock .mdi::after {
  font-size: 12px;
  position: relative;
  top: 2px;
}
#app .yunoTable .b-table .table tbody .actions {
  display: flex;
  visibility: hidden;
}
#app .yunoTable .b-table .table tbody .actions li a {
  display: block;
  position: relative;
}
#app .yunoTable .b-table .table tbody .actions li a:hover {
  text-decoration: none;
}
#app .yunoTable .b-table .table tbody .actions li .itemLabel {
  font-size: 0;
  position: absolute;
  left: -999999px;
}
#app .yunoTable .b-table .table tbody .actions li .itemIcon {
  font-size: 18px;
  display: block;
  padding: 2px 8px;
}
#app .yunoTable .b-table .table tbody .actions.copyToClipboard .wrapper {
  cursor: pointer;
  color: #A81E22;
}
#app .yunoTable .b-table .pagination {
  margin: 0;
}
#app .yunoTable .b-table .pagination .pagination-ellipsis {
  font-size: 0;
}
#app .yunoTable .b-table .pagination .pagination-ellipsis::after {
  content: "...";
  font-size: 14px;
  position: relative;
  top: 17px;
  color: #000;
}
#app .yunoTable .b-table .pagination-link {
  color: #000;
}
#app .yunoTable .b-table .pagination-link:hover {
  text-decoration: none;
}
#app .yunoTable .b-table .pagination-link.is-current {
  background-color: #A81E22;
  border-color: #A81E22;
  color: #FFF;
}
#app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-left:after, #app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-left:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  content: "\f060";
}
#app .yunoTable .b-table .pagination-link.pagination-previous .icon .mdi-chevron-right:after, #app .yunoTable .b-table .pagination-link.pagination-next .icon .mdi-chevron-right:after {
  content: "\f061";
}
#app .emptyStateV2 {
  display: flex;
  justify-content: center;
  margin-top: 5%;
}
#app .emptyStateV2 figure {
  display: flex;
  flex-direction: column;
  align-items: center;
}
#app .emptyStateV2 figure img {
  width: 80px;
  height: auto;
}
#app .emptyStateV2 figure figcaption {
  margin-top: 15px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
  color: #534342;
}
#app .emptyStateV2 figure .button {
  margin-top: 15px;
}
#app .emptyStateV2 .ctaWrapper {
  display: flex;
  flex-direction: column;
}
#app .yunoModal.drawerModal {
  justify-content: flex-start;
  align-items: flex-end;
}
#app .yunoModal.drawerModal .scrollable {
  overflow-y: auto;
}
#app .yunoModal.drawerModal .drawerTitle {
  font-size: 20px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .modal-close {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.6);
  right: 0;
  top: 0;
}
#app .yunoModal.drawerModal .modal-close::after {
  content: "\e5cd";
  color: rgba(0, 0, 0, 0.6);
}
#app .yunoModal.drawerModal .modal-content {
  height: 100%;
  max-height: none;
  margin: 0;
  border-radius: 0;
}
#app .yunoModal.drawerModal .learners {
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .learners.noBtmGap {
  margin-bottom: 0;
}
#app .yunoModal.drawerModal .learners .userImg {
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding-bottom: 15px;
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .learners .userImg img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 0;
  background-color: #E6E6E6;
}
#app .yunoModal.drawerModal .learners .userImg figcaption {
  font-size: 16px;
  font-weight: 500;
  margin-left: 10px;
}
#app .yunoModal.drawerModal .learners .userImg figcaption .primaryTitle {
  font-size: 16px;
  line-height: auto;
  font-weight: 500;
  text-transform: capitalize;
}
#app .yunoModal.drawerModal .learners .userImg figcaption .secondaryCaption {
  font-size: 12px;
  line-height: 16px;
}
#app .yunoModal.drawerModal .learners .userImg .inlineList {
  margin-top: 10px;
}
@media (min-width: 768px) {
  #app .yunoModal.drawerModal .learners .userImg .inlineList li {
    display: flex;
  }
}
#app .yunoModal.drawerModal .learners .userImg .inlineList li .listLabel {
  font-size: 12px;
  font-weight: 500;
}
#app .yunoModal.drawerModal .learners .userImg .inlineList li .value {
  display: flex;
}
@media (min-width: 768px) {
  #app .yunoModal.drawerModal .learners .userImg .inlineList li .value {
    margin-left: 10px;
  }
}
#app .yunoModal.drawerModal .learners .userImg .inlineList li .value span {
  font-size: 12px;
}
#app .yunoModal.drawerModal .learners .userImg .inlineList li .value span::after {
  content: "|";
  color: rgba(0, 0, 0, 0.08);
  padding-left: 5px;
  margin-right: 5px;
}
#app .yunoModal.drawerModal .learners .userImg .inlineList li .value span:last-child::after {
  display: none;
}
#app .yunoModal.drawerModal .learners.larger .userImg img {
  width: 96px;
  height: 96px;
  border-radius: 50%;
}
#app .yunoModal.drawerModal .classDate {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .intro .introLabel {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 5px;
  letter-spacing: 1.5px;
}
#app .yunoModal.drawerModal .intro .primaryTitle {
  font-size: 20px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .intro .secondaryTitle {
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .stats {
  display: flex;
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .stats li {
  flex: 0 0 50%;
}
#app .yunoModal.drawerModal .stats li .itemValue {
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 5px;
}
#app .yunoModal.drawerModal .stats li .itemLabel {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
}
#app .yunoModal.drawerModal .classDetail {
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  padding: 15px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  margin-top: 15px;
  background: rgba(0, 0, 0, 0.02);
}
#app .yunoModal.drawerModal .classDetail .loaderWrapper {
  height: 24px;
}
#app .yunoModal.drawerModal .classDetail .smallLoader {
  font-size: 4px;
  margin: 0 auto;
}
#app .yunoModal.drawerModal .classDetail .inform {
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  color: #A81E22;
}
#app .yunoModal.drawerModal .classDetail .classDateV2 {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 5px;
  letter-spacing: 1.5px;
}
#app .yunoModal.drawerModal .classDetail .classTitle {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .classDetail .classInfo {
  display: flex;
}
#app .yunoModal.drawerModal .classDetail .classInfo li {
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  padding-right: 15px;
  margin-right: 15px;
}
#app .yunoModal.drawerModal .classDetail .classInfo li:last-child {
  border-right: 0;
}
#app .yunoModal.drawerModal .classDetail .classInfo figure {
  display: flex;
}
#app .yunoModal.drawerModal .classDetail .classInfo img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 0;
  margin-right: 10px;
}
#app .yunoModal.drawerModal .classDetail .classInfo .itemLabel {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 5px;
  letter-spacing: 1.5px;
}
#app .yunoModal.drawerModal .classDetail .classInfo .itemValue {
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
}
#app .yunoModal.drawerModal .classDetail .barWrapper {
  margin-top: 15px;
}
#app .yunoModal.drawerModal .classDetail .barWrapper .barHeader {
  display: flex;
  justify-content: space-between;
}
#app .yunoModal.drawerModal .classDetail .barWrapper .barValue {
  font-size: 12px;
  line-height: 16px;
}
#app .yunoModal.drawerModal .classes {
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 4px;
  padding: 15px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
#app .yunoModal.drawerModal .classes .classesLabel {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 5px;
  letter-spacing: 1.5px;
}
#app .yunoModal.drawerModal .classes .days {
  margin: 5px -12px 0;
  display: flex;
  flex-wrap: wrap;
}
#app .yunoModal.drawerModal .classes .days li {
  padding: 0 12px;
  margin-top: 10px;
}
#app .yunoModal.drawerModal .classes .days li .disc {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
#app .yunoModal.drawerModal .classes .days li.active .disc {
  background-color: rgba(37, 211, 102, 0.37);
  color: white;
}
#app .yunoModal.drawerModal .classes .days li.active .disc:hover {
  background-color: rgba(37, 211, 102, 0.74);
}
#app .yunoModal.drawerModal .classes .days li.active .disc:active {
  background-color: #25D366;
}
#app .yunoModal.drawerModal .classes .days li.active.isClicked .disc {
  background-color: #25D366;
}
#app .yunoModal.drawerModal .classes .days li.disable .disc:hover {
  background-color: rgba(0, 0, 0, 0.38);
  color: white;
}
#app .yunoModal.drawerModal .classes .days li.disable .disc:active {
  background-color: rgba(0, 0, 0, 0.87);
  color: white;
}
#app .yunoModal.drawerModal .classes .days li.disable.isClicked .disc {
  background-color: rgba(0, 0, 0, 0.87);
  color: white;
}
#app .yunoModal.drawerModal .yunoTable {
  padding: 0;
  border-radius: 4px;
  border: 1px solid;
  border-color: rgba(0, 0, 0, 0.1);
  min-height: 500px;
}
#app .yunoModal.drawerModal .yunoTable .b-table .table-wrapper {
  overflow-y: auto;
  margin-bottom: 15px;
}
#app .yunoModal.drawerModal .yunoTable .b-table .table-wrapper.has-mobile-cards tr:not(.detail):not(.is-empty):not(.table-footer):hover {
  background-color: #FFF;
}
#app .yunoModal.drawerModal .yunoTable .b-table .level {
  display: none;
}
@media (min-width: 768px) {
  #app .yunoModal.drawerModal .yunoTable .b-table thead tr th {
    border-bottom: 1px solid #E6E6E6;
  }
}
@media (min-width: 768px) {
  #app .yunoModal.drawerModal .yunoTable .b-table tbody tr td {
    border-top: 0;
  }
}
#app .yunoModal.drawerModal .yunoTable .b-table tbody tr.is-empty td {
  border-bottom: 0;
}
#app .yunoModal.drawerModal .yunoTable .b-table tbody tr:hover {
  background-color: #FFF;
}
#app .yunoModal.drawerModal .filterNav > li > a {
  font-size: 16px;
  display: block;
  padding: 15px;
  border-bottom: 1px solid;
  border-color: rgba(0, 0, 0, 0.08);
  font-weight: 500;
  line-height: 24px;
}
#app .yunoModal.drawerModal .filterNav > li > a:hover {
  text-decoration: none;
}
#app .yunoModal.drawerModal .filterNav > li > a.is-active {
  background-color: rgba(0, 0, 0, 0.02);
}
#app .yunoModal.drawerModal .coursesMain {
  margin-bottom: 30px;
}
#app .yunoModal.drawerModal .coursesMain.hideMe {
  display: none;
}
#app .yunoModal .availabilityWrapper .ctaWrapper {
  justify-content: flex-start;
}
#app .yunoModal .hasScroll {
  min-height: 280px;
}
#app .filtersWrapper {
  position: sticky;
  top: 0;
  z-index: 9;
  background-color: #FFF;
}
#app .filtersWrapper .filterHeader {
  display: none;
}
#app .filtersWrapper.mobileView {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0 15px 15px;
}
#app .filtersWrapper.mobileView .filterHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#app .filters {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
  padding: 0;
  background-color: #FFFFFF;
}
#app .filters.categoryFilter {
  margin-bottom: 15px;
}
@media (min-width: 768px) {
  #app .filters.categoryFilter {
    margin-bottom: 0;
  }
}
#app .filters.categoryFilter.mobileView {
  margin-bottom: 0;
}
@media (min-width: 768px) {
  #app .filters.otherFilters {
    display: flex;
  }
}
#app .filters.otherFilters.mobileView {
  display: block;
}
#app .filters.noBtmGap {
  padding-bottom: 0;
}
#app .filters.noTopGap {
  padding-top: 0;
}
#app .filters .yunoAutocompleteSearch {
  flex: 0 0 25%;
  margin: 0 0 0;
  padding: 0 0 0 15px;
  display: none;
}
@media (min-width: 768px) {
  #app .filters .yunoAutocompleteSearch {
    display: block;
    flex: 0 0 250px;
    margin: 15px 0 0;
  }
}
#app .filters .yunoAutocompleteSearch .field .control input[type=text] {
  border-color: #E6E6E6;
  font-weight: 500;
  font-size: 14px;
  height: 43px;
}
#app .filters .yunoAutocompleteSearch .field .control .icon .mdi {
  display: flex;
  align-items: center;
}
#app .filters .yunoAutocompleteSearch .field .control .icon .mdi::after {
  content: "\e5c9";
  color: #A81E22;
}
#app .filters .yunoDropdown {
  flex: 0 0 25%;
  margin: 0 0 0;
  padding: 0 0 0 15px;
  display: none;
}
#app .filters .yunoDropdown:first-child {
  display: block;
  flex: 0 0 calc(100% - 43px);
}
@media (min-width: 768px) {
  #app .filters .yunoDropdown:first-child {
    flex: 0 0 auto;
  }
}
@media (min-width: 768px) {
  #app .filters .yunoDropdown {
    display: block;
    flex: 0 0 auto;
    margin: 15px 0 0;
  }
}
#app .filters .yunoCheckbox {
  margin: 0 0 0;
  padding: 0 0 0 15px;
  display: none;
}
@media (min-width: 768px) {
  #app .filters .yunoCheckbox {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
    margin: 15px 0 0;
  }
}
#app .filters.mobileView .yunoDropdown {
  flex: 0 0 100%;
  display: block;
  margin-bottom: 15px;
}
#app .filters.mobileView .yunoDropdown:first-child {
  display: block;
  flex: 0 0 100%;
}
#app .filters.mobileView .filterTrigger {
  display: none;
}
#app .filters .filterTrigger {
  display: block;
  align-self: center;
  padding-right: 15px;
}
#app .filters .filterTrigger .material-icons, #app .filters .filterTrigger .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .filters .filterTrigger .dropdown-item.is-active::before, #app .filters .filterTrigger .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .filters .filterTrigger .dropdown-item.is-active::before, #app .filters .filterTrigger .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .filterTrigger .mdi::after {
  font-size: 28px;
}
@media (min-width: 768px) {
  #app .filters .filterTrigger {
    display: none;
  }
}
#app .yunoDropdown {
  min-width: 0;
}
#app .yunoDropdown.slider .dropdown-menu {
  padding: 15px;
}
#app .yunoDropdown.slider .b-slider.is-primary .b-slider-fill {
  background: #A81E22 !important;
}
#app .yunoDropdown.slider .b-tooltip.is-primary .tooltip-content {
  background: #A81E22 !important;
}
#app .yunoDropdown .timesDays {
  padding-bottom: 10px;
}
#app .yunoDropdown .timesDays .wrapper {
  padding: 10px 15px 0;
}
#app .yunoDropdown .timesDays ul {
  display: flex;
  flex-wrap: wrap;
  margin: 10px -5px 0;
}
#app .yunoDropdown .timesDays ul li {
  flex: 0 0 25%;
  padding: 0 5px 5px;
}
#app .yunoDropdown .timesDays ul li .inner {
  text-align: center;
  font-size: 10px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  border-radius: 4px;
  border: 1px solid #E6E6E6;
  padding: 5px;
  cursor: pointer;
}
#app .yunoDropdown .timesDays ul li .inner:hover {
  border-color: #A81E22;
  color: #A81E22;
}
#app .yunoDropdown .timesDays ul li .material-icons, #app .yunoDropdown.hideColumn .timesDays ul li .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.hideColumn .dropdown-menu .timesDays ul li .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .timesDays ul li .dropdown-menu .dropdown-item.is-active::before, #app .yunoDropdown.multiSelect .dropdown-menu .timesDays ul li .dropdown-item.is-active::before, #app .yunoDropdown .timesDays ul li .filters .yunoAutocompleteSearch .field .control .icon .mdi::after, #app .filters .yunoAutocompleteSearch .field .control .icon .yunoDropdown .timesDays ul li .mdi::after {
  display: block;
  margin-bottom: 5px;
}
#app .yunoDropdown .timesDays ul li.active .inner {
  background-color: #A81E22;
  color: white;
}
#app .yunoDropdown .dropdown {
  width: 100%;
}
#app .yunoDropdown .dropdown-trigger {
  width: 100%;
}
#app .yunoDropdown.availability .dropdown-menu {
  width: 300px;
  left: auto;
  right: 0;
}
#app .yunoDropdown .dropdown-menu {
  padding: 0;
  max-height: 300px;
  overflow-y: auto;
}
#app .yunoDropdown .dropdown-menu .dropdown-content {
  box-shadow: none;
  padding: 0;
}
#app .yunoDropdown .dropdown-menu a {
  padding: 5px 15px;
}
#app .yunoDropdown .dropdown-menu a.dropdown-item.is-active {
  background-color: #A81E22;
  color: white;
}
#app .yunoDropdown .labelWrapper {
  padding: 10px 35px 10px 15px;
  border: 1px solid #E6E6E6;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
}
#app .yunoDropdown .labelWrapper .icon {
  position: absolute;
  right: 10px;
  top: calc(50% - 12px);
}
#app .yunoDropdown .labelWrapper .placeHolder {
  font-size: 12px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
  display: block;
}
#app .yunoDropdown .labelWrapper .clearFilter {
  position: absolute;
  right: 35px;
  bottom: 2px;
}
#app .yunoDropdown .labelWrapper .selectedItem {
  font-weight: 400;
  padding-top: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  text-wrap: nowrap;
  display: block;
  padding-right: 27px;
  font-size: 14px;
}
#app .yunoDropdown .labelWrapper .selectedItem span {
  flex-grow: 0;
}
#app .yunoDropdown .labelWrapper .selectedItem span:last-child::after {
  display: none;
}
#app .yunoDropdown .labelWrapper .selectedItem span::after {
  content: ",";
  position: relative;
  left: -2px;
}
#app .yunoDropdown .labelWrapper .selectedItem.hasGrid {
  display: flex;
  padding-right: 0;
}
#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item {
  flex-grow: 0;
  display: flex;
  position: relative;
  padding-right: 27px;
  margin-right: 5px;
}
#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item:last-child {
  margin-right: 0;
}
#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .item span::after {
  display: none;
}
#app .yunoDropdown .labelWrapper .selectedItem.hasGrid .clearFilter {
  position: absolute;
  right: 0;
  top: -2px;
}
#app .yunoDropdown.iconOnly .labelWrapper {
  border: 0;
  padding: 0 5px 0;
}
#app .yunoDropdown.iconOnly .labelWrapper .icon {
  top: calc(50% - 14px);
}
#app .yunoDropdown.multiSelect .dropdown-menu {
  width: max-content;
}
#app .yunoDropdown.multiSelect .dropdown-menu .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 15px;
}
#app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item {
  position: relative;
  padding-left: 35px;
}
#app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item::before {
  content: "\e835";
  position: absolute;
  left: 13px;
  top: 6px;
  font-size: 18px;
}
#app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active {
  background-color: #FFF;
}
#app .yunoDropdown.multiSelect .dropdown-menu .dropdown-item.is-active::before {
  content: "\e834";
  font-size: 18px;
  color: #A81E22;
}
#app .yunoDropdown.hideColumn .dropdown-menu {
  left: auto;
  right: 0;
}
#app .yunoDropdown.hideColumn .dropdown-menu .listCaption {
  font-size: 14px;
  line-height: 24px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 0 15px;
}
#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item {
  position: relative;
  padding-left: 35px;
}
#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item::before {
  content: "\e835";
  position: absolute;
  left: 13px;
  top: 6px;
  font-size: 18px;
}
#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active {
  background-color: #FFF;
}
#app .yunoDropdown.hideColumn .dropdown-menu .dropdown-item.is-active::before {
  content: "\e834";
  font-size: 18px;
  color: #A81E22;
}
#app .fontColorDark, #app .yunoTabsV5 ul li.is-active a {
  color: #201A19;
}
#app .fontColorDarkVariant {
  color: #534342;
}
#app .fontColorDarkVariant2, #app .yunoTabsV5 ul li a {
  color: rgba(0, 0, 0, 0.38);
}
#app .yunoTabsWrapper {
  position: sticky;
  top: 0;
  background-color: #FFF;
  z-index: 2;
}
#app .yunoTabsWrapper.tabsNotAvailable .b-tabs.yunoTabsV5 > .tabs {
  display: none;
}
#app .yunoTabsWrapper.tabsNotAvailable .tab-content {
  padding-top: 0;
}
#app .yunoTabsWrapper.tabsNotAvailable .mainHeader {
  margin-top: 0;
}
#app .yunoTabsWrapper.tabsAvailable .mainHeader {
  margin-top: 0;
}
#app .yunoTabsV5 {
  margin-top: 15px;
}
#app .yunoTabsV5 ul li a {
  font-size: 14px;
}
#app .yunoTabsV5 ul li.is-active a {
  border-bottom-color: #201A19;
}
#app .yunoTabsV5.noContent .tab-content {
  display: none;
}
#app .dark87, #app .mainHeader .gridInfo .actions li a, #app .mainHeader .gridInfo .actions, #app .mainHeader .gridInfo .note span {
  color: rgba(0, 0, 0, 0.87);
}
#app .dark60, #app .mainHeader .gridInfo .note {
  color: rgba(0, 0, 0, 0.6);
}
#app .dark38 {
  color: rgba(0, 0, 0, 0.38);
}
#app .pageGrid .mainBody {
  position: relative;
  z-index: 7;
}
#app .dateOptions {
  overflow-y: auto;
  margin-top: 15px;
}
@media (min-width: 768px) {
  #app .dateOptions {
    overflow-y: visible;
  }
}
#app .dateOptions .button {
  font-size: 12px;
}
#app .dateOptions .button.is-primary {
  background-color: #A81E22;
}
#app .dateOptions .field.has-addons .control:first-child:not(:only-child) .button {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
#app .dateOptions .field.has-addons .control:not(:first-child):not(:last-child) .button {
  border-radius: 0;
}
#app .mainHeader {
  margin: 15px 0;
  background-color: #FFF;
  position: sticky;
  top: 0;
  z-index: 2;
}
@media (max-width: 767px) {
  #app .mainHeader {
    margin: 30px 0 15px 0;
    padding-left: 18px;
  }
}
#app .mainHeader .block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
}
#app .mainHeader .pageTitle {
  font-size: 24px;
  line-height: 28px;
  font-weight: 500;
  margin-bottom: 0;
  display: flex;
  align-items: center;
}
#app .mainHeader .pageTitle a {
  position: relative;
  top: 4px;
  font-size: 18px;
  margin-left: 5px;
}
#app .mainHeader .pageTitle a:hover {
  text-decoration: none;
}
#app .mainHeader .gridInfo {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
  align-items: center;
}
#app .mainHeader .gridInfo .note {
  font-size: 14px;
  line-height: normal;
  font-weight: 400;
  margin-bottom: 0;
}
#app .mainHeader .gridInfo .note span {
  font-weight: 500;
}
#app .mainHeader .gridInfo .actions {
  text-decoration: none;
  display: flex;
  margin: 0 -5px;
}
#app .mainHeader .gridInfo .actions li {
  padding: 0 5px;
}