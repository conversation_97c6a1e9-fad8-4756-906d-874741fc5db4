Vue.component('yuno-past', {
    props: {
        storage: {
            type: Object,
            required: true
        },
        apiURL: {
            type: Object,
            required: true,
        },
        filterAPIURL: {
            type: Object,
            required: true,
        }
    },
    template: `
        <div>   
            <b-loading :is-full-page="true" v-model="isLoading" :can-cancel="false"></b-loading>
            <yuno-table-grid
                :apiURL="apiURL"
                :filterAPIURL="filterAPIURL"
                apiMethodType="GET"
                :payload="filterResult.payload"
                :sessionStorage="storage"
                emptyStateImg="/assets/images/group.png"
                @onRefineFilters="onRefineFilters"
                @onQuerySearch="onQuerySearch"
                @onSearchInput="onSearchInput"
                @manageDrawer="manageDrawer"
                @gotFilters="gotFilters"
                @onPageChange="onPageChange"
                @gotGrid="onGotGrid"
                @initAction="initAction"
                recordFoundCaption="Batches found"
                :errorSlot="true"
            >
                <template v-slot:stats>
                </template>
                <template v-slot:emptyState>
                    <yuno-empty-state-v2 
                        v-if="filterResult.errorData === 'course_id is required.'"
                        :options="{'type': 'noDataFound', 'message': 'Choose a course above to see its batches'}"
                        image="/assets/images/choose.png"
                    >
                    </yuno-empty-state-v2>
                    <yuno-empty-state-v2 
                        v-else
                        :options="{'type': 'noDataFound', 'message': filterResult.errorData}"
                        image="/assets/images/group.png"
                    >
                    </yuno-empty-state-v2>
                </template>
            </yuno-table-grid>
            <b-modal 
                :active.sync="drawerModal.modal" 
                :width="drawerModal.width" 
                animation="slide-out"
                :can-cancel="['escape', 'x']"
                :on-cancel="closeDrawer"
                class="yunoModal drawerModal">
                    <template v-if="drawerModal.modal">
                        <div class="scrollable">
                            <h2 class="drawerTitle">
                                {{ drawerModal.data.learner.full_name }}
                            </h2>
                            <yuno-learner-attendance v-if="drawerModal.type === 'attendance'" :data="drawer" @getClassDetail="onGetClassDetail"></yuno-learner-attendance>
                        </div>
                    </template>
            </b-modal>
        </div>
    `,
    data() {
        return {
            isLoading: false,
            drawerModal: {
                modal: false,
                data: [],
                width: 1000,
                type: ""
            },
        }
    },
    computed: {
        ...Vuex.mapState([
            'userInfo',
            'filters',
            'filterResult',
            'userRole',
            'drawer',
            'orgAdmin',
            'counsellorList',
            'allCourses',
            'allBatches',
            'learnerInsightsClass'
        ]),
    },
    mounted() {
    },
    methods: {
        onTabChanged() {
            
        },
        initAction(row, action) {
        },
        isFilterAvailable(data) {
            return YUNOCommon.findInArray(data, this.userRole.data);
        },
        onGotGrid(data) {
            const rows = data.rows;
        },
        onPageChange() {
            this.$emit("onPageChange");
        },
        onRefineFilters(filters) {
            // Implementation if needed
        },
        onQuerySearch(name, filter, isSelect) {
            if (isSelect) {
                const payload = this.filterResult.payload;
                const filters = this.filters.data;
                payload[filter.filter] = filter.selected ?? 0;

                if (payload.course_id !== 0) {
                    filters.forEach(filter => {
                        filter.is_disabled = false;
                    });
                } else {
                    filters.forEach(filter => {
                        filter.is_disabled = true;

                        if (filter.filter === "course_id") {
                            filter.is_disabled = false;
                        }
                    });
                }

            } else {
                switch (filter.filter) {
                    case "course_id":
                        const payload = {
                            "category": [0],
                            "academy": [0],
                            "text": name
                        }
                        this.fetchCourseSuggestions(name, filter, payload);
                        break;
                    case "instructor_id":
                        this.fetchInstructorSuggestions(name, filter);
                        break;
                }
            }
        },
        onSearchInput(name, filter) {
            // Implementation if needed
        },
        manageDrawer(data) {
            this.drawerModal.modal = true;
            this.drawerModal.data = data.row;
            this.drawerModal.type = data.action.slug;
        },
        closeDrawer() {
            this.drawerModal.modal = false;
        },
        activeOrg() {
            return this.userInfo.data.current_state.org_id || 0;
        },
        gotFilters(data) {
            const filters = data;
            const organization = YUNOCommon.findObjectByKey(filters, "filter", "org_id");
            const counselor = YUNOCommon.findObjectByKey(filters, "filter", "counselor_id");

            filters.forEach(filter => {
                filter.search_field = "";
                filter.current = "";
                filter.id_field = "";
                filter.is_disabled = true;
        
                switch (filter.filter) {
                    case "course_id":
                        filter.search_field = "title";
                        filter.current = filter.selected ? filter.selected.title : "";
                        filter.id_field = "id";
                        filter.is_disabled = false;
                        break;
                    case "instructor_id":
                        filter.search_field = "name";
                        filter.current = filter.selected ? filter.selected.name : "";
                        filter.id_field = "user_id";
                        break;
                }
            });
            

            // if (this.userRole.data.includes("yuno-admin")) {
            //     this.fetchOrgAdminList(organization, "all");
            //     this.fetchCounsellorList(counselor);    
            // }

            
        },
        gotCourseSuggestions(options, filter) {
            filter.loading = false;
            if (options.response?.data?.code === 200) {
                const { course } = options.response.data.data;
                if (course) {
                    filter.items.push(...course);
                }

                console.log(filter);
            }
        },
        fetchCourseSuggestions(name, filter, payload) {
            const options = {
                apiURL: YUNOCommon.config.courseV4("suggestions", false),
                module: "gotData",
                store: "searchSuggestions",
                payload: payload,
                headers: {
                    'accept': 'application/json',
                    'content-type': 'application/json'
                },
                callback: true,
                callbackFunc: (options) => this.gotCourseSuggestions(options, filter)
            };
        
            this.$store.dispatch('postData', options);
        },
        gotInstructorSuggestions(options, filter) {
            filter.loading = false;
            if (options.response?.data?.code === 200) {
                filter.items = options.response.data.data;
            }
        },
        fetchInstructorSuggestions(name, filter) {
            const options = {
                apiURL: YUNOCommon.config.generic("userSearch", name, "instructor", this.activeOrg()),
                module: "gotData",
                store: "searchSuggestions",
                callback: true,
                callbackFunc: (options) => this.gotInstructorSuggestions(options, filter)
            };
        
            this.$store.dispatch('fetchData', options);
        },
    }
}); 