<?php

namespace V4;

use Exception;
use WP_Error;

/**
 * Map Controller
 * 
 * This controller handles Google Maps API integration through the Yuno platform.
 * It provides endpoints for geocoding, place autocomplete, and place details.
 * 
 * @package V4
 * <AUTHOR>
 * @since 1.0.0
 */
class MapController extends Controller
{
    /**
     * Constructor
     * 
     * Initializes the MapController by loading required libraries and models.
     * 
     * <AUTHOR>
     */
    public function __construct()
    {
        parent::__construct();
        $this->loadLibary('response');
        $this->loadLibary('common');
        $this->loadLibary('validate');
    }

    /**
     * Get location information from coordinates
     * 
     * @param object $request The request object containing lat and lng parameters
     * @return object JSON response with location data
     */
    public function getLocationFromCoordinates($request)
    {
        try {
            $lat = $request->get_param('lat');
            $lng = $request->get_param('lng');

            // Validate coordinates
            if (!$lat || !$lng) {
                return $this->response->error('INVALID_PARAMS', ['message' => 'Latitude and longitude are required']);
            }

            // Validate coordinate format
            if (!is_numeric($lat) || !is_numeric($lng)) {
                return $this->response->error('INVALID_PARAMS', ['message' => 'Invalid coordinate format']);
            }

            // Make request to Google Maps API through Yuno platform
            $url = 'https://maps.googleapis.com/maps/api/geocode/json';
            $params = [
                'latlng' => $lat . ',' . $lng,
                'key' => 'AIzaSyA3fzybiKpzAU03ibY7vVAjqGzzPMZYyxI'
            ];

            $api_response = \wp_remote_get(\add_query_arg($params, $url));

            if (\is_wp_error($api_response)) {
                return $this->response->error('API_ERROR', ['message' => 'Failed to fetch location data']);
            }

            $body = json_decode(\wp_remote_retrieve_body($api_response), true);

            if ($body['status'] !== 'OK') {
                return $this->response->error('API_ERROR', ['message' => $body['status']]);
            }

            return $this->response->success('GET_SUCCESS', $body['results'], ['message' => 'Location data retrieved successfully']);

        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }

    /**
     * Get place suggestions based on search input
     * 
     * @param object $request The request object containing input parameter
     * @return object JSON response with place suggestions
     */
    public function getPlaceSuggestions($request)
    {
        try {
            $input = $request->get_param('input');

            if (!$input) {
                return $this->response->error('INVALID_PARAMS', ['message' => 'Search input is required']);
            }

            // Make request to Google Places API through Yuno platform
            $url = 'https://maps.googleapis.com/maps/api/place/autocomplete/json';
            $params = [
                'input' => $input,
                'key' => 'AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo'
            ];

            $api_response = \wp_remote_get(\add_query_arg($params, $url));

            if (\is_wp_error($api_response)) {
                return $this->response->error('API_ERROR', ['message' => 'Failed to fetch place suggestions']);
            }

            $body = json_decode(\wp_remote_retrieve_body($api_response), true);

            if ($body['status'] !== 'OK' && $body['status'] !== 'ZERO_RESULTS') {
                return $this->response->error('API_ERROR', ['message' => $body['status']]);
            }

            return $this->response->success('GET_SUCCESS', $body['predictions'], ['message' => 'Place suggestions retrieved successfully']);

        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }

    /**
     * Get detailed place information using place_id
     * 
     * @param object $request The request object containing place_id parameter
     * @return object JSON response with place details
     */
    public function getPlaceDetails($request)
    {
        try {
            $placeId = $request->get_param('place_id');

            if (!$placeId) {
                return $this->response->error('INVALID_PARAMS', ['message' => 'Place ID is required']);
            }

            // Make request to Google Places API through Yuno platform
            $url = 'https://maps.googleapis.com/maps/api/place/details/json';
            $params = [
                'place_id' => $placeId,
                'key' => 'AIzaSyC0dcBT_kU_Q4TxL2CsTGAZYrt8mwowdwo'
            ];

            $api_response = \wp_remote_get(\add_query_arg($params, $url));

            if (\is_wp_error($api_response)) {
                return $this->response->error('API_ERROR', ['message' => 'Failed to fetch place details']);
            }

            $body = json_decode(\wp_remote_retrieve_body($api_response), true);

            if ($body['status'] !== 'OK') {
                return $this->response->error('API_ERROR', ['message' => $body['status']]);
            }

            return $this->response->success('GET_SUCCESS', $body['result'], ['message' => 'Place details retrieved successfully']);

        } catch (Exception $e) {
            return $this->response->error('GET_FAIL', ['message' => $this->common->globalExceptionMessage($e)]);
        }
    }
} 