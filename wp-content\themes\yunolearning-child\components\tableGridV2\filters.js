Vue.component('yuno-table-grid-filters', {
    props: ["data", "options", "defaultFilters"],
    template: `
        <section class="filtersWrapper" id="filters" :class="[options.isFilterMobile ? 'mobileView' : '']">
            <template v-if="filters.loading">
                <div class="filters">
                    <div class="yunoDropdown" v-for="i in 4" :key="i">
                        <b-skeleton height="43px" width="150px"></b-skeleton>
                    </div>
                </div>
            </template>
            <template v-if="filters.success">
                <div class="filterHeader">
                    <h3 class="largerTitle">Filters</h3>
                    <a class="filterTrigger" @click="manageFilters">
                        <span class="material-icons">close</span>
                    </a>
                </div>
                <div class="filters otherFilters" :class="[options.isFilterMobile ? 'mobileView' : '']">
                    <template v-for="(filter, i) in filters.data">
                        <template v-if="filter.ui_control_type === 'suggestion'">
                            <yuno-autocomplete
                                :data="filter"
                                :key="'filter-' + i"
                                :defaultFilters="defaultFilters"
                                :payload="data"
                            >
                            </yuno-autocomplete>
                        </template>
                        <template v-if="filter.ui_control_type === 'checkbox'">
                            <yuno-checkbox
                                :data="filter"
                                :key="'filter-' + i"
                                :defaultFilters="defaultFilters"
                                :payload="data"
                                @onCheckboxChange="onCheckboxChange"
                            >
                            </yuno-checkbox>
                        </template>
                        <template v-if="filter.ui_control_type === 'query_suggestion'">
                            <yuno-autocomplete-search
                                :data="filter"
                                :key="'filter-' + i"
                                :defaultFilters="defaultFilters"
                                :payload="data"
                                @onQuerySearch="onQuerySearch"
                            >
                            </yuno-autocomplete-search>
                        </template>
                        <template v-if="filter.ui_control_type === 'dropdown'">
                            <yuno-simple-dropdown
                                :data="filter"
                                :key="'filter-' + i"
                                :payload="data"
                                @onDropdownChange="onDropdownChange"
                                @clearFilter="clearFilter"
                                :defaultFilters="defaultFilters"
                            >
                            </yuno-simple-dropdown>
                        </template>
                        <template v-if="filter.ui_control_type === 'dynamic_dropdown'">
                            <yuno-dynamic-dropdown
                                :data="filter"
                                :key="'filter-' + i"
                                :payload="data"
                                @onDropdownChange="onDropdownChange"
                                @clearFilter="clearFilter"
                                :defaultFilters="defaultFilters"
                            >
                            </yuno-dynamic-dropdown>
                        </template>
                        <template v-if="filter.ui_control_type === 'dropdown_multi_select'">
                            <yuno-multi-select-dropdown
                                :data="filter"
                                :key="'filter-' + i"
                                :options="{'payload': data}"
                                @onDropdownChange="onDropdownChange"
                                @clearFilter="clearFilter"
                                :defaultFilters="defaultFilters"
                            >
                            </yuno-multi-select-dropdown>
                        </template>
                        <template v-if="filter.ui_control_type === 'search'">
                            <yuno-search
                                :data="filter"
                                :key="'filter-' + i"
                                :defaultFilters="defaultFilters"
                                :payload="data"
                                @onSearchInput="onSearchInput"
                            >
                            </yuno-search>
                        </template>
                    </template>
                    <a class="filterTrigger" @click="manageFilters">
                        <span class="material-icons">filter_list</span>
                    </a>
                </div>
            </template>
        </section>
    `,
    data() {
        return {
            mobileView: false
        }
    },
    computed: {
        ...Vuex.mapState([
            'user',
            'filters'
        ]),
    },
    async created() {
        
    },
    mounted() {
        
    },
    methods: {
        onSearchInput(name, filter, isSelect) {
            this.$emit("onSearchInput", name, filter, isSelect)
        },
        onQuerySearch(name, filter, isSelect) {
            this.$emit("onQuerySearch", name, filter, isSelect)
        },
        clearFilter(e, data) {
            this.$emit("clearFilter", e, data)
        },
        manageFilters() {
            this.$emit("manageFilters")
        },
        onCheckboxChange(value, data) {
            this.$emit("onCheckboxChange", value, data)
        },
        onDropdownChange(data, e) {
            this.$emit("onDropdownChange", data, e)
        }
    }
});